/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Ma=Object.create;var $t=Object.defineProperty;var Oa=Object.getOwnPropertyDescriptor;var _a=Object.getOwnPropertyNames;var Ca=Object.getPrototypeOf,La=Object.prototype.hasOwnProperty;var kr=g=>$t(g,"__esModule",{value:!0});var Tr=(g,s)=>()=>(s||g((s={exports:{}}).exports,s),s.exports),Ia=(g,s)=>{kr(g);for(var u in s)$t(g,u,{get:s[u],enumerable:!0})},Aa=(g,s,u)=>{if(s&&typeof s=="object"||typeof s=="function")for(let h of _a(s))!La.call(g,h)&&h!=="default"&&$t(g,h,{get:()=>s[h],enumerable:!(u=Oa(s,h))||u.enumerable});return g},be=g=>Aa(kr($t(g!=null?Ma(Ca(g)):{},"default",g&&g.__esModule&&"default"in g?{get:()=>g.default,enumerable:!0}:{value:g,enumerable:!0})),g);var Or=Tr((Ya,Mr)=>{Mr.exports=function(s,u){u||(u=[0,""]),s=String(s);var h=parseFloat(s,10);return u[0]=h,u[1]=s.match(/[\d.\-\+]*\s*(.*)/)[1]||"",u}});var $r=Tr((Kr,Zn)=>{(function(g){typeof Kr=="object"&&typeof Zn!="undefined"?Zn.exports=g():typeof define=="function"&&define.amd?define([],g):(typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:this).interact=g()})(function(){var g={};Object.defineProperty(g,"__esModule",{value:!0}),g.default=void 0,g.default=function(e){return!(!e||!e.Window)&&e instanceof e.Window};var s={};Object.defineProperty(s,"__esModule",{value:!0}),s.init=v,s.getWindow=function(e){return(0,g.default)(e)?e:(e.ownerDocument||e).defaultView||h.window},s.window=s.realWindow=void 0;var u=void 0;s.realWindow=u;var h=void 0;function v(e){s.realWindow=u=e;var t=e.document.createTextNode("");t.ownerDocument!==e.document&&typeof e.wrap=="function"&&e.wrap(t)===t&&(e=e.wrap(e)),s.window=h=e}s.window=h,typeof window!="undefined"&&window&&v(window);var d={};function E(e){return(E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(d,"__esModule",{value:!0}),d.default=void 0;var k=function(e){return!!e&&E(e)==="object"},P=function(e){return typeof e=="function"},T={window:function(e){return e===s.window||(0,g.default)(e)},docFrag:function(e){return k(e)&&e.nodeType===11},object:k,func:P,number:function(e){return typeof e=="number"},bool:function(e){return typeof e=="boolean"},string:function(e){return typeof e=="string"},element:function(e){if(!e||E(e)!=="object")return!1;var t=s.getWindow(e)||s.window;return/object|function/.test(E(t.Element))?e instanceof t.Element:e.nodeType===1&&typeof e.nodeName=="string"},plainObject:function(e){return k(e)&&!!e.constructor&&/function Object\b/.test(e.constructor.toString())},array:function(e){return k(e)&&e.length!==void 0&&P(e.splice)}};d.default=T;var _={};function M(e){var t=e.interaction;if(t.prepared.name==="drag"){var o=t.prepared.axis;o==="x"?(t.coords.cur.page.y=t.coords.start.page.y,t.coords.cur.client.y=t.coords.start.client.y,t.coords.velocity.client.y=0,t.coords.velocity.page.y=0):o==="y"&&(t.coords.cur.page.x=t.coords.start.page.x,t.coords.cur.client.x=t.coords.start.client.x,t.coords.velocity.client.x=0,t.coords.velocity.page.x=0)}}function B(e){var t=e.iEvent,o=e.interaction;if(o.prepared.name==="drag"){var n=o.prepared.axis;if(n==="x"||n==="y"){var r=n==="x"?"y":"x";t.page[r]=o.coords.start.page[r],t.client[r]=o.coords.start.client[r],t.delta[r]=0}}}Object.defineProperty(_,"__esModule",{value:!0}),_.default=void 0;var H={id:"actions/drag",install:function(e){var t=e.actions,o=e.Interactable,n=e.defaults;o.prototype.draggable=H.draggable,t.map.drag=H,t.methodDict.drag="draggable",n.actions.drag=H.defaults},listeners:{"interactions:before-action-move":M,"interactions:action-resume":M,"interactions:action-move":B,"auto-start:check":function(e){var t=e.interaction,o=e.interactable,n=e.buttons,r=o.options.drag;if(r&&r.enabled&&(!t.pointerIsDown||!/mouse|pointer/.test(t.pointerType)||(n&o.options.drag.mouseButtons)!=0))return e.action={name:"drag",axis:r.lockAxis==="start"?r.startAxis:r.lockAxis},!1}},draggable:function(e){return d.default.object(e)?(this.options.drag.enabled=e.enabled!==!1,this.setPerAction("drag",e),this.setOnEvents("drag",e),/^(xy|x|y|start)$/.test(e.lockAxis)&&(this.options.drag.lockAxis=e.lockAxis),/^(xy|x|y)$/.test(e.startAxis)&&(this.options.drag.startAxis=e.startAxis),this):d.default.bool(e)?(this.options.drag.enabled=e,this):this.options.drag},beforeMove:M,move:B,defaults:{startAxis:"xy",lockAxis:"xy"},getCursor:function(){return"move"}},q=H;_.default=q;var D={};Object.defineProperty(D,"__esModule",{value:!0}),D.default=void 0;var W={init:function(e){var t=e;W.document=t.document,W.DocumentFragment=t.DocumentFragment||Q,W.SVGElement=t.SVGElement||Q,W.SVGSVGElement=t.SVGSVGElement||Q,W.SVGElementInstance=t.SVGElementInstance||Q,W.Element=t.Element||Q,W.HTMLElement=t.HTMLElement||W.Element,W.Event=t.Event,W.Touch=t.Touch||Q,W.PointerEvent=t.PointerEvent||t.MSPointerEvent},document:null,DocumentFragment:null,SVGElement:null,SVGSVGElement:null,SVGElementInstance:null,Element:null,HTMLElement:null,Event:null,Touch:null,PointerEvent:null};function Q(){}var le=W;D.default=le;var $={};Object.defineProperty($,"__esModule",{value:!0}),$.default=void 0;var J={init:function(e){var t=D.default.Element,o=e.navigator||{};J.supportsTouch="ontouchstart"in e||d.default.func(e.DocumentTouch)&&D.default.document instanceof e.DocumentTouch,J.supportsPointerEvent=o.pointerEnabled!==!1&&!!D.default.PointerEvent,J.isIOS=/iP(hone|od|ad)/.test(o.platform),J.isIOS7=/iP(hone|od|ad)/.test(o.platform)&&/OS 7[^\d]/.test(o.appVersion),J.isIe9=/MSIE 9/.test(o.userAgent),J.isOperaMobile=o.appName==="Opera"&&J.supportsTouch&&/Presto/.test(o.userAgent),J.prefixedMatchesSelector="matches"in t.prototype?"matches":"webkitMatchesSelector"in t.prototype?"webkitMatchesSelector":"mozMatchesSelector"in t.prototype?"mozMatchesSelector":"oMatchesSelector"in t.prototype?"oMatchesSelector":"msMatchesSelector",J.pEventTypes=J.supportsPointerEvent?D.default.PointerEvent===e.MSPointerEvent?{up:"MSPointerUp",down:"MSPointerDown",over:"mouseover",out:"mouseout",move:"MSPointerMove",cancel:"MSPointerCancel"}:{up:"pointerup",down:"pointerdown",over:"pointerover",out:"pointerout",move:"pointermove",cancel:"pointercancel"}:null,J.wheelEvent=D.default.document&&"onmousewheel"in D.default.document?"mousewheel":"wheel"},supportsTouch:null,supportsPointerEvent:null,isIOS7:null,isIOS:null,isIe9:null,isOperaMobile:null,prefixedMatchesSelector:null,pEventTypes:null,wheelEvent:null},rn=J;$.default=rn;var A={};function ht(e){var t=e.parentNode;if(d.default.docFrag(t)){for(;(t=t.host)&&d.default.docFrag(t););return t}return t}function vt(e,t){return s.window!==s.realWindow&&(t=t.replace(/\/deep\//g," ")),e[$.default.prefixedMatchesSelector](t)}Object.defineProperty(A,"__esModule",{value:!0}),A.nodeContains=function(e,t){if(e.contains)return e.contains(t);for(;t;){if(t===e)return!0;t=t.parentNode}return!1},A.closest=function(e,t){for(;d.default.element(e);){if(vt(e,t))return e;e=ht(e)}return null},A.parentNode=ht,A.matchesSelector=vt,A.indexOfDeepestElement=function(e){for(var t,o=[],n=0;n<e.length;n++){var r=e[n],i=e[t];if(r&&n!==t)if(i){var c=an(r),a=an(i);if(c!==r.ownerDocument)if(a!==r.ownerDocument)if(c!==a){o=o.length?o:Jn(i);var l=void 0;if(i instanceof D.default.HTMLElement&&r instanceof D.default.SVGElement&&!(r instanceof D.default.SVGSVGElement)){if(r===a)continue;l=r.ownerSVGElement}else l=r;for(var p=Jn(l,i.ownerDocument),f=0;p[f]&&p[f]===o[f];)f++;var m=[p[f-1],p[f],o[f]];if(m[0])for(var b=m[0].lastChild;b;){if(b===m[1]){t=n,o=p;break}if(b===m[2])break;b=b.previousSibling}}else w=r,y=i,(parseInt(s.getWindow(w).getComputedStyle(w).zIndex,10)||0)>=(parseInt(s.getWindow(y).getComputedStyle(y).zIndex,10)||0)&&(t=n);else t=n}else t=n}var w,y;return t},A.matchesUpTo=function(e,t,o){for(;d.default.element(e);){if(vt(e,t))return!0;if((e=ht(e))===o)return vt(e,t)}return!1},A.getActualElement=function(e){return e.correspondingUseElement||e},A.getScrollXY=eo,A.getElementClientRect=to,A.getElementRect=function(e){var t=to(e);if(!$.default.isIOS7&&t){var o=eo(s.getWindow(e));t.left+=o.x,t.right+=o.x,t.top+=o.y,t.bottom+=o.y}return t},A.getPath=function(e){for(var t=[];e;)t.push(e),e=ht(e);return t},A.trySelector=function(e){return!!d.default.string(e)&&(D.default.document.querySelector(e),!0)};var an=function(e){return e.parentNode||e.host};function Jn(e,t){for(var o,n=[],r=e;(o=an(r))&&r!==t&&o!==r.ownerDocument;)n.unshift(r),r=o;return n}function eo(e){return{x:(e=e||s.window).scrollX||e.document.documentElement.scrollLeft,y:e.scrollY||e.document.documentElement.scrollTop}}function to(e){var t=e instanceof D.default.SVGElement?e.getBoundingClientRect():e.getClientRects()[0];return t&&{left:t.left,right:t.right,top:t.top,bottom:t.bottom,width:t.width||t.right-t.left,height:t.height||t.bottom-t.top}}var I={};Object.defineProperty(I,"__esModule",{value:!0}),I.default=function(e,t){for(var o in t)e[o]=t[o];return e};var G={};function sn(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}function no(e,t,o){return e==="parent"?(0,A.parentNode)(o):e==="self"?t.getRect(o):(0,A.closest)(o,e)}Object.defineProperty(G,"__esModule",{value:!0}),G.getStringOptionResult=no,G.resolveRectLike=function(e,t,o,n){var r,i=e;return d.default.string(i)?i=no(i,t,o):d.default.func(i)&&(i=i.apply(void 0,function(c){if(Array.isArray(c))return sn(c)}(r=n)||function(c){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(c))return Array.from(c)}(r)||function(c,a){if(c){if(typeof c=="string")return sn(c,a);var l=Object.prototype.toString.call(c).slice(8,-1);return l==="Object"&&c.constructor&&(l=c.constructor.name),l==="Map"||l==="Set"?Array.from(c):l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?sn(c,a):void 0}}(r)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}())),d.default.element(i)&&(i=(0,A.getElementRect)(i)),i},G.rectToXY=function(e){return e&&{x:"x"in e?e.x:e.left,y:"y"in e?e.y:e.top}},G.xywhToTlbr=function(e){return!e||"left"in e&&"top"in e||((e=(0,I.default)({},e)).left=e.x||0,e.top=e.y||0,e.right=e.right||e.left+e.width,e.bottom=e.bottom||e.top+e.height),e},G.tlbrToXywh=function(e){return!e||"x"in e&&"y"in e||((e=(0,I.default)({},e)).x=e.left||0,e.y=e.top||0,e.width=e.width||(e.right||0)-e.x,e.height=e.height||(e.bottom||0)-e.y),e},G.addEdges=function(e,t,o){e.left&&(t.left+=o.x),e.right&&(t.right+=o.x),e.top&&(t.top+=o.y),e.bottom&&(t.bottom+=o.y),t.width=t.right-t.left,t.height=t.bottom-t.top};var ke={};Object.defineProperty(ke,"__esModule",{value:!0}),ke.default=function(e,t,o){var n=e.options[o],r=n&&n.origin||e.options.origin,i=(0,G.resolveRectLike)(r,e,t,[e&&t]);return(0,G.rectToXY)(i)||{x:0,y:0}};var De={};function oo(e){return e.trim().split(/ +/)}Object.defineProperty(De,"__esModule",{value:!0}),De.default=function e(t,o,n){if(n=n||{},d.default.string(t)&&t.search(" ")!==-1&&(t=oo(t)),d.default.array(t))return t.reduce(function(l,p){return(0,I.default)(l,e(p,o,n))},n);if(d.default.object(t)&&(o=t,t=""),d.default.func(o))n[t]=n[t]||[],n[t].push(o);else if(d.default.array(o))for(var r=0;r<o.length;r++){var i;i=o[r],e(t,i,n)}else if(d.default.object(o))for(var c in o){var a=oo(c).map(function(l){return"".concat(t).concat(l)});e(a,o[c],n)}return n};var we={};Object.defineProperty(we,"__esModule",{value:!0}),we.default=void 0,we.default=function(e,t){return Math.sqrt(e*e+t*t)};var Ze={};function ln(e,t){for(var o in t){var n=ln.prefixedPropREs,r=!1;for(var i in n)if(o.indexOf(i)===0&&n[i].test(o)){r=!0;break}r||typeof t[o]=="function"||(e[o]=t[o])}return e}Object.defineProperty(Ze,"__esModule",{value:!0}),Ze.default=void 0,ln.prefixedPropREs={webkit:/(Movement[XY]|Radius[XY]|RotationAngle|Force)$/,moz:/(Pressure)$/};var Zr=ln;Ze.default=Zr;var L={};function cn(e){return e instanceof D.default.Event||e instanceof D.default.Touch}function Qe(e,t,o){return e=e||"page",(o=o||{}).x=t[e+"X"],o.y=t[e+"Y"],o}function ro(e,t){return t=t||{x:0,y:0},$.default.isOperaMobile&&cn(e)?(Qe("screen",e,t),t.x+=window.scrollX,t.y+=window.scrollY):Qe("page",e,t),t}function io(e,t){return t=t||{},$.default.isOperaMobile&&cn(e)?Qe("screen",e,t):Qe("client",e,t),t}function gt(e){var t=[];return d.default.array(e)?(t[0]=e[0],t[1]=e[1]):e.type==="touchend"?e.touches.length===1?(t[0]=e.touches[0],t[1]=e.changedTouches[0]):e.touches.length===0&&(t[0]=e.changedTouches[0],t[1]=e.changedTouches[1]):(t[0]=e.touches[0],t[1]=e.touches[1]),t}function ao(e){for(var t={pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0},o=0;o<e.length;o++){var n=e[o];for(var r in t)t[r]+=n[r]}for(var i in t)t[i]/=e.length;return t}Object.defineProperty(L,"__esModule",{value:!0}),L.copyCoords=function(e,t){e.page=e.page||{},e.page.x=t.page.x,e.page.y=t.page.y,e.client=e.client||{},e.client.x=t.client.x,e.client.y=t.client.y,e.timeStamp=t.timeStamp},L.setCoordDeltas=function(e,t,o){e.page.x=o.page.x-t.page.x,e.page.y=o.page.y-t.page.y,e.client.x=o.client.x-t.client.x,e.client.y=o.client.y-t.client.y,e.timeStamp=o.timeStamp-t.timeStamp},L.setCoordVelocity=function(e,t){var o=Math.max(t.timeStamp/1e3,.001);e.page.x=t.page.x/o,e.page.y=t.page.y/o,e.client.x=t.client.x/o,e.client.y=t.client.y/o,e.timeStamp=o},L.setZeroCoords=function(e){e.page.x=0,e.page.y=0,e.client.x=0,e.client.y=0},L.isNativePointer=cn,L.getXY=Qe,L.getPageXY=ro,L.getClientXY=io,L.getPointerId=function(e){return d.default.number(e.pointerId)?e.pointerId:e.identifier},L.setCoords=function(e,t,o){var n=t.length>1?ao(t):t[0];ro(n,e.page),io(n,e.client),e.timeStamp=o},L.getTouchPair=gt,L.pointerAverage=ao,L.touchBBox=function(e){if(!e.length)return null;var t=gt(e),o=Math.min(t[0].pageX,t[1].pageX),n=Math.min(t[0].pageY,t[1].pageY),r=Math.max(t[0].pageX,t[1].pageX),i=Math.max(t[0].pageY,t[1].pageY);return{x:o,y:n,left:o,top:n,right:r,bottom:i,width:r-o,height:i-n}},L.touchDistance=function(e,t){var o=t+"X",n=t+"Y",r=gt(e),i=r[0][o]-r[1][o],c=r[0][n]-r[1][n];return(0,we.default)(i,c)},L.touchAngle=function(e,t){var o=t+"X",n=t+"Y",r=gt(e),i=r[1][o]-r[0][o],c=r[1][n]-r[0][n];return 180*Math.atan2(c,i)/Math.PI},L.getPointerType=function(e){return d.default.string(e.pointerType)?e.pointerType:d.default.number(e.pointerType)?[void 0,void 0,"touch","pen","mouse"][e.pointerType]:/touch/.test(e.type||"")||e instanceof D.default.Touch?"touch":"mouse"},L.getEventTargets=function(e){var t=d.default.func(e.composedPath)?e.composedPath():e.path;return[A.getActualElement(t?t[0]:e.target),A.getActualElement(e.currentTarget)]},L.newCoords=function(){return{page:{x:0,y:0},client:{x:0,y:0},timeStamp:0}},L.coordsToEvent=function(e){return{coords:e,get page(){return this.coords.page},get client(){return this.coords.client},get timeStamp(){return this.coords.timeStamp},get pageX(){return this.coords.page.x},get pageY(){return this.coords.page.y},get clientX(){return this.coords.client.x},get clientY(){return this.coords.client.y},get pointerId(){return this.coords.pointerId},get target(){return this.coords.target},get type(){return this.coords.type},get pointerType(){return this.coords.pointerType},get buttons(){return this.coords.buttons},preventDefault:function(){}}},Object.defineProperty(L,"pointerExtend",{enumerable:!0,get:function(){return Ze.default}});var je={};function Qr(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(je,"__esModule",{value:!0}),je.BaseEvent=void 0;var so=function(){function e(n){(function(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")})(this,e),this.type=void 0,this.target=void 0,this.currentTarget=void 0,this.interactable=void 0,this._interaction=void 0,this.timeStamp=void 0,this.immediatePropagationStopped=!1,this.propagationStopped=!1,this._interaction=n}var t,o;return t=e,(o=[{key:"preventDefault",value:function(){}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}}])&&Qr(t.prototype,o),e}();je.BaseEvent=so,Object.defineProperty(so.prototype,"interaction",{get:function(){return this._interaction._proxy},set:function(){}});var K={};Object.defineProperty(K,"__esModule",{value:!0}),K.find=K.findIndex=K.from=K.merge=K.remove=K.contains=void 0,K.contains=function(e,t){return e.indexOf(t)!==-1},K.remove=function(e,t){return e.splice(e.indexOf(t),1)};var lo=function(e,t){for(var o=0;o<t.length;o++){var n=t[o];e.push(n)}return e};K.merge=lo,K.from=function(e){return lo([],e)};var co=function(e,t){for(var o=0;o<e.length;o++)if(t(e[o],o,e))return o;return-1};K.findIndex=co,K.find=function(e,t){return e[co(e,t)]};var he={};function uo(e){return(uo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Jr(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function po(e,t){return(po=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o})(e,t)}function ei(e,t){return!t||uo(t)!=="object"&&typeof t!="function"?function(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}(e):t}function un(e){return(un=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(he,"__esModule",{value:!0}),he.DropEvent=void 0;var ti=function(e){(function(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),l&&po(a,l)})(c,e);var t,o,n,r,i=(n=c,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}(),function(){var a,l=un(n);if(r){var p=un(this).constructor;a=Reflect.construct(l,arguments,p)}else a=l.apply(this,arguments);return ei(this,a)});function c(a,l,p){var f;(function(y,S){if(!(y instanceof S))throw new TypeError("Cannot call a class as a function")})(this,c),(f=i.call(this,l._interaction)).target=void 0,f.dropzone=void 0,f.dragEvent=void 0,f.relatedTarget=void 0,f.draggable=void 0,f.timeStamp=void 0,f.propagationStopped=!1,f.immediatePropagationStopped=!1;var m=p==="dragleave"?a.prev:a.cur,b=m.element,w=m.dropzone;return f.type=p,f.target=b,f.currentTarget=b,f.dropzone=w,f.dragEvent=l,f.relatedTarget=l.target,f.draggable=l.interactable,f.timeStamp=l.timeStamp,f}return t=c,(o=[{key:"reject",value:function(){var a=this,l=this._interaction.dropState;if(this.type==="dropactivate"||this.dropzone&&l.cur.dropzone===this.dropzone&&l.cur.element===this.target)if(l.prev.dropzone=this.dropzone,l.prev.element=this.target,l.rejected=!0,l.events.enter=null,this.stopImmediatePropagation(),this.type==="dropactivate"){var p=l.activeDrops,f=K.findIndex(p,function(b){var w=b.dropzone,y=b.element;return w===a.dropzone&&y===a.target});l.activeDrops.splice(f,1);var m=new c(l,this.dragEvent,"dropdeactivate");m.dropzone=this.dropzone,m.target=this.target,this.dropzone.fire(m)}else this.dropzone.fire(new c(l,this.dragEvent,"dragleave"))}},{key:"preventDefault",value:function(){}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}}])&&Jr(t.prototype,o),c}(je.BaseEvent);he.DropEvent=ti;var mt={};function fo(e,t){for(var o=0;o<e.slice().length;o++){var n=e.slice()[o],r=n.dropzone,i=n.element;t.dropzone=r,t.target=i,r.fire(t),t.propagationStopped=t.immediatePropagationStopped=!1}}function pn(e,t){for(var o=function(i,c){for(var a=i.interactables,l=[],p=0;p<a.list.length;p++){var f=a.list[p];if(f.options.drop.enabled){var m=f.options.drop.accept;if(!(d.default.element(m)&&m!==c||d.default.string(m)&&!A.matchesSelector(c,m)||d.default.func(m)&&!m({dropzone:f,draggableElement:c})))for(var b=d.default.string(f.target)?f._context.querySelectorAll(f.target):d.default.array(f.target)?f.target:[f.target],w=0;w<b.length;w++){var y=b[w];y!==c&&l.push({dropzone:f,element:y,rect:f.getRect(y)})}}}return l}(e,t),n=0;n<o.length;n++){var r=o[n];r.rect=r.dropzone.getRect(r.element)}return o}function ho(e,t,o){for(var n=e.dropState,r=e.interactable,i=e.element,c=[],a=0;a<n.activeDrops.length;a++){var l=n.activeDrops[a],p=l.dropzone,f=l.element,m=l.rect;c.push(p.dropCheck(t,o,r,i,f,m)?f:null)}var b=A.indexOfDeepestElement(c);return n.activeDrops[b]||null}function dn(e,t,o){var n=e.dropState,r={enter:null,leave:null,activate:null,deactivate:null,move:null,drop:null};return o.type==="dragstart"&&(r.activate=new he.DropEvent(n,o,"dropactivate"),r.activate.target=null,r.activate.dropzone=null),o.type==="dragend"&&(r.deactivate=new he.DropEvent(n,o,"dropdeactivate"),r.deactivate.target=null,r.deactivate.dropzone=null),n.rejected||(n.cur.element!==n.prev.element&&(n.prev.dropzone&&(r.leave=new he.DropEvent(n,o,"dragleave"),o.dragLeave=r.leave.target=n.prev.element,o.prevDropzone=r.leave.dropzone=n.prev.dropzone),n.cur.dropzone&&(r.enter=new he.DropEvent(n,o,"dragenter"),o.dragEnter=n.cur.element,o.dropzone=n.cur.dropzone)),o.type==="dragend"&&n.cur.dropzone&&(r.drop=new he.DropEvent(n,o,"drop"),o.dropzone=n.cur.dropzone,o.relatedTarget=n.cur.element),o.type==="dragmove"&&n.cur.dropzone&&(r.move=new he.DropEvent(n,o,"dropmove"),r.move.dragmove=o,o.dropzone=n.cur.dropzone)),r}function fn(e,t){var o=e.dropState,n=o.activeDrops,r=o.cur,i=o.prev;t.leave&&i.dropzone.fire(t.leave),t.enter&&r.dropzone.fire(t.enter),t.move&&r.dropzone.fire(t.move),t.drop&&r.dropzone.fire(t.drop),t.deactivate&&fo(n,t.deactivate),o.prev.dropzone=r.dropzone,o.prev.element=r.element}function vo(e,t){var o=e.interaction,n=e.iEvent,r=e.event;if(n.type==="dragmove"||n.type==="dragend"){var i=o.dropState;t.dynamicDrop&&(i.activeDrops=pn(t,o.element));var c=n,a=ho(o,c,r);i.rejected=i.rejected&&!!a&&a.dropzone===i.cur.dropzone&&a.element===i.cur.element,i.cur.dropzone=a&&a.dropzone,i.cur.element=a&&a.element,i.events=dn(o,0,c)}}Object.defineProperty(mt,"__esModule",{value:!0}),mt.default=void 0;var go={id:"actions/drop",install:function(e){var t=e.actions,o=e.interactStatic,n=e.Interactable,r=e.defaults;e.usePlugin(_.default),n.prototype.dropzone=function(i){return function(c,a){if(d.default.object(a)){if(c.options.drop.enabled=a.enabled!==!1,a.listeners){var l=(0,De.default)(a.listeners),p=Object.keys(l).reduce(function(f,m){return f[/^(enter|leave)/.test(m)?"drag".concat(m):/^(activate|deactivate|move)/.test(m)?"drop".concat(m):m]=l[m],f},{});c.off(c.options.drop.listeners),c.on(p),c.options.drop.listeners=p}return d.default.func(a.ondrop)&&c.on("drop",a.ondrop),d.default.func(a.ondropactivate)&&c.on("dropactivate",a.ondropactivate),d.default.func(a.ondropdeactivate)&&c.on("dropdeactivate",a.ondropdeactivate),d.default.func(a.ondragenter)&&c.on("dragenter",a.ondragenter),d.default.func(a.ondragleave)&&c.on("dragleave",a.ondragleave),d.default.func(a.ondropmove)&&c.on("dropmove",a.ondropmove),/^(pointer|center)$/.test(a.overlap)?c.options.drop.overlap=a.overlap:d.default.number(a.overlap)&&(c.options.drop.overlap=Math.max(Math.min(1,a.overlap),0)),"accept"in a&&(c.options.drop.accept=a.accept),"checker"in a&&(c.options.drop.checker=a.checker),c}return d.default.bool(a)?(c.options.drop.enabled=a,c):c.options.drop}(this,i)},n.prototype.dropCheck=function(i,c,a,l,p,f){return function(m,b,w,y,S,O,x){var C=!1;if(!(x=x||m.getRect(O)))return!!m.options.drop.checker&&m.options.drop.checker(b,w,C,m,O,y,S);var j=m.options.drop.overlap;if(j==="pointer"){var F=(0,ke.default)(y,S,"drag"),X=L.getPageXY(b);X.x+=F.x,X.y+=F.y;var Z=X.x>x.left&&X.x<x.right,N=X.y>x.top&&X.y<x.bottom;C=Z&&N}var Y=y.getRect(S);if(Y&&j==="center"){var ue=Y.left+Y.width/2,ye=Y.top+Y.height/2;C=ue>=x.left&&ue<=x.right&&ye>=x.top&&ye<=x.bottom}return Y&&d.default.number(j)&&(C=Math.max(0,Math.min(x.right,Y.right)-Math.max(x.left,Y.left))*Math.max(0,Math.min(x.bottom,Y.bottom)-Math.max(x.top,Y.top))/(Y.width*Y.height)>=j),m.options.drop.checker&&(C=m.options.drop.checker(b,w,C,m,O,y,S)),C}(this,i,c,a,l,p,f)},o.dynamicDrop=function(i){return d.default.bool(i)?(e.dynamicDrop=i,o):e.dynamicDrop},(0,I.default)(t.phaselessTypes,{dragenter:!0,dragleave:!0,dropactivate:!0,dropdeactivate:!0,dropmove:!0,drop:!0}),t.methodDict.drop="dropzone",e.dynamicDrop=!1,r.actions.drop=go.defaults},listeners:{"interactions:before-action-start":function(e){var t=e.interaction;t.prepared.name==="drag"&&(t.dropState={cur:{dropzone:null,element:null},prev:{dropzone:null,element:null},rejected:null,events:null,activeDrops:[]})},"interactions:after-action-start":function(e,t){var o=e.interaction,n=(e.event,e.iEvent);if(o.prepared.name==="drag"){var r=o.dropState;r.activeDrops=null,r.events=null,r.activeDrops=pn(t,o.element),r.events=dn(o,0,n),r.events.activate&&(fo(r.activeDrops,r.events.activate),t.fire("actions/drop:start",{interaction:o,dragEvent:n}))}},"interactions:action-move":vo,"interactions:after-action-move":function(e,t){var o=e.interaction,n=e.iEvent;o.prepared.name==="drag"&&(fn(o,o.dropState.events),t.fire("actions/drop:move",{interaction:o,dragEvent:n}),o.dropState.events={})},"interactions:action-end":function(e,t){if(e.interaction.prepared.name==="drag"){var o=e.interaction,n=e.iEvent;vo(e,t),fn(o,o.dropState.events),t.fire("actions/drop:end",{interaction:o,dragEvent:n})}},"interactions:stop":function(e){var t=e.interaction;if(t.prepared.name==="drag"){var o=t.dropState;o&&(o.activeDrops=null,o.events=null,o.cur.dropzone=null,o.cur.element=null,o.prev.dropzone=null,o.prev.element=null,o.rejected=!1)}}},getActiveDrops:pn,getDrop:ho,getDropEvents:dn,fireDropEvents:fn,defaults:{enabled:!1,accept:null,overlap:"pointer"}},ni=go;mt.default=ni;var yt={};function hn(e){var t=e.interaction,o=e.iEvent,n=e.phase;if(t.prepared.name==="gesture"){var r=t.pointers.map(function(p){return p.pointer}),i=n==="start",c=n==="end",a=t.interactable.options.deltaSource;if(o.touches=[r[0],r[1]],i)o.distance=L.touchDistance(r,a),o.box=L.touchBBox(r),o.scale=1,o.ds=0,o.angle=L.touchAngle(r,a),o.da=0,t.gesture.startDistance=o.distance,t.gesture.startAngle=o.angle;else if(c){var l=t.prevEvent;o.distance=l.distance,o.box=l.box,o.scale=l.scale,o.ds=0,o.angle=l.angle,o.da=0}else o.distance=L.touchDistance(r,a),o.box=L.touchBBox(r),o.scale=o.distance/t.gesture.startDistance,o.angle=L.touchAngle(r,a),o.ds=o.scale-t.gesture.scale,o.da=o.angle-t.gesture.angle;t.gesture.distance=o.distance,t.gesture.angle=o.angle,d.default.number(o.scale)&&o.scale!==1/0&&!isNaN(o.scale)&&(t.gesture.scale=o.scale)}}Object.defineProperty(yt,"__esModule",{value:!0}),yt.default=void 0;var vn={id:"actions/gesture",before:["actions/drag","actions/resize"],install:function(e){var t=e.actions,o=e.Interactable,n=e.defaults;o.prototype.gesturable=function(r){return d.default.object(r)?(this.options.gesture.enabled=r.enabled!==!1,this.setPerAction("gesture",r),this.setOnEvents("gesture",r),this):d.default.bool(r)?(this.options.gesture.enabled=r,this):this.options.gesture},t.map.gesture=vn,t.methodDict.gesture="gesturable",n.actions.gesture=vn.defaults},listeners:{"interactions:action-start":hn,"interactions:action-move":hn,"interactions:action-end":hn,"interactions:new":function(e){e.interaction.gesture={angle:0,distance:0,scale:1,startAngle:0,startDistance:0}},"auto-start:check":function(e){if(!(e.interaction.pointers.length<2)){var t=e.interactable.options.gesture;if(t&&t.enabled)return e.action={name:"gesture"},!1}}},defaults:{},getCursor:function(){return""}},oi=vn;yt.default=oi;var bt={};function ri(e,t,o,n,r,i,c){if(!t)return!1;if(t===!0){var a=d.default.number(i.width)?i.width:i.right-i.left,l=d.default.number(i.height)?i.height:i.bottom-i.top;if(c=Math.min(c,Math.abs((e==="left"||e==="right"?a:l)/2)),a<0&&(e==="left"?e="right":e==="right"&&(e="left")),l<0&&(e==="top"?e="bottom":e==="bottom"&&(e="top")),e==="left")return o.x<(a>=0?i.left:i.right)+c;if(e==="top")return o.y<(l>=0?i.top:i.bottom)+c;if(e==="right")return o.x>(a>=0?i.right:i.left)-c;if(e==="bottom")return o.y>(l>=0?i.bottom:i.top)-c}return!!d.default.element(n)&&(d.default.element(t)?t===n:A.matchesUpTo(n,t,r))}function mo(e){var t=e.iEvent,o=e.interaction;if(o.prepared.name==="resize"&&o.resizeAxes){var n=t;o.interactable.options.resize.square?(o.resizeAxes==="y"?n.delta.x=n.delta.y:n.delta.y=n.delta.x,n.axes="xy"):(n.axes=o.resizeAxes,o.resizeAxes==="x"?n.delta.y=0:o.resizeAxes==="y"&&(n.delta.x=0))}}Object.defineProperty(bt,"__esModule",{value:!0}),bt.default=void 0;var ve={id:"actions/resize",before:["actions/drag"],install:function(e){var t=e.actions,o=e.browser,n=e.Interactable,r=e.defaults;ve.cursors=function(i){return i.isIe9?{x:"e-resize",y:"s-resize",xy:"se-resize",top:"n-resize",left:"w-resize",bottom:"s-resize",right:"e-resize",topleft:"se-resize",bottomright:"se-resize",topright:"ne-resize",bottomleft:"ne-resize"}:{x:"ew-resize",y:"ns-resize",xy:"nwse-resize",top:"ns-resize",left:"ew-resize",bottom:"ns-resize",right:"ew-resize",topleft:"nwse-resize",bottomright:"nwse-resize",topright:"nesw-resize",bottomleft:"nesw-resize"}}(o),ve.defaultMargin=o.supportsTouch||o.supportsPointerEvent?20:10,n.prototype.resizable=function(i){return function(c,a,l){return d.default.object(a)?(c.options.resize.enabled=a.enabled!==!1,c.setPerAction("resize",a),c.setOnEvents("resize",a),d.default.string(a.axis)&&/^x$|^y$|^xy$/.test(a.axis)?c.options.resize.axis=a.axis:a.axis===null&&(c.options.resize.axis=l.defaults.actions.resize.axis),d.default.bool(a.preserveAspectRatio)?c.options.resize.preserveAspectRatio=a.preserveAspectRatio:d.default.bool(a.square)&&(c.options.resize.square=a.square),c):d.default.bool(a)?(c.options.resize.enabled=a,c):c.options.resize}(this,i,e)},t.map.resize=ve,t.methodDict.resize="resizable",r.actions.resize=ve.defaults},listeners:{"interactions:new":function(e){e.interaction.resizeAxes="xy"},"interactions:action-start":function(e){(function(t){var o=t.iEvent,n=t.interaction;if(n.prepared.name==="resize"&&n.prepared.edges){var r=o,i=n.rect;n._rects={start:(0,I.default)({},i),corrected:(0,I.default)({},i),previous:(0,I.default)({},i),delta:{left:0,right:0,width:0,top:0,bottom:0,height:0}},r.edges=n.prepared.edges,r.rect=n._rects.corrected,r.deltaRect=n._rects.delta}})(e),mo(e)},"interactions:action-move":function(e){(function(t){var o=t.iEvent,n=t.interaction;if(n.prepared.name==="resize"&&n.prepared.edges){var r=o,i=n.interactable.options.resize.invert,c=i==="reposition"||i==="negate",a=n.rect,l=n._rects,p=l.start,f=l.corrected,m=l.delta,b=l.previous;if((0,I.default)(b,f),c){if((0,I.default)(f,a),i==="reposition"){if(f.top>f.bottom){var w=f.top;f.top=f.bottom,f.bottom=w}if(f.left>f.right){var y=f.left;f.left=f.right,f.right=y}}}else f.top=Math.min(a.top,p.bottom),f.bottom=Math.max(a.bottom,p.top),f.left=Math.min(a.left,p.right),f.right=Math.max(a.right,p.left);for(var S in f.width=f.right-f.left,f.height=f.bottom-f.top,f)m[S]=f[S]-b[S];r.edges=n.prepared.edges,r.rect=f,r.deltaRect=m}})(e),mo(e)},"interactions:action-end":function(e){var t=e.iEvent,o=e.interaction;if(o.prepared.name==="resize"&&o.prepared.edges){var n=t;n.edges=o.prepared.edges,n.rect=o._rects.corrected,n.deltaRect=o._rects.delta}},"auto-start:check":function(e){var t=e.interaction,o=e.interactable,n=e.element,r=e.rect,i=e.buttons;if(r){var c=(0,I.default)({},t.coords.cur.page),a=o.options.resize;if(a&&a.enabled&&(!t.pointerIsDown||!/mouse|pointer/.test(t.pointerType)||(i&a.mouseButtons)!=0)){if(d.default.object(a.edges)){var l={left:!1,right:!1,top:!1,bottom:!1};for(var p in l)l[p]=ri(p,a.edges[p],c,t._latestPointer.eventTarget,n,r,a.margin||ve.defaultMargin);l.left=l.left&&!l.right,l.top=l.top&&!l.bottom,(l.left||l.right||l.top||l.bottom)&&(e.action={name:"resize",edges:l})}else{var f=a.axis!=="y"&&c.x>r.right-ve.defaultMargin,m=a.axis!=="x"&&c.y>r.bottom-ve.defaultMargin;(f||m)&&(e.action={name:"resize",axes:(f?"x":"")+(m?"y":"")})}return!e.action&&void 0}}}},defaults:{square:!1,preserveAspectRatio:!1,axis:"xy",margin:NaN,edges:null,invert:"none"},cursors:null,getCursor:function(e){var t=e.edges,o=e.axis,n=e.name,r=ve.cursors,i=null;if(o)i=r[n+o];else if(t){for(var c="",a=["top","bottom","left","right"],l=0;l<a.length;l++){var p=a[l];t[p]&&(c+=p)}i=r[c]}return i},defaultMargin:null},ii=ve;bt.default=ii;var wt={};Object.defineProperty(wt,"__esModule",{value:!0}),wt.default=void 0;var ai={id:"actions",install:function(e){e.usePlugin(yt.default),e.usePlugin(bt.default),e.usePlugin(_.default),e.usePlugin(mt.default)}};wt.default=ai;var de={};Object.defineProperty(de,"__esModule",{value:!0}),de.default=void 0;var ge,Te,yo=0,si={request:function(e){return ge(e)},cancel:function(e){return Te(e)},init:function(e){if(ge=e.requestAnimationFrame,Te=e.cancelAnimationFrame,!ge)for(var t=["ms","moz","webkit","o"],o=0;o<t.length;o++){var n=t[o];ge=e["".concat(n,"RequestAnimationFrame")],Te=e["".concat(n,"CancelAnimationFrame")]||e["".concat(n,"CancelRequestAnimationFrame")]}ge=ge&&ge.bind(e),Te=Te&&Te.bind(e),ge||(ge=function(r){var i=Date.now(),c=Math.max(0,16-(i-yo)),a=e.setTimeout(function(){r(i+c)},c);return yo=i+c,a},Te=function(r){return clearTimeout(r)})}};de.default=si;var Ee={};Object.defineProperty(Ee,"__esModule",{value:!0}),Ee.getContainer=Et,Ee.getScroll=Je,Ee.getScrollSize=function(e){return d.default.window(e)&&(e=window.document.body),{x:e.scrollWidth,y:e.scrollHeight}},Ee.getScrollSizeDelta=function(e,t){var o=e.interaction,n=e.element,r=o&&o.interactable.options[o.prepared.name].autoScroll;if(!r||!r.enabled)return t(),{x:0,y:0};var i=Et(r.container,o.interactable,n),c=Je(i);t();var a=Je(i);return{x:a.x-c.x,y:a.y-c.y}},Ee.default=void 0;var R={defaults:{enabled:!1,margin:60,container:null,speed:300},now:Date.now,interaction:null,i:0,x:0,y:0,isScrolling:!1,prevTime:0,margin:0,speed:0,start:function(e){R.isScrolling=!0,de.default.cancel(R.i),e.autoScroll=R,R.interaction=e,R.prevTime=R.now(),R.i=de.default.request(R.scroll)},stop:function(){R.isScrolling=!1,R.interaction&&(R.interaction.autoScroll=null),de.default.cancel(R.i)},scroll:function(){var e=R.interaction,t=e.interactable,o=e.element,n=e.prepared.name,r=t.options[n].autoScroll,i=Et(r.container,t,o),c=R.now(),a=(c-R.prevTime)/1e3,l=r.speed*a;if(l>=1){var p={x:R.x*l,y:R.y*l};if(p.x||p.y){var f=Je(i);d.default.window(i)?i.scrollBy(p.x,p.y):i&&(i.scrollLeft+=p.x,i.scrollTop+=p.y);var m=Je(i),b={x:m.x-f.x,y:m.y-f.y};(b.x||b.y)&&t.fire({type:"autoscroll",target:o,interactable:t,delta:b,interaction:e,container:i})}R.prevTime=c}R.isScrolling&&(de.default.cancel(R.i),R.i=de.default.request(R.scroll))},check:function(e,t){var o;return(o=e.options[t].autoScroll)==null?void 0:o.enabled},onInteractionMove:function(e){var t=e.interaction,o=e.pointer;if(t.interacting()&&R.check(t.interactable,t.prepared.name))if(t.simulation)R.x=R.y=0;else{var n,r,i,c,a=t.interactable,l=t.element,p=t.prepared.name,f=a.options[p].autoScroll,m=Et(f.container,a,l);if(d.default.window(m))c=o.clientX<R.margin,n=o.clientY<R.margin,r=o.clientX>m.innerWidth-R.margin,i=o.clientY>m.innerHeight-R.margin;else{var b=A.getElementClientRect(m);c=o.clientX<b.left+R.margin,n=o.clientY<b.top+R.margin,r=o.clientX>b.right-R.margin,i=o.clientY>b.bottom-R.margin}R.x=r?1:c?-1:0,R.y=i?1:n?-1:0,R.isScrolling||(R.margin=f.margin,R.speed=f.speed,R.start(t))}}};function Et(e,t,o){return(d.default.string(e)?(0,G.getStringOptionResult)(e,t,o):e)||(0,s.getWindow)(o)}function Je(e){return d.default.window(e)&&(e=window.document.body),{x:e.scrollLeft,y:e.scrollTop}}var li={id:"auto-scroll",install:function(e){var t=e.defaults,o=e.actions;e.autoScroll=R,R.now=function(){return e.now()},o.phaselessTypes.autoscroll=!0,t.perAction.autoScroll=R.defaults},listeners:{"interactions:new":function(e){e.interaction.autoScroll=null},"interactions:destroy":function(e){e.interaction.autoScroll=null,R.stop(),R.interaction&&(R.interaction=null)},"interactions:stop":R.stop,"interactions:action-move":function(e){return R.onInteractionMove(e)}}};Ee.default=li;var ae={};Object.defineProperty(ae,"__esModule",{value:!0}),ae.warnOnce=function(e,t){var o=!1;return function(){return o||(s.window.console.warn(t),o=!0),e.apply(this,arguments)}},ae.copyAction=function(e,t){return e.name=t.name,e.axis=t.axis,e.edges=t.edges,e},ae.sign=void 0,ae.sign=function(e){return e>=0?1:-1};var xt={};function ci(e){return d.default.bool(e)?(this.options.styleCursor=e,this):e===null?(delete this.options.styleCursor,this):this.options.styleCursor}function ui(e){return d.default.func(e)?(this.options.actionChecker=e,this):e===null?(delete this.options.actionChecker,this):this.options.actionChecker}Object.defineProperty(xt,"__esModule",{value:!0}),xt.default=void 0;var pi={id:"auto-start/interactableMethods",install:function(e){var t=e.Interactable;t.prototype.getAction=function(o,n,r,i){var c=function(a,l,p,f,m){var b=a.getRect(f),w={action:null,interactable:a,interaction:p,element:f,rect:b,buttons:l.buttons||{0:1,1:4,3:8,4:16}[l.button]};return m.fire("auto-start:check",w),w.action}(this,n,r,i,e);return this.options.actionChecker?this.options.actionChecker(o,n,c,this,i,r):c},t.prototype.ignoreFrom=(0,ae.warnOnce)(function(o){return this._backCompatOption("ignoreFrom",o)},"Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue})."),t.prototype.allowFrom=(0,ae.warnOnce)(function(o){return this._backCompatOption("allowFrom",o)},"Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue})."),t.prototype.actionChecker=ui,t.prototype.styleCursor=ci}};xt.default=pi;var He={};function bo(e,t,o,n,r){return t.testIgnoreAllow(t.options[e.name],o,n)&&t.options[e.name].enabled&&St(t,o,e,r)?e:null}function di(e,t,o,n,r,i,c){for(var a=0,l=n.length;a<l;a++){var p=n[a],f=r[a],m=p.getAction(t,o,e,f);if(m){var b=bo(m,p,f,i,c);if(b)return{action:b,interactable:p,element:f}}}return{action:null,interactable:null,element:null}}function wo(e,t,o,n,r){var i=[],c=[],a=n;function l(f){i.push(f),c.push(a)}for(;d.default.element(a);){i=[],c=[],r.interactables.forEachMatch(a,l);var p=di(e,t,o,i,c,n,r);if(p.action&&!p.interactable.options[p.action.name].manualStart)return p;a=A.parentNode(a)}return{action:null,interactable:null,element:null}}function Eo(e,t,o){var n=t.action,r=t.interactable,i=t.element;n=n||{name:null},e.interactable=r,e.element=i,(0,ae.copyAction)(e.prepared,n),e.rect=r&&n.name?r.getRect(i):null,So(e,o),o.fire("autoStart:prepared",{interaction:e})}function St(e,t,o,n){var r=e.options,i=r[o.name].max,c=r[o.name].maxPerElement,a=n.autoStart.maxInteractions,l=0,p=0,f=0;if(!(i&&c&&a))return!1;for(var m=0;m<n.interactions.list.length;m++){var b=n.interactions.list[m],w=b.prepared.name;if(b.interacting()&&(++l>=a||b.interactable===e&&((p+=w===o.name?1:0)>=i||b.element===t&&(f++,w===o.name&&f>=c))))return!1}return a>0}function xo(e,t){return d.default.number(e)?(t.autoStart.maxInteractions=e,this):t.autoStart.maxInteractions}function gn(e,t,o){var n=o.autoStart.cursorElement;n&&n!==e&&(n.style.cursor=""),e.ownerDocument.documentElement.style.cursor=t,e.style.cursor=t,o.autoStart.cursorElement=t?e:null}function So(e,t){var o=e.interactable,n=e.element,r=e.prepared;if(e.pointerType==="mouse"&&o&&o.options.styleCursor){var i="";if(r.name){var c=o.options[r.name].cursorChecker;i=d.default.func(c)?c(r,o,n,e._interacting):t.actions.map[r.name].getCursor(r)}gn(e.element,i||"",t)}else t.autoStart.cursorElement&&gn(t.autoStart.cursorElement,"",t)}Object.defineProperty(He,"__esModule",{value:!0}),He.default=void 0;var fi={id:"auto-start/base",before:["actions"],install:function(e){var t=e.interactStatic,o=e.defaults;e.usePlugin(xt.default),o.base.actionChecker=null,o.base.styleCursor=!0,(0,I.default)(o.perAction,{manualStart:!1,max:1/0,maxPerElement:1,allowFrom:null,ignoreFrom:null,mouseButtons:1}),t.maxInteractions=function(n){return xo(n,e)},e.autoStart={maxInteractions:1/0,withinInteractionLimit:St,cursorElement:null}},listeners:{"interactions:down":function(e,t){var o=e.interaction,n=e.pointer,r=e.event,i=e.eventTarget;o.interacting()||Eo(o,wo(o,n,r,i,t),t)},"interactions:move":function(e,t){(function(o,n){var r=o.interaction,i=o.pointer,c=o.event,a=o.eventTarget;r.pointerType!=="mouse"||r.pointerIsDown||r.interacting()||Eo(r,wo(r,i,c,a,n),n)})(e,t),function(o,n){var r=o.interaction;if(r.pointerIsDown&&!r.interacting()&&r.pointerWasMoved&&r.prepared.name){n.fire("autoStart:before-start",o);var i=r.interactable,c=r.prepared.name;c&&i&&(i.options[c].manualStart||!St(i,r.element,r.prepared,n)?r.stop():(r.start(r.prepared,i,r.element),So(r,n)))}}(e,t)},"interactions:stop":function(e,t){var o=e.interaction,n=o.interactable;n&&n.options.styleCursor&&gn(o.element,"",t)}},maxInteractions:xo,withinInteractionLimit:St,validateAction:bo};He.default=fi;var Pt={};Object.defineProperty(Pt,"__esModule",{value:!0}),Pt.default=void 0;var hi={id:"auto-start/dragAxis",listeners:{"autoStart:before-start":function(e,t){var o=e.interaction,n=e.eventTarget,r=e.dx,i=e.dy;if(o.prepared.name==="drag"){var c=Math.abs(r),a=Math.abs(i),l=o.interactable.options.drag,p=l.startAxis,f=c>a?"x":c<a?"y":"xy";if(o.prepared.axis=l.lockAxis==="start"?f[0]:l.lockAxis,f!=="xy"&&p!=="xy"&&p!==f){o.prepared.name=null;for(var m=n,b=function(y){if(y!==o.interactable){var S=o.interactable.options.drag;if(!S.manualStart&&y.testIgnoreAllow(S,m,n)){var O=y.getAction(o.downPointer,o.downEvent,o,m);if(O&&O.name==="drag"&&function(x,C){if(!C)return!1;var j=C.options.drag.startAxis;return x==="xy"||j==="xy"||j===x}(f,y)&&He.default.validateAction(O,y,m,n,t))return y}}};d.default.element(m);){var w=t.interactables.forEachMatch(m,b);if(w){o.prepared.name="drag",o.interactable=w,o.element=m;break}m=(0,A.parentNode)(m)}}}}}};Pt.default=hi;var kt={};function mn(e){var t=e.prepared&&e.prepared.name;if(!t)return null;var o=e.interactable.options;return o[t].hold||o[t].delay}Object.defineProperty(kt,"__esModule",{value:!0}),kt.default=void 0;var vi={id:"auto-start/hold",install:function(e){var t=e.defaults;e.usePlugin(He.default),t.perAction.hold=0,t.perAction.delay=0},listeners:{"interactions:new":function(e){e.interaction.autoStartHoldTimer=null},"autoStart:prepared":function(e){var t=e.interaction,o=mn(t);o>0&&(t.autoStartHoldTimer=setTimeout(function(){t.start(t.prepared,t.interactable,t.element)},o))},"interactions:move":function(e){var t=e.interaction,o=e.duplicate;t.autoStartHoldTimer&&t.pointerWasMoved&&!o&&(clearTimeout(t.autoStartHoldTimer),t.autoStartHoldTimer=null)},"autoStart:before-start":function(e){var t=e.interaction;mn(t)>0&&(t.prepared.name=null)}},getHoldDuration:mn};kt.default=vi;var Tt={};Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.default=void 0;var gi={id:"auto-start",install:function(e){e.usePlugin(He.default),e.usePlugin(kt.default),e.usePlugin(Pt.default)}};Tt.default=gi;var Re={};function mi(e){return/^(always|never|auto)$/.test(e)?(this.options.preventDefault=e,this):d.default.bool(e)?(this.options.preventDefault=e?"always":"never",this):this.options.preventDefault}function yi(e){var t=e.interaction,o=e.event;t.interactable&&t.interactable.checkAndPreventDefault(o)}function Po(e){var t=e.Interactable;t.prototype.preventDefault=mi,t.prototype.checkAndPreventDefault=function(o){return function(n,r,i){var c=n.options.preventDefault;if(c!=="never")if(c!=="always"){if(r.events.supportsPassive&&/^touch(start|move)$/.test(i.type)){var a=(0,s.getWindow)(i.target).document,l=r.getDocOptions(a);if(!l||!l.events||l.events.passive!==!1)return}/^(mouse|pointer|touch)*(down|start)/i.test(i.type)||d.default.element(i.target)&&(0,A.matchesSelector)(i.target,"input,select,textarea,[contenteditable=true],[contenteditable=true] *")||i.preventDefault()}else i.preventDefault()}(this,e,o)},e.interactions.docEvents.push({type:"dragstart",listener:function(o){for(var n=0;n<e.interactions.list.length;n++){var r=e.interactions.list[n];if(r.element&&(r.element===o.target||(0,A.nodeContains)(r.element,o.target)))return void r.interactable.checkAndPreventDefault(o)}}})}Object.defineProperty(Re,"__esModule",{value:!0}),Re.install=Po,Re.default=void 0;var bi={id:"core/interactablePreventDefault",install:Po,listeners:["down","move","up","cancel"].reduce(function(e,t){return e["interactions:".concat(t)]=yi,e},{})};Re.default=bi;var Mt={};Object.defineProperty(Mt,"__esModule",{value:!0}),Mt.default=void 0,Mt.default={};var ze,Ot={};function yn(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}Object.defineProperty(Ot,"__esModule",{value:!0}),Ot.default=void 0,function(e){e.touchAction="touchAction",e.boxSizing="boxSizing",e.noListeners="noListeners"}(ze||(ze={}));var ko="[interact.js] ",bn={touchAction:"https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action",boxSizing:"https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing"},wn=[{name:ze.touchAction,perform:function(e){return!function(t,o,n){for(var r=t;d.default.element(r);){if(To(r,"touchAction",n))return!0;r=(0,A.parentNode)(r)}return!1}(e.element,0,/pan-|pinch|none/)},getInfo:function(e){return[e.element,bn.touchAction]},text:`Consider adding CSS "touch-action: none" to this element
`},{name:ze.boxSizing,perform:function(e){var t=e.element;return e.prepared.name==="resize"&&t instanceof D.default.HTMLElement&&!To(t,"boxSizing",/border-box/)},text:'Consider adding CSS "box-sizing: border-box" to this resizable element',getInfo:function(e){return[e.element,bn.boxSizing]}},{name:ze.noListeners,perform:function(e){var t=e.prepared.name;return!(e.interactable.events.types["".concat(t,"move")]||[]).length},getInfo:function(e){return[e.prepared.name,e.interactable]},text:"There are no listeners set for this action"}];function To(e,t,o){var n=e.style[t]||s.window.getComputedStyle(e)[t];return o.test((n||"").toString())}var wi={id:"dev-tools",install:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=t.logger,n=e.Interactable,r=e.defaults;e.logger=o||console,r.base.devTools={ignore:{}},n.prototype.devTools=function(i){return i?((0,I.default)(this.options.devTools,i),this):this.options.devTools},e.usePlugin(Mt.default)},listeners:{"interactions:action-start":function(e,t){for(var o=e.interaction,n=0;n<wn.length;n++){var r,i=wn[n],c=o.interactable&&o.interactable.options;c&&c.devTools&&c.devTools.ignore[i.name]||!i.perform(o)||(r=t.logger).warn.apply(r,[ko+i.text].concat(function(l){if(Array.isArray(l))return yn(l)}(a=i.getInfo(o))||function(l){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(l))return Array.from(l)}(a)||function(l,p){if(l){if(typeof l=="string")return yn(l,p);var f=Object.prototype.toString.call(l).slice(8,-1);return f==="Object"&&l.constructor&&(f=l.constructor.name),f==="Map"||f==="Set"?Array.from(l):f==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(f)?yn(l,p):void 0}}(a)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()))}var a}},checks:wn,CheckName:ze,links:bn,prefix:ko};Ot.default=wi;var Me={};Object.defineProperty(Me,"__esModule",{value:!0}),Me.default=function e(t){var o={};for(var n in t){var r=t[n];d.default.plainObject(r)?o[n]=e(r):d.default.array(r)?o[n]=K.from(r):o[n]=r}return o};var Oe={};function Mo(e,t){return function(o){if(Array.isArray(o))return o}(e)||function(o,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(o)){var r=[],i=!0,c=!1,a=void 0;try{for(var l,p=o[Symbol.iterator]();!(i=(l=p.next()).done)&&(r.push(l.value),!n||r.length!==n);i=!0);}catch(f){c=!0,a=f}finally{try{i||p.return==null||p.return()}finally{if(c)throw a}}return r}}(e,t)||function(o,n){if(o){if(typeof o=="string")return Oo(o,n);var r=Object.prototype.toString.call(o).slice(8,-1);return r==="Object"&&o.constructor&&(r=o.constructor.name),r==="Map"||r==="Set"?Array.from(o):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Oo(o,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Oo(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}function Ei(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.getRectOffset=_o,Oe.default=void 0;var xi=function(){function e(n){(function(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")})(this,e),this.states=[],this.startOffset={left:0,right:0,top:0,bottom:0},this.startDelta=void 0,this.result=void 0,this.endResult=void 0,this.edges=void 0,this.interaction=void 0,this.interaction=n,this.result=_t()}var t,o;return t=e,(o=[{key:"start",value:function(n,r){var i=n.phase,c=this.interaction,a=function(p){var f=p.interactable.options[p.prepared.name],m=f.modifiers;return m&&m.length?m:["snap","snapSize","snapEdges","restrict","restrictEdges","restrictSize"].map(function(b){var w=f[b];return w&&w.enabled&&{options:w,methods:w._methods}}).filter(function(b){return!!b})}(c);this.prepareStates(a),this.edges=(0,I.default)({},c.edges),this.startOffset=_o(c.rect,r),this.startDelta={x:0,y:0};var l=this.fillArg({phase:i,pageCoords:r,preEnd:!1});return this.result=_t(),this.startAll(l),this.result=this.setAll(l)}},{key:"fillArg",value:function(n){var r=this.interaction;return n.interaction=r,n.interactable=r.interactable,n.element=r.element,n.rect=n.rect||r.rect,n.edges=this.edges,n.startOffset=this.startOffset,n}},{key:"startAll",value:function(n){for(var r=0;r<this.states.length;r++){var i=this.states[r];i.methods.start&&(n.state=i,i.methods.start(n))}}},{key:"setAll",value:function(n){var r=n.phase,i=n.preEnd,c=n.skipModifiers,a=n.rect;n.coords=(0,I.default)({},n.pageCoords),n.rect=(0,I.default)({},a);for(var l=c?this.states.slice(c):this.states,p=_t(n.coords,n.rect),f=0;f<l.length;f++){var m,b=l[f],w=b.options,y=(0,I.default)({},n.coords),S=null;(m=b.methods)!=null&&m.set&&this.shouldDo(w,i,r)&&(n.state=b,S=b.methods.set(n),G.addEdges(this.interaction.edges,n.rect,{x:n.coords.x-y.x,y:n.coords.y-y.y})),p.eventProps.push(S)}p.delta.x=n.coords.x-n.pageCoords.x,p.delta.y=n.coords.y-n.pageCoords.y,p.rectDelta.left=n.rect.left-a.left,p.rectDelta.right=n.rect.right-a.right,p.rectDelta.top=n.rect.top-a.top,p.rectDelta.bottom=n.rect.bottom-a.bottom;var O=this.result.coords,x=this.result.rect;if(O&&x){var C=p.rect.left!==x.left||p.rect.right!==x.right||p.rect.top!==x.top||p.rect.bottom!==x.bottom;p.changed=C||O.x!==p.coords.x||O.y!==p.coords.y}return p}},{key:"applyToInteraction",value:function(n){var r=this.interaction,i=n.phase,c=r.coords.cur,a=r.coords.start,l=this.result,p=this.startDelta,f=l.delta;i==="start"&&(0,I.default)(this.startDelta,l.delta);for(var m=[[a,p],[c,f]],b=0;b<m.length;b++){var w=Mo(m[b],2),y=w[0],S=w[1];y.page.x+=S.x,y.page.y+=S.y,y.client.x+=S.x,y.client.y+=S.y}var O=this.result.rectDelta,x=n.rect||r.rect;x.left+=O.left,x.right+=O.right,x.top+=O.top,x.bottom+=O.bottom,x.width=x.right-x.left,x.height=x.bottom-x.top}},{key:"setAndApply",value:function(n){var r=this.interaction,i=n.phase,c=n.preEnd,a=n.skipModifiers,l=this.setAll(this.fillArg({preEnd:c,phase:i,pageCoords:n.modifiedCoords||r.coords.cur.page}));if(this.result=l,!l.changed&&(!a||a<this.states.length)&&r.interacting())return!1;if(n.modifiedCoords){var p=r.coords.cur.page,f={x:n.modifiedCoords.x-p.x,y:n.modifiedCoords.y-p.y};l.coords.x+=f.x,l.coords.y+=f.y,l.delta.x+=f.x,l.delta.y+=f.y}this.applyToInteraction(n)}},{key:"beforeEnd",value:function(n){var r=n.interaction,i=n.event,c=this.states;if(c&&c.length){for(var a=!1,l=0;l<c.length;l++){var p=c[l];n.state=p;var f=p.options,m=p.methods,b=m.beforeEnd&&m.beforeEnd(n);if(b)return this.endResult=b,!1;a=a||!a&&this.shouldDo(f,!0,n.phase,!0)}a&&r.move({event:i,preEnd:!0})}}},{key:"stop",value:function(n){var r=n.interaction;if(this.states&&this.states.length){var i=(0,I.default)({states:this.states,interactable:r.interactable,element:r.element,rect:null},n);this.fillArg(i);for(var c=0;c<this.states.length;c++){var a=this.states[c];i.state=a,a.methods.stop&&a.methods.stop(i)}this.states=null,this.endResult=null}}},{key:"prepareStates",value:function(n){this.states=[];for(var r=0;r<n.length;r++){var i=n[r],c=i.options,a=i.methods,l=i.name;this.states.push({options:c,methods:a,index:r,name:l})}return this.states}},{key:"restoreInteractionCoords",value:function(n){var r=n.interaction,i=r.coords,c=r.rect,a=r.modification;if(a.result){for(var l=a.startDelta,p=a.result,f=p.delta,m=p.rectDelta,b=[[i.start,l],[i.cur,f]],w=0;w<b.length;w++){var y=Mo(b[w],2),S=y[0],O=y[1];S.page.x-=O.x,S.page.y-=O.y,S.client.x-=O.x,S.client.y-=O.y}c.left-=m.left,c.right-=m.right,c.top-=m.top,c.bottom-=m.bottom}}},{key:"shouldDo",value:function(n,r,i,c){return!(!n||n.enabled===!1||c&&!n.endOnly||n.endOnly&&!r||i==="start"&&!n.setStart)}},{key:"copyFrom",value:function(n){this.startOffset=n.startOffset,this.startDelta=n.startDelta,this.edges=n.edges,this.states=n.states.map(function(r){return(0,Me.default)(r)}),this.result=_t((0,I.default)({},n.result.coords),(0,I.default)({},n.result.rect))}},{key:"destroy",value:function(){for(var n in this)this[n]=null}}])&&Ei(t.prototype,o),e}();function _t(e,t){return{rect:t,coords:e,delta:{x:0,y:0},rectDelta:{left:0,right:0,top:0,bottom:0},eventProps:[],changed:!0}}function _o(e,t){return e?{left:t.x-e.left,top:t.y-e.top,right:e.right-t.x,bottom:e.bottom-t.y}:{left:0,top:0,right:0,bottom:0}}Oe.default=xi;var ee={};function Ct(e){var t=e.iEvent,o=e.interaction.modification.result;o&&(t.modifiers=o.eventProps)}Object.defineProperty(ee,"__esModule",{value:!0}),ee.makeModifier=function(e,t){var o=e.defaults,n={start:e.start,set:e.set,beforeEnd:e.beforeEnd,stop:e.stop},r=function(i){var c=i||{};for(var a in c.enabled=c.enabled!==!1,o)a in c||(c[a]=o[a]);var l={options:c,methods:n,name:t,enable:function(){return c.enabled=!0,l},disable:function(){return c.enabled=!1,l}};return l};return t&&typeof t=="string"&&(r._defaults=o,r._methods=n),r},ee.addEventModifiers=Ct,ee.default=void 0;var Si={id:"modifiers/base",before:["actions"],install:function(e){e.defaults.perAction.modifiers=[]},listeners:{"interactions:new":function(e){var t=e.interaction;t.modification=new Oe.default(t)},"interactions:before-action-start":function(e){var t=e.interaction.modification;t.start(e,e.interaction.coords.start.page),e.interaction.edges=t.edges,t.applyToInteraction(e)},"interactions:before-action-move":function(e){return e.interaction.modification.setAndApply(e)},"interactions:before-action-end":function(e){return e.interaction.modification.beforeEnd(e)},"interactions:action-start":Ct,"interactions:action-move":Ct,"interactions:action-end":Ct,"interactions:after-action-start":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:after-action-move":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:stop":function(e){return e.interaction.modification.stop(e)}}};ee.default=Si;var et={};Object.defineProperty(et,"__esModule",{value:!0}),et.defaults=void 0,et.defaults={base:{preventDefault:"auto",deltaSource:"page"},perAction:{enabled:!1,origin:{x:0,y:0}},actions:{}};var tt={};function Co(e){return(Co=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Pi(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Lo(e,t){return(Lo=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o})(e,t)}function ki(e,t){return!t||Co(t)!=="object"&&typeof t!="function"?Io(e):t}function Io(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function En(e){return(En=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(tt,"__esModule",{value:!0}),tt.InteractEvent=void 0;var Ao=function(e){(function(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),l&&Lo(a,l)})(c,e);var t,o,n,r,i=(n=c,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}(),function(){var a,l=En(n);if(r){var p=En(this).constructor;a=Reflect.construct(l,arguments,p)}else a=l.apply(this,arguments);return ki(this,a)});function c(a,l,p,f,m,b,w){var y;(function(Z,N){if(!(Z instanceof N))throw new TypeError("Cannot call a class as a function")})(this,c),(y=i.call(this,a)).target=void 0,y.currentTarget=void 0,y.relatedTarget=null,y.screenX=void 0,y.screenY=void 0,y.button=void 0,y.buttons=void 0,y.ctrlKey=void 0,y.shiftKey=void 0,y.altKey=void 0,y.metaKey=void 0,y.page=void 0,y.client=void 0,y.delta=void 0,y.rect=void 0,y.x0=void 0,y.y0=void 0,y.t0=void 0,y.dt=void 0,y.duration=void 0,y.clientX0=void 0,y.clientY0=void 0,y.velocity=void 0,y.speed=void 0,y.swipe=void 0,y.timeStamp=void 0,y.axes=void 0,y.preEnd=void 0,m=m||a.element;var S=a.interactable,O=(S&&S.options||et.defaults).deltaSource,x=(0,ke.default)(S,m,p),C=f==="start",j=f==="end",F=C?Io(y):a.prevEvent,X=C?a.coords.start:j?{page:F.page,client:F.client,timeStamp:a.coords.cur.timeStamp}:a.coords.cur;return y.page=(0,I.default)({},X.page),y.client=(0,I.default)({},X.client),y.rect=(0,I.default)({},a.rect),y.timeStamp=X.timeStamp,j||(y.page.x-=x.x,y.page.y-=x.y,y.client.x-=x.x,y.client.y-=x.y),y.ctrlKey=l.ctrlKey,y.altKey=l.altKey,y.shiftKey=l.shiftKey,y.metaKey=l.metaKey,y.button=l.button,y.buttons=l.buttons,y.target=m,y.currentTarget=m,y.preEnd=b,y.type=w||p+(f||""),y.interactable=S,y.t0=C?a.pointers[a.pointers.length-1].downTime:F.t0,y.x0=a.coords.start.page.x-x.x,y.y0=a.coords.start.page.y-x.y,y.clientX0=a.coords.start.client.x-x.x,y.clientY0=a.coords.start.client.y-x.y,y.delta=C||j?{x:0,y:0}:{x:y[O].x-F[O].x,y:y[O].y-F[O].y},y.dt=a.coords.delta.timeStamp,y.duration=y.timeStamp-y.t0,y.velocity=(0,I.default)({},a.coords.velocity[O]),y.speed=(0,we.default)(y.velocity.x,y.velocity.y),y.swipe=j||f==="inertiastart"?y.getSwipe():null,y}return t=c,(o=[{key:"getSwipe",value:function(){var a=this._interaction;if(a.prevEvent.speed<600||this.timeStamp-a.prevEvent.timeStamp>150)return null;var l=180*Math.atan2(a.prevEvent.velocityY,a.prevEvent.velocityX)/Math.PI;l<0&&(l+=360);var p=112.5<=l&&l<247.5,f=202.5<=l&&l<337.5;return{up:f,down:!f&&22.5<=l&&l<157.5,left:p,right:!p&&(292.5<=l||l<67.5),angle:l,speed:a.prevEvent.speed,velocity:{x:a.prevEvent.velocityX,y:a.prevEvent.velocityY}}}},{key:"preventDefault",value:function(){}},{key:"stopImmediatePropagation",value:function(){this.immediatePropagationStopped=this.propagationStopped=!0}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}}])&&Pi(t.prototype,o),c}(je.BaseEvent);tt.InteractEvent=Ao,Object.defineProperties(Ao.prototype,{pageX:{get:function(){return this.page.x},set:function(e){this.page.x=e}},pageY:{get:function(){return this.page.y},set:function(e){this.page.y=e}},clientX:{get:function(){return this.client.x},set:function(e){this.client.x=e}},clientY:{get:function(){return this.client.y},set:function(e){this.client.y=e}},dx:{get:function(){return this.delta.x},set:function(e){this.delta.x=e}},dy:{get:function(){return this.delta.y},set:function(e){this.delta.y=e}},velocityX:{get:function(){return this.velocity.x},set:function(e){this.velocity.x=e}},velocityY:{get:function(){return this.velocity.y},set:function(e){this.velocity.y=e}}});var nt={};Object.defineProperty(nt,"__esModule",{value:!0}),nt.PointerInfo=void 0,nt.PointerInfo=function e(t,o,n,r,i){(function(c,a){if(!(c instanceof a))throw new TypeError("Cannot call a class as a function")})(this,e),this.id=void 0,this.pointer=void 0,this.event=void 0,this.downTime=void 0,this.downTarget=void 0,this.id=t,this.pointer=o,this.event=n,this.downTime=r,this.downTarget=i};var Lt,It,ie={};function Ti(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(ie,"__esModule",{value:!0}),Object.defineProperty(ie,"PointerInfo",{enumerable:!0,get:function(){return nt.PointerInfo}}),ie.default=ie.Interaction=ie._ProxyMethods=ie._ProxyValues=void 0,ie._ProxyValues=Lt,function(e){e.interactable="",e.element="",e.prepared="",e.pointerIsDown="",e.pointerWasMoved="",e._proxy=""}(Lt||(ie._ProxyValues=Lt={})),ie._ProxyMethods=It,function(e){e.start="",e.move="",e.end="",e.stop="",e.interacting=""}(It||(ie._ProxyMethods=It={}));var Mi=0,Do=function(){function e(n){var r=this,i=n.pointerType,c=n.scopeFire;(function(b,w){if(!(b instanceof w))throw new TypeError("Cannot call a class as a function")})(this,e),this.interactable=null,this.element=null,this.rect=void 0,this._rects=void 0,this.edges=void 0,this._scopeFire=void 0,this.prepared={name:null,axis:null,edges:null},this.pointerType=void 0,this.pointers=[],this.downEvent=null,this.downPointer={},this._latestPointer={pointer:null,event:null,eventTarget:null},this.prevEvent=null,this.pointerIsDown=!1,this.pointerWasMoved=!1,this._interacting=!1,this._ending=!1,this._stopped=!0,this._proxy=null,this.simulation=null,this.doMove=(0,ae.warnOnce)(function(b){this.move(b)},"The interaction.doMove() method has been renamed to interaction.move()"),this.coords={start:L.newCoords(),prev:L.newCoords(),cur:L.newCoords(),delta:L.newCoords(),velocity:L.newCoords()},this._id=Mi++,this._scopeFire=c,this.pointerType=i;var a=this;this._proxy={};var l=function(b){Object.defineProperty(r._proxy,b,{get:function(){return a[b]}})};for(var p in Lt)l(p);var f=function(b){Object.defineProperty(r._proxy,b,{value:function(){return a[b].apply(a,arguments)}})};for(var m in It)f(m);this._scopeFire("interactions:new",{interaction:this})}var t,o;return t=e,(o=[{key:"pointerMoveTolerance",get:function(){return 1}},{key:"pointerDown",value:function(n,r,i){var c=this.updatePointer(n,r,i,!0),a=this.pointers[c];this._scopeFire("interactions:down",{pointer:n,event:r,eventTarget:i,pointerIndex:c,pointerInfo:a,type:"down",interaction:this})}},{key:"start",value:function(n,r,i){return!(this.interacting()||!this.pointerIsDown||this.pointers.length<(n.name==="gesture"?2:1)||!r.options[n.name].enabled)&&((0,ae.copyAction)(this.prepared,n),this.interactable=r,this.element=i,this.rect=r.getRect(i),this.edges=this.prepared.edges?(0,I.default)({},this.prepared.edges):{left:!0,right:!0,top:!0,bottom:!0},this._stopped=!1,this._interacting=this._doPhase({interaction:this,event:this.downEvent,phase:"start"})&&!this._stopped,this._interacting)}},{key:"pointerMove",value:function(n,r,i){this.simulation||this.modification&&this.modification.endResult||this.updatePointer(n,r,i,!1);var c,a,l=this.coords.cur.page.x===this.coords.prev.page.x&&this.coords.cur.page.y===this.coords.prev.page.y&&this.coords.cur.client.x===this.coords.prev.client.x&&this.coords.cur.client.y===this.coords.prev.client.y;this.pointerIsDown&&!this.pointerWasMoved&&(c=this.coords.cur.client.x-this.coords.start.client.x,a=this.coords.cur.client.y-this.coords.start.client.y,this.pointerWasMoved=(0,we.default)(c,a)>this.pointerMoveTolerance);var p=this.getPointerIndex(n),f={pointer:n,pointerIndex:p,pointerInfo:this.pointers[p],event:r,type:"move",eventTarget:i,dx:c,dy:a,duplicate:l,interaction:this};l||L.setCoordVelocity(this.coords.velocity,this.coords.delta),this._scopeFire("interactions:move",f),l||this.simulation||(this.interacting()&&(f.type=null,this.move(f)),this.pointerWasMoved&&L.copyCoords(this.coords.prev,this.coords.cur))}},{key:"move",value:function(n){n&&n.event||L.setZeroCoords(this.coords.delta),(n=(0,I.default)({pointer:this._latestPointer.pointer,event:this._latestPointer.event,eventTarget:this._latestPointer.eventTarget,interaction:this},n||{})).phase="move",this._doPhase(n)}},{key:"pointerUp",value:function(n,r,i,c){var a=this.getPointerIndex(n);a===-1&&(a=this.updatePointer(n,r,i,!1));var l=/cancel$/i.test(r.type)?"cancel":"up";this._scopeFire("interactions:".concat(l),{pointer:n,pointerIndex:a,pointerInfo:this.pointers[a],event:r,eventTarget:i,type:l,curEventTarget:c,interaction:this}),this.simulation||this.end(r),this.removePointer(n,r)}},{key:"documentBlur",value:function(n){this.end(n),this._scopeFire("interactions:blur",{event:n,type:"blur",interaction:this})}},{key:"end",value:function(n){var r;this._ending=!0,n=n||this._latestPointer.event,this.interacting()&&(r=this._doPhase({event:n,interaction:this,phase:"end"})),this._ending=!1,r===!0&&this.stop()}},{key:"currentAction",value:function(){return this._interacting?this.prepared.name:null}},{key:"interacting",value:function(){return this._interacting}},{key:"stop",value:function(){this._scopeFire("interactions:stop",{interaction:this}),this.interactable=this.element=null,this._interacting=!1,this._stopped=!0,this.prepared.name=this.prevEvent=null}},{key:"getPointerIndex",value:function(n){var r=L.getPointerId(n);return this.pointerType==="mouse"||this.pointerType==="pen"?this.pointers.length-1:K.findIndex(this.pointers,function(i){return i.id===r})}},{key:"getPointerInfo",value:function(n){return this.pointers[this.getPointerIndex(n)]}},{key:"updatePointer",value:function(n,r,i,c){var a=L.getPointerId(n),l=this.getPointerIndex(n),p=this.pointers[l];return c=c!==!1&&(c||/(down|start)$/i.test(r.type)),p?p.pointer=n:(p=new nt.PointerInfo(a,n,r,null,null),l=this.pointers.length,this.pointers.push(p)),L.setCoords(this.coords.cur,this.pointers.map(function(f){return f.pointer}),this._now()),L.setCoordDeltas(this.coords.delta,this.coords.prev,this.coords.cur),c&&(this.pointerIsDown=!0,p.downTime=this.coords.cur.timeStamp,p.downTarget=i,L.pointerExtend(this.downPointer,n),this.interacting()||(L.copyCoords(this.coords.start,this.coords.cur),L.copyCoords(this.coords.prev,this.coords.cur),this.downEvent=r,this.pointerWasMoved=!1)),this._updateLatestPointer(n,r,i),this._scopeFire("interactions:update-pointer",{pointer:n,event:r,eventTarget:i,down:c,pointerInfo:p,pointerIndex:l,interaction:this}),l}},{key:"removePointer",value:function(n,r){var i=this.getPointerIndex(n);if(i!==-1){var c=this.pointers[i];this._scopeFire("interactions:remove-pointer",{pointer:n,event:r,eventTarget:null,pointerIndex:i,pointerInfo:c,interaction:this}),this.pointers.splice(i,1),this.pointerIsDown=!1}}},{key:"_updateLatestPointer",value:function(n,r,i){this._latestPointer.pointer=n,this._latestPointer.event=r,this._latestPointer.eventTarget=i}},{key:"destroy",value:function(){this._latestPointer.pointer=null,this._latestPointer.event=null,this._latestPointer.eventTarget=null}},{key:"_createPreparedEvent",value:function(n,r,i,c){return new tt.InteractEvent(this,n,this.prepared.name,r,this.element,i,c)}},{key:"_fireEvent",value:function(n){this.interactable.fire(n),(!this.prevEvent||n.timeStamp>=this.prevEvent.timeStamp)&&(this.prevEvent=n)}},{key:"_doPhase",value:function(n){var r=n.event,i=n.phase,c=n.preEnd,a=n.type,l=this.rect;if(l&&i==="move"&&(G.addEdges(this.edges,l,this.coords.delta[this.interactable.options.deltaSource]),l.width=l.right-l.left,l.height=l.bottom-l.top),this._scopeFire("interactions:before-action-".concat(i),n)===!1)return!1;var p=n.iEvent=this._createPreparedEvent(r,i,c,a);return this._scopeFire("interactions:action-".concat(i),n),i==="start"&&(this.prevEvent=p),this._fireEvent(p),this._scopeFire("interactions:after-action-".concat(i),n),!0}},{key:"_now",value:function(){return Date.now()}}])&&Ti(t.prototype,o),e}();ie.Interaction=Do;var Oi=Do;ie.default=Oi;var _e={};function jo(e){e.pointerIsDown&&(Sn(e.coords.cur,e.offset.total),e.offset.pending.x=0,e.offset.pending.y=0)}function Ho(e){xn(e.interaction)}function xn(e){if(!function(o){return!(!o.offset.pending.x&&!o.offset.pending.y)}(e))return!1;var t=e.offset.pending;return Sn(e.coords.cur,t),Sn(e.coords.delta,t),G.addEdges(e.edges,e.rect,t),t.x=0,t.y=0,!0}function _i(e){var t=e.x,o=e.y;this.offset.pending.x+=t,this.offset.pending.y+=o,this.offset.total.x+=t,this.offset.total.y+=o}function Sn(e,t){var o=e.page,n=e.client,r=t.x,i=t.y;o.x+=r,o.y+=i,n.x+=r,n.y+=i}Object.defineProperty(_e,"__esModule",{value:!0}),_e.addTotal=jo,_e.applyPending=xn,_e.default=void 0,ie._ProxyMethods.offsetBy="";var Ci={id:"offset",before:["modifiers","pointer-events","actions","inertia"],install:function(e){e.Interaction.prototype.offsetBy=_i},listeners:{"interactions:new":function(e){e.interaction.offset={total:{x:0,y:0},pending:{x:0,y:0}}},"interactions:update-pointer":function(e){return jo(e.interaction)},"interactions:before-action-start":Ho,"interactions:before-action-move":Ho,"interactions:before-action-end":function(e){var t=e.interaction;if(xn(t))return t.move({offset:!0}),t.end(),!1},"interactions:stop":function(e){var t=e.interaction;t.offset.total.x=0,t.offset.total.y=0,t.offset.pending.x=0,t.offset.pending.y=0}}};_e.default=Ci;var We={};function Li(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(We,"__esModule",{value:!0}),We.default=We.InertiaState=void 0;var Ro=function(){function e(n){(function(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")})(this,e),this.active=!1,this.isModified=!1,this.smoothEnd=!1,this.allowResume=!1,this.modification=void 0,this.modifierCount=0,this.modifierArg=void 0,this.startCoords=void 0,this.t0=0,this.v0=0,this.te=0,this.targetOffset=void 0,this.modifiedOffset=void 0,this.currentOffset=void 0,this.lambda_v0=0,this.one_ve_v0=0,this.timeout=void 0,this.interaction=void 0,this.interaction=n}var t,o;return t=e,(o=[{key:"start",value:function(n){var r=this.interaction,i=At(r);if(!i||!i.enabled)return!1;var c=r.coords.velocity.client,a=(0,we.default)(c.x,c.y),l=this.modification||(this.modification=new Oe.default(r));if(l.copyFrom(r.modification),this.t0=r._now(),this.allowResume=i.allowResume,this.v0=a,this.currentOffset={x:0,y:0},this.startCoords=r.coords.cur.page,this.modifierArg=l.fillArg({pageCoords:this.startCoords,preEnd:!0,phase:"inertiastart"}),this.t0-r.coords.cur.timeStamp<50&&a>i.minSpeed&&a>i.endSpeed)this.startInertia();else{if(l.result=l.setAll(this.modifierArg),!l.result.changed)return!1;this.startSmoothEnd()}return r.modification.result.rect=null,r.offsetBy(this.targetOffset),r._doPhase({interaction:r,event:n,phase:"inertiastart"}),r.offsetBy({x:-this.targetOffset.x,y:-this.targetOffset.y}),r.modification.result.rect=null,this.active=!0,r.simulation=this,!0}},{key:"startInertia",value:function(){var n=this,r=this.interaction.coords.velocity.client,i=At(this.interaction),c=i.resistance,a=-Math.log(i.endSpeed/this.v0)/c;this.targetOffset={x:(r.x-a)/c,y:(r.y-a)/c},this.te=a,this.lambda_v0=c/this.v0,this.one_ve_v0=1-i.endSpeed/this.v0;var l=this.modification,p=this.modifierArg;p.pageCoords={x:this.startCoords.x+this.targetOffset.x,y:this.startCoords.y+this.targetOffset.y},l.result=l.setAll(p),l.result.changed&&(this.isModified=!0,this.modifiedOffset={x:this.targetOffset.x+l.result.delta.x,y:this.targetOffset.y+l.result.delta.y}),this.onNextFrame(function(){return n.inertiaTick()})}},{key:"startSmoothEnd",value:function(){var n=this;this.smoothEnd=!0,this.isModified=!0,this.targetOffset={x:this.modification.result.delta.x,y:this.modification.result.delta.y},this.onNextFrame(function(){return n.smoothEndTick()})}},{key:"onNextFrame",value:function(n){var r=this;this.timeout=de.default.request(function(){r.active&&n()})}},{key:"inertiaTick",value:function(){var n,r,i,c,a,l=this,p=this.interaction,f=At(p).resistance,m=(p._now()-this.t0)/1e3;if(m<this.te){var b,w=1-(Math.exp(-f*m)-this.lambda_v0)/this.one_ve_v0;this.isModified?(n=this.targetOffset.x,r=this.targetOffset.y,i=this.modifiedOffset.x,c=this.modifiedOffset.y,b={x:zo(a=w,0,n,i),y:zo(a,0,r,c)}):b={x:this.targetOffset.x*w,y:this.targetOffset.y*w};var y={x:b.x-this.currentOffset.x,y:b.y-this.currentOffset.y};this.currentOffset.x+=y.x,this.currentOffset.y+=y.y,p.offsetBy(y),p.move(),this.onNextFrame(function(){return l.inertiaTick()})}else p.offsetBy({x:this.modifiedOffset.x-this.currentOffset.x,y:this.modifiedOffset.y-this.currentOffset.y}),this.end()}},{key:"smoothEndTick",value:function(){var n=this,r=this.interaction,i=r._now()-this.t0,c=At(r).smoothEndDuration;if(i<c){var a={x:Wo(i,0,this.targetOffset.x,c),y:Wo(i,0,this.targetOffset.y,c)},l={x:a.x-this.currentOffset.x,y:a.y-this.currentOffset.y};this.currentOffset.x+=l.x,this.currentOffset.y+=l.y,r.offsetBy(l),r.move({skipModifiers:this.modifierCount}),this.onNextFrame(function(){return n.smoothEndTick()})}else r.offsetBy({x:this.targetOffset.x-this.currentOffset.x,y:this.targetOffset.y-this.currentOffset.y}),this.end()}},{key:"resume",value:function(n){var r=n.pointer,i=n.event,c=n.eventTarget,a=this.interaction;a.offsetBy({x:-this.currentOffset.x,y:-this.currentOffset.y}),a.updatePointer(r,i,c,!0),a._doPhase({interaction:a,event:i,phase:"resume"}),(0,L.copyCoords)(a.coords.prev,a.coords.cur),this.stop()}},{key:"end",value:function(){this.interaction.move(),this.interaction.end(),this.stop()}},{key:"stop",value:function(){this.active=this.smoothEnd=!1,this.interaction.simulation=null,de.default.cancel(this.timeout)}}])&&Li(t.prototype,o),e}();function At(e){var t=e.interactable,o=e.prepared;return t&&t.options&&o.name&&t.options[o.name].inertia}function zo(e,t,o,n){var r=1-e;return r*r*t+2*r*e*o+e*e*n}function Wo(e,t,o,n){return-o*(e/=n)*(e-2)+t}We.InertiaState=Ro;var Ii={id:"inertia",before:["modifiers","actions"],install:function(e){var t=e.defaults;e.usePlugin(_e.default),e.usePlugin(ee.default),e.actions.phases.inertiastart=!0,e.actions.phases.resume=!0,t.perAction.inertia={enabled:!1,resistance:10,minSpeed:100,endSpeed:10,allowResume:!0,smoothEndDuration:300}},listeners:{"interactions:new":function(e){var t=e.interaction;t.inertia=new Ro(t)},"interactions:before-action-end":function(e){var t=e.interaction,o=e.event;return(!t._interacting||t.simulation||!t.inertia.start(o))&&null},"interactions:down":function(e){var t=e.interaction,o=e.eventTarget,n=t.inertia;if(n.active)for(var r=o;d.default.element(r);){if(r===t.element){n.resume(e);break}r=A.parentNode(r)}},"interactions:stop":function(e){var t=e.interaction.inertia;t.active&&t.stop()},"interactions:before-action-resume":function(e){var t=e.interaction.modification;t.stop(e),t.start(e,e.interaction.coords.cur.page),t.applyToInteraction(e)},"interactions:before-action-inertiastart":function(e){return e.interaction.modification.setAndApply(e)},"interactions:action-resume":ee.addEventModifiers,"interactions:action-inertiastart":ee.addEventModifiers,"interactions:after-action-inertiastart":function(e){return e.interaction.modification.restoreInteractionCoords(e)},"interactions:after-action-resume":function(e){return e.interaction.modification.restoreInteractionCoords(e)}}};We.default=Ii;var ot={};function Ai(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Fo(e,t){for(var o=0;o<t.length;o++){var n=t[o];if(e.immediatePropagationStopped)break;n(e)}}Object.defineProperty(ot,"__esModule",{value:!0}),ot.Eventable=void 0;var Di=function(){function e(n){(function(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")})(this,e),this.options=void 0,this.types={},this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.global=void 0,this.options=(0,I.default)({},n||{})}var t,o;return t=e,(o=[{key:"fire",value:function(n){var r,i=this.global;(r=this.types[n.type])&&Fo(n,r),!n.propagationStopped&&i&&(r=i[n.type])&&Fo(n,r)}},{key:"on",value:function(n,r){var i=(0,De.default)(n,r);for(n in i)this.types[n]=K.merge(this.types[n]||[],i[n])}},{key:"off",value:function(n,r){var i=(0,De.default)(n,r);for(n in i){var c=this.types[n];if(c&&c.length)for(var a=0;a<i[n].length;a++){var l=i[n][a],p=c.indexOf(l);p!==-1&&c.splice(p,1)}}}},{key:"getRect",value:function(n){return null}}])&&Ai(t.prototype,o),e}();ot.Eventable=Di;var rt={};Object.defineProperty(rt,"__esModule",{value:!0}),rt.default=function(e,t){if(t.phaselessTypes[e])return!0;for(var o in t.map)if(e.indexOf(o)===0&&e.substr(o.length)in t.phases)return!0;return!1};var Pn={};Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.createInteractStatic=function(e){var t=function o(n,r){var i=e.interactables.get(n,r);return i||((i=e.interactables.new(n,r)).events.global=o.globalEvents),i};return t.getPointerAverage=L.pointerAverage,t.getTouchBBox=L.touchBBox,t.getTouchDistance=L.touchDistance,t.getTouchAngle=L.touchAngle,t.getElementRect=A.getElementRect,t.getElementClientRect=A.getElementClientRect,t.matchesSelector=A.matchesSelector,t.closest=A.closest,t.globalEvents={},t.version="1.10.11",t.scope=e,t.use=function(o,n){return this.scope.usePlugin(o,n),this},t.isSet=function(o,n){return!!this.scope.interactables.get(o,n&&n.context)},t.on=(0,ae.warnOnce)(function(o,n,r){if(d.default.string(o)&&o.search(" ")!==-1&&(o=o.trim().split(/ +/)),d.default.array(o)){for(var i=0;i<o.length;i++){var c=o[i];this.on(c,n,r)}return this}if(d.default.object(o)){for(var a in o)this.on(a,o[a],n);return this}return(0,rt.default)(o,this.scope.actions)?this.globalEvents[o]?this.globalEvents[o].push(n):this.globalEvents[o]=[n]:this.scope.events.add(this.scope.document,o,n,{options:r}),this},"The interact.on() method is being deprecated"),t.off=(0,ae.warnOnce)(function(o,n,r){if(d.default.string(o)&&o.search(" ")!==-1&&(o=o.trim().split(/ +/)),d.default.array(o)){for(var i=0;i<o.length;i++){var c=o[i];this.off(c,n,r)}return this}if(d.default.object(o)){for(var a in o)this.off(a,o[a],n);return this}var l;return(0,rt.default)(o,this.scope.actions)?o in this.globalEvents&&(l=this.globalEvents[o].indexOf(n))!==-1&&this.globalEvents[o].splice(l,1):this.scope.events.remove(this.scope.document,o,n,r),this},"The interact.off() method is being deprecated"),t.debug=function(){return this.scope},t.supportsTouch=function(){return $.default.supportsTouch},t.supportsPointerEvent=function(){return $.default.supportsPointerEvent},t.stop=function(){for(var o=0;o<this.scope.interactions.list.length;o++)this.scope.interactions.list[o].stop();return this},t.pointerMoveTolerance=function(o){return d.default.number(o)?(this.scope.interactions.pointerMoveTolerance=o,this):this.scope.interactions.pointerMoveTolerance},t.addDocument=function(o,n){this.scope.addDocument(o,n)},t.removeDocument=function(o){this.scope.removeDocument(o)},t};var Dt={};function ji(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(Dt,"__esModule",{value:!0}),Dt.Interactable=void 0;var Hi=function(){function e(n,r,i,c){(function(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")})(this,e),this.options=void 0,this._actions=void 0,this.target=void 0,this.events=new ot.Eventable,this._context=void 0,this._win=void 0,this._doc=void 0,this._scopeEvents=void 0,this._rectChecker=void 0,this._actions=r.actions,this.target=n,this._context=r.context||i,this._win=(0,s.getWindow)((0,A.trySelector)(n)?this._context:n),this._doc=this._win.document,this._scopeEvents=c,this.set(r)}var t,o;return t=e,(o=[{key:"_defaults",get:function(){return{base:{},perAction:{},actions:{}}}},{key:"setOnEvents",value:function(n,r){return d.default.func(r.onstart)&&this.on("".concat(n,"start"),r.onstart),d.default.func(r.onmove)&&this.on("".concat(n,"move"),r.onmove),d.default.func(r.onend)&&this.on("".concat(n,"end"),r.onend),d.default.func(r.oninertiastart)&&this.on("".concat(n,"inertiastart"),r.oninertiastart),this}},{key:"updatePerActionListeners",value:function(n,r,i){(d.default.array(r)||d.default.object(r))&&this.off(n,r),(d.default.array(i)||d.default.object(i))&&this.on(n,i)}},{key:"setPerAction",value:function(n,r){var i=this._defaults;for(var c in r){var a=c,l=this.options[n],p=r[a];a==="listeners"&&this.updatePerActionListeners(n,l.listeners,p),d.default.array(p)?l[a]=K.from(p):d.default.plainObject(p)?(l[a]=(0,I.default)(l[a]||{},(0,Me.default)(p)),d.default.object(i.perAction[a])&&"enabled"in i.perAction[a]&&(l[a].enabled=p.enabled!==!1)):d.default.bool(p)&&d.default.object(i.perAction[a])?l[a].enabled=p:l[a]=p}}},{key:"getRect",value:function(n){return n=n||(d.default.element(this.target)?this.target:null),d.default.string(this.target)&&(n=n||this._context.querySelector(this.target)),(0,A.getElementRect)(n)}},{key:"rectChecker",value:function(n){var r=this;return d.default.func(n)?(this._rectChecker=n,this.getRect=function(i){var c=(0,I.default)({},r._rectChecker(i));return"width"in c||(c.width=c.right-c.left,c.height=c.bottom-c.top),c},this):n===null?(delete this.getRect,delete this._rectChecker,this):this.getRect}},{key:"_backCompatOption",value:function(n,r){if((0,A.trySelector)(r)||d.default.object(r)){for(var i in this.options[n]=r,this._actions.map)this.options[i][n]=r;return this}return this.options[n]}},{key:"origin",value:function(n){return this._backCompatOption("origin",n)}},{key:"deltaSource",value:function(n){return n==="page"||n==="client"?(this.options.deltaSource=n,this):this.options.deltaSource}},{key:"context",value:function(){return this._context}},{key:"inContext",value:function(n){return this._context===n.ownerDocument||(0,A.nodeContains)(this._context,n)}},{key:"testIgnoreAllow",value:function(n,r,i){return!this.testIgnore(n.ignoreFrom,r,i)&&this.testAllow(n.allowFrom,r,i)}},{key:"testAllow",value:function(n,r,i){return!n||!!d.default.element(i)&&(d.default.string(n)?(0,A.matchesUpTo)(i,n,r):!!d.default.element(n)&&(0,A.nodeContains)(n,i))}},{key:"testIgnore",value:function(n,r,i){return!(!n||!d.default.element(i))&&(d.default.string(n)?(0,A.matchesUpTo)(i,n,r):!!d.default.element(n)&&(0,A.nodeContains)(n,i))}},{key:"fire",value:function(n){return this.events.fire(n),this}},{key:"_onOff",value:function(n,r,i,c){d.default.object(r)&&!d.default.array(r)&&(c=i,i=null);var a=n==="on"?"add":"remove",l=(0,De.default)(r,i);for(var p in l){p==="wheel"&&(p=$.default.wheelEvent);for(var f=0;f<l[p].length;f++){var m=l[p][f];(0,rt.default)(p,this._actions)?this.events[n](p,m):d.default.string(this.target)?this._scopeEvents["".concat(a,"Delegate")](this.target,this._context,p,m,c):this._scopeEvents[a](this.target,p,m,c)}}return this}},{key:"on",value:function(n,r,i){return this._onOff("on",n,r,i)}},{key:"off",value:function(n,r,i){return this._onOff("off",n,r,i)}},{key:"set",value:function(n){var r=this._defaults;for(var i in d.default.object(n)||(n={}),this.options=(0,Me.default)(r.base),this._actions.methodDict){var c=i,a=this._actions.methodDict[c];this.options[c]={},this.setPerAction(c,(0,I.default)((0,I.default)({},r.perAction),r.actions[c])),this[a](n[c])}for(var l in n)d.default.func(this[l])&&this[l](n[l]);return this}},{key:"unset",value:function(){if(d.default.string(this.target))for(var n in this._scopeEvents.delegatedEvents)for(var r=this._scopeEvents.delegatedEvents[n],i=r.length-1;i>=0;i--){var c=r[i],a=c.selector,l=c.context,p=c.listeners;a===this.target&&l===this._context&&r.splice(i,1);for(var f=p.length-1;f>=0;f--)this._scopeEvents.removeDelegate(this.target,this._context,n,p[f][0],p[f][1])}else this._scopeEvents.remove(this.target,"all")}}])&&ji(t.prototype,o),e}();Dt.Interactable=Hi;var jt={};function Ri(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(jt,"__esModule",{value:!0}),jt.InteractableSet=void 0;var zi=function(){function e(n){var r=this;(function(i,c){if(!(i instanceof c))throw new TypeError("Cannot call a class as a function")})(this,e),this.list=[],this.selectorMap={},this.scope=void 0,this.scope=n,n.addListeners({"interactable:unset":function(i){var c=i.interactable,a=c.target,l=c._context,p=d.default.string(a)?r.selectorMap[a]:a[r.scope.id],f=K.findIndex(p,function(m){return m.context===l});p[f]&&(p[f].context=null,p[f].interactable=null),p.splice(f,1)}})}var t,o;return t=e,(o=[{key:"new",value:function(n,r){r=(0,I.default)(r||{},{actions:this.scope.actions});var i=new this.scope.Interactable(n,r,this.scope.document,this.scope.events),c={context:i._context,interactable:i};return this.scope.addDocument(i._doc),this.list.push(i),d.default.string(n)?(this.selectorMap[n]||(this.selectorMap[n]=[]),this.selectorMap[n].push(c)):(i.target[this.scope.id]||Object.defineProperty(n,this.scope.id,{value:[],configurable:!0}),n[this.scope.id].push(c)),this.scope.fire("interactable:new",{target:n,options:r,interactable:i,win:this.scope._win}),i}},{key:"get",value:function(n,r){var i=r&&r.context||this.scope.document,c=d.default.string(n),a=c?this.selectorMap[n]:n[this.scope.id];if(!a)return null;var l=K.find(a,function(p){return p.context===i&&(c||p.interactable.inContext(n))});return l&&l.interactable}},{key:"forEachMatch",value:function(n,r){for(var i=0;i<this.list.length;i++){var c=this.list[i],a=void 0;if((d.default.string(c.target)?d.default.element(n)&&A.matchesSelector(n,c.target):n===c.target)&&c.inContext(n)&&(a=r(c)),a!==void 0)return a}}}])&&Ri(t.prototype,o),e}();jt.InteractableSet=zi;var Ht={};function Wi(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function kn(e,t){return function(o){if(Array.isArray(o))return o}(e)||function(o,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(o)){var r=[],i=!0,c=!1,a=void 0;try{for(var l,p=o[Symbol.iterator]();!(i=(l=p.next()).done)&&(r.push(l.value),!n||r.length!==n);i=!0);}catch(f){c=!0,a=f}finally{try{i||p.return==null||p.return()}finally{if(c)throw a}}return r}}(e,t)||function(o,n){if(o){if(typeof o=="string")return Vo(o,n);var r=Object.prototype.toString.call(o).slice(8,-1);return r==="Object"&&o.constructor&&(r=o.constructor.name),r==="Map"||r==="Set"?Array.from(o):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Vo(o,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Vo(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}Object.defineProperty(Ht,"__esModule",{value:!0}),Ht.default=void 0;var Fi=function(){function e(n){(function(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")})(this,e),this.currentTarget=void 0,this.originalEvent=void 0,this.type=void 0,this.originalEvent=n,(0,Ze.default)(this,n)}var t,o;return t=e,(o=[{key:"preventOriginalDefault",value:function(){this.originalEvent.preventDefault()}},{key:"stopPropagation",value:function(){this.originalEvent.stopPropagation()}},{key:"stopImmediatePropagation",value:function(){this.originalEvent.stopImmediatePropagation()}}])&&Wi(t.prototype,o),e}();function it(e){if(!d.default.object(e))return{capture:!!e,passive:!1};var t=(0,I.default)({},e);return t.capture=!!e.capture,t.passive=!!e.passive,t}var Vi={id:"events",install:function(e){var t,o=[],n={},r=[],i={add:c,remove:a,addDelegate:function(f,m,b,w,y){var S=it(y);if(!n[b]){n[b]=[];for(var O=0;O<r.length;O++){var x=r[O];c(x,b,l),c(x,b,p,!0)}}var C=n[b],j=K.find(C,function(F){return F.selector===f&&F.context===m});j||(j={selector:f,context:m,listeners:[]},C.push(j)),j.listeners.push([w,S])},removeDelegate:function(f,m,b,w,y){var S,O=it(y),x=n[b],C=!1;if(x)for(S=x.length-1;S>=0;S--){var j=x[S];if(j.selector===f&&j.context===m){for(var F=j.listeners,X=F.length-1;X>=0;X--){var Z=kn(F[X],2),N=Z[0],Y=Z[1],ue=Y.capture,ye=Y.passive;if(N===w&&ue===O.capture&&ye===O.passive){F.splice(X,1),F.length||(x.splice(S,1),a(m,b,l),a(m,b,p,!0)),C=!0;break}}if(C)break}}},delegateListener:l,delegateUseCapture:p,delegatedEvents:n,documents:r,targets:o,supportsOptions:!1,supportsPassive:!1};function c(f,m,b,w){var y=it(w),S=K.find(o,function(O){return O.eventTarget===f});S||(S={eventTarget:f,events:{}},o.push(S)),S.events[m]||(S.events[m]=[]),f.addEventListener&&!K.contains(S.events[m],b)&&(f.addEventListener(m,b,i.supportsOptions?y:y.capture),S.events[m].push(b))}function a(f,m,b,w){var y=it(w),S=K.findIndex(o,function(X){return X.eventTarget===f}),O=o[S];if(O&&O.events)if(m!=="all"){var x=!1,C=O.events[m];if(C){if(b==="all"){for(var j=C.length-1;j>=0;j--)a(f,m,C[j],y);return}for(var F=0;F<C.length;F++)if(C[F]===b){f.removeEventListener(m,b,i.supportsOptions?y:y.capture),C.splice(F,1),C.length===0&&(delete O.events[m],x=!0);break}}x&&!Object.keys(O.events).length&&o.splice(S,1)}else for(m in O.events)O.events.hasOwnProperty(m)&&a(f,m,"all")}function l(f,m){for(var b=it(m),w=new Fi(f),y=n[f.type],S=kn(L.getEventTargets(f),1)[0],O=S;d.default.element(O);){for(var x=0;x<y.length;x++){var C=y[x],j=C.selector,F=C.context;if(A.matchesSelector(O,j)&&A.nodeContains(F,S)&&A.nodeContains(F,O)){var X=C.listeners;w.currentTarget=O;for(var Z=0;Z<X.length;Z++){var N=kn(X[Z],2),Y=N[0],ue=N[1],ye=ue.capture,Wn=ue.passive;ye===b.capture&&Wn===b.passive&&Y(w)}}}O=A.parentNode(O)}}function p(f){return l(f,!0)}return(t=e.document)==null||t.createElement("div").addEventListener("test",null,{get capture(){return i.supportsOptions=!0},get passive(){return i.supportsPassive=!0}}),e.events=i,i}};Ht.default=Vi;var Rt={};Object.defineProperty(Rt,"__esModule",{value:!0}),Rt.default=void 0;var zt={methodOrder:["simulationResume","mouseOrPen","hasPointer","idle"],search:function(e){for(var t=0;t<zt.methodOrder.length;t++){var o;o=zt.methodOrder[t];var n=zt[o](e);if(n)return n}return null},simulationResume:function(e){var t=e.pointerType,o=e.eventType,n=e.eventTarget,r=e.scope;if(!/down|start/i.test(o))return null;for(var i=0;i<r.interactions.list.length;i++){var c=r.interactions.list[i],a=n;if(c.simulation&&c.simulation.allowResume&&c.pointerType===t)for(;a;){if(a===c.element)return c;a=A.parentNode(a)}}return null},mouseOrPen:function(e){var t,o=e.pointerId,n=e.pointerType,r=e.eventType,i=e.scope;if(n!=="mouse"&&n!=="pen")return null;for(var c=0;c<i.interactions.list.length;c++){var a=i.interactions.list[c];if(a.pointerType===n){if(a.simulation&&!Bo(a,o))continue;if(a.interacting())return a;t||(t=a)}}if(t)return t;for(var l=0;l<i.interactions.list.length;l++){var p=i.interactions.list[l];if(!(p.pointerType!==n||/down/i.test(r)&&p.simulation))return p}return null},hasPointer:function(e){for(var t=e.pointerId,o=e.scope,n=0;n<o.interactions.list.length;n++){var r=o.interactions.list[n];if(Bo(r,t))return r}return null},idle:function(e){for(var t=e.pointerType,o=e.scope,n=0;n<o.interactions.list.length;n++){var r=o.interactions.list[n];if(r.pointers.length===1){var i=r.interactable;if(i&&(!i.options.gesture||!i.options.gesture.enabled))continue}else if(r.pointers.length>=2)continue;if(!r.interacting()&&t===r.pointerType)return r}return null}};function Bo(e,t){return e.pointers.some(function(o){return o.id===t})}var Bi=zt;Rt.default=Bi;var Wt={};function No(e){return(No=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function qo(e,t){return function(o){if(Array.isArray(o))return o}(e)||function(o,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(o)){var r=[],i=!0,c=!1,a=void 0;try{for(var l,p=o[Symbol.iterator]();!(i=(l=p.next()).done)&&(r.push(l.value),!n||r.length!==n);i=!0);}catch(f){c=!0,a=f}finally{try{i||p.return==null||p.return()}finally{if(c)throw a}}return r}}(e,t)||function(o,n){if(o){if(typeof o=="string")return Xo(o,n);var r=Object.prototype.toString.call(o).slice(8,-1);return r==="Object"&&o.constructor&&(r=o.constructor.name),r==="Map"||r==="Set"?Array.from(o):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Xo(o,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Xo(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}function Ni(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qi(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Yo(e,t){return(Yo=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o})(e,t)}function Xi(e,t){return!t||No(t)!=="object"&&typeof t!="function"?function(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}(e):t}function Tn(e){return(Tn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(Wt,"__esModule",{value:!0}),Wt.default=void 0;var Mn=["pointerDown","pointerMove","pointerUp","updatePointer","removePointer","windowBlur"];function Uo(e,t){return function(o){var n=t.interactions.list,r=L.getPointerType(o),i=qo(L.getEventTargets(o),2),c=i[0],a=i[1],l=[];if(/^touch/.test(o.type)){t.prevTouchTime=t.now();for(var p=0;p<o.changedTouches.length;p++){var f=o.changedTouches[p],m={pointer:f,pointerId:L.getPointerId(f),pointerType:r,eventType:o.type,eventTarget:c,curEventTarget:a,scope:t},b=Ko(m);l.push([m.pointer,m.eventTarget,m.curEventTarget,b])}}else{var w=!1;if(!$.default.supportsPointerEvent&&/mouse/.test(o.type)){for(var y=0;y<n.length&&!w;y++)w=n[y].pointerType!=="mouse"&&n[y].pointerIsDown;w=w||t.now()-t.prevTouchTime<500||o.timeStamp===0}if(!w){var S={pointer:o,pointerId:L.getPointerId(o),pointerType:r,eventType:o.type,curEventTarget:a,eventTarget:c,scope:t},O=Ko(S);l.push([S.pointer,S.eventTarget,S.curEventTarget,O])}}for(var x=0;x<l.length;x++){var C=qo(l[x],4),j=C[0],F=C[1],X=C[2];C[3][e](j,o,F,X)}}}function Ko(e){var t=e.pointerType,o=e.scope,n={interaction:Rt.default.search(e),searchDetails:e};return o.fire("interactions:find",n),n.interaction||o.interactions.new({pointerType:t})}function On(e,t){var o=e.doc,n=e.scope,r=e.options,i=n.interactions.docEvents,c=n.events,a=c[t];for(var l in n.browser.isIOS&&!r.events&&(r.events={passive:!1}),c.delegatedEvents)a(o,l,c.delegateListener),a(o,l,c.delegateUseCapture,!0);for(var p=r&&r.events,f=0;f<i.length;f++){var m=i[f];a(o,m.type,m.listener,p)}}var Yi={id:"core/interactions",install:function(e){for(var t={},o=0;o<Mn.length;o++){var n=Mn[o];t[n]=Uo(n,e)}var r,i=$.default.pEventTypes;function c(){for(var a=0;a<e.interactions.list.length;a++){var l=e.interactions.list[a];if(l.pointerIsDown&&l.pointerType==="touch"&&!l._interacting)for(var p=function(){var m=l.pointers[f];e.documents.some(function(b){var w=b.doc;return(0,A.nodeContains)(w,m.downTarget)})||l.removePointer(m.pointer,m.event)},f=0;f<l.pointers.length;f++)p()}}(r=D.default.PointerEvent?[{type:i.down,listener:c},{type:i.down,listener:t.pointerDown},{type:i.move,listener:t.pointerMove},{type:i.up,listener:t.pointerUp},{type:i.cancel,listener:t.pointerUp}]:[{type:"mousedown",listener:t.pointerDown},{type:"mousemove",listener:t.pointerMove},{type:"mouseup",listener:t.pointerUp},{type:"touchstart",listener:c},{type:"touchstart",listener:t.pointerDown},{type:"touchmove",listener:t.pointerMove},{type:"touchend",listener:t.pointerUp},{type:"touchcancel",listener:t.pointerUp}]).push({type:"blur",listener:function(a){for(var l=0;l<e.interactions.list.length;l++)e.interactions.list[l].documentBlur(a)}}),e.prevTouchTime=0,e.Interaction=function(a){(function(y,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function");y.prototype=Object.create(S&&S.prototype,{constructor:{value:y,writable:!0,configurable:!0}}),S&&Yo(y,S)})(w,a);var l,p,f,m,b=(f=w,m=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(y){return!1}}(),function(){var y,S=Tn(f);if(m){var O=Tn(this).constructor;y=Reflect.construct(S,arguments,O)}else y=S.apply(this,arguments);return Xi(this,y)});function w(){return Ni(this,w),b.apply(this,arguments)}return l=w,(p=[{key:"pointerMoveTolerance",get:function(){return e.interactions.pointerMoveTolerance},set:function(y){e.interactions.pointerMoveTolerance=y}},{key:"_now",value:function(){return e.now()}}])&&qi(l.prototype,p),w}(ie.default),e.interactions={list:[],new:function(a){a.scopeFire=function(p,f){return e.fire(p,f)};var l=new e.Interaction(a);return e.interactions.list.push(l),l},listeners:t,docEvents:r,pointerMoveTolerance:1},e.usePlugin(Re.default)},listeners:{"scope:add-document":function(e){return On(e,"add")},"scope:remove-document":function(e){return On(e,"remove")},"interactable:unset":function(e,t){for(var o=e.interactable,n=t.interactions.list.length-1;n>=0;n--){var r=t.interactions.list[n];r.interactable===o&&(r.stop(),t.fire("interactions:destroy",{interaction:r}),r.destroy(),t.interactions.list.length>2&&t.interactions.list.splice(n,1))}}},onDocSignal:On,doOnInteractions:Uo,methodNames:Mn};Wt.default=Yi;var at={};function $o(e){return($o=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function _n(e,t,o){return(_n=typeof Reflect!="undefined"&&Reflect.get?Reflect.get:function(n,r,i){var c=function(l,p){for(;!Object.prototype.hasOwnProperty.call(l,p)&&(l=Fe(l))!==null;);return l}(n,r);if(c){var a=Object.getOwnPropertyDescriptor(c,r);return a.get?a.get.call(i):a.value}})(e,t,o||e)}function Go(e,t){return(Go=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o})(e,t)}function Ui(e,t){return!t||$o(t)!=="object"&&typeof t!="function"?function(o){if(o===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return o}(e):t}function Fe(e){return(Fe=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function Zo(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qo(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Jo(e,t,o){return t&&Qo(e.prototype,t),o&&Qo(e,o),e}Object.defineProperty(at,"__esModule",{value:!0}),at.initScope=er,at.Scope=void 0;var Ki=function(){function e(){var t=this;Zo(this,e),this.id="__interact_scope_".concat(Math.floor(100*Math.random())),this.isInitialized=!1,this.listenerMaps=[],this.browser=$.default,this.defaults=(0,Me.default)(et.defaults),this.Eventable=ot.Eventable,this.actions={map:{},phases:{start:!0,move:!0,end:!0},methodDict:{},phaselessTypes:{}},this.interactStatic=(0,Pn.createInteractStatic)(this),this.InteractEvent=tt.InteractEvent,this.Interactable=void 0,this.interactables=new jt.InteractableSet(this),this._win=void 0,this.document=void 0,this.window=void 0,this.documents=[],this._plugins={list:[],map:{}},this.onWindowUnload=function(n){return t.removeDocument(n.target)};var o=this;this.Interactable=function(n){(function(l,p){if(typeof p!="function"&&p!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(p&&p.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),p&&Go(l,p)})(a,n);var r,i,c=(r=a,i=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(l){return!1}}(),function(){var l,p=Fe(r);if(i){var f=Fe(this).constructor;l=Reflect.construct(p,arguments,f)}else l=p.apply(this,arguments);return Ui(this,l)});function a(){return Zo(this,a),c.apply(this,arguments)}return Jo(a,[{key:"_defaults",get:function(){return o.defaults}},{key:"set",value:function(l){return _n(Fe(a.prototype),"set",this).call(this,l),o.fire("interactable:set",{options:l,interactable:this}),this}},{key:"unset",value:function(){_n(Fe(a.prototype),"unset",this).call(this),o.interactables.list.splice(o.interactables.list.indexOf(this),1),o.fire("interactable:unset",{interactable:this})}}]),a}(Dt.Interactable)}return Jo(e,[{key:"addListeners",value:function(t,o){this.listenerMaps.push({id:o,map:t})}},{key:"fire",value:function(t,o){for(var n=0;n<this.listenerMaps.length;n++){var r=this.listenerMaps[n].map[t];if(r&&r(o,this,t)===!1)return!1}}},{key:"init",value:function(t){return this.isInitialized?this:er(this,t)}},{key:"pluginIsInstalled",value:function(t){return this._plugins.map[t.id]||this._plugins.list.indexOf(t)!==-1}},{key:"usePlugin",value:function(t,o){if(!this.isInitialized)return this;if(this.pluginIsInstalled(t))return this;if(t.id&&(this._plugins.map[t.id]=t),this._plugins.list.push(t),t.install&&t.install(this,o),t.listeners&&t.before){for(var n=0,r=this.listenerMaps.length,i=t.before.reduce(function(a,l){return a[l]=!0,a[tr(l)]=!0,a},{});n<r;n++){var c=this.listenerMaps[n].id;if(i[c]||i[tr(c)])break}this.listenerMaps.splice(n,0,{id:t.id,map:t.listeners})}else t.listeners&&this.listenerMaps.push({id:t.id,map:t.listeners});return this}},{key:"addDocument",value:function(t,o){if(this.getDocIndex(t)!==-1)return!1;var n=s.getWindow(t);o=o?(0,I.default)({},o):{},this.documents.push({doc:t,options:o}),this.events.documents.push(t),t!==this.document&&this.events.add(n,"unload",this.onWindowUnload),this.fire("scope:add-document",{doc:t,window:n,scope:this,options:o})}},{key:"removeDocument",value:function(t){var o=this.getDocIndex(t),n=s.getWindow(t),r=this.documents[o].options;this.events.remove(n,"unload",this.onWindowUnload),this.documents.splice(o,1),this.events.documents.splice(o,1),this.fire("scope:remove-document",{doc:t,window:n,scope:this,options:r})}},{key:"getDocIndex",value:function(t){for(var o=0;o<this.documents.length;o++)if(this.documents[o].doc===t)return o;return-1}},{key:"getDocOptions",value:function(t){var o=this.getDocIndex(t);return o===-1?null:this.documents[o].options}},{key:"now",value:function(){return(this.window.Date||Date).now()}}]),e}();function er(e,t){return e.isInitialized=!0,d.default.window(t)&&s.init(t),D.default.init(t),$.default.init(t),de.default.init(t),e.window=t,e.document=t.document,e.usePlugin(Wt.default),e.usePlugin(Ht.default),e}function tr(e){return e&&e.replace(/\/.*$/,"")}at.Scope=Ki;var te={};Object.defineProperty(te,"__esModule",{value:!0}),te.default=void 0;var nr=new at.Scope,$i=nr.interactStatic;te.default=$i;var Gi=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:void 0;nr.init(Gi);var Ft={};Object.defineProperty(Ft,"__esModule",{value:!0}),Ft.default=void 0,Ft.default=function(){};var Vt={};Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.default=void 0,Vt.default=function(){};var Bt={};function or(e,t){return function(o){if(Array.isArray(o))return o}(e)||function(o,n){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(o)){var r=[],i=!0,c=!1,a=void 0;try{for(var l,p=o[Symbol.iterator]();!(i=(l=p.next()).done)&&(r.push(l.value),!n||r.length!==n);i=!0);}catch(f){c=!0,a=f}finally{try{i||p.return==null||p.return()}finally{if(c)throw a}}return r}}(e,t)||function(o,n){if(o){if(typeof o=="string")return rr(o,n);var r=Object.prototype.toString.call(o).slice(8,-1);return r==="Object"&&o.constructor&&(r=o.constructor.name),r==="Map"||r==="Set"?Array.from(o):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?rr(o,n):void 0}}(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function rr(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}Object.defineProperty(Bt,"__esModule",{value:!0}),Bt.default=void 0,Bt.default=function(e){var t=[["x","y"],["left","top"],["right","bottom"],["width","height"]].filter(function(n){var r=or(n,2),i=r[0],c=r[1];return i in e||c in e}),o=function(n,r){for(var i=e.range,c=e.limits,a=c===void 0?{left:-1/0,right:1/0,top:-1/0,bottom:1/0}:c,l=e.offset,p=l===void 0?{x:0,y:0}:l,f={range:i,grid:e,x:null,y:null},m=0;m<t.length;m++){var b=or(t[m],2),w=b[0],y=b[1],S=Math.round((n-p.x)/e[w]),O=Math.round((r-p.y)/e[y]);f[w]=Math.max(a.left,Math.min(a.right,S*e[w]+p.x)),f[y]=Math.max(a.top,Math.min(a.bottom,O*e[y]+p.y))}return f};return o.grid=e,o.coordFields=t,o};var st={};Object.defineProperty(st,"__esModule",{value:!0}),Object.defineProperty(st,"edgeTarget",{enumerable:!0,get:function(){return Ft.default}}),Object.defineProperty(st,"elements",{enumerable:!0,get:function(){return Vt.default}}),Object.defineProperty(st,"grid",{enumerable:!0,get:function(){return Bt.default}});var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.default=void 0;var Zi={id:"snappers",install:function(e){var t=e.interactStatic;t.snappers=(0,I.default)(t.snappers||{},st),t.createSnapGrid=t.snappers.grid}};Nt.default=Zi;var Ve={};function ir(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),o.push.apply(o,n)}return o}function Cn(e){for(var t=1;t<arguments.length;t++){var o=arguments[t]!=null?arguments[t]:{};t%2?ir(Object(o),!0).forEach(function(n){Qi(e,n,o[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ir(Object(o)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(o,n))})}return e}function Qi(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}Object.defineProperty(Ve,"__esModule",{value:!0}),Ve.aspectRatio=Ve.default=void 0;var ar={start:function(e){if(!e.state.options.enabled)return!1;var t=e.state,o=e.rect,n=e.edges,r=e.pageCoords,i=t.options.ratio,c=t.options,a=c.equalDelta,l=c.modifiers;i==="preserve"&&(i=o.width/o.height),t.startCoords=(0,I.default)({},r),t.startRect=(0,I.default)({},o),t.ratio=i,t.equalDelta=a;var p=t.linkedEdges={top:n.top||n.left&&!n.bottom,left:n.left||n.top&&!n.right,bottom:n.bottom||n.right&&!n.top,right:n.right||n.bottom&&!n.left};if(t.xIsPrimaryAxis=!(!n.left&&!n.right),t.equalDelta)t.edgeSign=(p.left?1:-1)*(p.top?1:-1);else{var f=t.xIsPrimaryAxis?p.top:p.left;t.edgeSign=f?-1:1}if((0,I.default)(e.edges,p),l&&l.length){var m=new Oe.default(e.interaction);m.copyFrom(e.interaction.modification),m.prepareStates(l),t.subModification=m,m.startAll(Cn({},e))}},set:function(e){if(e.state.options.enabled===!1)return!1;var t=e.state,o=e.rect,n=e.coords,r=(0,I.default)({},n),i=t.equalDelta?Ji:ea;if(i(t,t.xIsPrimaryAxis,n,o),!t.subModification)return null;var c=(0,I.default)({},o);(0,G.addEdges)(t.linkedEdges,c,{x:n.x-r.x,y:n.y-r.y});var a=t.subModification.setAll(Cn(Cn({},e),{},{rect:c,edges:t.linkedEdges,pageCoords:n,prevCoords:n,prevRect:c})),l=a.delta;return a.changed&&(i(t,Math.abs(l.x)>Math.abs(l.y),a.coords,a.rect),(0,I.default)(n,a.coords)),a.eventProps},defaults:{ratio:"preserve",equalDelta:!1,modifiers:[],enabled:!1}};function Ji(e,t,o){var n=e.startCoords,r=e.edgeSign;t?o.y=n.y+(o.x-n.x)*r:o.x=n.x+(o.y-n.y)*r}function ea(e,t,o,n){var r=e.startRect,i=e.startCoords,c=e.ratio,a=e.edgeSign;if(t){var l=n.width/c;o.y=i.y+(l-r.height)*a}else{var p=n.height*c;o.x=i.x+(p-r.width)*a}}Ve.aspectRatio=ar;var ta=(0,ee.makeModifier)(ar,"aspectRatio");Ve.default=ta;var Ce={};Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.default=void 0;var sr=function(){};sr._defaults={};var na=sr;Ce.default=na;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Object.defineProperty(Ln,"default",{enumerable:!0,get:function(){return Ce.default}});var ne={};function In(e,t,o){return d.default.func(e)?G.resolveRectLike(e,t.interactable,t.element,[o.x,o.y,t]):G.resolveRectLike(e,t.interactable,t.element)}Object.defineProperty(ne,"__esModule",{value:!0}),ne.getRestrictionRect=In,ne.restrict=ne.default=void 0;var lr={start:function(e){var t=e.rect,o=e.startOffset,n=e.state,r=e.interaction,i=e.pageCoords,c=n.options,a=c.elementRect,l=(0,I.default)({left:0,top:0,right:0,bottom:0},c.offset||{});if(t&&a){var p=In(c.restriction,r,i);if(p){var f=p.right-p.left-t.width,m=p.bottom-p.top-t.height;f<0&&(l.left+=f,l.right+=f),m<0&&(l.top+=m,l.bottom+=m)}l.left+=o.left-t.width*a.left,l.top+=o.top-t.height*a.top,l.right+=o.right-t.width*(1-a.right),l.bottom+=o.bottom-t.height*(1-a.bottom)}n.offset=l},set:function(e){var t=e.coords,o=e.interaction,n=e.state,r=n.options,i=n.offset,c=In(r.restriction,o,t);if(c){var a=G.xywhToTlbr(c);t.x=Math.max(Math.min(a.right-i.right,t.x),a.left+i.left),t.y=Math.max(Math.min(a.bottom-i.bottom,t.y),a.top+i.top)}},defaults:{restriction:null,elementRect:null,offset:null,endOnly:!1,enabled:!1}};ne.restrict=lr;var oa=(0,ee.makeModifier)(lr,"restrict");ne.default=oa;var fe={};Object.defineProperty(fe,"__esModule",{value:!0}),fe.restrictEdges=fe.default=void 0;var cr={top:1/0,left:1/0,bottom:-1/0,right:-1/0},ur={top:-1/0,left:-1/0,bottom:1/0,right:1/0};function pr(e,t){for(var o=["top","left","bottom","right"],n=0;n<o.length;n++){var r=o[n];r in e||(e[r]=t[r])}return e}var dr={noInner:cr,noOuter:ur,start:function(e){var t,o=e.interaction,n=e.startOffset,r=e.state,i=r.options;if(i){var c=(0,ne.getRestrictionRect)(i.offset,o,o.coords.start.page);t=G.rectToXY(c)}t=t||{x:0,y:0},r.offset={top:t.y+n.top,left:t.x+n.left,bottom:t.y-n.bottom,right:t.x-n.right}},set:function(e){var t=e.coords,o=e.edges,n=e.interaction,r=e.state,i=r.offset,c=r.options;if(o){var a=(0,I.default)({},t),l=(0,ne.getRestrictionRect)(c.inner,n,a)||{},p=(0,ne.getRestrictionRect)(c.outer,n,a)||{};pr(l,cr),pr(p,ur),o.top?t.y=Math.min(Math.max(p.top+i.top,a.y),l.top+i.top):o.bottom&&(t.y=Math.max(Math.min(p.bottom+i.bottom,a.y),l.bottom+i.bottom)),o.left?t.x=Math.min(Math.max(p.left+i.left,a.x),l.left+i.left):o.right&&(t.x=Math.max(Math.min(p.right+i.right,a.x),l.right+i.right))}},defaults:{inner:null,outer:null,offset:null,endOnly:!1,enabled:!1}};fe.restrictEdges=dr;var ra=(0,ee.makeModifier)(dr,"restrictEdges");fe.default=ra;var Be={};Object.defineProperty(Be,"__esModule",{value:!0}),Be.restrictRect=Be.default=void 0;var ia=(0,I.default)({get elementRect(){return{top:0,left:0,bottom:1,right:1}},set elementRect(e){}},ne.restrict.defaults),fr={start:ne.restrict.start,set:ne.restrict.set,defaults:ia};Be.restrictRect=fr;var aa=(0,ee.makeModifier)(fr,"restrictRect");Be.default=aa;var Ne={};Object.defineProperty(Ne,"__esModule",{value:!0}),Ne.restrictSize=Ne.default=void 0;var sa={width:-1/0,height:-1/0},la={width:1/0,height:1/0},hr={start:function(e){return fe.restrictEdges.start(e)},set:function(e){var t=e.interaction,o=e.state,n=e.rect,r=e.edges,i=o.options;if(r){var c=G.tlbrToXywh((0,ne.getRestrictionRect)(i.min,t,e.coords))||sa,a=G.tlbrToXywh((0,ne.getRestrictionRect)(i.max,t,e.coords))||la;o.options={endOnly:i.endOnly,inner:(0,I.default)({},fe.restrictEdges.noInner),outer:(0,I.default)({},fe.restrictEdges.noOuter)},r.top?(o.options.inner.top=n.bottom-c.height,o.options.outer.top=n.bottom-a.height):r.bottom&&(o.options.inner.bottom=n.top+c.height,o.options.outer.bottom=n.top+a.height),r.left?(o.options.inner.left=n.right-c.width,o.options.outer.left=n.right-a.width):r.right&&(o.options.inner.right=n.left+c.width,o.options.outer.right=n.left+a.width),fe.restrictEdges.set(e),o.options=i}},defaults:{min:null,max:null,endOnly:!1,enabled:!1}};Ne.restrictSize=hr;var ca=(0,ee.makeModifier)(hr,"restrictSize");Ne.default=ca;var An={};Object.defineProperty(An,"__esModule",{value:!0}),Object.defineProperty(An,"default",{enumerable:!0,get:function(){return Ce.default}});var xe={};Object.defineProperty(xe,"__esModule",{value:!0}),xe.snap=xe.default=void 0;var vr={start:function(e){var t,o=e.interaction,n=e.interactable,r=e.element,i=e.rect,c=e.state,a=e.startOffset,l=c.options,p=l.offsetWithOrigin?function(b){var w=b.interaction.element;return(0,G.rectToXY)((0,G.resolveRectLike)(b.state.options.origin,null,null,[w]))||(0,ke.default)(b.interactable,w,b.interaction.prepared.name)}(e):{x:0,y:0};if(l.offset==="startCoords")t={x:o.coords.start.page.x,y:o.coords.start.page.y};else{var f=(0,G.resolveRectLike)(l.offset,n,r,[o]);(t=(0,G.rectToXY)(f)||{x:0,y:0}).x+=p.x,t.y+=p.y}var m=l.relativePoints;c.offsets=i&&m&&m.length?m.map(function(b,w){return{index:w,relativePoint:b,x:a.left-i.width*b.x+t.x,y:a.top-i.height*b.y+t.y}}):[{index:0,relativePoint:null,x:t.x,y:t.y}]},set:function(e){var t=e.interaction,o=e.coords,n=e.state,r=n.options,i=n.offsets,c=(0,ke.default)(t.interactable,t.element,t.prepared.name),a=(0,I.default)({},o),l=[];r.offsetWithOrigin||(a.x-=c.x,a.y-=c.y);for(var p=0;p<i.length;p++)for(var f=i[p],m=a.x-f.x,b=a.y-f.y,w=0,y=r.targets.length;w<y;w++){var S,O=r.targets[w];(S=d.default.func(O)?O(m,b,t._proxy,f,w):O)&&l.push({x:(d.default.number(S.x)?S.x:m)+f.x,y:(d.default.number(S.y)?S.y:b)+f.y,range:d.default.number(S.range)?S.range:r.range,source:O,index:w,offset:f})}for(var x={target:null,inRange:!1,distance:0,range:0,delta:{x:0,y:0}},C=0;C<l.length;C++){var j=l[C],F=j.range,X=j.x-a.x,Z=j.y-a.y,N=(0,we.default)(X,Z),Y=N<=F;F===1/0&&x.inRange&&x.range!==1/0&&(Y=!1),x.target&&!(Y?x.inRange&&F!==1/0?N/F<x.distance/x.range:F===1/0&&x.range!==1/0||N<x.distance:!x.inRange&&N<x.distance)||(x.target=j,x.distance=N,x.range=F,x.inRange=Y,x.delta.x=X,x.delta.y=Z)}return x.inRange&&(o.x=x.target.x,o.y=x.target.y),n.closest=x,x},defaults:{range:1/0,targets:null,offset:null,offsetWithOrigin:!0,origin:null,relativePoints:null,endOnly:!1,enabled:!1}};xe.snap=vr;var ua=(0,ee.makeModifier)(vr,"snap");xe.default=ua;var me={};function gr(e,t){(t==null||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}Object.defineProperty(me,"__esModule",{value:!0}),me.snapSize=me.default=void 0;var mr={start:function(e){var t=e.state,o=e.edges,n=t.options;if(!o)return null;e.state={options:{targets:null,relativePoints:[{x:o.left?0:1,y:o.top?0:1}],offset:n.offset||"self",origin:{x:0,y:0},range:n.range}},t.targetFields=t.targetFields||[["width","height"],["x","y"]],xe.snap.start(e),t.offsets=e.state.offsets,e.state=t},set:function(e){var t,o,n=e.interaction,r=e.state,i=e.coords,c=r.options,a=r.offsets,l={x:i.x-a[0].x,y:i.y-a[0].y};r.options=(0,I.default)({},c),r.options.targets=[];for(var p=0;p<(c.targets||[]).length;p++){var f=(c.targets||[])[p],m=void 0;if(m=d.default.func(f)?f(l.x,l.y,n):f){for(var b=0;b<r.targetFields.length;b++){var w=(t=r.targetFields[b],o=2,function(x){if(Array.isArray(x))return x}(t)||function(x,C){if(typeof Symbol!="undefined"&&Symbol.iterator in Object(x)){var j=[],F=!0,X=!1,Z=void 0;try{for(var N,Y=x[Symbol.iterator]();!(F=(N=Y.next()).done)&&(j.push(N.value),!C||j.length!==C);F=!0);}catch(ue){X=!0,Z=ue}finally{try{F||Y.return==null||Y.return()}finally{if(X)throw Z}}return j}}(t,o)||function(x,C){if(x){if(typeof x=="string")return gr(x,C);var j=Object.prototype.toString.call(x).slice(8,-1);return j==="Object"&&x.constructor&&(j=x.constructor.name),j==="Map"||j==="Set"?Array.from(x):j==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(j)?gr(x,C):void 0}}(t,o)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()),y=w[0],S=w[1];if(y in m||S in m){m.x=m[y],m.y=m[S];break}}r.options.targets.push(m)}}var O=xe.snap.set(e);return r.options=c,O},defaults:{range:1/0,targets:null,offset:null,endOnly:!1,enabled:!1}};me.snapSize=mr;var pa=(0,ee.makeModifier)(mr,"snapSize");me.default=pa;var qe={};Object.defineProperty(qe,"__esModule",{value:!0}),qe.snapEdges=qe.default=void 0;var yr={start:function(e){var t=e.edges;return t?(e.state.targetFields=e.state.targetFields||[[t.left?"left":"right",t.top?"top":"bottom"]],me.snapSize.start(e)):null},set:me.snapSize.set,defaults:(0,I.default)((0,Me.default)(me.snapSize.defaults),{targets:null,range:null,offset:{x:0,y:0}})};qe.snapEdges=yr;var da=(0,ee.makeModifier)(yr,"snapEdges");qe.default=da;var Dn={};Object.defineProperty(Dn,"__esModule",{value:!0}),Object.defineProperty(Dn,"default",{enumerable:!0,get:function(){return Ce.default}});var jn={};Object.defineProperty(jn,"__esModule",{value:!0}),Object.defineProperty(jn,"default",{enumerable:!0,get:function(){return Ce.default}});var Xe={};Object.defineProperty(Xe,"__esModule",{value:!0}),Xe.default=void 0;var fa={aspectRatio:Ve.default,restrictEdges:fe.default,restrict:ne.default,restrictRect:Be.default,restrictSize:Ne.default,snapEdges:qe.default,snap:xe.default,snapSize:me.default,spring:Dn.default,avoid:Ln.default,transform:jn.default,rubberband:An.default};Xe.default=fa;var qt={};Object.defineProperty(qt,"__esModule",{value:!0}),qt.default=void 0;var ha={id:"modifiers",install:function(e){var t=e.interactStatic;for(var o in e.usePlugin(ee.default),e.usePlugin(Nt.default),t.modifiers=Xe.default,Xe.default){var n=Xe.default[o],r=n._defaults,i=n._methods;r._methods=i,e.defaults.perAction[o]=r}}};qt.default=ha;var Le={};function br(e){return(br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function va(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function wr(e,t){return(wr=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o})(e,t)}function ga(e,t){return!t||br(t)!=="object"&&typeof t!="function"?Hn(e):t}function Hn(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rn(e){return(Rn=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}Object.defineProperty(Le,"__esModule",{value:!0}),Le.PointerEvent=Le.default=void 0;var ma=function(e){(function(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),l&&wr(a,l)})(c,e);var t,o,n,r,i=(n=c,r=function(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}(),function(){var a,l=Rn(n);if(r){var p=Rn(this).constructor;a=Reflect.construct(l,arguments,p)}else a=l.apply(this,arguments);return ga(this,a)});function c(a,l,p,f,m,b){var w;if(function(O,x){if(!(O instanceof x))throw new TypeError("Cannot call a class as a function")}(this,c),(w=i.call(this,m)).type=void 0,w.originalEvent=void 0,w.pointerId=void 0,w.pointerType=void 0,w.double=void 0,w.pageX=void 0,w.pageY=void 0,w.clientX=void 0,w.clientY=void 0,w.dt=void 0,w.eventable=void 0,L.pointerExtend(Hn(w),p),p!==l&&L.pointerExtend(Hn(w),l),w.timeStamp=b,w.originalEvent=p,w.type=a,w.pointerId=L.getPointerId(l),w.pointerType=L.getPointerType(l),w.target=f,w.currentTarget=null,a==="tap"){var y=m.getPointerIndex(l);w.dt=w.timeStamp-m.pointers[y].downTime;var S=w.timeStamp-m.tapTime;w.double=!!(m.prevTap&&m.prevTap.type!=="doubletap"&&m.prevTap.target===w.target&&S<500)}else a==="doubletap"&&(w.dt=l.timeStamp-m.tapTime);return w}return t=c,(o=[{key:"_subtractOrigin",value:function(a){var l=a.x,p=a.y;return this.pageX-=l,this.pageY-=p,this.clientX-=l,this.clientY-=p,this}},{key:"_addOrigin",value:function(a){var l=a.x,p=a.y;return this.pageX+=l,this.pageY+=p,this.clientX+=l,this.clientY+=p,this}},{key:"preventDefault",value:function(){this.originalEvent.preventDefault()}}])&&va(t.prototype,o),c}(je.BaseEvent);Le.PointerEvent=Le.default=ma;var lt={};Object.defineProperty(lt,"__esModule",{value:!0}),lt.default=void 0;var Xt={id:"pointer-events/base",before:["inertia","modifiers","auto-start","actions"],install:function(e){e.pointerEvents=Xt,e.defaults.actions.pointerEvents=Xt.defaults,(0,I.default)(e.actions.phaselessTypes,Xt.types)},listeners:{"interactions:new":function(e){var t=e.interaction;t.prevTap=null,t.tapTime=0},"interactions:update-pointer":function(e){var t=e.down,o=e.pointerInfo;!t&&o.hold||(o.hold={duration:1/0,timeout:null})},"interactions:move":function(e,t){var o=e.interaction,n=e.pointer,r=e.event,i=e.eventTarget;e.duplicate||o.pointerIsDown&&!o.pointerWasMoved||(o.pointerIsDown&&zn(e),Se({interaction:o,pointer:n,event:r,eventTarget:i,type:"move"},t))},"interactions:down":function(e,t){(function(o,n){for(var r=o.interaction,i=o.pointer,c=o.event,a=o.eventTarget,l=o.pointerIndex,p=r.pointers[l].hold,f=A.getPath(a),m={interaction:r,pointer:i,event:c,eventTarget:a,type:"hold",targets:[],path:f,node:null},b=0;b<f.length;b++){var w=f[b];m.node=w,n.fire("pointerEvents:collect-targets",m)}if(m.targets.length){for(var y=1/0,S=0;S<m.targets.length;S++){var O=m.targets[S].eventable.options.holdDuration;O<y&&(y=O)}p.duration=y,p.timeout=setTimeout(function(){Se({interaction:r,eventTarget:a,pointer:i,event:c,type:"hold"},n)},y)}})(e,t),Se(e,t)},"interactions:up":function(e,t){zn(e),Se(e,t),function(o,n){var r=o.interaction,i=o.pointer,c=o.event,a=o.eventTarget;r.pointerWasMoved||Se({interaction:r,eventTarget:a,pointer:i,event:c,type:"tap"},n)}(e,t)},"interactions:cancel":function(e,t){zn(e),Se(e,t)}},PointerEvent:Le.PointerEvent,fire:Se,collectEventTargets:Er,defaults:{holdDuration:600,ignoreFrom:null,allowFrom:null,origin:{x:0,y:0}},types:{down:!0,move:!0,up:!0,cancel:!0,tap:!0,doubletap:!0,hold:!0}};function Se(e,t){var o=e.interaction,n=e.pointer,r=e.event,i=e.eventTarget,c=e.type,a=e.targets,l=a===void 0?Er(e,t):a,p=new Le.PointerEvent(c,n,r,i,o,t.now());t.fire("pointerEvents:new",{pointerEvent:p});for(var f={interaction:o,pointer:n,event:r,eventTarget:i,targets:l,type:c,pointerEvent:p},m=0;m<l.length;m++){var b=l[m];for(var w in b.props||{})p[w]=b.props[w];var y=(0,ke.default)(b.eventable,b.node);if(p._subtractOrigin(y),p.eventable=b.eventable,p.currentTarget=b.node,b.eventable.fire(p),p._addOrigin(y),p.immediatePropagationStopped||p.propagationStopped&&m+1<l.length&&l[m+1].node!==p.currentTarget)break}if(t.fire("pointerEvents:fired",f),c==="tap"){var S=p.double?Se({interaction:o,pointer:n,event:r,eventTarget:i,type:"doubletap"},t):p;o.prevTap=S,o.tapTime=S.timeStamp}return p}function Er(e,t){var o=e.interaction,n=e.pointer,r=e.event,i=e.eventTarget,c=e.type,a=o.getPointerIndex(n),l=o.pointers[a];if(c==="tap"&&(o.pointerWasMoved||!l||l.downTarget!==i))return[];for(var p=A.getPath(i),f={interaction:o,pointer:n,event:r,eventTarget:i,type:c,path:p,targets:[],node:null},m=0;m<p.length;m++){var b=p[m];f.node=b,t.fire("pointerEvents:collect-targets",f)}return c==="hold"&&(f.targets=f.targets.filter(function(w){var y;return w.eventable.options.holdDuration===((y=o.pointers[a])==null?void 0:y.hold.duration)})),f.targets}function zn(e){var t=e.interaction,o=e.pointerIndex,n=t.pointers[o].hold;n&&n.timeout&&(clearTimeout(n.timeout),n.timeout=null)}var ya=Xt;lt.default=ya;var Yt={};function ba(e){var t=e.interaction;t.holdIntervalHandle&&(clearInterval(t.holdIntervalHandle),t.holdIntervalHandle=null)}Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.default=void 0;var wa={id:"pointer-events/holdRepeat",install:function(e){e.usePlugin(lt.default);var t=e.pointerEvents;t.defaults.holdRepeatInterval=0,t.types.holdrepeat=e.actions.phaselessTypes.holdrepeat=!0},listeners:["move","up","cancel","endall"].reduce(function(e,t){return e["pointerEvents:".concat(t)]=ba,e},{"pointerEvents:new":function(e){var t=e.pointerEvent;t.type==="hold"&&(t.count=(t.count||0)+1)},"pointerEvents:fired":function(e,t){var o=e.interaction,n=e.pointerEvent,r=e.eventTarget,i=e.targets;if(n.type==="hold"&&i.length){var c=i[0].eventable.options.holdRepeatInterval;c<=0||(o.holdIntervalHandle=setTimeout(function(){t.pointerEvents.fire({interaction:o,eventTarget:r,type:"hold",pointer:n,event:n},t)},c))}}})};Yt.default=wa;var Ut={};function Ea(e){return(0,I.default)(this.events.options,e),this}Object.defineProperty(Ut,"__esModule",{value:!0}),Ut.default=void 0;var xa={id:"pointer-events/interactableTargets",install:function(e){var t=e.Interactable;t.prototype.pointerEvents=Ea;var o=t.prototype._backCompatOption;t.prototype._backCompatOption=function(n,r){var i=o.call(this,n,r);return i===this&&(this.events.options[n]=r),i}},listeners:{"pointerEvents:collect-targets":function(e,t){var o=e.targets,n=e.node,r=e.type,i=e.eventTarget;t.interactables.forEachMatch(n,function(c){var a=c.events,l=a.options;a.types[r]&&a.types[r].length&&c.testIgnoreAllow(l,n,i)&&o.push({node:n,eventable:a,props:{interactable:c}})})},"interactable:new":function(e){var t=e.interactable;t.events.getRect=function(o){return t.getRect(o)}},"interactable:set":function(e,t){var o=e.interactable,n=e.options;(0,I.default)(o.events.options,t.pointerEvents.defaults),(0,I.default)(o.events.options,n.pointerEvents||{})}}};Ut.default=xa;var Kt={};Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.default=void 0;var Sa={id:"pointer-events",install:function(e){e.usePlugin(lt),e.usePlugin(Yt.default),e.usePlugin(Ut.default)}};Kt.default=Sa;var ct={};function xr(e){var t=e.Interactable;e.actions.phases.reflow=!0,t.prototype.reflow=function(o){return function(n,r,i){for(var c=d.default.string(n.target)?K.from(n._context.querySelectorAll(n.target)):[n.target],a=i.window.Promise,l=a?[]:null,p=function(){var m=c[f],b=n.getRect(m);if(!b)return"break";var w=K.find(i.interactions.list,function(C){return C.interacting()&&C.interactable===n&&C.element===m&&C.prepared.name===r.name}),y=void 0;if(w)w.move(),l&&(y=w._reflowPromise||new a(function(C){w._reflowResolve=C}));else{var S=(0,G.tlbrToXywh)(b),O={page:{x:S.x,y:S.y},client:{x:S.x,y:S.y},timeStamp:i.now()},x=L.coordsToEvent(O);y=function(C,j,F,X,Z){var N=C.interactions.new({pointerType:"reflow"}),Y={interaction:N,event:Z,pointer:Z,eventTarget:F,phase:"reflow"};N.interactable=j,N.element=F,N.prevEvent=Z,N.updatePointer(Z,Z,F,!0),L.setZeroCoords(N.coords.delta),(0,ae.copyAction)(N.prepared,X),N._doPhase(Y);var ue=C.window.Promise,ye=ue?new ue(function(Wn){N._reflowResolve=Wn}):void 0;return N._reflowPromise=ye,N.start(X,j,F),N._interacting?(N.move(Y),N.end(Z)):(N.stop(),N._reflowResolve()),N.removePointer(Z,Z),ye}(i,n,m,r,x)}l&&l.push(y)},f=0;f<c.length&&p()!=="break";f++);return l&&a.all(l).then(function(){return n})}(this,o,e)}}Object.defineProperty(ct,"__esModule",{value:!0}),ct.install=xr,ct.default=void 0;var Pa={id:"reflow",install:xr,listeners:{"interactions:stop":function(e,t){var o=e.interaction;o.pointerType==="reflow"&&(o._reflowResolve&&o._reflowResolve(),K.remove(t.interactions.list,o))}}};ct.default=Pa;var ce={exports:{}};function Sr(e){return(Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(ce.exports,"__esModule",{value:!0}),ce.exports.default=void 0,te.default.use(Re.default),te.default.use(_e.default),te.default.use(Kt.default),te.default.use(We.default),te.default.use(qt.default),te.default.use(Tt.default),te.default.use(wt.default),te.default.use(Ee.default),te.default.use(ct.default),te.default.use(Ot.default);var ka=te.default;if(ce.exports.default=ka,Sr(ce)==="object"&&ce)try{ce.exports=te.default}catch(e){}te.default.default=te.default,ce=ce.exports;var Ie={exports:{}};function Pr(e){return(Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}Object.defineProperty(Ie.exports,"__esModule",{value:!0}),Ie.exports.default=void 0;var Ta=ce.default;if(Ie.exports.default=Ta,Pr(Ie)==="object"&&Ie)try{Ie.exports=ce.default}catch(e){}return ce.default.default=ce.default,Ie.exports})});Ia(exports,{default:()=>on,genId:()=>qn});function oe(g,s){let u=Object.keys(s).map(h=>Da(g,h,s[h]));return u.length===1?u[0]:function(){u.forEach(h=>h())}}function Da(g,s,u){let h=g[s],v=g.hasOwnProperty(s),d=u(h);return h&&Object.setPrototypeOf(d,h),Object.setPrototypeOf(E,d),g[s]=E,k;function E(...P){return d===h&&g[s]===E&&k(),d.apply(this,P)}function k(){g[s]===E&&(v?g[s]=h:delete g[s]),d!==h&&(d=h,Object.setPrototypeOf(E,h||Function))}}var V=be(require("obsidian"));var Pe=be(require("obsidian"));var z=be(require("obsidian"));function Fn(g){let s=Or(),[u,h]=s(g);if(!u)return!1;if(h||(h="px"),["em","ex","ch","rem","vw","vh","vmin","vmax","%","cm","mm","in","px","pt","pc"].contains(h))return u+h}function pe(g,s){var u;return g instanceof s||((u=g==null?void 0:g.instanceOf)==null?void 0:u.call(g,s))}var _r=10,Vn=60;function Ye(g){let s=g.body.querySelector(".app-container, .workspace-split"),u=g.body.querySelector(".mod-left.workspace-ribbon"),h=s.offsetTop,v=g.body.hasClass("hider-ribbon")?0:u?u.offsetWidth:0;return{top:h,left:v}}function ja(g){let s=g.getAttribute("data-orig-height"),u=g.getAttribute("data-orig-width"),h=parseFloat(g.getAttribute("data-orig-pos-left")||"0"),v=parseFloat(g.getAttribute("data-orig-pos-top")||"0"),d=Ye(g.ownerDocument).top;return v<d&&(v=d),{height:s,width:u,top:v,left:h}}function Ue(g,s){let{height:u,width:h,top:v,left:d}=ja(g);s||(g.removeAttribute("data-orig-width"),g.removeAttribute("data-orig-height"),g.removeAttribute("data-orig-pos-left"),g.removeAttribute("data-orig-pos-top")),h&&(g.style.width=h+"px"),u&&(g.style.height=u+"px"),v&&(g.style.top=v+"px",g.setAttribute("data-y",String(v))),d&&(g.style.left=d+"px")}function Gt(g){if(g.hasClass("snap-to-viewport")){g.removeClass("snap-to-viewport"),Ue(g);return}}function Bn(g,s){let u=g.querySelector(".view-content").offsetHeight;u=s?-u:u;let h=parseFloat(g.getAttribute("data-y")||"0")+u;g.style.top=h+"px",g.setAttribute("data-y",String(h))}function ut(g){g.hasAttribute("data-orig-width")||g.setAttribute("data-orig-width",String(g.offsetWidth)),g.hasAttribute("data-orig-height")||g.setAttribute("data-orig-height",String(g.offsetHeight)),g.hasAttribute("data-orig-pos-left")||g.setAttribute("data-orig-pos-left",String(parseFloat(g.style.left))),g.hasAttribute("data-orig-pos-top")||g.setAttribute("data-orig-pos-top",String(parseFloat(g.style.top)))}function Cr(g){return g.hasAttribute("data-orig-width")&&g.hasAttribute("data-orig-height")&&g.hasAttribute("data-orig-pos-left")&&g.hasAttribute("data-orig-pos-top")}function Nn(g){let s=g.target,u=g.client.x-g.rect.left,h=g.rect.width,v=u/h,d=s.offsetWidth,E=String(g.client.x-v*d),k=String(g.client.y);s.setAttribute("data-x",String(E)),s.setAttribute("data-y",String(k))}function Ae(g,s,u){g.addClass(`snap-to-${s}`),g.style.top=u.top+"px",g.style.height=`calc(100vh - ${u.top}px)`,g.style.left=s==="right"?"unset":u.left+"px",s==="viewport"&&(g.style.width=`calc(100vw - ${u.left}px)`)}function Lr(g){let s=g.target,{x:u,y:h}=s.dataset;if(u=u||s.style.left,h=h||s.style.top,u=String((parseFloat(u)||0)+g.dx),h=String((parseFloat(h)||0)+g.dy),this.plugin.settings.snapToEdges){let v,d=s.ownerDocument,E=g.client.x<_r,k=g.client.x>d.body.offsetWidth-_r,P=g.client.y<30;if((E||k||P)&&(v=Ye(d),ut(s)),E&&g.buttons){Ae(s,"left",v);return}else if(k&&g.buttons){Ae(s,"right",v);return}else if(P&&g.buttons){Ae(s,"viewport",v);return}else if(s.hasClass("snap-to-viewport")){if(g.client.y<Vn)return;s.removeClass("snap-to-viewport"),Ue(s),Nn(g);return}else if(s.hasClass("snap-to-left")){if(g.client.y<Vn)return;s.removeClass("snap-to-left"),Ue(s),Nn(g);return}else if(s.hasClass("snap-to-right")){if(g.client.y<Vn)return;s.removeClass("snap-to-right"),Ue(s),Nn(g);return}}s.style.top=h?h+"px":s.style.top,s.style.left=u?u+"px":s.style.left,s.setAttribute("data-x",String(u)),s.setAttribute("data-y",String(h))}var Ir=["left","right","viewport"],Ar=(g,s)=>{var h;let u=(h=U.activePopover)==null?void 0:h.hoverEl;if(u&&pe(u,HTMLElement)){if(!s){Cr(u)?Ue(u,!0):ut(u),u.removeClasses(["snap-to-left","snap-to-right","snap-to-viewport"]);let v=Ye(u.ownerDocument);Ae(u,g,v)}return!0}return!1},Dr=g=>{var u;let s=(u=U.activePopover)==null?void 0:u.hoverEl;return s&&pe(s,HTMLElement)?(g||Cr(s)&&(s.removeClasses(["snap-to-left","snap-to-right","snap-to-viewport"]),Ue(s)),!0):!1},jr=g=>{var h;let s=(h=U.activePopover)==null?void 0:h.hoverEl,u=U.activePopovers().find(v=>v.hoverEl===s);return u?(g||u.toggleMinimized(),!0):!1};var Zt=new WeakMap,Qt={x:0,y:0};function Ha(g){let s=function(){return Object.setPrototypeOf(new z.Component,new.target.prototype)};return s.prototype=g.prototype,Object.setPrototypeOf(s,g)}var Rr,zr,Wr,Fr,Vr,U=class extends Ha(z.HoverPopover){constructor(s,u,h,v,d){super();this.targetEl=u;this.plugin=h;this.onShowCallback=d;this.isPinned=this.plugin.settings.autoPin==="always";this.abortController=this.addChild(new z.Component);this.detaching=!1;this.opening=!1;this.rootSplit=new z.WorkspaceSplit(window.app.workspace,"vertical");this.targetRect=(Rr=this.targetEl)==null?void 0:Rr.getBoundingClientRect();this.oldPopover=(zr=this.parent)==null?void 0:zr.hoverPopover;this.document=(Vr=(Fr=(Wr=this.targetEl)==null?void 0:Wr.ownerDocument)!=null?Fr:window.activeDocument)!=null?Vr:window.document;this.interactStatic=this.plugin.interact.forDom(this.document.body).interact;this.id=qn(8);this.hoverEl=this.document.defaultView.createDiv({cls:"popover hover-popover",attr:{id:"he"+this.id}});v===void 0&&(v=300),this.onTarget=!0,this.onHover=!1,this.shownPos=null,this.parent=s,this.waitTime=v,this.state=z.PopoverState.Showing;let{hoverEl:E}=this;this.onMouseIn=this._onMouseIn.bind(this),this.onMouseOut=this._onMouseOut.bind(this),this.abortController.load(),u&&(u.addEventListener("mouseover",this.onMouseIn),u.addEventListener("mouseout",this.onMouseOut)),E.addEventListener("mouseover",P=>{Jt(P,E)&&(this.onHover=!0,this.onTarget=!1,this.transition())}),E.addEventListener("mouseout",P=>{Jt(P,E)&&(this.onHover=!1,this.onTarget=!1,this.transition())}),this.timer=window.setTimeout(this.show.bind(this),v),this.document.addEventListener("mousemove",dt),Zt.set(this.hoverEl,this),this.hoverEl.addClass("hover-editor"),this.containerEl=this.hoverEl.createDiv("popover-content"),this.buildWindowControls(),this.setInitialDimensions();let k=this.pinEl=this.document.defaultView.createEl("a","popover-header-icon mod-pin-popover");this.titleEl.prepend(this.pinEl),k.onclick=()=>{this.togglePin()},z.requireApiVersion&&(0,z.requireApiVersion)("0.13.27")?(0,z.setIcon)(k,"lucide-pin",17):(0,z.setIcon)(k,"pin",17),this.createResizeHandles(),this.plugin.settings.imageZoom&&this.registerZoomImageHandlers()}static activeWindows(){let s=[window],{floatingSplit:u}=app.workspace;if(u)for(let h of u.children)h.win&&s.push(h.win);return s}static containerForDocument(s){if(s!==document&&app.workspace.floatingSplit){for(let u of app.workspace.floatingSplit.children)if(u.doc===s)return u}return app.workspace.rootSplit}static activePopovers(){return this.activeWindows().flatMap(this.popoversForWindow)}static popoversForWindow(s){var u,h;return Array.prototype.slice.call((h=(u=s==null?void 0:s.document)==null?void 0:u.body.querySelectorAll(".hover-popover"))!=null?h:[]).map(v=>Zt.get(v)).filter(v=>v)}static forLeaf(s){let u=s&&document.body.matchParent.call(s.containerEl,".hover-popover");return u?Zt.get(u):void 0}static iteratePopoverLeaves(s,u){for(let h of this.activePopovers())if(h.rootSplit&&s.iterateLeaves(u,h.rootSplit))return!0;return!1}adopt(s){if(this.targetEl===s)return!0;let u=s==null?void 0:s.getBoundingClientRect();if(Hr(this.targetRect,u)){this.targetEl.removeEventListener("mouseover",this.onMouseIn),this.targetEl.removeEventListener("mouseout",this.onMouseOut),s.addEventListener("mouseover",this.onMouseIn),s.addEventListener("mouseout",this.onMouseOut),this.targetEl=s,this.targetRect=u;let{x:h,y:v}=Qt;return this.onTarget=Hr(u,{left:h,right:h,top:v,bottom:v}),this.transition(),!0}else this.onTarget=!1,this.transition();return!1}onZoomOut(){this.document.body.removeEventListener("mouseup",this.boundOnZoomOut),this.document.body.removeEventListener("dragend",this.boundOnZoomOut),this.hoverEl.hasClass("do-not-restore")?this.hoverEl.removeClass("do-not-restore"):Gt(this.hoverEl)}onZoomIn(s){if(s.button!==0)return;this.hoverEl.hasClass("snap-to-viewport")&&this.hoverEl.addClass("do-not-restore"),this.document.body.addEventListener("mouseup",this.boundOnZoomOut,{once:!0}),this.document.body.addEventListener("dragend",this.boundOnZoomOut,{once:!0});let u=Ye(this.document);return ut(this.hoverEl),Ae(this.hoverEl,"viewport",u),!1}registerZoomImageHandlers(){this.hoverEl.addClass("image-zoom"),this.boundOnZoomOut=this.onZoomOut.bind(this),this.hoverEl.on("mousedown","img",this.onZoomIn.bind(this))}togglePin(s){var u;s===void 0&&(s=!this.isPinned),s&&((u=this.abortController)==null||u.unload()),this.hoverEl.toggleClass("is-pinned",s),this.pinEl.toggleClass("is-active",s),this.isPinned=s}getDefaultMode(){var s,u;return((u=(s=this.parent)==null?void 0:s.view)==null?void 0:u.getMode)?this.parent.view.getMode():"preview"}updateLeaves(){this.onTarget&&this.targetEl&&!this.document.contains(this.targetEl)&&(this.onTarget=!1,this.transition());let s=0;this.plugin.app.workspace.iterateLeaves(u=>{s++},this.rootSplit),s===0?this.hide():s>1&&this.toggleConstrainAspectRatio(!1),this.hoverEl.setAttribute("data-leaf-count",s.toString())}get headerHeight(){let s=this.hoverEl;return this.titleEl.getBoundingClientRect().bottom-s.getBoundingClientRect().top}toggleMinimized(s){var v;let u=this.hoverEl,h=this.headerHeight;if(!u.hasAttribute("data-restore-height"))this.plugin.settings.rollDown&&Bn(u,!1),u.setAttribute("data-restore-height",String(u.offsetHeight)),u.style.minHeight=h+"px",u.style.maxHeight=h+"px",u.toggleClass("is-minimized",!0);else{let d=u.getAttribute("data-restore-height");d&&(u.removeAttribute("data-restore-height"),u.style.height=d+"px"),u.style.removeProperty("max-height"),u.toggleClass("is-minimized",!1),this.plugin.settings.rollDown&&Bn(u,!0)}(v=this.interact)==null||v.reflow({name:"drag",axis:"xy"})}attachLeaf(){this.rootSplit.getRoot=()=>app.workspace[this.document===document?"rootSplit":"floatingSplit"],this.rootSplit.getContainer=()=>U.containerForDocument(this.document),this.titleEl.insertAdjacentElement("afterend",this.rootSplit.containerEl);let s=this.plugin.app.workspace.createLeafInParent(this.rootSplit,0);return this.updateLeaves(),s}onload(){super.onload(),this.registerEvent(this.plugin.app.workspace.on("layout-change",this.updateLeaves,this)),this.registerEvent(app.workspace.on("layout-change",()=>{this.rootSplit.children.forEach((s,u)=>{s instanceof z.WorkspaceTabs&&this.rootSplit.replaceChild(u,s.children[0])})}))}leaves(){let s=[];return this.plugin.app.workspace.iterateLeaves(u=>{s.push(u)},this.rootSplit),s}setInitialDimensions(){this.hoverEl.style.height=this.plugin.settings.initialHeight,this.hoverEl.style.width=this.plugin.settings.initialWidth}adjustHeight(s){this.hoverEl.style.height=this.hoverEl.offsetHeight+s+"px"}toggleViewHeader(s,u){var d;s===void 0&&(s=!this.hoverEl.hasClass("show-navbar")),(d=this.hideNavBarEl)==null||d.toggleClass("is-active",s),this.hoverEl.toggleClass("show-navbar",s);let h=this.hoverEl.querySelector(".view-header");if(!h||u)return;let v=parseFloat(getComputedStyle(h).getPropertyValue("--he-view-header-height"));this.hoverEl.style.transition="height 0.2s",this.adjustHeight(s?v:-v),setTimeout(()=>{this.hoverEl.style.removeProperty("transition")},200),this.requestLeafMeasure()}buildWindowControls(){this.titleEl=this.document.defaultView.createDiv("popover-titlebar"),this.titleEl.createDiv("popover-title");let s=this.titleEl.createDiv("popover-actions"),u=this.hideNavBarEl=s.createEl("a","popover-action mod-show-navbar");(0,z.setIcon)(u,"sidebar-open",14),u.addEventListener("click",E=>{this.toggleViewHeader()}),this.plugin.settings.showViewHeader&&this.toggleViewHeader(!0,!0);let h=s.createEl("a","popover-action mod-minimize");(0,z.setIcon)(h,"minus"),h.addEventListener("click",E=>{Gt(this.hoverEl),this.toggleMinimized()});let v=s.createEl("a","popover-action mod-maximize");(0,z.setIcon)(v,"maximize",14),v.addEventListener("click",E=>{if(this.hoverEl.hasClass("snap-to-viewport")){(0,z.setIcon)(v,"maximize",14),Gt(this.hoverEl);return}(0,z.setIcon)(v,"minimize",14);let k=Ye(this.document);ut(this.hoverEl),Ae(this.hoverEl,"viewport",k)});let d=s.createEl("a","popover-action mod-close");(0,z.setIcon)(d,"x"),d.addEventListener("click",E=>{this.hide()}),this.containerEl.prepend(this.titleEl)}requestLeafMeasure(){let s=this.leaves();s.length&&setTimeout(()=>{s.forEach(u=>u.onResize())},200)}onShow(){var u,h;let{closeDelay:s}=this.plugin.settings;setTimeout(()=>this.waitTime=s,s),(u=this.oldPopover)==null||u.hide(),this.oldPopover=null,this.hoverEl.toggleClass("is-new",!0),this.document.body.addEventListener("click",()=>{this.hoverEl.toggleClass("is-new",!1)},{once:!0,capture:!0}),this.parent&&(this.parent.hoverPopover=this),(0,z.requireApiVersion)("0.15.1")&&!(0,z.requireApiVersion)("0.15.7")&&app.workspace.iterateLeaves(v=>{var d,E;v.view instanceof z.MarkdownView&&((E=(d=v.view.editMode).reinit)==null||E.call(d))},this.rootSplit),this.togglePin(this.isPinned),(h=this.onShowCallback)==null||h.call(this),this.onShowCallback=void 0}startBounce(){this.bounce=setTimeout(()=>{this.hoverEl.style.left=parseFloat(this.hoverEl.style.left)+this.xspeed+"px",this.hoverEl.style.top=parseFloat(this.hoverEl.style.top)+this.yspeed+"px",this.checkHitBox(),this.startBounce()},20)}toggleBounce(){if(this.xspeed=7,this.yspeed=7,this.bounce){clearTimeout(this.bounce),this.bounce=void 0;let s=this.hoverEl.querySelector(".view-content");(s==null?void 0:s.style)&&s.style.removeProperty("backgroundColor")}else this.startBounce()}checkHitBox(){let s=parseFloat(this.hoverEl.style.left),u=parseFloat(this.hoverEl.style.top),h=parseFloat(this.hoverEl.style.width),v=parseFloat(this.hoverEl.style.height);(s<=0||s+h>=this.document.body.offsetWidth)&&(this.xspeed*=-1,this.pickColor()),(u<=0||u+v>=this.document.body.offsetHeight)&&(this.yspeed*=-1,this.pickColor())}pickColor(){let s=Math.random()*(254-0)+0,u=Math.random()*(254-0)+0,h=Math.random()*(254-0)+0,v=this.hoverEl.querySelector(".view-content");(v==null?void 0:v.style)&&(v.style.backgroundColor="rgb("+s+","+u+", "+h+")")}transition(){this.shouldShow()?this.state===z.PopoverState.Hiding&&(this.state=z.PopoverState.Shown,clearTimeout(this.timer)):this.state===z.PopoverState.Showing?this.hide():this.state===z.PopoverState.Shown&&(this.state=z.PopoverState.Hiding,this.timer=window.setTimeout(()=>{this.shouldShow()?this.transition():this.hide()},this.waitTime))}detect(s){let{targetEl:u,hoverEl:h}=this;u&&(this.onTarget=s===u||u.contains(s)),this.onHover=s===h||h.contains(s)}_onMouseIn(s){this.targetEl&&!Jt(s,this.targetEl)||(this.onTarget=!0,this.transition())}_onMouseOut(s){this.targetEl&&!Jt(s,this.targetEl)||(this.onTarget=!1,this.transition())}position(s){s===void 0&&(s=this.shownPos);let u;if(s)u={top:s.y-10,bottom:s.y+10,left:s.x,right:s.x};else if(this.targetEl){let h=za(this.targetEl,this.document.body);u={top:h.top,bottom:h.top+this.targetEl.offsetHeight,left:h.left,right:h.left+this.targetEl.offsetWidth}}else u={top:0,bottom:0,left:0,right:0};this.document.body.appendChild(this.hoverEl),Ra(u,this.hoverEl,{gap:10},this.document),s&&setTimeout(()=>{let h=parseFloat(this.hoverEl.style.left),v=parseFloat(this.hoverEl.style.top);this.hoverEl.setAttribute("data-x",String(h)),this.hoverEl.setAttribute("data-y",String(v))},0)}shouldShow(){return this.shouldShowSelf()||this.shouldShowChild()}shouldShowChild(){return U.activePopovers().some(s=>s!==this&&s.targetEl&&this.hoverEl.contains(s.targetEl)?s.shouldShow():!1)}shouldShowSelf(){return!this.detaching&&!!(this.onTarget||this.onHover||this.state==z.PopoverState.Shown&&this.isPinned||this.document.querySelector(`body>.modal-container, body > #he${this.id} ~ .menu, body > #he${this.id} ~ .suggestion-container`))}calculateMinSize(){return{width:40,height:this.headerHeight}}calculateBoundaries(s,u,h){let v=h.element.closest("body"),d=(v==null?void 0:v.querySelector(".workspace"))||(v==null?void 0:v.querySelector(".workspace-window"));return d==null?void 0:d.getBoundingClientRect()}calculateMaxSize(s,u,h){let v=h.pointerType==="reflow"?this.document.body.offsetWidth/1.5:this.document.body.offsetWidth,d=h.pointerType==="reflow"?this.document.body.offsetHeight/1.5:this.document.body.offsetHeight;return{width:v,height:d}}toggleConstrainAspectRatio(s,u){let h=this.resizeModifiers.find(v=>v.name=="aspectRatio");!h||(s===void 0&&(s=!h.options.enabled),s?(h.enable(),this.constrainAspectRatio=!0,u!==void 0&&h.options.ratio!==u&&(h.options.ratio=u)):(h.disable(),this.constrainAspectRatio=!1))}registerInteract(){var T,_;let s=this.document.querySelector("div.app-container, div.workspace-split"),u=this,h=function(M,B,H){let{top:q,right:D,bottom:W,left:Q,x:le,y:$,width:J,height:rn}=s.getBoundingClientRect(),A={top:q,right:D,bottom:W,left:Q,x:le,y:$,width:J,height:rn};return H.pointerType==="reflow"?u.dragElementRect.bottom=1:u.dragElementRect.bottom=0,u.plugin.settings.snapToEdges&&(A.top=q-30),A.bottom=W-u.headerHeight,A},v=!0,d,E=((T=this.hoverEl.dataset)==null?void 0:T.imgRatio)?parseFloat((_=this.hoverEl.dataset)==null?void 0:_.imgRatio):void 0;this.resizeModifiers=[this.interactStatic.modifiers.restrictEdges({outer:u.calculateBoundaries.bind(this)}),this.interactStatic.modifiers.restrictSize({min:u.calculateMinSize.bind(this),max:u.calculateMaxSize.bind(this)}),this.interactStatic.modifiers.aspectRatio({ratio:E||"preserve",enabled:!1})],this.dragElementRect={top:0,left:1,bottom:0,right:0};let k=[this.interactStatic.modifiers.restrict({restriction:h,offset:{top:0,left:40,bottom:0,right:40},elementRect:this.dragElementRect,endOnly:!1})];this.constrainAspectRatio&&E!==void 0&&this.toggleConstrainAspectRatio(!0,E);let P=this.interactStatic(this.hoverEl).preventDefault("always").on("doubletap",this.onDoubleTap.bind(this)).draggable({modifiers:k,allowFrom:".popover-titlebar",listeners:{start(M){M.buttons&&u.togglePin(!0),M.buttons&&pe(M.target,HTMLElement)&&M.target.addClass("is-dragging")},end(M){pe(M.target,HTMLElement)&&M.target.removeClass("is-dragging")},move:Lr.bind(u)}}).resizable({edges:{top:".top-left, .top-right, .top",left:".top-left, .bottom-left, .left",bottom:".bottom-left, .bottom-right, .bottom",right:".top-right, .bottom-right, .right"},modifiers:this.resizeModifiers,listeners:{start(M){var D;M.target.style.removeProperty("max-height");let H=(D=u.hoverEl.querySelector(".view-header"))==null?void 0:D.offsetHeight;d=u.titleEl.offsetHeight+H,v=!0,M.buttons&&u.togglePin(!0)},move:function(M){var Q,le,$;if(!(M==null?void 0:M.deltaRect)||!M.edges)return;let{target:B}=M,{x:H,y:q}=B.dataset,D=M.rect.height,W=M.rect.width;H=H||B.style.left,q=q||B.style.top,H=String((parseFloat(H)||0)+((Q=M.deltaRect)==null?void 0:Q.left)),q=String((parseFloat(q)||0)+((le=M.deltaRect)==null?void 0:le.top)),u.constrainAspectRatio&&E&&M.buttons!==void 0?(v&&(M.edges.top&&(M.edges.right||M.edges.left)?q=String(parseFloat(q)-d):M.edges.top?H=String(parseFloat(H)+d*E):M.edges.left&&!(M.edges.top||M.edges.bottom)&&(q=String(parseFloat(q)-d))),v=!1,(M.edges.top&&!(M.edges.right||M.edges.left)||M.edges.bottom&&!(M.edges.right||M.edges.left))&&(D=D-d,W=W-d*E),D=D+d,(B.hasClass("snap-to-left")||B.hasClass("snap-to-right"))&&(q=String(parseFloat(B.style.top)),H=String(parseFloat(B.style.left)))):E&&D>(($=this==null?void 0:this.document)==null?void 0:$.body.offsetHeight)&&(D=D/1.5,W=D*E),Object.assign(B.style,{width:`${W}px`,height:`${D}px`,top:`${q}px`,left:H==="NaN"?"unset":`${H}px`}),Object.assign(B.dataset,{x:H,y:q})},end:function(M){M.rect.height>u.headerHeight&&M.target.removeAttribute("data-restore-height"),P.reflow({name:"drag",axis:"xy"})}}});this.interact=P}createResizeHandles(){this.hoverEl.createDiv("resize-handle bottom-left"),this.hoverEl.createDiv("resize-handle bottom-right"),this.hoverEl.createDiv("resize-handle top-left"),this.hoverEl.createDiv("resize-handle top-right"),this.hoverEl.createDiv("resize-handle right"),this.hoverEl.createDiv("resize-handle left"),this.hoverEl.createDiv("resize-handle bottom"),this.hoverEl.createDiv("resize-handle top")}onDoubleTap(s){s.target.tagName==="DIV"&&s.target.closest(".popover-titlebar")&&(s.preventDefault(),this.togglePin(!0),this.toggleMinimized())}show(){var s,u;!this.targetEl||this.document.body.contains(this.targetEl)?(this.state=z.PopoverState.Shown,this.timer=0,this.shownPos=Qt,this.position(Qt),this.document.removeEventListener("mousemove",dt),this.onShow(),app.workspace.onLayoutChange(),this.load()):this.hide(),this.hoverEl.dataset.imgHeight&&this.hoverEl.dataset.imgWidth&&(this.hoverEl.style.height=parseFloat(this.hoverEl.dataset.imgHeight)+this.titleEl.offsetHeight+"px",this.hoverEl.style.width=parseFloat(this.hoverEl.dataset.imgWidth)+"px"),this.registerInteract(),(s=this.interact)==null||s.reflow({name:"resize",edges:{right:!0,bottom:!0}}),(u=this.interact)==null||u.reflow({name:"drag",axis:"xy"})}onHide(){var s;this.oldPopover=null,((s=this.parent)==null?void 0:s.hoverPopover)===this&&(this.parent.hoverPopover=null)}hide(){var u,h;if(this.onTarget=this.onHover=!1,this.isPinned=!1,this.detaching=!0,this.document.removeEventListener("mousemove",dt),this.timer&&(clearTimeout(this.timer),this.timer=0),this.hoverEl.hide(),this.opening)return;let s=this.leaves();if(s.length)s.forEach(v=>{v.view instanceof z.MarkdownView&&!this._loaded&&(v.view.onMarkdownFold=()=>null),v.detach(),v===app.workspace.activeLeaf&&(app.workspace.activeLeaf=null)});else return this.parent=null,((u=this.interact)==null?void 0:u.unset)&&this.interact.unset(),(h=this.abortController)==null||h.unload(),this.abortController=void 0,this.interact=void 0,this.nativeHide()}nativeHide(){var h;let{hoverEl:s,targetEl:u}=this;if(this.state=z.PopoverState.Hidden,s.detach(),u){let v=u.matchParent(".hover-popover");v&&((h=Zt.get(v))==null||h.transition()),u.removeEventListener("mouseover",this.onMouseIn),u.removeEventListener("mouseout",this.onMouseOut)}this.onHide(),this.unload()}resolveLink(s,u){let h=(0,z.parseLinktext)(s);return h?this.plugin.app.metadataCache.getFirstLinkpathDest(h.path,u):null}async openLink(s,u,h,v){var H,q,D;let d=this.resolveLink(s,u),E=(0,z.parseLinktext)(s);if(!d&&v){let W=this.plugin.app.fileManager.getNewFileParent(u);d=await this.plugin.app.fileManager.createNewMarkdownFile(W,E.path)}if(!d){this.displayCreateFileAction(s,u,h);return}let{viewRegistry:k}=this.plugin.app,P=k.typeByExtension[d.extension];if(!P||!k.viewByType[P]){this.displayOpenFileAction(d);return}h=Object.assign(this.buildEphemeralState(d,E),h);let T=this.getDefaultMode(),_=this.buildState(T,h),M=await this.openFile(d,_,v),B=(H=M==null?void 0:M.view)==null?void 0:H.getViewType();if(B==="image"){this.plugin.settings.autoFocus&&((q=this.parent)==null?void 0:q.hasOwnProperty("editorEl"))&&this.parent.editorEl.hasClass("is-live-preview")&&(this.waitTime=3e3),this.constrainAspectRatio=!0;let W=M.view.contentEl.querySelector("img");this.hoverEl.dataset.imgHeight=String(W.naturalHeight),this.hoverEl.dataset.imgWidth=String(W.naturalWidth),this.hoverEl.dataset.imgRatio=String(W.naturalWidth/W.naturalHeight)}else B==="pdf"&&(this.hoverEl.style.height="800px",this.hoverEl.style.width="600px");((D=_.state)==null?void 0:D.mode)==="source"&&this.whenShown(()=>{var W,Q,le,$;(0,z.requireApiVersion)("1.0")&&((le=(Q=(W=M==null?void 0:M.view)==null?void 0:W.editMode)==null?void 0:Q.reinit)==null||le.call(Q)),($=M==null?void 0:M.view)==null||$.setEphemeralState(_.eState)})}displayOpenFileAction(s){let h=this.attachLeaf().view;h.emptyTitleEl.hide(),h.actionListEl.empty();let{actionListEl:v}=h;v.createDiv({cls:"file-embed-title"},d=>{d.createSpan({cls:"file-embed-icon"},E=>(0,z.setIcon)(E,"document",22)),d.appendText(" "+s.name)}),v.addEventListener("click",()=>this.plugin.app.openWithDefaultApp(s.path)),v.setAttribute("aria-label",i18next.t("interface.embed-open-in-default-app-tooltip"))}displayCreateFileAction(s,u,h){var E,k,P;let v=this.attachLeaf(),d=v.view;if(d){(E=d.emptyTitleEl)==null||E.hide(),(k=d.actionListEl)==null||k.empty();let T=(P=d.actionListEl)==null?void 0:P.createEl("button","empty-state-action");if(!T)return;T.textContent=`${s} is not yet created. Click to create.`,this.plugin.settings.autoFocus&&setTimeout(()=>{T==null||T.focus()},200),T.addEventListener("click",async()=>{this.togglePin(!0),await this.openLink(s,u,h,v)},{once:!0})}}whenShown(s){if(this.detaching)return;let u=this.onShowCallback;this.onShowCallback=()=>{this.detaching||(s(),typeof u=="function"&&u())},this.state===z.PopoverState.Shown&&(this.onShowCallback(),this.onShowCallback=void 0)}async openFile(s,u,h){var d,E,k;if(this.detaching)return;let v=h!=null?h:this.attachLeaf();this.opening=!0;try{if(await v.openFile(s,u),this.plugin.settings.autoFocus&&!this.detaching)this.whenShown(()=>{app.workspace.setActiveLeaf(v,!1,!1),app.workspace.activeLeaf===v&&v.setEphemeralState({focus:!0}),setTimeout(oe(z.Workspace.prototype,{recordMostRecentOpenedFile(T){return function(_){if(_!==s)return T.call(this,_)}}}),1);let P=this.plugin.app.plugins.plugins["recent-files-obsidian"];P&&setTimeout(oe(P,{shouldAddFile(T){return function(_){return _!==s&&T.call(this,_)}}}),1)});else if(!this.plugin.settings.autoFocus&&!this.detaching){let P=this.hoverEl.querySelector(".popover-title");if(!P)return;P.textContent=(d=v.view)==null?void 0:d.getDisplayText(),P.setAttribute("data-path",(k=(E=v.view)==null?void 0:E.file)==null?void 0:k.path)}}catch(P){console.error(P)}finally{this.opening=!1,this.detaching&&this.hide()}return v}buildState(s,u){let v=this.plugin.settings.defaultMode==="match"?s:this.plugin.settings.defaultMode;return{active:!1,state:{mode:v},eState:u}}buildEphemeralState(s,u){let h=this.plugin.app.metadataCache.getFileCache(s),v=h?(0,z.resolveSubpath)(h,(u==null?void 0:u.subpath)||""):void 0,d={subpath:u==null?void 0:u.subpath};return v&&(d.line=v.start.line,d.startLoc=v.start,d.endLoc=v.end||void 0),d}};function pt(g){return g.containerEl.matches(".popover.hover-popover.hover-editor .workspace-leaf")}function Ra(g,s,u,h){u=u||{},s.show();let v=u.gap||0,d=u.preference||"bottom",E=u.offsetParent||s.offsetParent||h.documentElement,k=u.horizontalAlignment||"left",P=E.scrollTop+10,T=E.scrollTop+E.clientHeight-10,_=Math.min(g.top,T),M=Math.max(g.bottom,P),B=s.offsetHeight,H=g.top-P>=B+v,q=T-g.bottom>=B+v,D=0,W="";!H||d!=="top"&&q?!q||d!=="bottom"&&H?E.clientHeight<B+v?(D=P,W="overlap"):d==="top"?(D=P+v,W="overlap"):(D=T-B,W="overlap"):(D=M+v,W="bottom"):(D=_-v-B,W="top");let Q=E.scrollLeft+10,le=E.scrollLeft+E.clientWidth-10,$=s.offsetWidth,J=k==="left"?g.left:g.right-$;return J<Q?J=Q:J>le-$&&(J=le-$),s.style.top="".concat(D.toString(),"px"),s.style.left="".concat(J.toString(),"px"),{top:D,left:J,vresult:W}}function za(g,s){let u=0,h=0;for(let v=s?s.offsetParent:null;g&&g!==s&&g!==v;){u+=g.offsetTop,h+=g.offsetLeft;let d=g.offsetParent;for(let E=g.parentElement;E&&E!==d;)u-=E.scrollTop,h-=E.scrollLeft,E=E.parentElement;d&&d!==s&&d!==v&&(u-=d.scrollTop,h-=d.scrollLeft),g=d}return{top:u,left:h}}function dt(g){Qt={x:g.clientX,y:g.clientY}}function Jt(g,s){let u=g.relatedTarget;return!(pe(u,Node)&&s.contains(u))}function Hr(g,s){return!!(g&&s&&g.right>s.left&&g.left<s.right&&g.bottom>s.top&&g.top<s.bottom)}var en=new WeakMap;function Br(g,s,u,h,v,d,...E){var T;u&&u.matches('.workspace-leaf-content[data-type="calendar"] table.calendar td > div')&&(u=u.parentElement),u&&u.matches(".bookmark .tree-item-inner")&&(s&&s.innerEl===u&&(s=s.tree),u=(T=u.parentElement)!=null?T:u);let k=en.has(u)?en.get(u):s.hoverPopover;if(k==null?void 0:k.lockedOut)return;if(k&&k.state!==Pe.PopoverState.Hidden&&(!k.isPinned||g.settings.autoPin==="always")&&k.targetEl!==null&&k.originalLinkText===h&&k.originalPath===v&&u&&k.adopt(u))en.set(u,k);else{let _=new U(s,u,g,g.settings.triggerDelay);u&&en.set(u,_),_.originalLinkText=h,_.originalPath=v,s.hoverPopover=_;let M=_.abortController,B=function(){!_||(_.lockedOut=!1)},H=function(W){!_||pe(W.target,HTMLElement)&&!W.target.closest(".hover-editor, .menu")&&(_.state=Pe.PopoverState.Hidden,_.hide(),_.lockedOut=!0,setTimeout(B,1e3))},{document:q}=_,D=function(W){if(!_)return;let Q=Pe.Platform.isMacOS?"Meta":"Control";!_.onHover&&_.state!==Pe.PopoverState.Shown&&W.key!==Q?(_.state=Pe.PopoverState.Hidden,_.hide(),_.lockedOut=!0,setTimeout(B,1e3)):q.body.removeEventListener("keyup",D,!0)};q.addEventListener("pointerdown",H,!0),q.addEventListener("mousedown",H,!0),q.body.addEventListener("keyup",D,!0),M.register(()=>{q.removeEventListener("pointerdown",H,!0),q.removeEventListener("mousedown",H,!0),q.body.removeEventListener("keyup",D,!0)}),setTimeout(()=>{(_==null?void 0:_.state)!=Pe.PopoverState.Hidden&&(_==null||_.openLink(h,v,d))},0)}}var Ge=be(require("obsidian"));var Xn=Symbol.for("v1.to-use.peak-dev.org"),Yn=Symbol.for("v1.factory.to-use.peak-dev.org"),Ke,ft,se=function(){return Object.defineProperties(g(),{this:{get(){if(Ke)return Ke;throw new TypeError("No current context")}},me:{value:Xn},factory:{value:Yn}});function g(v){let d=new Map;d.prev=v;let E=Object.assign(v?P=>{let T=d.get(P);if(!T){for(let H=d.prev;H;H=H.prev)if(T=H.get(P)){T=Object.assign(Object.assign({},T),{s:T.s||1});break}T=T||{s:2,v:u},d.set(P,T)}let _,M,B;for(;;)switch(T.s){case 0:return Ke===E&&ft&&ft.push(P),T.v;case 1:if(_=T.d,!_||k(()=>_.k.every(H=>E(H)===_.c(H)))){T.s=0;break}T.v=_.f;case 2:T.s=4;try{s(d,P,0,k(M=T.v,P,B=[])),B.length&&(T.d={c:E,f:M,k:B});break}catch(H){T.s=3,T.v=H,T.d=null}case 3:throw T.v;case 4:throw new Error(`Factory ${String(T.v)} didn't resolve ${String(P)}`)}}:P=>se.this(P),{def(P,T){return s(d,P,2,T),E},set(P,T){return s(d,P,1,T),E},fork(P){let T=g(d);return P!=null?T(P):T}});return v?E.use=E:E;function k(P,T,_){let M=Ke,B=ft;try{return Ke=E,ft=_,P(T)}finally{Ke=M,ft=B}}}function s(v,d,E,k){if(v.has(d)){let P=v.get(d);if(!P.s)throw new Error(`Already read: ${String(d)}`);P.s=E,P.v=k,P.d=null}else v.set(d,{s:E,v:k})}function u(v){if(typeof v[Xn]=="function")return v[Xn](v);if(h(v))return typeof v.prototype[Yn]=="function"?v.prototype[Yn]():new v;throw new ReferenceError(`No config for ${String(v)}`)}function h(v){return typeof v=="function"&&v.prototype!==void 0&&(Object.getPrototypeOf(v.prototype)!==Object.prototype||Object.getOwnPropertyNames(v.prototype).length>1||v.toString().startsWith("class"))}}();var Nr,$e=(Nr=window.queueMicrotask)!=null?Nr:(g=>s=>g.then(s))(Promise.resolve());se.def(Ge.Plugin,()=>{throw new Error("Plugin not created yet")});var tn=class extends Ge.Component{constructor(){super(...arguments);this.use=se.service(this)}};se.service=function g(g){return se(Un).addChild(g),se.this};se.plugin=function g(g){let s=se.fork().set(Ge.Plugin,g).set(g.constructor,g);return g.addChild(s.use(Un)),s};var Un=class extends Ge.Component{constructor(){super(...arguments);this.children=new Set([this])}onload(){this.loaded=!0}onunload(){this.loaded=!1,this.children.clear()}addChild(s){return this.children.has(s)||(this.children.add(s),this.loaded?$e(()=>super.addChild(s)):super.addChild(s)),s}};var qr=be(require("obsidian"));var Kn=2,_s=Symbol.for(`v${Kn}.layout-storage-events.ophidian.peak-dev.org`);var Cs=`ophidian-layout-storage:v${Kn}:item-load`,Ls=`ophidian-layout-storage:v${Kn}:item-save`;var Xr=be(require("obsidian"));var Yr=be(require("obsidian"));var $n=class extends Yr.Component{constructor(s,u){super();this.use=s;this.win=u}get container(){return Va(this.win)}[se.factory](){return new Ur(this.constructor)}},Ur=class extends tn{constructor(s){super();this.factory=s;this.instances=new WeakMap;this.watching=!1}watch(){if(!this._loaded)this.onload=()=>this.watch();else if(!this.watching){let{workspace:s}=app;this.watching=!0,this.registerEvent(s.on("window-open",(u,h)=>{s.onLayoutReady(()=>$e(()=>this.forWindow(h)))})),s.onLayoutReady(()=>$e(()=>this.forAll()))}return this}forWindow(s=(h=>(h=window.activeWindow)!=null?h:window)(),u=!0){let v=this.instances.get(s);return!v&&u&&(v=new this.factory(this.use,s),v&&(this.instances.set(s,v),v.registerDomEvent(s,"beforeunload",()=>{this.removeChild(v),this.instances.delete(s)}),this.addChild(v))),v||void 0}forDom(s,u=!0){return this.forWindow(Fa(s),u)}forLeaf(s,u=!0){return this.forDom(s.containerEl,u)}forView(s,u=!0){return this.forLeaf(s.leaf,u)}forAll(s=!0){return Wa().map(u=>this.forWindow(u,s)).filter(u=>u)}};function Wa(){let g=[window],{floatingSplit:s}=app.workspace;if(s)for(let u of s.children)u.win&&g.push(u.win);return g}function Fa(g){return g.win||(g.ownerDocument||g).defaultView||window}function Va(g){if(g===window)return app.workspace.rootSplit;let{floatingSplit:s}=app.workspace;if(s){for(let u of s.children)if(g===u.win)return u}}var re=be(require("obsidian"));var nn={defaultMode:"preview",autoPin:"onMove",triggerDelay:300,closeDelay:600,autoFocus:!0,rollDown:!1,snapToEdges:!1,initialHeight:"340px",initialWidth:"400px",showViewHeader:!1,imageZoom:!0,hoverEmbeds:!1},Ba={preview:"Reading view",source:"Editing view",match:"Match current view"},Na={onMove:"On drag or resize",always:"Always"},Gn=class extends re.PluginSettingTab{constructor(s,u){super(s,u);this.plugin=u}hide(){}display(){let{containerEl:s}=this;s.empty(),new re.Setting(s).setName("Default Mode").addDropdown(u=>{u.addOptions(Ba),u.setValue(this.plugin.settings.defaultMode),u.onChange(async h=>{this.plugin.settings.defaultMode=h,await this.plugin.saveSettings()})}),new re.Setting(s).setName("Auto Pin").addDropdown(u=>{u.addOptions(Na),u.setValue(this.plugin.settings.autoPin),u.onChange(async h=>{this.plugin.settings.autoPin=h,await this.plugin.saveSettings()})}),new re.Setting(s).setName("Trigger hover preview on embeds").setDesc("Allow hover preview to trigger when hovering over any type of rendered embed such as images or block references").addToggle(u=>u.setValue(this.plugin.settings.hoverEmbeds).onChange(h=>{this.plugin.settings.hoverEmbeds=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Auto Focus").setDesc("Set the hover editor as the active pane when opened").addToggle(u=>u.setValue(this.plugin.settings.autoFocus).onChange(h=>{this.plugin.settings.autoFocus=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Minimize downwards").setDesc("When double clicking to minimize, the window will roll down instead of rolling up").addToggle(u=>u.setValue(this.plugin.settings.rollDown).onChange(h=>{this.plugin.settings.rollDown=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Snap to edges").setDesc(`Quickly arrange popovers by dragging them to the edges of the screen. The left and right edges 
        will maximize the popover vertically. The top edge will maximize the popover to fill the entire 
        screen. Dragging the popovers away from the edges will restore the popver to its original size.`).addToggle(u=>u.setValue(this.plugin.settings.snapToEdges).onChange(h=>{this.plugin.settings.snapToEdges=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Show view header by default").setDesc(`Show the view header by default when triggering a hover editor.
         When disabled, view headers will only show if you click the view header icon to the left of the minimize button.`).addToggle(u=>u.setValue(this.plugin.settings.showViewHeader).onChange(h=>{this.plugin.settings.showViewHeader=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Click to zoom image").setDesc(`Click and hold an image within a hover editor to temporarily maximize the popover and image to fill the entire viewport. 
        On mouse up, the hover editor will restore to its original size.`).addToggle(u=>u.setValue(this.plugin.settings.imageZoom).onChange(h=>{this.plugin.settings.imageZoom=h,this.plugin.saveSettings()})),new re.Setting(s).setName("Initial popover width").setDesc("Enter any valid CSS unit").addText(u=>{u.setPlaceholder(this.plugin.settings.initialWidth),u.inputEl.type="text",u.setValue(this.plugin.settings.initialWidth),u.onChange(async h=>{h=Fn(h),h||(h=nn.initialWidth),this.plugin.settings.initialWidth=h,this.plugin.saveSettings()})}),new re.Setting(s).setName("Initial popover height").setDesc("Enter any valid CSS unit").addText(u=>{u.setPlaceholder(String(this.plugin.settings.initialHeight)),u.inputEl.type="text",u.setValue(String(this.plugin.settings.initialHeight)),u.onChange(async h=>{h=Fn(h),h||(h=nn.initialHeight),this.plugin.settings.initialHeight=h,this.plugin.saveSettings()})}),new re.Setting(s).setName("Hover Trigger Delay (ms)").setDesc("How long to wait before showing a Hover Editor when hovering over a link").addText(u=>{u.setPlaceholder(String(this.plugin.settings.triggerDelay)),u.inputEl.type="number",u.setValue(String(this.plugin.settings.triggerDelay)),u.onChange(async h=>{this.plugin.settings.triggerDelay=Number(h),this.plugin.saveSettings()})}),new re.Setting(s).setName("Hover Close Delay (ms)").setDesc("How long to wait before closing a Hover Editor once the mouse leaves").addText(u=>{u.setPlaceholder(String(this.plugin.settings.closeDelay)),u.inputEl.type="number",u.setValue(String(this.plugin.settings.closeDelay)),u.onChange(async h=>{this.plugin.settings.closeDelay=Number(h),this.plugin.saveSettings()})})}};var Qn=be($r());var Gr=class extends $n{constructor(){super(...arguments);this.interact=this.createInteractor();this.plugin=this.use(on)}createInteractor(){if(this.win===window)return Qn.default;let s=Qn.default.scope,h=new s.constructor().init(this.win).interactStatic;for(let v of s._plugins.list)h.use(v);return h}onload(){this.win.addEventListener("resize",this.plugin.debouncedPopoverReflow)}onunload(){this.win.removeEventListener("resize",this.plugin.debouncedPopoverReflow);try{this.interact.removeDocument(this.win.document)}catch(s){console.error(s)}}},on=class extends V.Plugin{constructor(){super(...arguments);this.use=se.plugin(this);this.interact=this.use(Gr);this.debouncedPopoverReflow=(0,V.debounce)(()=>{U.activePopovers().forEach(s=>{var u;(u=s.interact)==null||u.reflow({name:"drag",axis:"xy"})})},100,!0)}async onload(){this.registerActivePopoverHandler(),this.registerFileRenameHandler(),this.registerContextMenuHandler(),this.registerCommands(),this.patchUnresolvedGraphNodeHover(),this.patchWorkspace(),this.patchQuickSwitcher(),this.patchWorkspaceLeaf(),this.patchItemView(),this.patchMarkdownPreviewRenderer(),this.patchMarkdownPreviewView(),await this.loadSettings(),this.registerSettingsTab(),this.app.workspace.onLayoutReady(()=>{this.patchSlidingPanes(),this.patchLinkHover(),setTimeout(()=>{this.app.workspace.trigger("css-change")},2e3)})}get activePopovers(){return U.activePopovers()}patchWorkspaceLeaf(){this.register(oe(V.WorkspaceLeaf.prototype,{getRoot(s){return function(){let u=s.call(this);return u.getRoot===this.getRoot?u:u.getRoot()}},onResize(s){return function(){var u;(u=this.view)==null||u.onResize()}},setViewState(s){return async function(u,h){var d,E,k;let v=await s.call(this,u,h);try{let P=U.forLeaf(this);if(P){u.type&&P.hoverEl.setAttribute("data-active-view-type",u.type);let T=P.hoverEl.querySelector(".popover-title");T&&(T.textContent=(d=this.view)==null?void 0:d.getDisplayText(),((k=(E=this.view)==null?void 0:E.file)==null?void 0:k.path)?T.setAttribute("data-path",this.view.file.path):T.removeAttribute("data-path"))}}catch(P){}return v}},setEphemeralState(s){return function(u){var h;s.call(this,u),u.focus&&((h=this.view)==null?void 0:h.getViewType())==="empty"&&(this.view.contentEl.tabIndex=-1,this.view.contentEl.focus())}}})),this.register(oe(V.WorkspaceItem.prototype,{getContainer(s){return function(){if(!!s)return!this.parentSplit||this instanceof V.WorkspaceContainer?s.call(this):this.parentSplit.getContainer()}}}))}patchQuickSwitcher(){let s=this,{QuickSwitcherModal:u}=this.app.internalPlugins.plugins.switcher.instance,h=oe(u.prototype,{open(v){return function(){let d=v.call(this);return this.setInstructions([{command:V.Platform.isMacOS?"cmd p":"ctrl p",purpose:"to open in new popover"}]),this.scope.register(["Mod"],"p",E=>{this.close();let k=this.chooser.values[this.chooser.selectedItem];if(!(k==null?void 0:k.file))return;let P=s.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(P,!1,!0));return P.openFile(k.file),!1}),d}}});this.register(h)}patchItemView(){let s=this,[u,h]=V.View.prototype.onPaneMenu?[V.View,"onPaneMenu"]:[V.ItemView,"onMoreOptionsMenu"],v=oe(u.prototype,{[h](d){return function(E,...k){return(this.leaf?U.forLeaf(this.leaf):void 0)?E.addItem(T=>{var _,M;(M=(_=T.setIcon("popup-open").setTitle("Dock Hover Editor to workspace").onClick(()=>{s.dockPopoverToWorkspace(this.leaf)})).setSection)==null||M.call(_,"open")}):(E.addItem(T=>{var _,M;(M=(_=T.setIcon("popup-open").setTitle("Open in Hover Editor").onClick(()=>{var H;let B=s.spawnPopover();((H=this.leaf)==null?void 0:H.getViewState)&&B.setViewState(this.leaf.getViewState())})).setSection)==null||M.call(_,"open")}),E.addItem(T=>{var _,M;(M=(_=T.setIcon("popup-open").setTitle("Convert to Hover Editor").onClick(()=>{s.convertLeafToPopover(this.leaf)})).setSection)==null||M.call(_,"open")})),d.call(this,E,...k)}}});this.register(v)}patchMarkdownPreviewView(){this.register(oe(V.MarkdownPreviewView.prototype,{onResize(s){return function(){this.renderer.onResize(),this.view.scroll!==null&&this.view.scroll!==this.getScroll()&&this.renderer.applyScrollDelayed(this.view.scroll)}}}))}patchMarkdownPreviewRenderer(){let s=this,u=oe(V.MarkdownPreviewRenderer,{registerDomEvents(h){return function(v,d,...E){return v==null||v.on("mouseover",".internal-embed.is-loaded",(k,P)=>{var T,_,M,B,H;P&&s.settings.hoverEmbeds&&app.workspace.trigger("hover-link",{event:k,source:P.matchParent(".markdown-source-view")?"editor":"preview",hoverParent:(T=d.hoverParent)!=null?T:d.info,targetEl:P,linktext:P.getAttribute("src"),sourcePath:((H=(B=(M=(_=d.info)!=null?_:d).getFile)==null?void 0:B.call(M))==null?void 0:H.path)||""})}),h.call(this,v,d,...E)}}});this.register(u)}patchWorkspace(){let s=!1,u=oe(V.Workspace.prototype,{changeLayout(h){return async function(v){s=!0;try{await h.call(this,v)}finally{s=!1}}},recordHistory(h){return function(v,d,...E){var P;if(!(!((P=this.app.plugins.plugins["pane-relief"])==null?void 0:P._loaded)&&pt(v)))return h.call(this,v,d,...E)}},iterateLeaves(h){return function(v,d){if(h.call(this,v,d))return!0;let E=typeof v=="function"?v:d,k=typeof v=="function"?d:v;if(!k||s)return!1;if(k===app.workspace.rootSplit||V.WorkspaceContainer&&k instanceof V.WorkspaceContainer){for(let P of U.popoversForWindow(k.win))if(h.call(this,E,P.rootSplit))return!0}return!1}},getDropLocation(h){return function(d){for(let E of U.activePopovers()){let k=this.recursiveGetTarget(d,E.rootSplit);if(k)return V.requireApiVersion&&(0,V.requireApiVersion)("0.15.3")?k:{target:k,sidedock:!1}}return h.call(this,d)}},onDragLeaf(h){return function(v,d){let E=U.forLeaf(d);return E==null||E.togglePin(!0),h.call(this,v,d)}}});this.register(u)}patchSlidingPanes(){var u;let s=(u=this.app.plugins.plugins["sliding-panes-obsidian"])==null?void 0:u.constructor;if(s){let h=oe(s.prototype,{handleFileOpen(v){return function(...d){if(!pt(this.app.workspace.activeLeaf))return v.call(this,...d)}},handleLayoutChange(v){return function(...d){if(!pt(this.app.workspace.activeLeaf))return v.call(this,...d)}},focusActiveLeaf(v){return function(...d){if(!pt(this.app.workspace.activeLeaf))return v.call(this,...d)}}});this.register(h)}}patchLinkHover(){let s=this,u=this.app.internalPlugins.plugins["page-preview"];if(!u.enabled)return;let h=oe(u.instance.constructor.prototype,{onHoverLink(v){return function(d,...E){return d&&pe(d.event,MouseEvent)&&dt(d.event),v.call(this,d,...E)}},onLinkHover(v){return function(d,E,k,P,T,..._){Br(s,d,E,k,P,T,..._)}}});this.register(h),u.disable(),u.enable(),s.register(function(){!u.enabled||(u.disable(),u.enable())})}registerContextMenuHandler(){this.registerEvent(this.app.workspace.on("file-menu",(s,u,h,v)=>{let d=v?U.forLeaf(v):void 0;u instanceof V.TFile&&!d&&!v&&s.addItem(E=>{var k,P;(P=(k=E.setIcon("popup-open").setTitle("Open in Hover Editor").onClick(()=>{this.spawnPopover().openFile(u)})).setSection)==null||P.call(k,"open")})}))}registerActivePopoverHandler(){this.registerEvent(this.app.workspace.on("active-leaf-change",s=>{var h,v,d,E,k;(h=U.activePopover)==null||h.hoverEl.removeClass("is-active");let u=U.activePopover=s?U.forLeaf(s):void 0;if(u&&s){u.hoverEl.addClass("is-active");let P=u.hoverEl.querySelector(".popover-title");if(!P)return;P.textContent=(v=s.view)==null?void 0:v.getDisplayText(),((d=s==null?void 0:s.view)==null?void 0:d.getViewType())&&u.hoverEl.setAttribute("data-active-view-type",s.view.getViewType()),((k=(E=s.view)==null?void 0:E.file)==null?void 0:k.path)?P.setAttribute("data-path",s.view.file.path):P.removeAttribute("data-path")}}))}registerFileRenameHandler(){this.app.vault.on("rename",(s,u)=>{U.iteratePopoverLeaves(this.app.workspace,h=>{var v,d;if(s===((v=h==null?void 0:h.view)==null?void 0:v.file)&&s instanceof V.TFile){let E=U.forLeaf(h);if(E==null?void 0:E.hoverEl){let k=E.hoverEl.querySelector(".popover-title");if(!k)return;let P=k.getAttribute("data-path");u===P&&(k.textContent=(d=h.view)==null?void 0:d.getDisplayText(),k.setAttribute("data-path",s.path))}}})})}patchUnresolvedGraphNodeHover(){var d,E;let s=new V.WorkspaceLeaf(this.app),u=this.app.internalPlugins.plugins.graph.views.localgraph(s),h=u.engine.constructor;s.detach(),(E=(d=u.renderer)==null?void 0:d.worker)==null||E.terminate();let v=oe(h.prototype,{onNodeHover(k){return function(P,T,_,...M){if(_==="unresolved"){if(this.onNodeUnhover(),pe(P,MouseEvent)){if(this.hoverPopover&&this.hoverPopover.state!==V.PopoverState.Hidden&&this.lastHoverLink===T)return this.hoverPopover.onTarget=!0,void this.hoverPopover.transition();this.lastHoverLink=T,this.app.workspace.trigger("hover-link",{event:P,source:"graph",hoverParent:this,targetEl:null,linktext:T})}}else return k.call(this,P,T,_,...M)}}});this.register(v),s.detach()}onunload(){U.activePopovers().forEach(s=>s.hide())}async loadSettings(){this.settings=Object.assign({},nn,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}registerCommands(){this.addCommand({id:"bounce-popovers",name:"Toggle bouncing popovers",callback:()=>{this.activePopovers.forEach(s=>{s.toggleBounce()})}}),this.addCommand({id:"open-new-popover",name:"Open new Hover Editor",callback:()=>{let s=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(s,!1,!0))}}),this.addCommand({id:"open-link-in-new-popover",name:"Open link under cursor in new Hover Editor",checkCallback:s=>{let u=this.app.workspace.getActiveViewOfType(V.MarkdownView);if(u){if(!s){let h=u.editor.getClickableTokenAt(u.editor.getCursor());if((h==null?void 0:h.type)==="internal-link"){let v=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(v,!1,!0));v.openLinkText(h.text,u.file.path)}}return!0}return!1}}),this.addCommand({id:"open-current-file-in-new-popover",name:"Open current file in new Hover Editor",checkCallback:s=>{let u=this.app.workspace.getActiveViewOfType(V.MarkdownView);if(u){if(!s){let h=this.spawnPopover(void 0,()=>this.app.workspace.setActiveLeaf(h,!1,!0));h.openFile(u.file)}return!0}return!1}}),this.addCommand({id:"convert-active-pane-to-popover",name:"Convert active pane to Hover Editor",checkCallback:s=>{let{activeLeaf:u}=this.app.workspace;return u?(s||this.convertLeafToPopover(u),!0):!1}}),this.addCommand({id:"dock-active-popover-to-workspace",name:"Dock active Hover Editor to workspace",checkCallback:s=>{let{activeLeaf:u}=this.app.workspace;return u&&U.forLeaf(u)?(s||this.dockPopoverToWorkspace(u),!0):!1}}),this.addCommand({id:"restore-active-popover",name:"Restore active Hover Editor",checkCallback:s=>Dr(s)}),this.addCommand({id:"minimize-active-popover",name:"Minimize active Hover Editor",checkCallback:s=>jr(s)}),Ir.forEach(s=>{this.addCommand({id:`snap-active-popover-to-${s}`,name:`Snap active Hover Editor to ${s}`,checkCallback:u=>Ar(s,u)})})}convertLeafToPopover(s){if(!s)return;let u=this.spawnPopover(void 0,()=>{let{parentSplit:h}=u,{parentSplit:v}=s;v.removeChild(s),h.replaceChild(0,s,!0),this.app.workspace.setActiveLeaf(s,!1,!0)});return u}dockPopoverToWorkspace(s){if(!s)return;s.parentSplit.removeChild(s);let{rootSplit:u}=this.app.workspace;return(0,V.requireApiVersion)("0.16.3")&&u.children[0]instanceof V.WorkspaceTabs?u.children[0].insertChild(-1,s):u.insertChild(-1,s),app.workspace.activeLeaf=null,app.workspace.setActiveLeaf(s,!1,!0),s}spawnPopover(s,u){let h=this.app.workspace.activeLeaf;s||(s=h.containerEl);let v=new U(h,s,this,void 0,u);return v.togglePin(!0),v.attachLeaf()}registerSettingsTab(){this.settingsTab=new Gn(this.app,this),this.addSettingTab(this.settingsTab)}};function qn(g){let s=[];for(let u=0;u<g;u++)s.push((16*Math.random()|0).toString(16));return s.join("")}
