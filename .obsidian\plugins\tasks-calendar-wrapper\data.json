{"filterEmpty": true, "excludePaths": [], "includePaths": [], "useIncludeTags": false, "taskIncludeTags": [], "fileIncludeTags": [], "useExcludeTags": false, "taskExcludeTags": [], "fileExcludeTags": [], "styles": ["style1"], "dailyNoteFolder": "", "dailyNoteFormat": "YYYY, MMMM DD - dddd", "sectionForNewTasks": "## Tasks", "hideTags": [], "forward": false, "sort": "(t1, t2) => t1.order <= t2.order ? -1 : 1", "taskStatusOrder": ["overdue", "due", "scheduled", "start", "process", "unplanned", "done", "cancelled"], "dateFormat": "M月D日", "inbox": "Inbox.md", "taskFiles": [], "tagColorPalette": {"#TODO": "#339988", "#TEST": "#998877"}, "useCounters": false, "counterBehavior": "Filter", "useQuickEntry": false, "entryPosition": "top", "useYearHeader": false, "useRelative": true, "useRecurrence": true, "usePriority": true, "useTags": true, "useFileBadge": true, "useSection": true, "hideStatusTasks": ["x", "-", ">", "!", "?", "+"], "defaultTodayFocus": false, "defaultFilters": "to<PERSON><PERSON><PERSON><PERSON>", "useBuiltinStyle": false}