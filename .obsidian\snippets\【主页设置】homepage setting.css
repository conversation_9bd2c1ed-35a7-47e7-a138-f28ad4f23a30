/* 隐藏主页Properties但保留别的地方的properties */

/* 
Show Properties in reading mode by default.
Use "myhome" in `css_classes` property to hide them.
source: https://forum.obsidian.md/t/hide-show-properties-and-add-property-button-in-reading-mode/66209
============================================================= */
.workspace-leaf-content[data-mode="preview"]
.markdown-preview-view.myhome
.metadata-container{
    display: none;
}

/* 也处理掉rightlane的properties */
.workspace-leaf-content[data-mode="preview"]
.markdown-preview-view.rightlane
.metadata-container{
    display: none;
}

/* 也处理掉noyaml的properties */
.workspace-leaf-content[data-mode="preview"]
.markdown-preview-view.noyaml
.metadata-container{
    display: none;
}

/* 处理 ob1.0 鸟库顶部导航多出来的点 */
.myhome .list-bullet{
  display: none !important;
}




/* banner icon as date*/

.myhome .obsidian-banner-icon .icon-box img
{
content: url("https://api.wolai.com/v1/icon?type=1&locale=en&pro=0&color=grey");
}

/* end */

.markdown-rendered.show-indentation-guide li > ul::before, .markdown-rendered.show-indentation-guide li > ol::before {
  content: unset;
}




/* 优化 */

.admonition-flex .admonition-content div {
  flex: 1;
  margin: -0.6em 0em 0.1em 0.2em;
}



progress {
  width: 80px !important;
  height: 7px;
  border: 1px solid #000;  
  background-color: transparent;
  color: var(--text-accent); /*IE10*/
} 


/* progress::-moz-progress-bar { background: #a77575; }
progress::-webkit-progress-bar { background: #947d7d; }
progress::-webkit-progress-value  { background: #947d7d; } */


/* minimal 优化 */

.myhome .internal-embed .markdown-embed {
  border: 0px solid var(--background-modifier-border);
}

/* react components 设置 */ 

div.musicBirthday{
  display: flex;
  }

.myhome .external-link {
  background-image: none;
  padding-right: 0;

}


/* 不知道设置了啥 */

.myhome .admonition-col2 ul>li:not(.task-list-item){
  display: inline-table;
}

.myhome .admonition-col2 ul>li:not(.task-list-item) {
  flex: 1 1 90%;
}
.myhome .admonition-col2 ul > li.task-list-item {
  flex: 1 1 90%;
}

.myhome .admonition-flex ul > li.task-list-item {
  flex: 1 1 99%;
}


/* dataview 设置 */
.myhome ul ul li:nth-child(even) {
  text-indent: 0em;
}

.myhome ul ul li:not(.task-list-item) {
  text-indent: -1em;
}

button:hover {
  background-color: var(--interactive-accent);
}

button {
  background-color: #acacac80;
}

/* dataview 背景设置 */

/* .markdown-preview-view td:hover,.markdown-preview-view th:hover {
  background-color: var(--interactive-accent);
} */

/* .markdown-preview-view th, .markdown-preview-view td {
  background-color: var(--background-primary);
} */

/* 鼠标悬浮banner优先级设置 */

.obsidian-banner-wrapper{
  z-index: 0;
}

/* profile picture 设置 */

.homepage-profile-picture {
  position: absolute;
  top: 25%;
  right:10%;
  border:4px solid #000;
  border-radius: 0pt;
  outline: solid 2px black;
  border-style: double;
  opacity: 1;

}

/* 按钮设置 Button Settings */

/*双链预览面板*/
/* .popover.hover-popover {
  display: none;
  } */

/*提示条
.myhome .tooltip, .tooltip.mod-right {
  display: none;
  }
*/
.markdown-preview-view:not(.kanban) ul>li:not(.task-list-item) {
  z-index: 9;
}

a, .markdown-preview-view .internal-link {
  z-index: 10;
}

.markdown-preview-view a:hover {
  color: #ffffff;
  background: var(--interactive-accent);
  transition: all 200ms ease-in-out;
}

.markdown-preview-view:not(.kanban) ul>li:not(.task-list-item) {
  transition: all 150ms ease-in-out;
}

/* .external-link {
  text-decoration: none;
  background-image: none;
} */

.myhome a {
  color: var(--text-normal);
}

/*
.tooltip {
  display: none
}
*/


/* cssclass: myhome 看板设置*/
/* adapted from spectrum theme, thanks to @Braweria. https://github.com/Braweria/Spectrum */
/* a kind of css-based pseudo-myhome feature */

/* 手机端 

.is-mobile .myhome ul{ display: flex; }
.is-mobile .myhome ul>li:not(.task-list-item) {
  flex: 1 1 45%;
}

*/



/* pc端 */

.myhome ul {
  display: flex;
  flex-direction: row;
  border: 0px solid var(--p-myhome-border-color);
  padding: 0rem;
  border-radius: var(--radius-m);
  flex-wrap: wrap;
}



.myhome ul>li:not(.task-list-item) {
  flex: 1 1 20%;
  padding: 0.1em 0em 0.1em 1em;
  margin: 0 0.1em 0.3em 0  !important;
  list-style: none;
  border: 1px solid var(--p-myhome-border-color);
  border-left: px solid var(--p-myhome-border-color) !important;

  border-radius: 5pt;
  word-wrap: break-word;
  word-break: break-word;
  justify-content: center;
  align-items: center;
}

.myhome ul ul li:not(.task-list-item) {
  display: flex;
  width: 100%;
  display: block;
  background-color: #f7b2b280 !important ;
  justify-content: center;
  align-items: center;
  text-align: center;
}




.myhome ul .list-collapse-indicator.collapse-indicator.collapse-icon {
  margin-left: -1.2em;
  position: absolute;
}

.myhome ul .list-collapse-indicator.collapse-indicator.collapse-icon::before {
  color: var(--text-accent);
  transition: transform 10ms linear 0s
}


/* 上头ul li的padding我调整了 */


/* 

.myhome ul>li:nth-of-type(8n+1) {
  background-color: var(--p-myhome-color-1);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+2) {
  background-color: var(--p-myhome-color-2);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+3) {
  background-color: var(--p-myhome-color-3);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+4) {
  background-color: var(--p-myhome-color-4);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+5) {
  background-color: var(--p-myhome-color-5);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+6) {
  background-color: var(--p-myhome-color-6);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n+7) {
  background-color: var(--p-myhome-color-7);
  padding-left: 1em;
}

.myhome ul>li:nth-of-type(8n) {
  background-color: var(--p-myhome-color-8);
  padding-left: 1em;
} */

.myhome ul ul {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0rem;
  margin-top: 0.5rem;
  border-radius: 0;
  border: 0;
  background: none;
}



.myhome ul ul li:nth-child(even) {
  background-color: #fffbed !important;
}

.myhome ul>li:hover {
  border-color: var(--p-myhome-border-color) !important;
}

.myhome ul ul li:hover {
  background: var(--interactive-accent) !important;
  border-color: var(--p-myhome-border-color) !important;
  border-left: 1px solid var(--p-myhome-border-color) !important;
}

.theme-dark .myhome ul ul li:hover {
  border-color: #696969e1 !important;
  border-left: 1px solid #696969e1 !important;
}

.myhome ul ul li .task-list-item-checkbox {
  margin-left: 1.5rem;
}

.myhome ul ul ul {
  margin-right: 0;
}

.myhome ul ul::before {
  border-left: 0;
}

.markdown-preview-view.is-readable-line-width.myhome .markdown-preview-sizer>div>ul {
  max-width: 100%;
}

.myhome ul.contains-task-list ul,
.myhome ol.contains-task-list ul,
.myhome ul.contains-task-list ol,
.myhome ol.contains-task-list ol {
  position: initial;
}

.myhome ul.contains-task-list ul::before,
.myhome ol.contains-task-list ul::before,
.myhome ul.contains-task-list ol::before,
.myhome ol.contains-task-list ol::before {
  all: initial
}

.myhome ul > li.task-list-item {
  flex: 1 1 20%;
  padding: 0.1em 1em;
  margin: 0 0.1em 0.3em 0;
  list-style: none;
  border: 1px solid var(--p-myhome-border-color);
  border-left: 1px solid var(--p-myhome-border-color) !important;
  border-radius: var(--radius-s);
  word-wrap: break-word;
  word-break: break-word;
  text-indent: 0;
}

.myhome ul ul li.task-list-item {
  width: 100%;
  display: block;
  padding-left: 2em;
  text-indent: -3.1em;
  background-color: var(--p-myhome-card-color-1) !important;
}

.myhome .task-list-item-checkbox {
  border-color: var(--text-normal) !important;
}



/*
.myhome ul>li:nth-of-type(2)[data-line="1"],
.myhome ul>li:nth-of-type(4)[data-line="3"],
.myhome ul>li:nth-of-type(5)[data-line="4"],
.myhome ul>li:nth-of-type(7)[data-line="6"],
.myhome ul>li:nth-of-type(10)[data-line="9"],
.myhome ul>li:nth-of-type(12)[data-line="11"],
.myhome ul>li:nth-of-type(13)[data-line="12"],
.myhome ul>li:nth-of-type(15)[data-line="14"] {
  background-color: var(--p-myhome-card-color-1);
}

.myhome ul>li:nth-of-type(3)[data-line="2"],
.myhome ul>li:nth-of-type(6)[data-line="5"],s
.myhome ul>li:nth-of-type(8)[data-line="7"],
.myhome ul>li:nth-of-type(9)[data-line="8"],
.myhome ul>li:nth-of-type(11)[data-line="10"],
.myhome ul>li:nth-of-type(14)[data-line="13"],
.myhome ul>li:nth-of-type(16)[data-line="15"] {
  background-color: var(--p-myhome-color-1);
}
*/

/*=========================*/
/*==========tags===========*/

/*blank ad分栏
修改自 https://forum-zh.obsidian.md/t/topic/2081
*/
.admonition-col2 {
  box-shadow: none!important;
}
  
.admonition-col2 .admonition	{
  overflow: visible ;
  margin:0;
}

.admonition-col2>.admonition-content-holder>.admonition-content {	
  columns:150px 2;
  -moz-columns:150px 2;
  -webkit-columns:150px 2;
  column-gap: 2vw;
  overflow: visible ;
  margin:0;
  /*分栏的分割线
  column-rule: 1px solid #d4d2d2; */
}
.admonition-col2>.admonition-content-holder>.admonition-content ol {	
  margin-top: -0.25em;
}
.admonition-col2>.admonition-content-holder>.admonition-content ul {	
  margin-top: -0.25em;
}
.admonition-col2>.admonition-content-holder>.admonition-content h3 {	
  margin-top: -0.001em;
}
.admonition-col2>.admonition-content-holder>.admonition-content h4 {	
  margin-top: -0.001em;
}
.admonition-kanban .admonition-col2>.admonition-content-holder>.admonition-content ol{	
  margin:0;
  break-inside: avoid;
}

.admonition-kanban .admonition-col2>.admonition-content-holder>.admonition-content ul {	
  margin:0;
  break-inside: avoid;
}
.admonition-col2 .admonition-content {	
  overflow: visible ;
}

.admonition-col2 >.admonition-content-holder {
  margin-top:0!important;
  margin-bottom:0!important;
}
.admonition-col2 p{
  text-align: justify;
  margin-top:0;
  padding: 2px;
 /*  height:100%;
  overflow: auto; */
}
.admonition-col2 p>img{
  display: block;
  width: 100%;
  height: auto;
}

.admonition-col2-parent .admonition-col2 .admonition > .admonition-title.no-title + .admonition-content-holder {
  margin-top: 0;
}
/****col3 三栏*****/

.admonition-col3 {
  box-shadow: none!important;
}
  
.admonition-col3 .admonition	{
  overflow: visible ;
  margin:0;
}

.admonition-col3>.admonition-content-holder>.admonition-content {	
  columns:100px 3;
  -moz-columns:100px 3;
  -webkit-columns:100px 3;
  column-gap: 1vw;
  overflow: visible ;
  margin:0;
  break-inside: avoid;
  column-rule: 1px solid #d4d2d2; 
}
.admonition-col3>.admonition-content-holder>.admonition-content ol {	
  margin-top: -0.25em;
}
.admonition-col3>.admonition-content-holder>.admonition-content ul {	
  margin-top: -0.25em;
}
.admonition-kanban .admonition-col3>.admonition-content-holder>.admonition-content ul {	
  margin:0;
  break-inside: avoid;
}
.admonition-kanban .admonition-col3>.admonition-content-holder>.admonition-content ol {	
  margin:0;
  break-inside: avoid;
}
.admonition-col3 .admonition-content {	
  overflow: visible ;
}

.admonition-col3 >.admonition-content-holder {
  margin-top:0!important;
  margin-bottom:0!important;
}
.admonition-col3 p{
  text-align: justify;
  margin-top:0;
  padding: 2px;
 /*  height:100%;
  overflow: auto; */
}
.admonition-col3 p>img{
  display: block;
  width: 100%;
  height: auto;
}

.admonition-col3-parent .admonition-col3 .admonition > .admonition-title.no-title + .admonition-content-holder {
  margin-top: 0;
}
/****col4 四栏*****/

.admonition-col4 {
  box-shadow: none!important;
}
  
.admonition-col4 .admonition	{
  overflow: visible ;
  margin:0;
}

.admonition-col4>.admonition-content-holder>.admonition-content {	
  column-count: 4;
  column-gap: 1vw;
  overflow: visible ;
  margin:0;
  break-inside: avoid;
}
.admonition-col4>.admonition-content-holder>.admonition-content ol {	
  margin-top: -0.25em;
}
.admonition-col4>.admonition-content-holder>.admonition-content ul {	
  margin-top: -0.25em;
}
.admonition-kanban .admonition-col4>.admonition-content-holder>.admonition-content ul {	
  margin:0;
  break-inside: avoid;
}
.admonition-kanban .admonition-col4>.admonition-content-holder>.admonition-content ol {	
  margin:0;
  break-inside: avoid;
}
.admonition-col4 .admonition-content {	
  overflow: visible ;
}

.admonition-col4 >.admonition-content-holder {
  margin-top:0!important;
  margin-bottom:0!important;
}
.admonition-col4 p{
  text-align: justify;
  margin-top:0;
  padding: 2px;
}
.admonition-col4 p>img{
  display: block;
  width: 100%;
  height: auto;
  -webkit-transition: -webkit-transform .3s ease;
  -moz-transition: -moz-transform .3s ease;
  -ms-transition: -ms-transform .3s ease;
  transition: transform .3s ease;
}

.admonition-col4 p>img:hover{
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2)
}
   
.admonition-col4-parent .admonition-col4 .admonition > .admonition-title.no-title + .admonition-content-holder {
  margin-top: 0;
}

/* flex */

/*自适应分栏*/
.admonition-flex {
  margin: auto;
 box-shadow:none;
   
 }
 .admonition-flex >.admonition-content-holder {
  margin-top:0!important;
  margin-bottom:0!important;
}
.admonition-flex .admonition-content {
 width: 100%;
 margin: 0px;
 display: flex;
 flex-wrap: wrap;
}
.admonition-flex .admonition-content div{
 flex: 1;
 margin: 0 0.625rem;
}


/*blank ad框全透明隐藏*/
.admonition-blank {
  overflow: visible;
  background-color: rgba(255, 255, 255, 0)!important;
  border-left:0px!important;
  margin: auto;
  box-shadow:none;
}
.admonition-blank .admonition-content {
  overflow: visible;
  margin: 0;
}

/* ============ */
/* ============ */
/*image position*/
/* ============ */
/* ============ */
img[alt$="left"],
img[alt$="Left"],
img[alt$="LEFT"],
img[alt$="L"] {
  margin-left: 0;
}

img[alt$="right"],
img[alt$="Right"],
img[alt$="RIGHT"],
img[alt$="R"] {
  margin-right: 0;
}

img[alt$="inline"],
img[alt$="Inline"],
img[alt$="INLINE"],
img[alt$="inl"] {
  display: inline !important;
  padding: 0.2em 0.5em !important;
  vertical-align: bottom;
}

img[alt$="inlineL"],
img[alt$="InlineL"],
img[alt$="INLINEL"],
img[alt$="inlL"] {
  float: left;
  padding: 0 1em 0.5em 0 !important;
}

img[alt$="inlineR"],
img[alt$="InlineR"],
img[alt$="INLINER"],
img[alt$="inlR"] {
  float: right;
  padding: 0 0 0.5em 1em !important;
}


/* img captions */
/* modified from Discordian theme by @radekkozak */
.markdown-preview-view .image-embed[src$="center"],
.markdown-preview-view .image-embed[src$="centre"] {
  clear: both;
  margin: 0.5rem auto;
  width: fit-content;
  display: block;
}

.markdown-preview-view .image-embed[src$="center"]::after,
.markdown-preview-view .image-embed[src$="centre"]::after {
  content: attr(alt);
  /* padding: 0 1rem 0 1rem; */
  margin-top: 0.5rem;
  display: block;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}

.markdown-preview-view .image-embed[src$="left"] {
  position: relative;
  float: left;
  margin-top: 0.5rem;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  clear: both;
  display: table;
  width: fit-content;
}

.markdown-preview-view .image-embed[src$="left"]::after {
  content: attr(alt);
  padding: 0 1rem 0 1rem;
  margin-top: 0.5rem;
  display: table-caption;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}

.markdown-preview-view .image-embed[src$="right"] {
  position: relative;
  float: right;
  margin-top: 0.5rem;
  margin-left: 1rem;
  margin-bottom: 0.5rem;
  clear: both;
  display: table;
  width: fit-content;
}

.markdown-preview-view .image-embed[src$="right"]::after {
  content: attr(alt);
  padding: 0 1rem 0 1rem;
  margin-top: 0.5rem;
  display: table-caption;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}
/* img captions for  cm6 live preview */
.markdown-source-view.mod-cm6.is-live-preview .image-embed[src$="center"],
.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="centre"] {
  clear: both;
  margin: 0.5rem auto!important;
  width: fit-content;
  display: block;
}

.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="center"]::after,
.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="centre"]::after {
  content: attr(alt);
  /* padding: 0 1rem 0 1rem; */
  margin-top: 0.5rem;
  display: block;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}

.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="left"] {
  position: relative;
  float: left;
  margin-top: 0.5rem;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
  clear: both;
  display: table;
  width: fit-content;
}

.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="left"]::after {
  content: attr(alt);
  padding: 0 1rem 0 1rem;
  margin-top: 0.5rem;
  display: table-caption;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}

.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="right"] {
  position: relative;
  float: right;
  margin-top: 0.5rem;
  margin-left: 1rem;
  margin-bottom: 0.5rem;
  clear: both;
  display: table;
  width: fit-content;
}

.markdown-source-view.mod-cm6.is-live-preview  .image-embed[src$="right"]::after {
  content: attr(alt);
  padding: 0 1rem 0 1rem;
  margin-top: 0.5rem;
  display: table-caption;
  caption-side: bottom;
  text-align: center;
  font-size: 0.85rem;
}