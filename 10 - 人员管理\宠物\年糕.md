---
category: 英短
name: 年糕
aliases:
  - niangao
gender: 女
birthday: 2020-11-01
tags:
  - cat
photo:
---
姓名：`=(this.name)`
年龄：`$= let date = moment(dv.current().birthday.toString(),"yyyy-MM-DD"); let today = moment().startOf('day'); today.diff(date,"days")`天
性别：`=(this.gender)`
种类：`=(this.category)`


%% 
``` tracker
searchType: text  
searchTarget: '\[\[年糕\]\][\n\r\s]+(?<value>[\-]?[0-9]+[\.][0-9]+|[\-]?[0-9]+)'
folder: 00 - 每日日记/WeeklyNote
dateFormat: 'YYYY年WW周记录'
line:
    title: "年糕体重"
    xAxisLabel: 时间
    yAxisLabel: 体重
    fillGap: true
    lineColor: gray
``` 
%%

```dataviewjs

let folderChoicePath = "00 - 每日日记/01-DailyNote"
const files = app.vault.getMarkdownFiles().filter(file => file.path.includes(folderChoicePath))



let arr = files.map(async(file) => { 
    const content = await app.vault.cachedRead(file) 
    let lines = await content.split("\n").filter(line => line.includes("年糕") && !line.includes("年糕")) 
    //console.log(lines) 
    return ["[["+file.name.split(".")[0]+"]]", lines] 
}) 

Promise.all(arr).then(values => { 
    const beautify = values.map(value => { 
        const temp = value[1].map(line => { return line }) //美化要重写
        return [value[0],temp] 
    }) 
    const exists = beautify.filter(value => value[1][0] && value[0] != "[[未命名 10]]") .sort(value => value[0],'desc') 
    dv.table(["日期", "宠物记录"], exists) 
})
```