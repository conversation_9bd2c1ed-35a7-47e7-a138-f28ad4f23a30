'use strict';

var obsidian = require('obsidian');

const defaultMaxLength = 50;
const DEFAULT_DATA = {
    recentFiles: [],
    omittedPaths: [],
    maxLength: null,
    openType: 'tab',
};
const RecentFilesListViewType = 'recent-files';
class RecentFilesListView extends obsidian.ItemView {
    constructor(leaf, plugin, data) {
        super(leaf);
        this.redraw = () => {
            const openFile = this.app.workspace.getActiveFile();
            const rootEl = createDiv({ cls: 'nav-folder mod-root' });
            const childrenEl = rootEl.createDiv({ cls: 'nav-folder-children' });
            this.data.recentFiles.forEach((currentFile) => {
                const navFile = childrenEl.createDiv({ cls: 'tree-item nav-file recent-files-file' });
                const navFileTitle = navFile.createDiv({ cls: 'tree-item-self is-clickable nav-file-title recent-files-title' });
                const navFileTitleContent = navFileTitle.createDiv({ cls: 'tree-item-inner nav-file-title-content recent-files-title-content' });
                navFileTitleContent.setText(currentFile.basename);
                if (openFile && currentFile.path === openFile.path) {
                    navFileTitle.addClass('is-active');
                }
                navFileTitle.setAttr('draggable', 'true');
                navFileTitle.addEventListener('dragstart', (event) => {
                    const file = this.app.metadataCache.getFirstLinkpathDest(currentFile.path, '');
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const dragManager = this.app.dragManager;
                    const dragData = dragManager.dragFile(event, file);
                    dragManager.onDragStart(event, dragData);
                });
                navFileTitle.addEventListener('mouseover', (event) => {
                    this.app.workspace.trigger('hover-link', {
                        event,
                        source: RecentFilesListViewType,
                        hoverParent: rootEl,
                        targetEl: navFile,
                        linktext: currentFile.path,
                    });
                });
                navFileTitle.addEventListener('contextmenu', (event) => {
                    const menu = new obsidian.Menu(this.app);
                    const file = this.app.vault.getAbstractFileByPath(currentFile.path);
                    this.app.workspace.trigger('file-menu', menu, file, 'link-context-menu');
                    menu.showAtPosition({ x: event.clientX, y: event.clientY });
                });
                navFileTitleContent.addEventListener('click', (event) => {
                    this.focusFile(currentFile, event.ctrlKey || event.metaKey);
                });
                const navFileDelete = navFileTitle.createDiv({ cls: 'recent-files-file-delete' });
                navFileDelete.appendChild(obsidian.getIcon("lucide-x"));
                navFileDelete.addEventListener('click', async () => {
                    await this.removeFile(currentFile);
                    this.redraw();
                });
            });
            const contentEl = this.containerEl.children[1];
            contentEl.empty();
            contentEl.appendChild(rootEl);
        };
        this.removeFile = async (file) => {
            this.data.recentFiles = this.data.recentFiles.filter((currFile) => currFile.path !== file.path);
            await this.plugin.pruneLength(); // Handles the save
        };
        this.updateData = async (file) => {
            this.data.recentFiles = this.data.recentFiles.filter((currFile) => currFile.path !== file.path);
            this.data.recentFiles.unshift({
                basename: file.basename,
                path: file.path,
            });
            await this.plugin.pruneLength(); // Handles the save
        };
        this.update = async (openedFile) => {
            if (!openedFile || !this.plugin.shouldAddFile(openedFile)) {
                return;
            }
            await this.updateData(openedFile);
            this.redraw();
        };
        /**
         * Open the provided file in the most recent leaf.
         *
         * @param shouldSplit Whether the file should be opened in a new split, or in
         * the most recent split. If the most recent split is pinned, this is set to
         * true.
         */
        this.focusFile = (file, shouldSplit = false) => {
            const targetFile = this.app.vault
                .getFiles()
                .find((f) => f.path === file.path);
            if (targetFile) {
                let leaf = this.app.workspace.getMostRecentLeaf();
                const createLeaf = shouldSplit || leaf.getViewState().pinned;
                if (createLeaf) {
                    if (this.plugin.data.openType == 'split')
                        leaf = this.app.workspace.getLeaf('split');
                    else if (this.plugin.data.openType == 'window')
                        leaf = this.app.workspace.getLeaf('window');
                    else
                        leaf = this.app.workspace.getLeaf('tab');
                }
                leaf.openFile(targetFile);
            }
            else {
                new obsidian.Notice('Cannot find a file with that name');
                this.data.recentFiles = this.data.recentFiles.filter((fp) => fp.path !== file.path);
                this.plugin.saveData();
                this.redraw();
            }
        };
        this.plugin = plugin;
        this.data = data;
    }
    async onOpen() {
        this.redraw();
    }
    getViewType() {
        return RecentFilesListViewType;
    }
    getDisplayText() {
        return 'Recent Files';
    }
    getIcon() {
        return 'clock';
    }
    onHeaderMenu(menu) {
        menu
            .addItem((item) => {
            item
                .setTitle('Clear list')
                .setIcon('sweep')
                .onClick(async () => {
                this.data.recentFiles = [];
                await this.plugin.saveData();
                this.redraw();
            });
        })
            .addItem((item) => {
            item
                .setTitle('Close')
                .setIcon('cross')
                .onClick(() => {
                this.app.workspace.detachLeavesOfType(RecentFilesListViewType);
            });
        });
    }
    load() {
        super.load();
        this.registerEvent(this.app.workspace.on('file-open', this.update));
    }
}
class RecentFilesPlugin extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.pruneOmittedFiles = async () => {
            this.data.recentFiles = this.data.recentFiles.filter(this.shouldAddFile);
            await this.saveData();
        };
        this.pruneLength = async () => {
            const toRemove = this.data.recentFiles.length - (this.data.maxLength || defaultMaxLength);
            if (toRemove > 0) {
                this.data.recentFiles.splice(this.data.recentFiles.length - toRemove, toRemove);
            }
            await this.saveData();
        };
        this.shouldAddFile = (file) => {
            const patterns = this.data.omittedPaths.filter((path) => path.length > 0);
            const fileMatchesRegex = (pattern) => {
                try {
                    return new RegExp(pattern).test(file.path);
                }
                catch (err) {
                    console.error('Recent Files: Invalid regex pattern: ' + pattern);
                    return false;
                }
            };
            return !patterns.some(fileMatchesRegex);
        };
        this.initView = async () => {
            let leaf = null;
            for (leaf of this.app.workspace.getLeavesOfType(RecentFilesListViewType)) {
                if (leaf.view instanceof RecentFilesListView)
                    return;
                // The view instance was created by an older version of the plugin,
                // so clear it and recreate it (so it'll be the new version).
                // This avoids the need to reload Obsidian to update the plugin.
                await leaf.setViewState({ type: 'empty' });
                break;
            }
            (leaf !== null && leaf !== void 0 ? leaf : this.app.workspace.getLeftLeaf(false)).setViewState({
                type: RecentFilesListViewType,
                active: true,
            });
        };
        this.handleRename = async (file, oldPath) => {
            const entry = this.data.recentFiles.find((recentFile) => recentFile.path === oldPath);
            if (entry) {
                entry.path = file.path;
                entry.basename = this.trimExtension(file.name);
                this.view.redraw();
                await this.saveData();
            }
        };
        this.handleDelete = async (file) => {
            const beforeLen = this.data.recentFiles.length;
            this.data.recentFiles = this.data.recentFiles.filter((recentFile) => recentFile.path !== file.path);
            if (beforeLen !== this.data.recentFiles.length) {
                this.view.redraw();
                await this.saveData();
            }
        };
        // trimExtension can be used to turn a filename into a basename when
        // interacting with a TAbstractFile that does not have a basename property.
        // private readonly trimExtension = (name: string): string => name.split('.')[0];
        // from: https://stackoverflow.com/a/4250408/617864
        this.trimExtension = (name) => name.replace(/\.[^/.]+$/, '');
    }
    async onload() {
        console.log('Recent Files: Loading plugin v' + this.manifest.version);
        await this.loadData();
        obsidian.addIcon('sweep', sweepIcon);
        this.registerView(RecentFilesListViewType, (leaf) => (this.view = new RecentFilesListView(leaf, this, this.data)));
        this.addCommand({
            id: 'recent-files-open',
            name: 'Open',
            callback: async () => {
                let [leaf] = this.app.workspace.getLeavesOfType(RecentFilesListViewType);
                if (!leaf) {
                    leaf = this.app.workspace.getLeftLeaf(false);
                    await leaf.setViewState({ type: RecentFilesListViewType });
                }
                this.app.workspace.revealLeaf(leaf);
            }
        });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.app.workspace.registerHoverLinkSource(RecentFilesListViewType, {
            display: 'Recent Files',
            defaultMod: true,
        });
        if (this.app.workspace.layoutReady) {
            this.initView();
        }
        else {
            this.registerEvent(this.app.workspace.on('layout-ready', this.initView));
        }
        this.registerEvent(this.app.vault.on('rename', this.handleRename));
        this.registerEvent(this.app.vault.on('delete', this.handleDelete));
        this.addSettingTab(new RecentFilesSettingTab(this.app, this));
    }
    onunload() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.app.workspace.unregisterHoverLinkSource(RecentFilesListViewType);
    }
    async loadData() {
        this.data = Object.assign(DEFAULT_DATA, await super.loadData());
        if (!this.data.maxLength) {
            console.log('Recent Files: maxLength is not set, using default (' +
                defaultMaxLength.toString() +
                ')');
        }
    }
    async saveData() {
        await super.saveData(this.data);
    }
}
class RecentFilesSettingTab extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    display() {
        const { containerEl } = this;
        containerEl.empty();
        containerEl.createEl('h2', { text: 'Recent Files List' });
        const fragment = document.createDocumentFragment();
        const link = document.createElement('a');
        link.href =
            'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#writing_a_regular_expression_pattern';
        link.text = 'MDN - Regular expressions';
        fragment.append('RegExp patterns to ignore. One pattern per line. See ');
        fragment.append(link);
        fragment.append(' for help.');
        new obsidian.Setting(containerEl)
            .setName('Omitted pathname patterns')
            .setDesc(fragment)
            .addTextArea((textArea) => {
            textArea.inputEl.setAttr('rows', 6);
            textArea
                .setPlaceholder('^daily/\n\\.png$\nfoobar.*baz')
                .setValue(this.plugin.data.omittedPaths.join('\n'));
            textArea.inputEl.onblur = (e) => {
                const patterns = e.target.value;
                this.plugin.data.omittedPaths = patterns.split('\n');
                this.plugin.pruneOmittedFiles();
                this.plugin.view.redraw();
            };
        });
        new obsidian.Setting(containerEl)
            .setName('List length')
            .setDesc('Maximum number of filenames to keep in the list.')
            .addText((text) => {
            var _a;
            text.inputEl.setAttr('type', 'number');
            text.inputEl.setAttr('placeholder', defaultMaxLength);
            text
                .setValue((_a = this.plugin.data.maxLength) === null || _a === void 0 ? void 0 : _a.toString())
                .onChange((value) => {
                const parsed = parseInt(value, 10);
                if (!Number.isNaN(parsed) && parsed <= 0) {
                    new obsidian.Notice('List length must be a positive integer');
                    return;
                }
            });
            text.inputEl.onblur = (e) => {
                const maxfiles = e.target.value;
                const parsed = parseInt(maxfiles, 10);
                this.plugin.data.maxLength = parsed;
                this.plugin.pruneLength();
                this.plugin.view.redraw();
            };
        });
        new obsidian.Setting(containerEl)
            .setName("Open note in")
            .setDesc("Open the clicked recent file record in a new tab, split, or window (only works on the desktop app).")
            .addDropdown((dropdown) => {
            const options = {
                "tab": "tab",
                "split": "split",
                "window": "window",
            };
            dropdown
                .addOptions(options)
                .setValue(this.plugin.data.openType)
                .onChange(async (value) => {
                this.plugin.data.openType = value;
                await this.plugin.saveData();
                this.display();
            });
        });
        const div = containerEl.createEl('div', {
            cls: 'recent-files-donation',
        });
        const donateText = document.createElement('p');
        donateText.appendText('If this plugin adds value for you and you would like to help support ' +
            'continued development, please use the buttons below:');
        div.appendChild(donateText);
        const parser = new DOMParser();
        div.appendChild(createDonateButton('https://paypal.me/tgrosinger', parser.parseFromString(paypal, 'text/xml').documentElement));
        div.appendChild(createDonateButton('https://www.buymeacoffee.com/tgrosinger', parser.parseFromString(buyMeACoffee, 'text/xml').documentElement));
    }
}
const createDonateButton = (link, img) => {
    const a = document.createElement('a');
    a.setAttribute('href', link);
    a.addClass('recent-files-donate-button');
    a.appendChild(img);
    return a;
};
const sweepIcon = `
<svg fill="currentColor" stroke="currentColor" version="1.1" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <path d="m495.72 1.582c-7.456-3.691-16.421-0.703-20.142 6.694l-136.92 274.08-26.818-13.433c-22.207-11.118-49.277-2.065-60.396 20.083l-6.713 13.405 160.96 80.616 6.713-13.411c11.087-22.143 2.227-49.18-20.083-60.381l-26.823-13.435 136.92-274.08c3.706-7.412 0.703-16.421-6.694-20.141z"/>
  <circle cx="173" cy="497" r="15"/>
  <circle cx="23" cy="407" r="15"/>
  <circle cx="83" cy="437" r="15"/>
  <path d="m113 482h-60c-8.276 0-15-6.724-15-15 0-8.291-6.709-15-15-15s-15 6.709-15 15c0 24.814 20.186 45 45 45h60c8.291 0 15-6.709 15-15s-6.709-15-15-15z"/>
  <path d="m108.64 388.07c-6.563 0.82-11.807 5.845-12.92 12.349-1.113 6.519 2.153 12.993 8.057 15.952l71.675 35.889c12.935 6.475 27.231 9.053 41.177 7.573-1.641 6.65 1.479 13.784 7.852 16.992l67.061 33.589c5.636 2.78 12.169 1.8 16.685-2.197 2.347-2.091 53.436-48.056 83.3-98.718l-161.6-80.94c-36.208 48.109-120.36 59.39-121.28 59.511z"/>
</svg>`;
const buyMeACoffee = `
<svg width="150" height="42" viewBox="0 0 260 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 11.68C0 5.22932 5.22931 0 11.68 0H248.2C254.651 0 259.88 5.22931 259.88 11.68V61.32C259.88 67.7707 254.651 73 248.2 73H11.68C5.22931 73 0 67.7707 0 61.32V11.68Z" fill="#FFDD00"/>
<path d="M52.2566 24.0078L52.2246 23.9889L52.1504 23.9663C52.1802 23.9915 52.2176 24.0061 52.2566 24.0078Z" fill="#0D0C22"/>
<path d="M52.7248 27.3457L52.6895 27.3556L52.7248 27.3457Z" fill="#0D0C22"/>
<path d="M52.2701 24.0024C52.266 24.0019 52.2619 24.0009 52.258 23.9995C52.2578 24.0022 52.2578 24.0049 52.258 24.0076C52.2624 24.007 52.2666 24.0052 52.2701 24.0024Z" fill="#0D0C22"/>
<path d="M52.2578 24.0094H52.2643V24.0054L52.2578 24.0094Z" fill="#0D0C22"/>
<path d="M52.6973 27.3394L52.7513 27.3086L52.7714 27.2973L52.7897 27.2778C52.7554 27.2926 52.7241 27.3135 52.6973 27.3394Z" fill="#0D0C22"/>
<path d="M52.3484 24.0812L52.2956 24.031L52.2598 24.0115C52.279 24.0454 52.3108 24.0705 52.3484 24.0812Z" fill="#0D0C22"/>
<path d="M39.0684 56.469C39.0262 56.4872 38.9893 56.5158 38.9609 56.552L38.9943 56.5306C39.0169 56.5098 39.0489 56.4853 39.0684 56.469Z" fill="#0D0C22"/>
<path d="M46.7802 54.9518C46.7802 54.9041 46.7569 54.9129 46.7626 55.0826C46.7626 55.0687 46.7683 55.0549 46.7708 55.0417C46.7739 55.0115 46.7764 54.982 46.7802 54.9518Z" fill="#0D0C22"/>
<path d="M45.9844 56.469C45.9422 56.4872 45.9053 56.5158 45.877 56.552L45.9103 56.5306C45.9329 56.5098 45.9649 56.4853 45.9844 56.469Z" fill="#0D0C22"/>
<path d="M33.6307 56.8301C33.5987 56.8023 33.5595 56.784 33.5176 56.7773C33.5515 56.7937 33.5855 56.81 33.6081 56.8226L33.6307 56.8301Z" fill="#0D0C22"/>
<path d="M32.4118 55.6598C32.4068 55.6103 32.3916 55.5624 32.3672 55.519C32.3845 55.5642 32.399 55.6104 32.4106 55.6573L32.4118 55.6598Z" fill="#0D0C22"/>
<path d="M40.623 34.7221C38.9449 35.4405 37.0404 36.2551 34.5722 36.2551C33.5397 36.2531 32.5122 36.1114 31.5176 35.834L33.2247 53.3605C33.2851 54.093 33.6188 54.7761 34.1595 55.2739C34.7003 55.7718 35.4085 56.0482 36.1435 56.048C36.1435 56.048 38.564 56.1737 39.3716 56.1737C40.2409 56.1737 42.8474 56.048 42.8474 56.048C43.5823 56.048 44.2904 55.7716 44.831 55.2737C45.3716 54.7759 45.7052 54.0929 45.7656 53.3605L47.594 33.993C46.7769 33.714 45.9523 33.5286 45.0227 33.5286C43.415 33.5279 42.1196 34.0817 40.623 34.7221Z" fill="white"/>
<path d="M26.2344 27.2449L26.2633 27.2719L26.2821 27.2832C26.2676 27.2688 26.2516 27.2559 26.2344 27.2449Z" fill="#0D0C22"/>
<path d="M55.4906 25.6274L55.2336 24.3307C55.0029 23.1673 54.4793 22.068 53.2851 21.6475C52.9024 21.513 52.468 21.4552 52.1745 21.1768C51.881 20.8983 51.7943 20.4659 51.7264 20.0649C51.6007 19.3289 51.4825 18.5923 51.3537 17.8575C51.2424 17.2259 51.1544 16.5163 50.8647 15.9368C50.4876 15.1586 49.705 14.7036 48.9269 14.4025C48.5282 14.2537 48.1213 14.1278 47.7082 14.0254C45.7642 13.5125 43.7202 13.324 41.7202 13.2165C39.3197 13.084 36.9128 13.1239 34.518 13.3359C32.7355 13.4981 30.8581 13.6942 29.1642 14.3108C28.5451 14.5364 27.9071 14.8073 27.4364 15.2856C26.8587 15.8733 26.6702 16.7821 27.0919 17.515C27.3917 18.0354 27.8996 18.4031 28.4382 18.6463C29.1398 18.9597 29.8726 19.1982 30.6242 19.3578C32.7172 19.8204 34.885 20.0021 37.0233 20.0794C39.3932 20.175 41.767 20.0975 44.1256 19.8474C44.7089 19.7833 45.2911 19.7064 45.8723 19.6168C46.5568 19.5118 46.9961 18.6168 46.7943 17.9933C46.553 17.2479 45.9044 16.9587 45.1709 17.0712C45.0628 17.0882 44.9553 17.1039 44.8472 17.1196L44.7692 17.131C44.5208 17.1624 44.2723 17.1917 44.0238 17.219C43.5105 17.2743 42.9959 17.3195 42.4801 17.3547C41.3249 17.4352 40.1665 17.4722 39.0088 17.4741C37.8712 17.4741 36.7329 17.4421 35.5978 17.3673C35.0799 17.3333 34.5632 17.2902 34.0478 17.2378C33.8134 17.2133 33.5796 17.1875 33.3458 17.1586L33.1233 17.1303L33.0749 17.1234L32.8442 17.0901C32.3728 17.0191 31.9014 16.9374 31.435 16.8387C31.388 16.8283 31.3459 16.8021 31.3157 16.7645C31.2856 16.7269 31.2691 16.6801 31.2691 16.6319C31.2691 16.5837 31.2856 16.5369 31.3157 16.4993C31.3459 16.4617 31.388 16.4356 31.435 16.4251H31.4438C31.848 16.339 32.2553 16.2655 32.6638 16.2014C32.8 16.18 32.9366 16.159 33.0736 16.1385H33.0774C33.3332 16.1215 33.5903 16.0757 33.8448 16.0455C36.0595 15.8151 38.2874 15.7366 40.5128 15.8104C41.5933 15.8419 42.6731 15.9053 43.7485 16.0147C43.9798 16.0386 44.2098 16.0637 44.4399 16.092C44.5279 16.1027 44.6165 16.1153 44.7051 16.1259L44.8836 16.1517C45.404 16.2292 45.9217 16.3233 46.4367 16.4339C47.1997 16.5999 48.1796 16.6539 48.519 17.4898C48.6271 17.7551 48.6761 18.0499 48.7359 18.3283L48.8119 18.6834C48.8139 18.6898 48.8154 18.6963 48.8163 18.7029C48.9961 19.5409 49.176 20.379 49.3562 21.217C49.3694 21.2789 49.3697 21.3429 49.3571 21.4049C49.3445 21.4669 49.3193 21.5257 49.2829 21.5776C49.2466 21.6294 49.2 21.6732 49.146 21.7062C49.092 21.7392 49.0317 21.7608 48.969 21.7695H48.964L48.854 21.7846L48.7453 21.799C48.4009 21.8439 48.056 21.8858 47.7107 21.9247C47.0307 22.0022 46.3496 22.0693 45.6674 22.1259C44.3119 22.2386 42.9536 22.3125 41.5927 22.3477C40.8992 22.3662 40.2059 22.3748 39.5129 22.3735C36.7543 22.3713 33.9981 22.211 31.2578 21.8933C30.9611 21.8581 30.6645 21.8204 30.3678 21.7821C30.5978 21.8116 30.2006 21.7594 30.1202 21.7481C29.9316 21.7217 29.7431 21.6943 29.5545 21.6658C28.9216 21.5709 28.2924 21.454 27.6607 21.3515C26.8971 21.2258 26.1667 21.2887 25.476 21.6658C24.909 21.976 24.4501 22.4518 24.1605 23.0297C23.8626 23.6456 23.7739 24.3163 23.6407 24.9781C23.5074 25.6399 23.3 26.3521 23.3786 27.0315C23.5477 28.4979 24.5728 29.6895 26.0473 29.956C27.4345 30.2074 28.8292 30.4111 30.2276 30.5846C35.7212 31.2574 41.2711 31.3379 46.7818 30.8247C47.2305 30.7828 47.6787 30.7371 48.1262 30.6876C48.266 30.6723 48.4074 30.6884 48.5401 30.7348C48.6729 30.7812 48.7936 30.8566 48.8934 30.9557C48.9932 31.0548 49.0695 31.1749 49.1169 31.3073C49.1642 31.4397 49.1814 31.5811 49.167 31.7209L49.0275 33.0773C48.7463 35.8181 48.4652 38.5587 48.184 41.299C47.8907 44.1769 47.5955 47.0545 47.2984 49.9319C47.2146 50.7422 47.1308 51.5524 47.047 52.3624C46.9666 53.16 46.9552 53.9827 46.8038 54.7709C46.5649 56.0103 45.7258 56.7715 44.5015 57.0499C43.3798 57.3052 42.2339 57.4392 41.0836 57.4497C39.8083 57.4566 38.5336 57.4 37.2583 57.4069C35.897 57.4145 34.2295 57.2887 33.1786 56.2756C32.2553 55.3856 32.1277 53.9921 32.002 52.7872C31.8344 51.192 31.6682 49.5971 31.5036 48.0023L30.5796 39.1344L29.9819 33.3966C29.9718 33.3017 29.9618 33.208 29.9524 33.1125C29.8807 32.428 29.3961 31.758 28.6324 31.7926C27.9788 31.8215 27.2359 32.3771 27.3125 33.1125L27.7557 37.3664L28.672 46.1657C28.9331 48.6652 29.1935 51.165 29.4533 53.6653C29.5036 54.1442 29.5507 54.6244 29.6035 55.1034C29.8908 57.7205 31.8895 59.131 34.3646 59.5282C35.8102 59.7607 37.291 59.8085 38.758 59.8324C40.6386 59.8626 42.538 59.9348 44.3877 59.5942C47.1287 59.0914 49.1853 57.2611 49.4788 54.422C49.5626 53.6024 49.6464 52.7826 49.7302 51.9626C50.0088 49.2507 50.2871 46.5386 50.5649 43.8263L51.4737 34.9641L51.8904 30.9026C51.9112 30.7012 51.9962 30.5118 52.133 30.3625C52.2697 30.2132 52.4509 30.1119 52.6497 30.0736C53.4335 29.9208 54.1827 29.66 54.7402 29.0635C55.6277 28.1138 55.8043 26.8756 55.4906 25.6274ZM26.0071 26.5035C26.019 26.4979 25.997 26.6003 25.9876 26.6481C25.9857 26.5758 25.9895 26.5117 26.0071 26.5035ZM26.0831 27.0918C26.0894 27.0874 26.1083 27.1126 26.1278 27.1428C26.0982 27.1151 26.0794 27.0944 26.0825 27.0918H26.0831ZM26.1579 27.1905C26.185 27.2364 26.1994 27.2653 26.1579 27.1905V27.1905ZM26.3082 27.3125H26.3119C26.3119 27.3169 26.3188 27.3213 26.3214 27.3257C26.3172 27.3208 26.3126 27.3164 26.3075 27.3125H26.3082ZM52.6132 27.1302C52.3317 27.3979 51.9074 27.5224 51.4882 27.5846C46.7868 28.2823 42.0169 28.6355 37.264 28.4796C33.8624 28.3633 30.4967 27.9856 27.129 27.5098C26.799 27.4633 26.4414 27.403 26.2145 27.1597C25.7871 26.7009 25.997 25.777 26.1083 25.2226C26.2101 24.7148 26.405 24.0378 27.009 23.9656C27.9518 23.8549 29.0466 24.2528 29.9794 24.3942C31.1023 24.5656 32.2295 24.7028 33.3609 24.8059C38.1892 25.2459 43.0986 25.1774 47.9056 24.5337C48.7817 24.416 49.6548 24.2792 50.5246 24.1233C51.2996 23.9844 52.1588 23.7236 52.6271 24.5262C52.9482 25.073 52.991 25.8046 52.9413 26.4225C52.926 26.6917 52.8084 26.9448 52.6126 27.1302H52.6132Z" fill="#0D0C22"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.1302 40.1929C80.8556 40.7169 80.4781 41.1732 79.9978 41.5604C79.5175 41.9479 78.9571 42.2633 78.3166 42.5062C77.6761 42.7497 77.0315 42.9131 76.3835 42.9964C75.7352 43.0799 75.106 43.0727 74.4963 42.9735C73.8863 42.8749 73.3674 42.6737 72.9408 42.3695L73.4214 37.3779C73.8633 37.2261 74.4197 37.0703 75.0909 36.9107C75.7619 36.7513 76.452 36.6371 77.1613 36.5689C77.8705 36.5003 78.5412 36.5084 79.1744 36.5917C79.8068 36.6753 80.3065 36.8765 80.6725 37.1958C80.8707 37.378 81.0387 37.5754 81.176 37.7883C81.313 38.0011 81.3969 38.2214 81.4276 38.4493C81.5037 39.0875 81.4047 39.6687 81.1302 40.1929ZM74.153 29.5602C74.4734 29.3627 74.8585 29.1877 75.3083 29.0356C75.7581 28.8841 76.2195 28.7774 76.6923 28.7167C77.1648 28.6562 77.6262 28.6481 78.0763 28.6938C78.5258 28.7395 78.9228 28.8647 79.2659 29.0697C79.6089 29.2751 79.8643 29.5714 80.032 29.9586C80.1997 30.3464 80.2456 30.8365 80.1693 31.429C80.1083 31.9001 79.9211 32.2991 79.6089 32.6256C79.2963 32.9526 78.9147 33.2259 78.4652 33.4462C78.0154 33.6668 77.5388 33.8415 77.0356 33.9702C76.5321 34.0997 76.0477 34.1949 75.5828 34.2553C75.1176 34.3163 74.7137 34.3545 74.3706 34.3692C74.0273 34.3845 73.8021 34.3921 73.6956 34.3921L74.153 29.5602ZM83.6007 36.9676C83.3566 36.4361 83.0287 35.9689 82.6172 35.5658C82.2054 35.1633 81.717 34.8709 81.1531 34.6885C81.3969 34.491 81.6371 34.1795 81.8737 33.7539C82.1099 33.3288 82.3119 32.865 82.4796 32.3636C82.6474 31.8619 82.762 31.357 82.8229 30.8478C82.8836 30.3389 82.8607 29.902 82.7544 29.537C82.4947 28.6256 82.087 27.9114 81.5303 27.3946C80.9734 26.8782 80.3257 26.5211 79.586 26.3233C78.8462 26.1264 78.0304 26.0842 77.1383 26.1981C76.2462 26.312 75.3347 26.5361 74.4049 26.8704C74.4049 26.7946 74.4124 26.7148 74.4278 26.6312C74.4426 26.548 74.4504 26.4604 74.4504 26.369C74.4504 26.1411 74.3361 25.9439 74.1074 25.7765C73.8787 25.6093 73.6155 25.5107 73.3183 25.4801C73.0209 25.45 72.731 25.5142 72.4489 25.6738C72.1665 25.8334 71.9721 26.1264 71.8656 26.5511C71.7434 27.9189 71.6215 29.3398 71.4996 30.8134C71.3774 32.2875 71.248 33.7767 71.1107 35.2812C70.9735 36.7855 70.8362 38.2784 70.6989 39.7598C70.5616 41.2414 70.4244 42.6659 70.2871 44.0333C70.333 44.4436 70.4473 44.7629 70.6304 44.9907C70.8133 45.2189 71.0268 45.3556 71.2709 45.401C71.5147 45.4467 71.7704 45.4045 72.0371 45.2755C72.3038 45.1469 72.5365 44.9222 72.735 44.6032C73.3447 44.9375 74.0311 45.1541 74.7938 45.253C75.5561 45.3516 76.3298 45.3516 77.1157 45.253C77.9007 45.1541 78.6747 44.9682 79.4374 44.6943C80.1997 44.4211 80.8936 44.079 81.519 43.669C82.1441 43.2586 82.6703 42.7911 83.0975 42.2671C83.5244 41.7426 83.8065 41.1767 83.9437 40.5691C84.081 39.946 84.119 39.3231 84.0581 38.7C83.9971 38.0771 83.8445 37.5 83.6007 36.9676Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M105.915 49.0017C105.832 49.5031 105.713 50.0311 105.561 50.586C105.408 51.1403 105.229 51.6458 105.023 52.1018C104.818 52.5575 104.589 52.9256 104.337 53.207C104.085 53.488 103.815 53.606 103.525 53.5606C103.296 53.5297 103.151 53.3854 103.091 53.1274C103.029 52.8686 103.029 52.5497 103.091 52.17C103.151 51.7901 103.269 51.3607 103.445 50.8821C103.62 50.4035 103.834 49.9284 104.085 49.4577C104.337 48.9864 104.623 48.5347 104.943 48.1015C105.264 47.6686 105.599 47.3075 105.95 47.0189C106.026 47.11 106.06 47.3378 106.053 47.7028C106.045 48.0674 105.999 48.5006 105.915 49.0017ZM113.67 39.1097C113.464 38.8819 113.213 38.7529 112.915 38.7223C112.618 38.6919 112.317 38.859 112.012 39.2237C111.813 39.5883 111.562 39.9379 111.257 40.2722C110.952 40.6067 110.635 40.9103 110.307 41.1839C109.98 41.4572 109.667 41.6931 109.37 41.8903C109.072 42.0881 108.84 42.2324 108.672 42.3235C108.611 41.8374 108.576 41.3132 108.569 40.7507C108.561 40.1886 108.573 39.619 108.603 39.0415C108.649 38.2209 108.744 37.393 108.889 36.557C109.034 35.7213 109.244 34.9007 109.518 34.0951C109.518 33.67 109.419 33.3242 109.221 33.0582C109.022 32.7924 108.782 32.625 108.5 32.5567C108.218 32.4885 107.929 32.5264 107.631 32.6707C107.334 32.8153 107.078 33.0775 106.865 33.4569C106.682 33.9586 106.472 34.5207 106.236 35.1436C105.999 35.7667 105.732 36.4012 105.435 37.0469C105.138 37.6931 104.806 38.3197 104.44 38.9273C104.074 39.5354 103.674 40.075 103.239 40.5457C102.804 41.0168 102.331 41.3854 101.821 41.6512C101.31 41.9172 100.757 42.0349 100.162 42.0045C99.8876 41.9285 99.6893 41.7235 99.5675 41.3889C99.4453 41.0549 99.373 40.6368 99.3504 40.1354C99.3275 39.634 99.3504 39.0831 99.4189 38.4828C99.4877 37.8828 99.5791 37.2863 99.6934 36.6938C99.8078 36.101 99.9337 35.5389 100.071 35.0071C100.208 34.4753 100.337 34.0268 100.46 33.6622C100.643 33.2218 100.643 32.8529 100.46 32.5567C100.277 32.2604 100.025 32.0631 99.705 31.964C99.3846 31.8654 99.0489 31.8694 98.6983 31.9755C98.3474 32.0819 98.0958 32.3173 97.9435 32.682C97.684 33.3054 97.4475 34.004 97.2342 34.779C97.0206 35.5539 96.8491 36.3558 96.7197 37.1836C96.5896 38.0121 96.5171 38.8327 96.502 39.6456C96.5011 39.6985 96.5037 39.7488 96.5034 39.8014C96.1709 40.6848 95.854 41.3525 95.553 41.7992C95.1641 42.377 94.7253 42.6277 94.2375 42.5513C94.0236 42.4603 93.8832 42.2477 93.8147 41.9132C93.7453 41.5792 93.7227 41.1689 93.7453 40.6822C93.7688 40.1964 93.826 39.6456 93.9171 39.0299C94.0091 38.4146 94.1229 37.7764 94.2601 37.1154C94.3977 36.4541 94.5425 35.7899 94.6949 35.121C94.8472 34.4525 94.9845 33.8218 95.107 33.2291C95.0916 32.6973 94.9352 32.291 94.6377 32.0097C94.3405 31.7289 93.9247 31.6187 93.3913 31.6791C93.0253 31.8312 92.7542 32.029 92.579 32.2719C92.4034 32.5148 92.2623 32.8265 92.1558 33.2062C92.0946 33.404 92.0032 33.799 91.8813 34.3918C91.7591 34.984 91.603 35.6644 91.4123 36.4315C91.2217 37.1992 90.9967 38.0005 90.7376 38.8362C90.4781 39.6719 90.1885 40.4283 89.8684 41.1041C89.548 41.7801 89.1972 42.3235 88.8161 42.7338C88.4348 43.1438 88.023 43.3113 87.5807 43.2352C87.3366 43.1895 87.1805 42.9388 87.112 42.4831C87.0432 42.0271 87.0319 41.4653 87.0775 40.7964C87.1233 40.1279 87.2148 39.3946 87.352 38.5971C87.4893 37.7993 87.63 37.0434 87.7752 36.3289C87.92 35.6149 88.0535 34.984 88.1756 34.4372C88.2975 33.8901 88.3814 33.5254 88.4272 33.3433C88.4272 32.9026 88.3277 32.5495 88.1298 32.2832C87.9313 32.0178 87.6913 31.8503 87.4092 31.7818C87.1268 31.7136 86.8372 31.7514 86.54 31.8957C86.2426 32.0403 85.9872 32.3026 85.7736 32.682C85.6973 33.0923 85.598 33.5674 85.4761 34.1067C85.3539 34.6459 85.2361 35.2006 85.1218 35.7705C85.0074 36.3404 84.9003 36.8988 84.8014 37.4459C84.7021 37.993 84.6299 38.4716 84.584 38.8819C84.5536 39.2008 84.519 39.5923 84.4813 40.0556C84.443 40.5194 84.4238 41.0092 84.4238 41.5257C84.4238 42.0427 84.4618 42.5554 84.5385 43.0643C84.6145 43.5735 84.7518 44.0408 84.95 44.4659C85.1482 44.8915 85.4265 45.2408 85.7852 45.5144C86.1433 45.7879 86.5972 45.9397 87.1463 45.9704C87.7101 46.0005 88.202 45.9591 88.6217 45.8449C89.041 45.731 89.4221 45.5523 89.7654 45.3091C90.1084 45.0665 90.421 44.7776 90.7033 44.443C90.9851 44.1091 91.2637 43.7444 91.5383 43.3491C91.7974 43.9269 92.1329 44.3748 92.5447 44.694C92.9565 45.013 93.3913 45.2032 93.8486 45.2637C94.306 45.3241 94.7715 45.2602 95.2442 45.0699C95.7167 44.8803 96.1436 44.5573 96.5252 44.1012C96.7762 43.8216 97.0131 43.5038 97.2354 43.1525C97.3297 43.317 97.4301 43.4758 97.543 43.6224C97.9168 44.1091 98.424 44.443 99.0645 44.6255C99.7506 44.808 100.421 44.8386 101.077 44.7169C101.733 44.5954 102.358 44.3748 102.953 44.0559C103.548 43.7366 104.101 43.3532 104.612 42.9047C105.122 42.4565 105.568 41.9895 105.95 41.5028C105.934 41.8524 105.927 42.1832 105.927 42.4944C105.927 42.8061 105.919 43.1438 105.904 43.5088C105.141 44.0408 104.421 44.679 103.742 45.4233C103.064 46.1676 102.469 46.9616 101.958 47.8051C101.447 48.6483 101.047 49.5031 100.757 50.3691C100.467 51.2357 100.326 52.0445 100.334 52.7969C100.341 53.549 100.521 54.206 100.871 54.7681C101.222 55.3306 101.794 55.7331 102.587 55.9763C103.411 56.2348 104.135 56.242 104.76 55.9991C105.386 55.7559 105.931 55.3531 106.396 54.791C106.861 54.2289 107.242 53.549 107.54 52.7512C107.837 51.9534 108.073 51.1215 108.249 50.2555C108.424 49.3894 108.535 48.5379 108.58 47.7028C108.626 46.8668 108.626 46.1219 108.58 45.4687C109.892 44.9219 110.967 44.2305 111.806 43.3945C112.645 42.5594 113.338 41.6778 113.887 40.7507C114.055 40.5229 114.112 40.2493 114.059 39.9304C114.006 39.6111 113.876 39.3376 113.67 39.1097Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M142.53 37.6515C142.575 37.3022 142.644 36.9335 142.735 36.546C142.827 36.1585 142.941 35.7823 143.079 35.4177C143.216 35.0531 143.376 34.7379 143.559 34.4718C143.742 34.2061 143.937 34.0161 144.142 33.9019C144.348 33.7883 144.558 33.7995 144.771 33.936C145 34.0731 145.141 34.3617 145.195 34.8021C145.248 35.2433 145.195 35.7141 145.034 36.2155C144.874 36.7172 144.588 37.1879 144.177 37.6286C143.765 38.0696 143.208 38.3579 142.507 38.4947C142.476 38.2824 142.484 38.0011 142.53 37.6515ZM150.456 38.5857C150.204 38.5103 149.964 38.5025 149.735 38.5632C149.506 38.6239 149.361 38.7835 149.301 39.042C149.178 39.5281 148.984 40.0258 148.717 40.5347C148.45 41.0439 148.122 41.5262 147.734 41.9822C147.345 42.438 146.906 42.8408 146.418 43.1901C145.93 43.5397 145.419 43.7904 144.886 43.9422C144.351 44.1096 143.91 44.1284 143.559 43.9991C143.208 43.8705 142.93 43.6498 142.724 43.3384C142.518 43.027 142.369 42.6508 142.278 42.2101C142.186 41.7694 142.133 41.3137 142.118 40.8424C142.987 40.9034 143.761 40.7478 144.44 40.3751C145.118 40.0032 145.694 39.509 146.167 38.8937C146.639 38.2784 146.998 37.587 147.242 36.8195C147.485 36.0524 147.623 35.2887 147.653 34.5288C147.669 33.8146 147.562 33.2108 147.333 32.7169C147.105 32.2233 146.796 31.839 146.407 31.5658C146.018 31.2922 145.572 31.1326 145.069 31.0872C144.566 31.0415 144.054 31.11 143.536 31.2922C142.91 31.505 142.381 31.8506 141.946 32.3294C141.512 32.808 141.149 33.3629 140.86 33.9933C140.57 34.6239 140.341 35.3038 140.173 36.033C140.005 36.7626 139.883 37.4806 139.807 38.1873C139.739 38.8214 139.702 39.4278 139.689 40.013C139.657 40.0874 139.625 40.1588 139.59 40.2383C139.354 40.7782 139.079 41.3062 138.766 41.8226C138.454 42.3394 138.107 42.7725 137.726 43.1218C137.344 43.4714 136.948 43.5929 136.536 43.4865C136.292 43.426 136.159 43.1444 136.136 42.6433C136.113 42.1416 136.139 41.5187 136.216 40.7741C136.292 40.0298 136.38 39.2239 136.479 38.3579C136.578 37.4918 136.628 36.664 136.628 35.8737C136.628 35.1898 136.498 34.5329 136.239 33.9019C135.979 33.2718 135.625 32.7473 135.175 32.3294C134.725 31.9113 134.203 31.634 133.608 31.4975C133.013 31.3605 132.373 31.4518 131.687 31.7708C131 32.09 130.455 32.5382 130.051 33.1157C129.647 33.6934 129.277 34.3009 128.942 34.9391C128.819 34.4528 128.641 34.0011 128.404 33.583C128.167 33.1651 127.878 32.8005 127.535 32.4888C127.191 32.1776 126.806 31.9344 126.38 31.7595C125.953 31.5851 125.502 31.4975 125.03 31.4975C124.572 31.4975 124.149 31.5851 123.76 31.7595C123.371 31.9344 123.017 32.1583 122.696 32.4318C122.376 32.7056 122.087 33.013 121.827 33.3551C121.568 33.6969 121.339 34.0352 121.141 34.3692C121.11 33.9742 121.076 33.6286 121.038 33.332C121 33.0359 120.931 32.7852 120.832 32.5801C120.733 32.3748 120.592 32.2193 120.409 32.1129C120.226 32.0067 119.967 31.9532 119.632 31.9532C119.464 31.9532 119.296 31.9874 119.128 32.0556C118.96 32.1241 118.811 32.2193 118.682 32.3407C118.552 32.4627 118.453 32.6105 118.385 32.7852C118.316 32.9598 118.297 33.1614 118.327 33.3892C118.342 33.5566 118.385 33.7576 118.453 33.9933C118.522 34.2289 118.587 34.5369 118.648 34.9163C118.708 35.2962 118.758 35.756 118.796 36.2953C118.834 36.8349 118.846 37.4959 118.831 38.2784C118.815 39.0611 118.758 39.9763 118.659 41.0248C118.56 42.0733 118.403 43.289 118.19 44.6714C118.16 44.9907 118.282 45.2492 118.556 45.4467C118.831 45.6439 119.143 45.7578 119.494 45.7885C119.845 45.8188 120.177 45.7578 120.489 45.6063C120.802 45.4539 120.981 45.1882 121.027 44.8085C121.072 44.0943 121.16 43.3347 121.29 42.529C121.419 41.724 121.579 40.9262 121.77 40.1359C121.961 39.346 122.178 38.5938 122.422 37.8793C122.666 37.1651 122.937 36.5347 123.234 35.9876C123.532 35.4405 123.84 35.0039 124.161 34.6771C124.481 34.3504 124.816 34.187 125.167 34.187C125.594 34.187 125.926 34.3805 126.162 34.7679C126.398 35.1557 126.566 35.6536 126.666 36.2609C126.765 36.869 126.81 37.5341 126.803 38.2555C126.795 38.9773 126.765 39.6724 126.711 40.341C126.658 41.0098 126.597 41.606 126.528 42.1303C126.46 42.6545 126.41 43.0157 126.38 43.2129C126.38 43.5625 126.513 43.8395 126.78 44.0448C127.046 44.2498 127.344 44.3716 127.672 44.4095C128 44.4476 128.309 44.3866 128.598 44.227C128.888 44.0674 129.056 43.7982 129.102 43.4179C129.254 42.324 129.464 41.2264 129.731 40.1247C129.997 39.023 130.303 38.0355 130.646 37.1616C130.989 36.2878 131.37 35.5735 131.79 35.0189C132.209 34.4646 132.655 34.187 133.128 34.187C133.371 34.187 133.559 34.3544 133.688 34.6884C133.818 35.0227 133.883 35.4784 133.883 36.0559C133.883 36.4815 133.848 36.9184 133.78 37.3666C133.711 37.8148 133.631 38.2784 133.54 38.7569C133.448 39.2358 133.368 39.7256 133.299 40.227C133.231 40.7287 133.196 41.2527 133.196 41.7998C133.196 42.1797 133.235 42.6204 133.311 43.1218C133.387 43.6229 133.532 44.0983 133.745 44.5462C133.959 44.9947 134.252 45.3744 134.626 45.6858C135 45.9973 135.476 46.1531 136.056 46.1531C136.925 46.1531 137.695 45.9669 138.366 45.5947C139.037 45.2226 139.613 44.7365 140.093 44.1362C140.118 44.1047 140.141 44.0711 140.165 44.0399C140.202 44.1287 140.235 44.2227 140.276 44.3071C140.604 44.9756 141.05 45.4921 141.615 45.857C142.178 46.2216 142.842 46.4229 143.605 46.4611C144.367 46.4987 145.198 46.3581 146.098 46.0392C146.769 45.796 147.352 45.4921 147.848 45.1275C148.343 44.7628 148.789 44.3184 149.186 43.7941C149.583 43.2699 149.945 42.6658 150.273 41.9822C150.601 41.2981 150.932 40.5159 151.268 39.6342C151.329 39.3916 151.272 39.1751 151.097 38.9848C150.921 38.7951 150.708 38.6621 150.456 38.5857Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M162.887 36.0434C162.81 36.4918 162.707 36.986 162.578 37.525C162.448 38.0646 162.284 38.623 162.086 39.2004C161.888 39.7779 161.644 40.2984 161.354 40.7616C161.064 41.2254 160.733 41.5935 160.359 41.8671C159.985 42.1406 159.555 42.2546 159.066 42.2089C158.822 42.1788 158.635 42.0117 158.506 41.7075C158.376 41.4038 158.308 41.0161 158.3 40.545C158.292 40.0743 158.334 39.5575 158.426 38.9951C158.517 38.4333 158.658 37.8821 158.849 37.3426C159.04 36.8036 159.272 36.3056 159.547 35.8496C159.821 35.3939 160.138 35.0405 160.496 34.7898C160.854 34.5391 161.247 34.4217 161.674 34.4365C162.101 34.4518 162.559 34.6643 163.047 35.0747C163.016 35.2725 162.963 35.5954 162.887 36.0434ZM171.019 37.787C170.782 37.6656 170.538 37.6392 170.287 37.7075C170.035 37.7757 169.856 38.0076 169.749 38.4026C169.688 38.8283 169.551 39.3294 169.338 39.9069C169.124 40.4843 168.861 41.0317 168.548 41.5478C168.236 42.0646 167.877 42.494 167.473 42.8358C167.069 43.1778 166.638 43.3337 166.181 43.3028C165.799 43.2727 165.532 43.079 165.38 42.7218C165.227 42.3647 165.147 41.9168 165.14 41.3769C165.132 40.838 165.186 40.2301 165.3 39.5538C165.414 38.8777 165.552 38.2054 165.712 37.5363C165.872 36.868 166.036 36.2258 166.204 35.6105C166.371 34.9951 166.508 34.4747 166.616 34.0493C166.738 33.6693 166.699 33.3466 166.501 33.0803C166.303 32.8149 166.055 32.6246 165.758 32.5107C165.46 32.3967 165.159 32.3664 164.854 32.4196C164.549 32.4728 164.351 32.6362 164.259 32.9094C163.359 32.1345 162.494 31.7166 161.663 31.6559C160.831 31.5952 160.065 31.7776 159.364 32.203C158.662 32.6284 158.041 33.2437 157.5 34.0493C156.958 34.8549 156.52 35.7322 156.184 36.6818C155.849 37.6314 155.639 38.6004 155.555 39.5879C155.471 40.5757 155.536 41.4761 155.75 42.289C155.963 43.1018 156.34 43.7669 156.882 44.283C157.423 44.7998 158.159 45.0583 159.089 45.0583C159.501 45.0583 159.898 44.9747 160.279 44.8076C160.66 44.6401 161.011 44.4426 161.331 44.2148C161.651 43.9869 161.933 43.7475 162.178 43.4968C162.421 43.2461 162.612 43.0373 162.749 42.8699C162.856 43.417 163.032 43.8808 163.276 44.2605C163.519 44.6401 163.798 44.9521 164.111 45.1948C164.423 45.4376 164.751 45.6164 165.094 45.7306C165.437 45.8445 165.769 45.9015 166.089 45.9015C166.806 45.9015 167.477 45.6583 168.102 45.1719C168.727 44.6861 169.288 44.0893 169.784 43.3829C170.279 42.6762 170.687 41.9319 171.007 41.1491C171.328 40.3666 171.541 39.6715 171.648 39.0634C171.755 38.8355 171.735 38.5964 171.591 38.3457C171.446 38.095 171.255 37.909 171.019 37.787Z" fill="#0D0C23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M212.194 50.3701C212.064 50.8866 211.862 51.3238 211.587 51.6806C211.313 52.0377 210.97 52.2239 210.558 52.2393C210.299 52.2543 210.101 52.1175 209.963 51.8289C209.826 51.5401 209.731 51.1679 209.678 50.7122C209.624 50.2562 209.601 49.747 209.609 49.1849C209.616 48.6227 209.639 48.0681 209.678 47.521C209.715 46.9742 209.761 46.4647 209.815 45.9939C209.868 45.5226 209.91 45.1586 209.94 44.9C210.459 44.9608 210.89 45.1846 211.233 45.5723C211.576 45.9598 211.839 46.4193 212.022 46.9514C212.205 47.4831 212.312 48.0568 212.343 48.6722C212.373 49.2875 212.323 49.8534 212.194 50.3701ZM203.913 50.3701C203.783 50.8866 203.581 51.3238 203.307 51.6806C203.032 52.0377 202.689 52.2239 202.277 52.2393C202.018 52.2543 201.82 52.1175 201.683 51.8289C201.545 51.5401 201.45 51.1679 201.397 50.7122C201.343 50.2562 201.32 49.747 201.328 49.1849C201.336 48.6227 201.358 48.0681 201.397 47.521C201.434 46.9742 201.48 46.4647 201.534 45.9939C201.587 45.5226 201.629 45.1586 201.66 44.9C202.178 44.9608 202.609 45.1846 202.952 45.5723C203.295 45.9598 203.558 46.4193 203.741 46.9514C203.924 47.4831 204.031 48.0568 204.062 48.6722C204.092 49.2875 204.042 49.8534 203.913 50.3701ZM195.415 37.4241C195.399 37.7884 195.365 38.1114 195.312 38.3925C195.258 38.6741 195.186 38.8522 195.095 38.9283C194.927 38.8369 194.721 38.6018 194.477 38.2216C194.233 37.8419 194.042 37.4122 193.905 36.9336C193.768 36.4551 193.725 35.9843 193.779 35.5205C193.832 35.0573 194.073 34.6967 194.5 34.4379C194.667 34.3468 194.812 34.3809 194.934 34.5405C195.056 34.7001 195.155 34.9318 195.232 35.2357C195.308 35.5399 195.361 35.8892 195.392 36.2842C195.422 36.6795 195.43 37.0591 195.415 37.4241ZM193.39 41.9711C193.154 42.2215 192.89 42.4381 192.601 42.6206C192.311 42.803 192.014 42.9398 191.709 43.0309C191.404 43.1223 191.129 43.1448 190.885 43.0991C190.199 42.9627 189.673 42.666 189.307 42.2103C188.941 41.7545 188.708 41.219 188.609 40.6037C188.51 39.9881 188.521 39.3308 188.644 38.6319C188.765 37.933 188.971 37.2835 189.261 36.6832C189.551 36.0829 189.902 35.5662 190.313 35.1333C190.725 34.7001 191.175 34.4306 191.663 34.3239C191.48 35.0989 191.419 35.9007 191.48 36.7286C191.541 37.5568 191.739 38.3355 192.075 39.0648C192.288 39.506 192.544 39.9082 192.841 40.2729C193.139 40.6378 193.501 40.9492 193.928 41.2075C193.806 41.466 193.626 41.7204 193.39 41.9711ZM218.702 37.6519C218.747 37.3026 218.816 36.9336 218.908 36.5462C218.999 36.159 219.114 35.7828 219.251 35.4181C219.388 35.0532 219.548 34.738 219.731 34.4723C219.914 34.2065 220.108 34.0163 220.314 33.9024C220.52 33.7884 220.73 33.7997 220.943 33.9365C221.172 34.0735 221.313 34.3621 221.367 34.8025C221.42 35.2435 221.367 35.7142 221.207 36.2159C221.046 36.7173 220.761 37.1884 220.349 37.6288C219.937 38.07 219.38 38.3583 218.679 38.4951C218.648 38.2826 218.656 38.0015 218.702 37.6519ZM227.921 37.6519C227.966 37.3026 228.035 36.9336 228.126 36.5462C228.218 36.159 228.332 35.7828 228.47 35.4181C228.607 35.0532 228.767 34.738 228.95 34.4723C229.133 34.2065 229.328 34.0163 229.533 33.9024C229.739 33.7884 229.949 33.7997 230.162 33.9365C230.391 34.0735 230.532 34.3621 230.586 34.8025C230.639 35.2435 230.586 35.7142 230.425 36.2159C230.265 36.7173 229.979 37.1884 229.568 37.6288C229.156 38.07 228.599 38.3583 227.898 38.4951C227.867 38.2826 227.875 38.0015 227.921 37.6519ZM236.488 38.9852C236.312 38.7955 236.099 38.6625 235.847 38.5862C235.595 38.5104 235.355 38.5029 235.126 38.5636C234.897 38.6244 234.752 38.784 234.692 39.0422C234.57 39.5286 234.375 40.0262 234.108 40.5349C233.841 41.0444 233.514 41.5267 233.125 41.9824C232.736 42.4381 232.297 42.8412 231.81 43.1905C231.321 43.5401 230.81 43.7908 230.277 43.9423C229.743 44.1101 229.301 44.1289 228.95 43.9996C228.599 43.8706 228.321 43.6503 228.115 43.3389C227.909 43.0271 227.761 42.6512 227.669 42.2103C227.578 41.7699 227.524 41.3142 227.509 40.8428C228.378 40.9038 229.152 40.7483 229.831 40.3755C230.509 40.0034 231.085 39.5092 231.558 38.8939C232.031 38.2788 232.389 37.5874 232.633 36.82C232.877 36.0526 233.014 35.2892 233.045 34.5293C233.06 33.815 232.953 33.211 232.724 32.7171C232.496 32.2235 232.187 31.8395 231.798 31.5662C231.409 31.2924 230.963 31.133 230.46 31.0874C229.957 31.0417 229.445 31.1105 228.927 31.2924C228.302 31.5055 227.772 31.851 227.338 32.3296C226.903 32.8085 226.54 33.3634 226.251 33.9934C225.961 34.6244 225.732 35.3039 225.564 36.0335C225.396 36.7627 225.274 37.481 225.199 38.1874C225.124 38.873 225.084 39.5292 225.075 40.1572C225.017 40.2824 224.956 40.4082 224.889 40.5349C224.622 41.0444 224.295 41.5267 223.906 41.9824C223.517 42.4381 223.078 42.8412 222.591 43.1905C222.102 43.5401 221.592 43.7908 221.058 43.9423C220.524 44.1101 220.082 44.1289 219.731 43.9996C219.38 43.8706 219.102 43.6503 218.896 43.3389C218.691 43.0271 218.542 42.6512 218.45 42.2103C218.359 41.7699 218.305 41.3142 218.29 40.8428C219.159 40.9038 219.933 40.7483 220.612 40.3755C221.29 40.0034 221.866 39.5092 222.339 38.8939C222.811 38.2788 223.17 37.5874 223.414 36.82C223.658 36.0526 223.795 35.2892 223.826 34.5293C223.841 33.815 223.734 33.211 223.506 32.7171C223.277 32.2235 222.968 31.8395 222.579 31.5662C222.19 31.2924 221.744 31.133 221.241 31.0874C220.738 31.0417 220.227 31.1105 219.708 31.2924C219.083 31.5055 218.553 31.851 218.119 32.3296C217.684 32.8085 217.321 33.3634 217.032 33.9934C216.742 34.6244 216.513 35.3039 216.346 36.0335C216.178 36.7627 216.056 37.481 215.98 38.1874C215.936 38.5859 215.907 38.9722 215.886 39.3516C215.739 39.4765 215.595 39.6023 215.442 39.7258C214.916 40.1514 214.363 40.5349 213.784 40.8769C213.204 41.219 212.601 41.5001 211.977 41.7204C211.351 41.9408 210.71 42.0738 210.055 42.1192L211.473 26.9847C211.565 26.6655 211.519 26.3847 211.336 26.1415C211.153 25.8983 210.916 25.7312 210.627 25.6401C210.337 25.5488 210.028 25.5566 209.7 25.6627C209.372 25.7694 209.102 26.0126 208.888 26.3919C208.781 26.9697 208.671 27.7597 208.557 28.7625C208.442 29.7653 208.328 30.8595 208.213 32.0448C208.099 33.23 207.985 34.4532 207.87 35.7142C207.756 36.9759 207.657 38.1533 207.573 39.2472C207.569 39.2958 207.566 39.3398 207.562 39.3878C207.429 39.5005 207.299 39.6142 207.161 39.7258C206.635 40.1514 206.082 40.5349 205.503 40.8769C204.923 41.219 204.321 41.5001 203.696 41.7204C203.07 41.9408 202.429 42.0738 201.774 42.1192L203.192 26.9847C203.284 26.6655 203.238 26.3847 203.055 26.1415C202.872 25.8983 202.635 25.7312 202.346 25.6401C202.056 25.5488 201.747 25.5566 201.419 25.6627C201.091 25.7694 200.821 26.0126 200.607 26.3919C200.501 26.9697 200.39 27.7597 200.276 28.7625C200.161 29.7653 200.047 30.8595 199.933 32.0448C199.818 33.23 199.704 34.4532 199.589 35.7142C199.475 36.9759 199.376 38.1533 199.292 39.2472C199.29 39.2692 199.289 39.2891 199.287 39.3111C199.048 39.4219 198.786 39.519 198.503 39.6006C198.213 39.6844 197.885 39.7339 197.519 39.7489C197.58 39.4751 197.63 39.1712 197.668 38.8369C197.706 38.5029 197.737 38.1533 197.76 37.7884C197.782 37.4241 197.79 37.0591 197.782 36.6945C197.774 36.3296 197.755 35.9956 197.725 35.6914C197.649 35.0385 197.508 34.4191 197.302 33.8338C197.096 33.2491 196.818 32.7593 196.467 32.3637C196.116 31.9687 195.678 31.7027 195.151 31.5662C194.626 31.4294 194.012 31.4748 193.31 31.7027C192.273 31.5662 191.339 31.6613 190.508 31.9878C189.677 32.3149 188.956 32.7894 188.346 33.4122C187.736 34.0357 187.237 34.7684 186.848 35.6119C186.459 36.4551 186.2 37.3214 186.07 38.21C186.015 38.5868 185.988 38.9618 185.98 39.336C185.744 39.8177 185.486 40.2388 185.201 40.5921C184.797 41.0935 184.377 41.5038 183.943 41.8228C183.508 42.142 183.077 42.3852 182.65 42.5523C182.223 42.7198 181.842 42.8337 181.507 42.8941C181.11 42.9702 180.729 42.978 180.363 42.917C179.997 42.8565 179.661 42.6816 179.357 42.3927C179.112 42.1802 178.925 41.8381 178.796 41.3671C178.666 40.896 178.59 40.3608 178.567 39.7602C178.544 39.1599 178.567 38.533 178.636 37.8798C178.705 37.2266 178.822 36.6072 178.99 36.0222C179.158 35.4372 179.371 34.913 179.631 34.4492C179.89 33.9862 180.195 33.6554 180.546 33.4579C180.744 33.4886 180.866 33.606 180.912 33.811C180.958 34.0163 180.969 34.2595 180.946 34.5405C180.923 34.8219 180.889 35.1105 180.843 35.4066C180.797 35.703 180.775 35.9502 180.775 36.1474C180.851 36.5577 180.999 36.877 181.221 37.1048C181.441 37.3327 181.69 37.466 181.964 37.5036C182.239 37.5417 182.509 37.4773 182.776 37.3098C183.043 37.143 183.26 36.877 183.428 36.512C183.443 36.5274 183.466 36.5349 183.497 36.5349L183.817 33.6404C183.909 33.2451 183.847 32.8958 183.634 32.5919C183.42 32.288 183.138 32.113 182.788 32.0676C182.345 31.4294 181.747 31.0914 180.992 31.0532C180.237 31.0154 179.463 31.2623 178.67 31.7941C178.182 32.144 177.751 32.626 177.378 33.2413C177.004 33.857 176.699 34.5405 176.463 35.2926C176.226 36.0448 176.058 36.8391 175.959 37.6748C175.86 38.5104 175.841 39.3236 175.902 40.1133C175.963 40.9038 176.104 41.6484 176.325 42.347C176.546 43.0462 176.855 43.6312 177.252 44.102C177.587 44.5123 177.968 44.8127 178.395 45.0027C178.822 45.1927 179.268 45.3101 179.734 45.3558C180.199 45.4012 180.66 45.3821 181.118 45.2988C181.575 45.2155 182.01 45.0978 182.421 44.9454C182.955 44.7482 183.505 44.4972 184.069 44.1933C184.633 43.8897 185.174 43.5248 185.693 43.0991C185.966 42.8753 186.228 42.6313 186.482 42.3696C186.598 42.6553 186.727 42.9317 186.882 43.1905C187.294 43.8741 187.85 44.429 188.552 44.8544C189.253 45.2797 190.115 45.4844 191.137 45.4697C192.235 45.4544 193.249 45.1774 194.18 44.6378C195.11 44.0988 195.872 43.3042 196.467 42.256C197.358 42.256 198.234 42.1096 199.096 41.819C199.089 41.911 199.081 42.0079 199.075 42.0966C199.014 42.9019 198.983 43.4487 198.983 43.7376C198.968 44.239 198.934 44.8581 198.88 45.5949C198.827 46.332 198.793 47.1069 198.778 47.9198C198.763 48.7326 198.793 49.5532 198.869 50.3817C198.945 51.2096 199.105 51.962 199.349 52.6383C199.593 53.3141 199.94 53.8878 200.39 54.3591C200.84 54.8299 201.431 55.1112 202.163 55.2023C202.941 55.3084 203.612 55.1717 204.176 54.792C204.74 54.412 205.198 53.8918 205.549 53.2308C205.899 52.5695 206.147 51.8061 206.292 50.9401C206.437 50.074 206.479 49.2039 206.418 48.3301C206.357 47.4562 206.196 46.6321 205.937 45.8575C205.678 45.0822 205.319 44.444 204.862 43.9423C205.137 43.8669 205.465 43.7226 205.846 43.5095C206.227 43.2969 206.62 43.0575 207.024 42.7915C207.123 42.7261 207.221 42.6573 207.32 42.5902C207.283 43.1286 207.264 43.5126 207.264 43.7376C207.249 44.239 207.215 44.8581 207.161 45.5949C207.108 46.332 207.073 47.1069 207.058 47.9198C207.043 48.7326 207.073 49.5532 207.15 50.3817C207.226 51.2096 207.386 51.962 207.63 52.6383C207.874 53.3141 208.221 53.8878 208.671 54.3591C209.121 54.8299 209.712 55.1112 210.444 55.2023C211.221 55.3084 211.892 55.1717 212.457 54.792C213.021 54.412 213.478 53.8918 213.83 53.2308C214.18 52.5695 214.428 51.8061 214.573 50.9401C214.718 50.074 214.759 49.2039 214.699 48.3301C214.637 47.4562 214.477 46.6321 214.218 45.8575C213.959 45.0822 213.601 44.444 213.143 43.9423C213.418 43.8669 213.745 43.7226 214.127 43.5095C214.508 43.2969 214.9 43.0575 215.305 42.7915C215.515 42.6533 215.724 42.5107 215.932 42.3641C216.01 43.1072 216.179 43.759 216.448 44.3073C216.776 44.9761 217.222 45.4925 217.787 45.8575C218.351 46.2218 219.014 46.4234 219.777 46.4612C220.539 46.4988 221.37 46.3586 222.271 46.0393C222.941 45.7965 223.525 45.4925 224.02 45.1279C224.516 44.763 224.962 44.3185 225.358 43.7946C225.381 43.7642 225.403 43.7313 225.425 43.7006C225.496 43.9134 225.574 44.1179 225.667 44.3073C225.995 44.9761 226.441 45.4925 227.006 45.8575C227.569 46.2218 228.233 46.4234 228.996 46.4612C229.758 46.4988 230.589 46.3586 231.489 46.0393C232.16 45.7965 232.744 45.4925 233.239 45.1279C233.735 44.763 234.181 44.3185 234.577 43.7946C234.974 43.27 235.336 42.666 235.664 41.9824C235.992 41.2985 236.323 40.5164 236.659 39.6347C236.72 39.3918 236.663 39.1752 236.488 38.9852Z" fill="#0D0C23"/>
</svg>`;
const paypal = `
<svg xmlns="http://www.w3.org/2000/svg" width="150" height="40">
<path fill="#253B80" d="M46.211 6.749h-6.839a.95.95 0 0 0-.939.802l-2.766 17.537a.57.57 0 0 0 .564.658h3.265a.95.95 0 0 0 .939-.803l.746-4.73a.95.95 0 0 1 .938-.803h2.165c4.505 0 7.105-2.18 7.784-6.5.306-1.89.013-3.375-.872-4.415-.972-1.142-2.696-1.746-4.985-1.746zM47 13.154c-.374 2.454-2.249 2.454-4.062 2.454h-1.032l.724-4.583a.57.57 0 0 1 .563-.481h.473c1.235 0 2.4 0 3.002.704.359.42.469 1.044.332 1.906zM66.654 13.075h-3.275a.57.57 0 0 0-.563.481l-.145.916-.229-.332c-.709-1.029-2.29-1.373-3.868-1.373-3.619 0-6.71 2.741-7.312 6.586-.313 1.918.132 3.752 1.22 5.031.998 1.176 2.426 1.666 4.125 1.666 2.916 0 4.533-1.875 4.533-1.875l-.146.91a.57.57 0 0 0 .562.66h2.95a.95.95 0 0 0 .939-.803l1.77-11.209a.568.568 0 0 0-.561-.658zm-4.565 6.374c-.316 1.871-1.801 3.127-3.695 3.127-.951 0-1.711-.305-2.199-.883-.484-.574-.668-1.391-.514-2.301.295-1.855 1.805-3.152 3.67-3.152.93 0 1.686.309 2.184.892.499.589.697 1.411.554 2.317zM84.096 13.075h-3.291a.954.954 0 0 0-.787.417l-4.539 6.686-1.924-6.425a.953.953 0 0 0-.912-.678h-3.234a.57.57 0 0 0-.541.754l3.625 10.638-3.408 4.811a.57.57 0 0 0 .465.9h3.287a.949.949 0 0 0 .781-.408l10.946-15.8a.57.57 0 0 0-.468-.895z"/>
<path fill="#179BD7" d="M94.992 6.749h-6.84a.95.95 0 0 0-.938.802l-2.766 17.537a.569.569 0 0 0 .562.658h3.51a.665.665 0 0 0 .656-.562l.785-4.971a.95.95 0 0 1 .938-.803h2.164c4.506 0 7.105-2.18 7.785-6.5.307-1.89.012-3.375-.873-4.415-.971-1.142-2.694-1.746-4.983-1.746zm.789 6.405c-.373 2.454-2.248 2.454-4.062 2.454h-1.031l.725-4.583a.568.568 0 0 1 .562-.481h.473c1.234 0 2.4 0 3.002.704.359.42.468 1.044.331 1.906zM115.434 13.075h-3.273a.567.567 0 0 0-.562.481l-.145.916-.23-.332c-.709-1.029-2.289-1.373-3.867-1.373-3.619 0-6.709 2.741-7.311 6.586-.312 1.918.131 3.752 1.219 5.031 1 1.176 2.426 1.666 4.125 1.666 2.916 0 4.533-1.875 4.533-1.875l-.146.91a.57.57 0 0 0 .564.66h2.949a.95.95 0 0 0 .938-.803l1.771-11.209a.571.571 0 0 0-.565-.658zm-4.565 6.374c-.314 1.871-1.801 3.127-3.695 3.127-.949 0-1.711-.305-2.199-.883-.484-.574-.666-1.391-.514-2.301.297-1.855 1.805-3.152 3.67-3.152.93 0 1.686.309 2.184.892.501.589.699 1.411.554 2.317zM119.295 7.23l-2.807 17.858a.569.569 0 0 0 .562.658h2.822c.469 0 .867-.34.939-.803l2.768-17.536a.57.57 0 0 0-.562-.659h-3.16a.571.571 0 0 0-.562.482z"/>
<path fill="#253B80" d="M7.266 29.154l.523-3.322-1.165-.027H1.061L4.927 1.292a.316.316 0 0 1 .314-.268h9.38c3.114 0 5.263.648 6.385 1.927.526.6.861 1.227 1.023 1.917.17.724.173 1.589.007 2.644l-.012.077v.676l.526.298a3.69 3.69 0 0 1 1.065.812c.45.513.741 1.165.864 1.938.127.795.085 1.741-.123 2.812-.24 1.232-.628 2.305-1.152 3.183a6.547 6.547 0 0 1-1.825 2c-.696.494-1.523.869-2.458 1.109-.906.236-1.939.355-3.072.355h-.73c-.522 0-1.029.188-1.427.525a2.21 2.21 0 0 0-.744 1.328l-.055.299-.924 5.855-.042.215c-.011.068-.03.102-.058.125a.155.155 0 0 1-.096.035H7.266z"/>
<path fill="#179BD7" d="M23.048 7.667c-.028.179-.06.362-.096.55-1.237 6.351-5.469 8.545-10.874 8.545H9.326c-.661 0-1.218.48-1.321 1.132L6.596 26.83l-.399 2.533a.704.704 0 0 0 .695.814h4.881c.578 0 1.069-.42 1.16-.99l.048-.248.919-5.832.059-.32c.09-.572.582-.992 1.16-.992h.73c4.729 0 8.431-1.92 9.513-7.476.452-2.321.218-4.259-.978-5.622a4.667 4.667 0 0 0-1.336-1.03z"/>
<path fill="#222D65" d="M21.754 7.151a9.757 9.757 0 0 0-1.203-.267 15.284 15.284 0 0 0-2.426-.177h-7.352a1.172 1.172 0 0 0-1.159.992L8.05 17.605l-.045.289a1.336 1.336 0 0 1 1.321-1.132h2.752c5.405 0 9.637-2.195 10.874-8.545.037-.188.068-.371.096-.55a6.594 6.594 0 0 0-1.017-.429 9.045 9.045 0 0 0-.277-.087z"/>
<path fill="#253B80" d="M9.614 7.699a1.169 1.169 0 0 1 1.159-.991h7.352c.871 0 1.684.057 2.426.177a9.757 9.757 0 0 1 1.481.353c.365.121.704.264 1.017.429.368-2.347-.003-3.945-1.272-5.392C20.378.682 17.853 0 14.622 0h-9.38c-.66 0-1.223.48-1.325 1.133L.01 25.898a.806.806 0 0 0 .795.932h5.791l1.454-9.225 1.564-9.906z"/>
</svg>`;

module.exports = RecentFilesPlugin;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
