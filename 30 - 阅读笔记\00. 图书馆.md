---
banner: "40 - Obsidian/附件/banners/book-banner.gif"
---
---
banner: "40 - Obsidian/附件/banners/book-banner.gif"
---
---
banner: "40 - Obsidian/附件/banners/book-banner.gif"
cssClasses: cards cards-align-bottom cards-2-3 table-100
banner_x: 0.5
banner_y: undefined
---

# 图书馆

```dataviewjs
dv.table(["封面","书名", "作者", "类型", "评分","进度"], dv.pages("#reading")
    .map(b => [("![](" + b.cover + ")"), b.file.link, b.author, b.genre, (b.rating + "🌟"), b.status]))
```
