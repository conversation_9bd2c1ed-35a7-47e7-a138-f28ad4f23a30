{"recentFiles": [{"basename": "Graphiti MCP", "path": "20 - 工作学习/学习/AI/Graphiti MCP.md"}, {"basename": "2025年07月14日", "path": "00 - 每日日记/01-DailyNote/2025年07月14日.md"}, {"basename": "00. 主页", "path": "40 - Obsidian/主页/00. 主页.md"}, {"basename": "侧边栏当日日程", "path": "40 - Obsidian/侧边栏/侧边栏当日日程.md"}, {"basename": "收件箱", "path": "40 - Obsidian/侧边栏/收件箱.md"}, {"basename": "00. 参考汇总", "path": "40 - Obsidian/主页/00. 参考汇总.md"}, {"basename": "每周周记模板", "path": "40 - Obsidian/模板/每周周记模板.md"}, {"basename": "奥利奥", "path": "10 - 人员管理/宠物/奥利奥.md"}, {"basename": "00. 图书馆", "path": "30 - 阅读笔记/00. 图书馆.md"}, {"basename": "00. 导航", "path": "40 - Obsidian/主页/00. 导航.md"}, {"basename": "Dunciad", "path": "50 - 卡片知识/Dunciad.md"}, {"basename": "porpoises", "path": "50 - 卡片知识/porpoises.md"}, {"basename": "meridian", "path": "50 - 卡片知识/meridian.md"}, {"basename": "00. 卡片盒", "path": "50 - 卡片知识/00. 卡片盒.md"}, {"basename": "anemone", "path": "50 - 卡片知识/anemone.md"}, {"basename": "<PERSON>aun", "path": "50 - 卡片知识/Faun.md"}, {"basename": "Coral bleaching", "path": "50 - 卡片知识/Coral bleaching.md"}, {"basename": "<PERSON>", "path": "50 - 卡片知识/Chamberlain.md"}, {"basename": "coral reef", "path": "50 - 卡片知识/coral reef.md"}, {"basename": "cohort", "path": "50 - 卡片知识/cohort.md"}, {"basename": "address operator", "path": "50 - 卡片知识/address operator.md"}, {"basename": "Baader-<PERSON><PERSON><PERSON> Phenomenon", "path": "50 - 卡片知识/Baader-Meinhof Phenomenon.md"}, {"basename": "00. 学习", "path": "20 - 工作学习/学习/00. 学习.md"}, {"basename": "成吉思汗：征战、帝国及其遗产", "path": "30 - 阅读笔记/成吉思汗：征战、帝国及其遗产.md"}, {"basename": "破译金庸密码", "path": "30 - 阅读笔记/破译金庸密码.md"}, {"basename": "锁麟囊", "path": "30 - 阅读笔记/锁麟囊.md"}, {"basename": "侧边栏天气预报", "path": "40 - Obsidian/侧边栏/侧边栏天气预报.md"}, {"basename": "年糕", "path": "10 - 人员管理/宠物/年糕.md"}, {"basename": "00. 宠物", "path": "10 - 人员管理/宠物/00. 宠物.md"}, {"basename": "雪糕", "path": "10 - 人员管理/宠物/雪糕.md"}, {"basename": "00. 人员", "path": "10 - 人员管理/家人/00. 人员.md"}, {"basename": "00. 文件管理", "path": "40 - Obsidian/主页/00. 文件管理.md"}, {"basename": "木子金又二丨", "path": "10 - 人员管理/家人/木子金又二丨.md"}, {"basename": "软考中级-软件设计师", "path": "20 - 工作学习/学习/编程/SoftTest/软考中级-软件设计师.md"}, {"basename": "计算机组成", "path": "20 - 工作学习/学习/编程/SoftTest/计算机组成.md"}, {"basename": "algae", "path": "50 - 卡片知识/algae.md"}, {"basename": "任务面板", "path": "40 - Obsidian/脚本/tasksCalendar/任务面板.md"}, {"basename": "面试训练营", "path": "20 - 工作学习/学习/编程/Interview/面试训练营.md"}, {"basename": "100优化案例", "path": "20 - 工作学习/学习/编程/100Test/100优化案例.md"}, {"basename": "Day26", "path": "20 - 工作学习/学习/编程/Interview/Day26.md"}, {"basename": "Day27", "path": "20 - 工作学习/学习/编程/Interview/Day27.md"}, {"basename": "Day28", "path": "20 - 工作学习/学习/编程/Interview/Day28.md"}, {"basename": "Day30", "path": "20 - 工作学习/学习/编程/Interview/Day30.md"}, {"basename": "00. 编程学习", "path": "20 - 工作学习/学习/编程/00. 编程学习.md"}, {"basename": "chariot", "path": "50 - 卡片知识/chariot.md"}, {"basename": "Apple M1 Max chip", "path": "50 - 卡片知识/Apple M1 Max chip.md"}, {"basename": "每日日记模板", "path": "40 - Obsidian/模板/每日日记模板.md"}], "omittedPaths": [], "maxLength": null, "openType": "tab"}