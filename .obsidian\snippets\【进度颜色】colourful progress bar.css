
/* 
 * progress bar
 Modified from https://github.com/ceciliamay/obsidianmd-theme-primary
 Author: Cuman
 * ------------------------
 */

 progress {
  -webkit-appearance: none;
  appearance: none;
  width: calc(var(--font-text-size) + 220px);
  height: calc(var(--font-text-size) - 8px);
  margin-right: 10px;
}
progress[value]::-webkit-progress-bar {
  background: var(--background-primary-bg-4-bt);
  box-shadow: 0px 0px 0px 0.3px var(--background-modifier-form-field) inset;
  border-radius: 0px;
  overflow: hidden;
}
progress[value]::-webkit-progress-value {
  border-radius: 0px;
}

.theme-light progress {
  --color-l-progress-10: #f94144;
  --color-l-progress-20: #f3722c;
  --color-l-progress-30: #f8961e;
  --color-l-progress-40: #f9844a;;
  --color-l-progress-50: #f9c74f;
  --color-l-progress-60: #90be6d;
  --color-l-progress-70: #99d98c;
  --color-l-progress-80: #43aa8b;
  --color-l-progress-90: #4d908e;
  --color-l-progress-100: #38b000;
}
.theme-light progress[value^="1"]::-webkit-progress-value {
  background: var(--color-l-progress-10);
}
.theme-light progress[value^="2"]::-webkit-progress-value {
  background: var(--color-l-progress-20);
}
.theme-light progress[value^="3"]::-webkit-progress-value {
  background: var(--color-l-progress-30);
}
.theme-light progress[value^="4"]::-webkit-progress-value {
  background: var(--color-l-progress-40);
}
.theme-light progress[value^="5"]::-webkit-progress-value {
  background: var(--color-l-progress-50);
}
.theme-light progress[value^="6"]::-webkit-progress-value {
  background: var(--color-l-progress-60);
}
.theme-light progress[value^="7"]::-webkit-progress-value {
  background: var(--color-l-progress-70);
}
.theme-light progress[value^="8"]::-webkit-progress-value {
  background: var(--color-l-progress-80);
}
.theme-light progress[value^="9"]::-webkit-progress-value {
  background: var(--color-l-progress-90);
}
.theme-light progress[value^="100"]::-webkit-progress-value {
  background: var(--color-l-progress-100);
}

.theme-dark progress {
  --color-d-progress-10: hsl(4, 66%, 30%);
  --color-d-progress-20: hsl(4, 66%, 30%);
  --color-d-progress-30: hsl(18, 69%, 50%);
  --color-d-progress-40: hsl(18, 69%, 50%);
  --color-d-progress-50: hsl(43, 100%, 42%);
  --color-d-progress-60: hsl(43, 100%, 42%);
  --color-d-progress-70: hsl(165, 63%, 29%);
  --color-d-progress-80: hsl(205, 95%, 25%);
  --color-d-progress-90: hsl(266, 70%, 30%);
  --color-d-progress-100: linear-gradient(45deg, #ef92af95, #7bd7e995, #ffda2b95);
}
.theme-dark progress[value^="1"]::-webkit-progress-value {
  background: var(--color-d-progress-10);
}
.theme-dark progress[value^="2"]::-webkit-progress-value {
  background: var(--color-d-progress-20);
}
.theme-dark progress[value^="3"]::-webkit-progress-value {
  background: var(--color-d-progress-30);
}
.theme-dark progress[value^="4"]::-webkit-progress-value {
  background: var(--color-d-progress-40);
}
.theme-dark progress[value^="5"]::-webkit-progress-value {
  background: var(--color-d-progress-50);
}
.theme-dark progress[value^="6"]::-webkit-progress-value {
  background: var(--color-d-progress-60);
}
.theme-dark progress[value^="7"]::-webkit-progress-value {
  background: var(--color-d-progress-70);
}
.theme-dark progress[value^="8"]::-webkit-progress-value {
  background: var(--color-d-progress-80);
}
.theme-dark progress[value^="9"]::-webkit-progress-value {
  background: var(--color-d-progress-90);
}
.theme-dark progress[value="100"]::-webkit-progress-value {
  background: var(--color-d-progress-100);
}
