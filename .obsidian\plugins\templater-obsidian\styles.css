.templater_search {
    width: calc(100% - 20px);
}

.templater_div {
    border-top: 1px solid var(--background-modifier-border);
}

.templater_div > .setting-item {
    border-top: none !important;
    align-self: center;
}

.templater_div > .setting-item > .setting-item-control {
    justify-content: space-around;
    padding: 0;
    width: 100%;
}

.templater_div
    > .setting-item
    > .setting-item-control
    > .setting-editor-extra-setting-button {
    align-self: center;
}

.templater_donating {
    margin: 10px;
}

.templater_title {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    text-align: center;
}

.templater_template {
    align-self: center;
    margin-left: 5px;
    margin-right: 5px;
    width: 70%;
}

.templater_cmd {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.templater_div2 > .setting-item {
    align-content: center;
    justify-content: center;
}

.templater-prompt-div {
    display: flex;
}

.templater-prompt-form {
    display: flex;
    flex-grow: 1;
}

.templater-prompt-input {
    flex-grow: 1;
}

.templater-button-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

textarea.templater-prompt-input {
    height: 10rem;
}

textarea.templater-prompt-input:focus {
    border-color: var(--interactive-accent);
}

.cm-s-obsidian .templater-command-bg {
    left: 0px;
    right: 0px;
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command {
    font-size: 0.85em;
    font-family: var(--font-monospace);
    line-height: 1.3;
}

.cm-s-obsidian .templater-inline .cm-templater-command {
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
    color: #008bff;
}

.cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
    color: #c0d700;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-keyword {
    color: #00a7aa;
    font-weight: normal;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-atom {
    color: #f39b35;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-number {
    color: #a06fca;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-type {
    color: #a06fca;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-def {
    color: #98e342;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-property {
    color: #d4d4d4;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-variable {
    color: #d4d4d4;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-variable-2 {
    color: #da7dae;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-variable-3 {
    color: #a06fca;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-type.cm-def {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-property.cm-def {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-callee {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-operator {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-qualifier {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-tag {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-tag.cm-bracket {
    color: #d4d4d4;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-attribute {
    color: #a06fca;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-comment {
    color: #696d70;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-comment.cm-tag {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
    color: #d4d4d4;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-string {
    color: #e6db74;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-string-2 {
    color: #f39b35;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-meta {
    color: #d4d4d4;
    background: inherit;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-builtin {
    color: #fc4384;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-header {
    color: #da7dae;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-hr {
    color: #98e342;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-link {
    color: #696d70;
}

.theme-dark .cm-s-obsidian .cm-templater-command.cm-error {
    border-bottom: 1px solid #c42412;
}

.theme-dark .cm-s-obsidian pre.HyperMD-codeblock .cm-keyword {
    font-weight: normal;
}

.theme-dark
    .cm-s-obsidian
    .cm-templater-command.CodeMirror-activeline-background {
    background: #272727;
}

.theme-dark .cm-s-obsidian .cm-templater-command.CodeMirror-matchingbracket {
    outline: 1px solid grey;
    color: #d4d4d4 !important;
}

.CodeMirror-hints {
    position: absolute;
    z-index: 10;
    overflow: hidden;
    list-style: none;

    margin: 0;
    padding: 2px;

    -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 1px solid silver;

    background: white;
    font-size: 90%;
    font-family: monospace;

    max-height: 20em;
    overflow-y: auto;
}

.CodeMirror-hint {
    margin: 0;
    padding: 0 4px;
    border-radius: 2px;
    white-space: pre;
    color: black;
    cursor: pointer;
}

li.CodeMirror-hint-active {
    background: #08f;
    color: white;
}
