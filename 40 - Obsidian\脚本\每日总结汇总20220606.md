```dataviewjs

function isWithinWeek(page) {	
	
	let filemoment = moment(page.file.name, 'YYYY年MM月DD日')
	let today = moment().startOf('day');
	let tomorrow = today.clone().add(1, 'days').startOf('day');
	let weekago = today.clone().subtract(7, 'days').startOf('day');

	// 如果在本周里，且有总结
	if (filemoment.isAfter(weekago) && filemoment.isBefore(tomorrow)) { if (page.总结) { return true; } }
	else return false;

 }


dv.table(["Date","Summary"], dv.pages('"00 - 每日日记/01-DailyNote"')
	.filter(isWithinWeek)
	.sort(b => b.file.name,'desc')
	.map(b =>[dv.fileLink(b.file.name, false, moment(b.file.name,'YYYY年MM月DD日').format("ddd")),"<span id='summary1'>"+b.总结+"</span>"])
	)
```
