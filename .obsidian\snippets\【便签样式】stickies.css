  /* 手机调整 */

@media (max-width: 400pt)
{
  .stickies {
    width: 43%;
  }
}



.theme-dark {
  /* stickies */
  --stickies-color-1: #00000091;
  --stickies-color-2: #c7a3cf;
  --tape-color: #99999967;
}

.theme-light{
    /* stickies */
  --stickies-color-1: #b3e2b3;
  --stickies-color-2: #e9c6f1;
  --tape-color: #acacac65;
  
}
 /* =========================================*/
/* =========tapes pins and stickies=========*/
/* =========================================*/
/* thanks to death_au, <PERSON><PERSON><PERSON><PERSON> and <PERSON>th<PERSON> from Obsidian Members Group on Discord */

.stickies {
  text-align: center;
  transition: width 2s;
  padding: 5px;
  margin: 18px;
  position: relative;
  float: right;
  right: -10px;
  width: 40%;
  background-color: var(--stickies-color-1);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(2deg);
  transition: all 2s ease;
  z-index: 1;
  padding-top: 10px;
  padding-bottom: 10px;
  border-radius: 0px;
  color: black;
}

.stickies::after {
  content: "";
  left: -5%;
  top: -10px;
  height: 40px;
  width: 15px;
  border-radius: var(--radius-l);
  border: 3px solid #979797;
  display: inline-block;
  position: absolute;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(-11deg);
  z-index: 11;
}

.stickies::before {
  width: 11px;
  height: 20px;
  content: "";
  display: inline-block;
  position: absolute;
  left: -3.5%;
  top: -2px;
  border-radius: var(--radius-l);
  border: 3px solid #979797;
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  z-index: 10;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(-11deg);
}

.stickies2 {
  position: relative;
  float: left;
  box-shadow: 0 10px 10px 2px #9191912d;
  width: 30%;
  background-color: var(--stickies-color-2);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(-2deg);
  transition: all 2s ease;
  z-index: 1;
  padding: 20px;
  margin: 10px;
  color: black;
}

.stickies2::after {
  content: "";
  display: block;
  height: 32px;
  width: 2px;
  position: absolute;
  left: 50%;
  top: -10px;
  z-index: 1;
  border-radius: 50%;
  display: inline-block;
  height: 15px;
  width: 15px;
  border: 1px;
  box-shadow: inset -10px -10px 10px #f0b7a4, inset 3px 3px 5px;
}
