---
banner: "80 - Obsidian/附件/daily-note-banner.gif"
---
---
creation date: 2022-03-21 12:30
modification date: <%+ tp.file.last_modified_date("dddd Do MMMM YYYY HH:mm:ss") %>
tags: DailyNote 日记模板 1

banner: "80 - Obsidian/附件/daily-note-banner.gif"
cssclass: noyaml
banner_icon: 💌
banner_x: 0.5
banner_y: 0.38

week:: 2022-12
---

# 日记模板 1

<< [[0000年12月31日]] | [[0001年01月02日]]>>

## 总结
```ad-flex

<div>

**习惯**

[鸟崽 :: ]
[邮件 :: ]
[运动 :: ]
[护肤 :: ]

**健康**

[不点外卖 :: ]
[不吃零嘴 :: ]

</div>

<div>

**生活**

[心情 :: ]
[焦虑 :: ]
[支出 :: ]
[体重 :: ]
[例假 :: ]
[健身 :: ]

</div>


<div>

**总结**

[计划 :: ]
[计划完成度 :: ]

[总结 :: ]

</div>

```
## 日记
Nanjing, China: 🌧   +6°C


## 日程




## 鸟崽
**[[2022年03月21日]]**
^1

本周记录：[[2022年第12周鹦鹉记录]]

## 任务

<p class="stickies";>
<b>COUNTDOWN</b><br>
小黄已吃<%+* let edate = moment("2022-02-07", "yyyy-MM-DD"); let from = moment().startOf('day'); tR += from.diff(edate, "days") %>天多西环素</br>
小黄已滴<%+* let edate = moment("2022-02-05", "yyyy-MM-DD"); let from = moment().startOf('day'); tR += from.diff(edate, "days") %>天眼药水</br>
小黄和空空已打<%+* let edate = moment("2022-02-06", "yyyy-MM-DD"); let from = moment().startOf('day'); tR += from.diff(edate, "days") %>天谷胱甘肽</br>
白胖胖和鸟群已吃<%+* let edate = moment("2022-02-08", "yyyy-MM-DD"); let from = moment().startOf('day'); tR += from.diff(edate, "days") %>天多西环素</br>
白胖胖已滴<%+* let edate = moment("2022-02-08", "yyyy-MM-DD"); let from = moment().startOf('day'); tR += from.diff(edate, "days") %>天眼药水</br>
	<font color="red">注：以上统计不包含今天</font>
<!-- --- -->
</p>


#### 过期任务

```tasks

not done
hide edit button
hide due date
due before 2022-03-21

```

#### 下周任务

```tasks
not done
due after {{date:YYYY-MM-DD}}
due before {{date+7d:YYYY-MM-DD}}
path does not include 鸟群
```

#### 今日必做

```tasks

not done

due on 2022-03-21

hide backlink
hide due date

```

#### 长期任务
```tasks
no due date
path includes 年
path does not include 模板
path does not include 鸟群
path does not include 2022年03月21日
heading does not include 打卡群友
path does not include 2021年年度规划
exclude sub-items
hide edit button
not done
short mode
```

#### 来年日程
```tasks
due after 2022-03-21
due before 2025-01-01
path includes 年
path does not include 模板
path does not include 鸟群
path does not include 2021年12月09日
exclude sub-items
not done
hide backlink
hide edit button
```

#### 今日完成

```tasks

done on 2022-03-21
short mode
heading does not include 打卡群友
hide done date
hide backlink
hide edit button
limit to 10

```


---
```dataviewjs

let ftMd = dv.pages("").file.sort(t => t.cday)[0]
let total = parseInt([new Date() - ftMd.ctime] / (60*60*24*1000))
let totalDays = "您已使用 *Obsidian* "+total+" 天，"
let nofold = '!"misc/templates"'
let allFile = dv.pages(nofold).file
let totalMd = "共创建 "+
	allFile.length+" 篇文档"
let totalTag = allFile.etags.distinct().length+" 个标签"
let totalTask = allFile.tasks.length+" 个待办。 <br><br>"
dv.paragraph(
	totalDays+totalMd+"、"+totalTag+"、"+totalTask
)

```



