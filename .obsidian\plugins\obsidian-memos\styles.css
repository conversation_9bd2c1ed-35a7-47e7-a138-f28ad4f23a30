.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, <PERSON><PERSON>, <PERSON>solas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] #root {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, <PERSON><PERSON>, <PERSON>sol<PERSON>, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
@font-face {
  font-family: 'DINPro';
  src: '';
  font-weight: normal;
}
@font-face {
  font-family: 'DINPro';
  src: '';
  font-weight: bold;
}
@font-face {
  font-family: 'ubuntu-mono';
  src: '';
  font-style: normal;
}
div[data-type='memos_view'] {
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Noto Sans", "Noto Sans CJK SC", "Microsoft YaHei UI", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /*text-align: var();*/
  -webkit-font-smoothing: subpixel-antialiased;
}
div[data-type='memos_view'] .view-content:not(.images-wrapper) img {
  max-width: 100%;
  cursor: default;
}
.theme-light div[data-type='memos_view'] {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: #37352f;
  -webkit-tap-highlight-color: transparent;
}
.theme-light div[data-type='memos_view'] body,
.theme-light div[data-type='memos_view'] html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 15px;
}
.theme-light div[data-type='memos_view'] code {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
  background-color: pink;
  padding: 2px 4px;
  border-radius: 4px;
}
.theme-light div[data-type='memos_view'] pre {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light div[data-type='memos_view'] pre * {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light div[data-type='memos_view'] label,
.theme-light div[data-type='memos_view'] input,
.theme-light div[data-type='memos_view'] button:not(.rdp),
.theme-light div[data-type='memos_view'] textarea,
.theme-light div[data-type='memos_view'] img {
  background-color: transparent;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  border: none;
  outline: none;
}
.theme-light div[data-type='memos_view'] input:-webkit-autofill,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:hover,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:focus,
.theme-light div[data-type='memos_view'] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px white inset !important;
}
.theme-light div[data-type='memos_view'] li:not(.rta__item) {
  list-style-type: none;
}
.theme-light div[data-type='memos_view'] li:not(.rta__item)::before {
  content: '•';
  font-weight: bold;
  margin-right: 4px;
}
.theme-light div[data-type='memos_view'] a {
  cursor: pointer;
  color: #5783f7;
  text-underline-offset: 2px;
}
.theme-light div[data-type='memos_view'] a:hover {
  background-color: #e4e4e4;
}
.theme-light div[data-type='memos_view'] a.is-unresolved {
  cursor: pointer;
  color: #8fa2d6;
  text-underline-offset: 2px;
}
.theme-light div[data-type='memos_view'] a.is-unresolved:hover {
  background-color: #e4e4e4;
}
.theme-light div[data-type='memos_view'] .btn {
  border: unset;
  background-color: unset;
  font-size: unset;
  user-select: none;
  cursor: pointer;
  text-align: center;
}
.theme-light .hidden {
  display: none !important;
}
@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] body,
  .theme-light div[data-type='memos_view'] html {
    -webkit-overflow-scrolling: touch;
  }
}
.theme-light div[data-type='memos_view'].mobile-view body,
.theme-light div[data-type='memos_view'].mobile-view html {
  -webkit-overflow-scrolling: touch;
}
.theme-dark div[data-type='memos_view'] {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  color: #37352f;
  -webkit-tap-highlight-color: transparent;
}
.theme-dark div[data-type='memos_view'] body,
.theme-dark div[data-type='memos_view'] html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 15px;
}
.theme-dark div[data-type='memos_view'] code {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
  background-color: #a8a8a8;
  padding: 2px 4px;
  border-radius: 4px;
}
.theme-dark div[data-type='memos_view'] pre {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark div[data-type='memos_view'] pre * {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark div[data-type='memos_view'] label,
.theme-dark div[data-type='memos_view'] input,
.theme-dark div[data-type='memos_view'] button,
.theme-dark div[data-type='memos_view'] textarea,
.theme-dark div[data-type='memos_view'] img {
  background-color: transparent;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  border: none;
  outline: none;
}
.theme-dark div[data-type='memos_view'] input:-webkit-autofill,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:hover,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:focus,
.theme-dark div[data-type='memos_view'] input:-webkit-autofill:active {
  box-shadow: 0 0 0 30px #000000 inset !important;
}
.theme-dark div[data-type='memos_view'] li:not(.rta__item) {
  list-style-type: none;
}
.theme-dark div[data-type='memos_view'] li:not(.rta__item)::before {
  content: '•';
  font-weight: bold;
  margin-right: 4px;
}
.theme-dark div[data-type='memos_view'] a {
  cursor: pointer;
  color: #5783f7;
  text-underline-offset: 2px;
}
.theme-dark div[data-type='memos_view'] a:hover {
  background-color: #2c395a;
}
.theme-dark div[data-type='memos_view'] a.is-unresolved {
  cursor: pointer;
  color: #8fa2d6;
  text-underline-offset: 2px;
}
.theme-dark div[data-type='memos_view'] a.is-unresolved:hover {
  background-color: #353535;
}
.theme-dark div[data-type='memos_view'] .btn {
  border: unset;
  background-color: unset;
  font-size: unset;
  user-select: none;
  cursor: pointer;
  text-align: center;
}
.theme-dark .hidden {
  display: none !important;
}
.theme-dark div[data-type='memos_view'].mobile-view body,
.theme-dark div[data-type='memos_view'].mobile-view html {
  -webkit-overflow-scrolling: touch;
}
.theme-dark div[data-type='memos_view'] svg {
  fill: #cdcdcd;
}
.theme-dark .dialog-wrapper .btn-group,
.theme-dark .dialog-wrapper .btns-container {
  fill: #cdcdcd;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .dialog-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 100;
  transition: all 0.2s ease;
  overflow-x: hidden;
  overflow-y: scroll;
  padding: 64px 0;
  scrollbar-width: none;
}
.theme-light .dialog-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .dialog-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .dialog-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .dialog-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light .dialog-wrapper.showup {
  background-color: rgba(0, 0, 0, 0.6);
}
.theme-light .dialog-wrapper.showoff {
  display: none;
}
.theme-light .dialog-wrapper > .dialog-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background-color: white;
  padding: 16px;
  border-radius: 8px;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-header-container > .title-text > .icon-text {
  margin-right: 6px;
  font-size: 16px;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-header-container .btn {
  width: 24px;
  height: 24px;
  padding: 0;
  margin-right: 0;
  border-radius: 4px;
  background-color: transparent;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-header-container .btn > .icon-img {
  width: 20px;
  height: 20px;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-header-container .btn:hover {
  background-color: lightgray;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}
.theme-light .dialog-wrapper > .dialog-container > .dialog-footer-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-top: 16px;
}
@media only screen and (max-width: 875px) {
  .theme-light .dialog-wrapper {
    width: 100%;
    padding: 0 16px;
  }
  .theme-light .dialog-wrapper > .dialog-container {
    max-width: 100%;
  }
}
.theme-dark .dialog-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 100;
  transition: all 0.2s ease;
  overflow-x: hidden;
  overflow-y: scroll;
  padding: 64px 0;
  scrollbar-width: none;
}
.theme-dark .dialog-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .dialog-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .dialog-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .dialog-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark .dialog-wrapper.showup {
  background-color: rgba(85, 85, 85, 0.6);
}
.theme-dark .dialog-wrapper.showoff {
  display: none;
}
.theme-dark .dialog-wrapper > .dialog-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background-color: #000000;
  padding: 16px;
  border-radius: 8px;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-header-container > .title-text > .icon-text {
  margin-right: 6px;
  font-size: 16px;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-header-container .btn {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: transparent;
  padding: 0;
  margin-right: 0;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-header-container .btn > .icon-img {
  width: 20px;
  height: 20px;
  fill: #cdcdcd;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-header-container .btn:hover {
  background-color: #494949;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}
.theme-dark .dialog-wrapper > .dialog-container > .dialog-footer-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  margin-top: 16px;
}
@media only screen and (max-width: 875px) {
  .theme-dark .dialog-wrapper {
    width: 100%;
    padding: 0 16px;
  }
  .theme-dark .dialog-wrapper > .dialog-container {
    max-width: 100%;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.about-site-dialog > .dialog-container {
  width: 420px;
}
.about-site-dialog > .dialog-container > .dialog-content-container {
  line-height: 1.8;
}
.about-site-dialog > .dialog-container > .dialog-content-container > p {
  margin: 2px 0;
}
.about-site-dialog > .dialog-container > .dialog-content-container > hr {
  margin: 4px 0;
  width: 100%;
  height: 1px;
  background-color: lightgray;
  border: none;
}
.about-site-dialog > .dialog-container > .dialog-content-container .normal-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-size: 13px;
  color: gray;
  white-space: pre-wrap;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.about-site-dialog > .dialog-container > .dialog-content-container .pre-text {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
@media only screen and (max-width: 875px) {
  .dialog-wrapper.about-site-dialog {
    padding: 24px 16px;
    padding-top: 64px;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .menu-btns-popup {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  margin-top: 4px;
  margin-left: 90px;
  padding: 4px;
  width: 180px;
  border-radius: 8px;
  z-index: 20;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  background-color: white;
}
.theme-light div[data-type='memos_view'] .menu-btns-popup:hover {
  display: flex;
}
.theme-light div[data-type='memos_view'] .menu-btns-popup > .btn {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  padding: 8px 4px;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 4px;
  text-align: left;
}
.theme-light div[data-type='memos_view'] .menu-btns-popup > .btn > .icon {
  display: block;
  width: 28px;
  text-align: center;
  margin-right: 4px;
  font-size: 14px;
}
.theme-light div[data-type='memos_view'] .menu-btns-popup > .btn:hover {
  background-color: #f8f8f8;
}
div[data-type='memos_view'].mobile-view .menu-btns-popup {
  margin-left: 64px;
}
.theme-dark div[data-type='memos_view'] .menu-btns-popup {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  margin-top: 4px;
  margin-left: 90px;
  padding: 4px;
  width: 180px;
  border-radius: 8px;
  z-index: 20;
  background-color: #000000;
}
.theme-dark div[data-type='memos_view'] .menu-btns-popup:hover {
  display: flex;
}
.theme-dark div[data-type='memos_view'] .menu-btns-popup > .btn {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  padding: 8px 4px;
  font-size: 14px;
  line-height: 1.6;
  border-radius: 4px;
  text-align: left;
}
.theme-dark div[data-type='memos_view'] .menu-btns-popup > .btn > .icon {
  display: block;
  width: 28px;
  text-align: center;
  margin-right: 4px;
  font-size: 14px;
}
.theme-dark div[data-type='memos_view'] .menu-btns-popup > .btn:hover {
  background-color: #808080;
}
.theme-dark div[data-type='memos_view'].mobile-view .menu-btns-popup {
  margin-left: 64px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .preview-image-dialog {
  padding: 0;
}
.theme-light .preview-image-dialog > .dialog-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  background-color: unset;
  padding: 0;
}
.theme-light .preview-image-dialog > .dialog-container > .close-btn {
  position: fixed;
  top: 36px;
  right: 36px;
  width: 36px;
  height: 36px;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #d8d8d8;
  z-index: 1;
}
.theme-light .preview-image-dialog > .dialog-container > .close-btn > .icon-img {
  width: 28px;
  height: 28px;
}
.theme-light .preview-image-dialog > .dialog-container > .close-btn:hover {
  opacity: 0.8;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  scrollbar-width: none;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar {
  display: none;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container > img {
  padding: 16px;
  height: auto;
  margin: auto;
}
.theme-light .preview-image-dialog > .dialog-container > .img-container > .loading-text {
  color: white;
  font-size: 24px;
  margin: auto;
  border-bottom: 2px solid white;
  padding: 8px 4px;
}
.theme-light .preview-image-dialog > .dialog-container > .action-btns-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 36px;
  z-index: 1;
}
.theme-light .preview-image-dialog > .dialog-container > .action-btns-container > .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 20px;
  margin-right: 16px;
  border-radius: 4px;
  background-color: lightgray;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
}
.theme-light .preview-image-dialog > .dialog-container > .action-btns-container > .btn:last-child {
  margin-right: 0;
}
.theme-light .preview-image-dialog > .dialog-container > .action-btns-container > .btn:hover,
.theme-light .preview-image-dialog > .dialog-container > .action-btns-container > .btn:active {
  opacity: 0.8;
}
@media only screen and (max-width: 875px) {
  .theme-light .preview-image-dialog {
    padding: 0;
  }
  .theme-light .preview-image-dialog > .dialog-container {
    max-width: 100%;
  }
  .theme-light .preview-image-dialog > .dialog-container > .img-container > img {
    padding: 6px;
  }
}
.theme-dark .preview-image-dialog {
  padding: 0;
}
.theme-dark .preview-image-dialog > .dialog-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  background-color: unset;
  padding: 0;
}
.theme-dark .preview-image-dialog > .dialog-container > .close-btn {
  position: fixed;
  top: 36px;
  right: 36px;
  width: 36px;
  height: 36px;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #c39999;
  z-index: 1;
}
.theme-dark .preview-image-dialog > .dialog-container > .close-btn > .icon-img {
  width: 28px;
  height: 28px;
  fill: #cdcdcd;
}
.theme-dark .preview-image-dialog > .dialog-container > .close-btn:hover {
  opacity: 0.8;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  scrollbar-width: none;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container::-webkit-scrollbar {
  display: none;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container > img {
  padding: 16px;
  height: auto;
  margin: auto;
}
.theme-dark .preview-image-dialog > .dialog-container > .img-container > .loading-text {
  color: #000000;
  font-size: 24px;
  margin: auto;
  border-bottom: 2px solid #000000;
  padding: 8px 4px;
}
.theme-dark .preview-image-dialog > .dialog-container > .action-btns-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 36px;
  z-index: 1;
}
.theme-dark .preview-image-dialog > .dialog-container > .action-btns-container > .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-size: 20px;
  margin-right: 16px;
  border-radius: 4px;
  background-color: #2c2c2c;
}
.theme-dark .preview-image-dialog > .dialog-container > .action-btns-container > .btn:last-child {
  margin-right: 0;
}
.theme-dark .preview-image-dialog > .dialog-container > .action-btns-container > .btn:hover,
.theme-dark .preview-image-dialog > .dialog-container > .action-btns-container > .btn:active {
  opacity: 0.8;
}
@media only screen and (max-width: 875px) {
  .theme-dark .preview-image-dialog {
    padding: 0;
  }
  .theme-dark .preview-image-dialog > .dialog-container {
    max-width: 100%;
  }
  .theme-dark .preview-image-dialog > .dialog-container > .img-container > img {
    padding: 6px;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container {
  padding: 0;
  background-color: transparent;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > * {
  flex-shrink: 0;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 512px;
  min-height: 64px;
  max-width: 100%;
  padding: 12px 24px;
  margin-bottom: 12px;
  border-radius: 8px;
  background-color: #fbf4de;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > * {
  z-index: 1;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: auto;
  padding-bottom: 0;
  margin-bottom: 0;
  margin-top: 0;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .time-text {
  font-size: 14px;
  color: gray;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn {
  background-color: transparent;
  padding: 0;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn:hover {
  background-color: white;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn > .icon-img {
  width: 20px;
  height: 20px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding-top: 8px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .memo-content-text {
  width: 100%;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
  padding-top: 4px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .memo-content-text .tag-span {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #5783f7;
  background-color: unset;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
  cursor: unset;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 2px;
  scrollbar-width: none;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img:hover {
  border-color: lightgray;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .normal-text {
  margin-top: 8px;
  font-size: 13px;
  color: gray;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .layer-container,
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .background-layer-container {
  position: absolute;
  bottom: -3px;
  left: 3px;
  width: calc(100% - 6px);
  height: 100%;
  border-radius: 8px;
  z-index: -1;
  background-color: #fbf4de;
  border-bottom: 1px solid lightgray;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .layer-container {
  z-index: 0;
  background-color: #fbf4de;
  border: 1px solid lightgray;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 512px;
  max-width: 100%;
  margin-top: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  background-color: white;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper:last-child {
  margin-bottom: 36px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .normal-text {
  font-size: 13px;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container {
  font-size: 13px;
  line-height: 24px;
  margin-top: 8px;
  cursor: pointer;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container:hover {
  opacity: 0.8;
}
.theme-light .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container > .time-text {
  color: gray;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] .dialog-wrapper.memo-card-dialog {
    padding: 24px 16px;
    padding-top: 64px;
  }
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container {
  padding: 0;
  background-color: transparent;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > * {
  flex-shrink: 0;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 512px;
  min-height: 64px;
  max-width: 100%;
  padding: 12px 24px;
  margin-bottom: 12px;
  border-radius: 8px;
  background-color: #5f5f5f;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > * {
  z-index: 1;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: auto;
  padding-bottom: 0;
  margin-bottom: 0;
  margin-top: 0;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .time-text {
  font-size: 14px;
  color: #a8a8a8;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn {
  background-color: transparent;
  padding: 0;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn:hover {
  background-color: #444444;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .header-container > .btns-container > .btn > .icon-img {
  width: 20px;
  height: 20px;
  fill: #cdcdcd;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding-top: 8px;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .memo-content-text {
  width: 100%;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
  padding-top: 4px;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .memo-content-text .tag-span {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #bbbec7;
  background-color: unset;
  cursor: unset;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 2px;
  scrollbar-width: none;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img:hover {
  border-color: #3a3a3a;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .memo-container > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .normal-text {
  margin-top: 8px;
  font-size: 13px;
  color: gray;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .layer-container,
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .background-layer-container {
  position: absolute;
  bottom: -3px;
  left: 3px;
  width: calc(100% - 6px);
  height: 100%;
  border-radius: 8px;
  z-index: -1;
  background-color: #5f5f5f;
  border-bottom: 1px solid #3f3f3f;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .memo-card-container > .layer-container {
  z-index: 0;
  background-color: #5f5f5f;
  border: 1px solid #3f3f3f;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 512px;
  max-width: 100%;
  margin-top: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  background-color: #000000;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper:last-child {
  margin-bottom: 36px;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .normal-text {
  font-size: 13px;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container {
  font-size: 13px;
  line-height: 24px;
  margin-top: 8px;
  cursor: pointer;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container:hover {
  opacity: 0.8;
}
.theme-dark .dialog-wrapper.memo-card-dialog > .dialog-container > .linked-memos-wrapper > .linked-memo-container > .time-text {
  color: #a8a8a8;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
@media only screen and (max-width: 875px) {
  .theme-dark div[data-type='memos_view'] .dialog-wrapper.memo-card-dialog {
    padding: 24px 16px;
    padding-top: 64px;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .image-container {
  width: 200px;
  height: auto;
  overflow-y: scroll;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  scrollbar-width: none;
}
div[data-type='memos_view'] .image-container::-webkit-scrollbar {
  width: 2px;
  height: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .image-container::-webkit-scrollbar-thumb {
  width: 2px;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
div[data-type='memos_view'] .image-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
div[data-type='memos_view'] .image-container > img {
  margin: auto;
  width: 100%;
  height: auto;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .share-memo-image-dialog > .dialog-container {
  width: 380px;
  padding: 0;
  background-color: #eaeaea;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-header-container {
  padding: 8px 16px;
  padding-left: 24px;
  margin-bottom: 0;
  background-color: white;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  font-family: 'RobotoDraft', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-header-container > .btn-group > .copy-btn {
  background-color: #ffffff;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-header-container > .btn-group > .close-btn {
  background-color: #ffffff;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  min-height: 128px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  border-bottom: 1px solid lightgray;
  background-color: white;
  padding: 0 24px;
  padding-bottom: 8px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container > .tip-text {
  color: gray;
  font-size: 13px;
  line-height: 24px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container.loading > .tip-text {
  animation: 1s linear 1s infinite alternate breathing;
}
@keyframes breathing {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.4;
  }
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 380px;
  max-width: 100%;
  height: auto;
  user-select: none;
  position: relative;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-shortcut-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: auto;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  margin-right: 15px;
  margin-bottom: 15px;
  margin-left: 12px;
  width: calc(100% - 24px);
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  -webkit-box-shadow: 15px 15px 27px #e1e1e3, -15px -15px 27px #ffffff;
  box-shadow: 15px 15px 27px #c1c1c1, -15px -15px 27px #e2e2e2;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .property-image {
  height: 6em;
  width: 100%;
  position: Absolute;
  top: 0px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .time-text {
  width: 100%;
  padding: 0 24px;
  padding-top: 20px;
  font-size: 12px;
  color: gray;
  background-color: white;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .background-container {
  height: 6em;
  background: transparent;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .memo-content-text {
  padding-right: 8%;
  padding-bottom: 12px;
  padding-left: 8%;
  width: 100%;
  word-wrap: break-word;
  font-size: 15px;
  background-color: white;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: auto;
  padding: 0 20px;
  padding-bottom: 8px;
  background-color: white;
  scrollbar-width: none;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar {
  display: none;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container > img {
  width: 100%;
  height: auto;
  margin-bottom: 8px;
  border-radius: 4px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background .tag-span {
  display: inline-block;
  /*   width: auto; */
  padding-left: 4px;
  padding-right: 6px;
  margin-left: -2px;
  line-height: 22px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  color: #5783f7;
  background-color: #eef3fe;
  cursor: pointer;
  vertical-align: bottom;
  text-align: left;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  padding: 16px 26px;
  background: white;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #ffffff;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start .property-social-icons {
  width: 1em;
  height: 1em;
  background-color: black;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start > .name-text {
  font-size: 13px;
  color: #37352f;
  margin-left: 8px;
  line-height: 20px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start > .icon-text {
  font-size: 15px;
  margin-right: 6px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  color: gray;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end > .name-text {
  font-size: 13px;
  color: #37352f;
  margin-left: 4px;
  line-height: 20px;
}
.theme-light .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end > .icon-text {
  font-size: 15px;
  margin-right: 6px;
}
@media only screen and (max-width: 875px) {
  .theme-light .dialog-wrapper.share-memo-image-dialog {
    padding: 24px 16px;
    padding-top: 64px;
    justify-content: unset;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog::-webkit-scrollbar {
    display: none;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .memo-background > .memo-content-text {
    padding-right: 8%;
    padding-bottom: 12px;
    padding-left: 8%;
    width: 100%;
    word-wrap: break-word;
    font-size: 15px;
    background-color: white;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
    padding: 16px 26px;
    background: white;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    font-size: 12px;
    line-height: 20px;
    color: #ffffff;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-start .property-social-icons {
    width: 1em;
    height: 1em;
    background-color: black;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-start > .name-text {
    font-size: 13px;
    color: #37352f;
    margin-left: 8px;
    margin-right: -15px;
    line-height: 20px;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-start > .icon-text {
    font-size: 15px;
    margin-right: 6px;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-end {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    font-size: 12px;
    line-height: 20px;
    color: gray;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-end > .name-text {
    font-size: 13px;
    color: #37352f;
    margin-left: 4px;
    line-height: 20px;
  }
  .theme-light .dialog-wrapper.share-memo-image-dialog .watermark-container > .normal-text.footer-end > .icon-text {
    font-size: 15px;
    margin-right: 6px;
  }
}
.theme-dark .share-memo-image-dialog > .dialog-container {
  width: 380px;
  padding: 0;
  background-color: #727171;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-header-container {
  padding: 8px 16px;
  padding-left: 24px;
  margin-bottom: 0;
  background-color: #000000;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-header-container > .btn-group > .close-btn {
  background-color: #000000;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-header-container > .btn-group > .close-btn > .icon-img {
  fill: #cdcdcd;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  min-height: 128px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  border-bottom: 1px solid #3a3a3a;
  background-color: #000000;
  padding: 0 24px;
  padding-bottom: 8px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container > .tip-text {
  color: #bdbdbd;
  font-size: 13px;
  line-height: 24px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .tip-words-container.loading > .tip-text {
  animation: 1s linear 1s infinite alternate breathing;
}
@keyframes breathing {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.4;
  }
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 380px;
  max-width: 100%;
  height: auto;
  user-select: none;
  position: relative;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-shortcut-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: auto;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  margin-right: 15px;
  margin-bottom: 15px;
  margin-left: 12px;
  width: calc(100% - 27px);
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  -webkit-box-shadow: 15px 15px 27px #666666, -15px -15px 27px #3a3a3a;
  box-shadow: 6px 4px 4px 2px #383333, 5px 2px 4px 2px #444444;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .property-image {
  height: 6em;
  width: 100%;
  position: Absolute;
  top: 0px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .time-text {
  width: 100%;
  padding: 0 24px;
  padding-top: 20px;
  font-size: 12px;
  color: #b9b9b9;
  background-color: #1f1f1f;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .background-container {
  height: 6em;
  background: transparent;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .memo-content-text {
  padding-right: 8%;
  padding-bottom: 12px;
  padding-left: 8%;
  width: 100%;
  word-wrap: break-word;
  font-size: 15px;
  background-color: #1f1f1f;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: auto;
  padding: 0 20px;
  padding-bottom: 8px;
  background-color: #1f1f1f;
  scrollbar-width: none;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container::-webkit-scrollbar {
  display: none;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .images-container > img {
  width: 100%;
  height: auto;
  margin-bottom: 8px;
  border-radius: 4px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background .tag-span {
  display: inline-block;
  /*   width: auto; */
  padding-left: 4px;
  padding-right: 6px;
  margin-left: -2px;
  line-height: 22px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  color: #d7e0f7;
  background-color: #555a65;
  cursor: pointer;
  vertical-align: bottom;
  text-align: left;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
  padding: 16px 26px;
  background-color: #000000;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #a1a1a1;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start .property-social-icons {
  width: 1em;
  height: 1em;
  background-color: black;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start > .name-text {
  font-size: 13px;
  color: #d2d1cd;
  margin-left: 8px;
  line-height: 20px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-start > .icon-text {
  font-size: 15px;
  margin-right: 6px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #a1a1a1;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end > .name-text {
  font-size: 13px;
  color: #d2d1cd;
  margin-left: 4px;
  line-height: 20px;
}
.theme-dark .share-memo-image-dialog > .dialog-container > .dialog-content-container > .memo-container > .memo-background > .watermark-container > .normal-text.footer-end > .icon-text {
  font-size: 15px;
  margin-right: 6px;
}
@media only screen and (max-width: 875px) {
  .theme-dark .dialog-wrapper.share-memo-image-dialog {
    padding: 24px 16px;
    padding-top: 64px;
    justify-content: unset;
  }
  .theme-dark .dialog-wrapper.share-memo-image-dialog::-webkit-scrollbar {
    display: none;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 400px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text > p,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text > p {
  display: inline-block;
  width: 100%;
  height: auto;
  margin-bottom: 4px;
  font-size: 15px;
  line-height: 24px;
  min-height: 24px;
  white-space: pre-wrap;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text > p > a,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text > p > a {
  width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .tag-span,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .tag-span {
  display: inline-block;
  width: auto;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
  line-height: 24px;
  font-size: 13px;
  border: none;
  border-radius: 4px;
  color: #5783f7;
  background-color: #eef3fe;
  cursor: pointer;
  vertical-align: bottom;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .tag-span:hover,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .tag-span:hover {
  background-color: #5783f7;
  color: white;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .memo-link-text,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .memo-link-text {
  display: inline-block;
  color: #5783f7;
  font-weight: bold;
  border-bottom: none;
  text-decoration: none;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .memo-link-text:hover,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .memo-link-text:hover {
  opacity: 0.8;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .counter-block,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .counter-block,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .todo-block,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .todo-block {
  display: inline-block;
  text-align: center;
  width: 1.4rem;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text pre,
.theme-light div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text pre {
  width: 100%;
  margin: 4px 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
  background-color: #f6f5f4;
  white-space: pre-wrap;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text > p {
  display: inline-block;
  width: 100%;
  height: auto;
  margin-bottom: 4px;
  margin-top: -2px;
  font-size: 15px;
  line-height: 24px;
  min-height: 24px;
  white-space: pre-wrap;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .tag-span {
  display: inline-block;
  width: auto;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
  line-height: 24px;
  font-size: 13px;
  border: none;
  border-radius: 4px;
  color: #5783f7;
  background-color: #eef3fe;
  cursor: pointer;
  vertical-align: bottom;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .tag-span:hover {
  background-color: #5783f7;
  color: white;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .memo-link-text {
  display: inline-block;
  color: #5783f7;
  font-weight: bold;
  border-bottom: none;
  text-decoration: none;
  cursor: pointer;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .memo-link-text:hover {
  opacity: 0.8;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .counter-block,
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text .todo-block {
  display: inline-block;
  text-align: center;
  width: 1.4rem;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light [data-type='memos_view'] .memolist-wrapper .memo-content-text pre {
  width: 100%;
  margin: 4px 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
  background-color: #f6f5f4;
  white-space: pre-wrap;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-content-text > p {
  font-size: 15px;
  line-height: 26px;
  min-height: 26px;
  white-space: pre-wrap;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-content-text .tag-span {
  line-height: 26px;
  font-size: 14px;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text > p {
  display: inline-block;
  width: 100%;
  height: auto;
  margin-bottom: 4px;
  font-size: 15px;
  line-height: 24px;
  min-height: 24px;
  white-space: pre-wrap;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .tag-span {
  display: inline-block;
  width: auto;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
  line-height: 24px;
  font-size: 13px;
  border: none;
  border-radius: 4px;
  color: #bbbec7;
  background-color: #616161;
  cursor: pointer;
  vertical-align: bottom;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .tag-span:hover {
  background-color: #bbbec7;
  color: #000000;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .memo-link-text {
  display: inline-block;
  color: #bbbec7;
  font-weight: bold;
  border-bottom: none;
  text-decoration: none;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .memo-link-text:hover {
  opacity: 0.8;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .counter-block,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text .todo-block {
  display: inline-block;
  text-align: center;
  width: 1.4rem;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-content-text pre {
  width: 100%;
  margin: 4px 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
  background-color: #0c0c0c;
  white-space: pre-wrap;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text > p,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text > p {
  display: inline-block;
  width: 100%;
  height: auto;
  margin-bottom: 4px;
  margin-top: -2px;
  font-size: 15px;
  line-height: 24px;
  min-height: 24px;
  white-space: pre-wrap;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .tag-span,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .tag-span {
  display: inline-block;
  width: auto;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
  line-height: 24px;
  font-size: 13px;
  border: none;
  border-radius: 4px;
  color: #bbbec7;
  background-color: #616161;
  cursor: pointer;
  vertical-align: bottom;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .tag-span:hover,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .tag-span:hover {
  background-color: #bbbec7;
  color: white;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .memo-link-text,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .memo-link-text {
  display: inline-block;
  color: #bbbec7;
  font-weight: bold;
  border-bottom: none;
  text-decoration: none;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .memo-link-text:hover,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .memo-link-text:hover {
  opacity: 0.8;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .counter-block,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .counter-block,
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text .todo-block,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text .todo-block {
  display: inline-block;
  text-align: center;
  width: 1.4rem;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper .memo-content-text pre,
.theme-dark div[data-type='memos_view'] .daily-memo-wrapper .memo-comment-text pre {
  width: 100%;
  margin: 4px 0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 15px;
  line-height: 1.5;
  background-color: #f6f5f4;
  white-space: pre-wrap;
}
.theme-dark div[data-type='memos_view'].mobile-view .memolist-wrapper .memo-content-text {
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .memolist-wrapper .memo-content-text > p {
  font-size: 15px;
  line-height: 26px;
  min-height: 26px;
  white-space: pre-wrap;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .memolist-wrapper .memo-content-text .tag-span {
  line-height: 26px;
  font-size: 14px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 12px 18px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #f1f1f1;
}
.theme-light div[data-type='memos_view'] .memo-wrapper:hover {
  border-color: #e4e4e4;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 24px;
  margin-bottom: 14px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper > .time-text {
  font-size: 12px;
  line-height: 24px;
  color: #a8a8a8;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper > .memo-type-img {
  width: 11%;
  height: 20px;
  margin-left: 3px;
  filter: opacity(0.9) invert(80%);
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .comment-button-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  width: 40%;
  height: 21px;
  margin-right: 4px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  flex-wrap: nowrap;
  top: calc(100% - 14px);
  right: -16px;
  width: auto;
  height: auto;
  padding: 12px;
  z-index: 1;
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper:hover {
  display: flex;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  width: 112px;
  height: auto;
  line-height: 18px;
  padding: 4px;
  white-space: nowrap;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn {
  width: 100%;
  padding: 8px 0 8px 24px;
  border-radius: 4px;
  height: unset;
  line-height: unset;
  justify-content: flex-start;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn {
  color: #d28653;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 28px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn:hover {
  background-color: #f8f8f8;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn {
  width: 28px;
  cursor: unset;
  margin-right: -6px;
  opacity: 0.8;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn > .icon-img {
  width: 16px;
  height: 16px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn:hover {
  background-color: unset;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn:hover + .more-action-btns-wrapper {
  display: flex;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-content-text {
  width: 100%;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 4px;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img:hover {
  border-color: lightgray;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper {
  width: 100%;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list {
  border-top: 1px solid #e4e4e4;
  margin-top: 8px;
  overflow-y: auto;
  max-height: 300px;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment .memo-comment-time {
  font-size: 12px;
  line-height: 24px;
  color: #a8a8a8;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment .memo-comment-text {
  width: 100%;
  font-size: 12px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper {
  border: 1px solid #e4e4e4;
  margin-top: 8px;
  border-radius: 8px;
  padding-bottom: 10px;
  padding-top: 8px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper > .common-editor-inputer {
  font-size: 12px;
}
.theme-light div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper .confirm-btn {
  transform: scale(0.9);
}
.theme-light div[data-type='memos_view'].mobile-view .memo-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  line-height: 0;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 12px 18px;
  background-color: #303030;
  border-radius: 8px;
  border: 1px solid #4a4a4a;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper:hover {
  border-color: #353535;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 24px;
  margin-bottom: 14px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper > .time-text {
  font-size: 12px;
  line-height: 24px;
  color: #a8a8a8;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-left-wrapper > .memo-type-img {
  width: 11%;
  height: 20px;
  margin-left: 3px;
  filter: invert(0.9);
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .comment-button-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  width: 40%;
  height: 21px;
  margin-right: 4px;
  fill: #cdcdcd;
  filter: invert(0.9);
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  flex-wrap: nowrap;
  top: calc(100% - 14px);
  right: -16px;
  width: auto;
  height: auto;
  padding: 12px;
  z-index: 1;
  display: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper:hover {
  display: flex;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  width: 112px;
  height: auto;
  line-height: 18px;
  padding: 4px;
  white-space: nowrap;
  border-radius: 8px;
  background-color: #181818;
  z-index: 1;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn {
  width: 100%;
  padding: 8px 0 8px 24px;
  border-radius: 4px;
  height: unset;
  line-height: unset;
  justify-content: flex-start;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn {
  color: #940b01;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 28px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn:hover {
  background-color: #808080;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn {
  width: 28px;
  cursor: unset;
  margin-right: -6px;
  opacity: 0.8;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn > .icon-img {
  width: 16px;
  height: 16px;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn:hover {
  background-color: unset;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-top-wrapper > .memo-top-right-wrapper > .btns-container .btn.more-action-btn:hover + .more-action-btns-wrapper {
  display: flex;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-content-text {
  width: 100%;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 4px;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img:hover {
  border-color: #444444;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper {
  width: 100%;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list {
  border-top: 1px solid #7a7a7a;
  margin-top: 8px;
  overflow-y: auto;
  max-height: 400px;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment .memo-comment-time {
  font-size: 12px;
  line-height: 24px;
  color: #a8a8a8;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-list .memo-comment .memo-comment-text {
  width: 100%;
  font-size: 12px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper {
  border: 1px solid #353535;
  margin-top: 8px;
  border-radius: 8px;
  padding-bottom: 10px;
  padding-top: 8px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper > .common-editor-inputer {
  font-size: 12px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper > .common-editor-inputer .textarea {
  margin-left: 5px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper > .memo-comment-inputer > .common-editor-wrapper > .common-editor-inputer .textarea:focus {
  margin-left: 8px;
  margin-right: 8px;
}
.theme-dark div[data-type='memos_view'] .memo-wrapper > .memo-comment-wrapper .confirm-btn {
  transform: scale(0.9);
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  line-height: 0;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: white;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer {
  display: inline-block;
  width: 100%;
  min-height: 24px;
  max-height: 300px;
  font-size: 15px;
  line-height: 24px;
  resize: none;
  overflow-x: hidden;
  background-color: transparent;
  z-index: 1;
  margin-bottom: 4px;
  white-space: pre-wrap;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::placeholder {
  padding-left: 2px;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer:focus::placeholder {
  color: lightgray;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .common-tools-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .action-btn {
  border: none;
  user-select: none;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 32px;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .action-btn:hover {
  opacity: 0.8;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .cancel-btn {
  color: gray;
  background-color: transparent;
  margin-right: 8px;
  line-height: 18px;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn {
  cursor: pointer;
  padding: 0 12px;
  background-color: #55bb8e;
  color: white;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.theme-light div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn > .icon-text {
  margin-left: 4px;
}
.theme-light div[data-type='memos_view'] .scroll::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: #3b3b3b;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer {
  display: inline-block;
  width: 100%;
  min-height: 24px;
  max-height: 300px;
  font-size: 15px;
  line-height: 24px;
  resize: none;
  overflow-x: hidden;
  background-color: transparent;
  z-index: 1;
  margin-bottom: 4px;
  white-space: pre-wrap;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer::placeholder {
  padding-left: 2px;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-editor-inputer:focus::placeholder {
  color: #363636;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .common-tools-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .common-tools-container > img {
  filter: invert(0.8);
  color: yellowgreen;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .action-btn {
  border: none;
  user-select: none;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 32px;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .action-btn > img {
  filter: invert(0.8);
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .action-btn:hover {
  opacity: 0.8;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .cancel-btn {
  color: gray;
  background-color: transparent;
  margin-right: 8px;
  line-height: 18px;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn {
  cursor: pointer;
  padding: 0 12px;
  background-color: #940b01;
  color: white;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
.theme-dark div[data-type='memos_view'] .common-editor-wrapper > .common-tools-wrapper > .btns-container > .confirm-btn > .icon-text {
  margin-left: 4px;
}
.theme-dark div[data-type='memos_view'] .scroll::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .memo-editor-wrapper .confirm-btn {
  margin-right: unset;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .rta {
  position: relative;
  font-size: 15px;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.theme-light .rta > ::-webkit-scrollbar {
  width: 2px;
  height: 16px;
  background-color: #f5f5f5;
}
.theme-light .rta > ::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
.theme-light .rta ::-webkit-scrollbar-thumb {
  background-color: #555;
}
.theme-light .rta ::-webkit-scrollbar-track-piece {
  background-color: #ffffff;
}
.theme-light .rta__loader.rta__loader--empty-suggestion-data {
  display: none;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(27, 31, 35, 0.1);
  padding: 5px;
}
.theme-light .rta--loading .rta__loader.rta__loader--suggestion-data {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
}
.theme-light .rta--loading .rta__loader.rta__loader--suggestion-data > * {
  display: none;
  position: relative;
  top: 50%;
}
.theme-light .rta__textarea {
  width: 100%;
  height: 100%;
  font-size: 1em;
}
.theme-light .rta__autocomplete {
  position: absolute;
  display: block;
  margin-top: 1em;
}
.theme-light .rta__autocomplete--top {
  margin-top: 0;
  margin-bottom: 1em;
}
.theme-light .rta__list {
  margin: 0;
  padding: 0;
  background: #fff;
  border: 1px solid #dfe2e5;
  border-radius: 0px;
  box-shadow: 0 0 10px rgba(27, 31, 35, 0.1);
  list-style: none;
}
.theme-light .rta__entity {
  background: white;
  width: 100%;
  text-align: left;
  outline: none;
}
.theme-light .rta__entity:hover {
  cursor: pointer;
}
.theme-light .rta__item {
  text-overflow: 'ellipsis';
  line-height: 30px;
}
.theme-light .rta__item:fisrt-child {
  border-radius: 8px 8px 0px 0px;
}
.theme-light .rta__item:not(:last-child) {
  border-bottom: 1px solid #f3f3f3;
}
.theme-light .rta__item:last-child {
  border-radius: 0px 0px 0px 0px;
}
.theme-light .rta__entity > * {
  padding-left: 4px;
  padding-right: 4px;
}
.theme-light .rta__entity--selected {
  color: #fff;
  text-decoration: none;
  background: #82af48;
}
.theme-dark .rta {
  position: relative;
  font-size: 15px;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.theme-dark .rta > ::-webkit-scrollbar {
  width: 2px;
  height: 16px;
  background-color: #0f0f0f;
}
.theme-dark .rta > ::-webkit-scrollbar-track {
  background-color: #000000;
}
.theme-dark .rta ::-webkit-scrollbar-thumb {
  background-color: #c9c9c9;
}
.theme-dark .rta ::-webkit-scrollbar-track-piece {
  background-color: #000000;
}
.theme-dark .rta__loader.rta__loader--empty-suggestion-data {
  display: none;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(27, 31, 35, 0.1);
  padding: 5px;
}
.theme-dark .rta--loading .rta__loader.rta__loader--suggestion-data {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
}
.theme-dark .rta--loading .rta__loader.rta__loader--suggestion-data > * {
  display: none;
  position: relative;
  top: 50%;
}
.theme-dark .rta__textarea {
  width: 100%;
  height: 100%;
  font-size: 1em;
}
.theme-dark .rta__autocomplete {
  position: absolute;
  display: block;
  margin-top: 1em;
}
.theme-dark .rta__autocomplete--top {
  margin-top: 0;
  margin-bottom: 1em;
}
.theme-dark .rta__list {
  margin: 0;
  padding: 0;
  background: #ffffff;
  border: 1px solid #474747;
  border-radius: 0px;
  list-style: none;
}
.theme-dark .rta__entity {
  color: #d2d1cd;
  background: #000000;
  width: 100%;
  text-align: left;
  outline: none;
}
.theme-dark .rta__entity:hover {
  cursor: pointer;
}
.theme-dark .rta__item {
  text-overflow: 'ellipsis';
  line-height: 30px;
}
.theme-dark .rta__item:fisrt-child {
  border-radius: 8px 8px 0px 0px;
}
.theme-dark .rta__item:not(:last-child) {
  border-bottom: 1px solid #141414;
}
.theme-dark .rta__item:last-child {
  border-radius: 0px 0px 0px 0px;
}
.theme-dark .rta__entity > * {
  padding-left: 4px;
  padding-right: 4px;
}
.theme-dark .rta__entity--selected {
  color: #000000;
  text-decoration: none;
  background: #af487b;
}
.theme-light div[data-type='memos_view'].mobile-view .rta__textarea {
  max-height: 140px;
  overflow-y: scroll;
}
.theme-dark div[data-type='memos_view'].mobile-view .rta__textarea {
  max-height: 140px;
  overflow-y: scroll;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .daily-memo-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: calc(100% - 24px);
  margin-left: 24px;
  padding: 0;
  padding-bottom: 24px;
  border: none;
  border-left: 2px solid #f8f8f8;
}
.theme-light .daily-memo-wrapper:last-child {
  border-left: none;
  padding-bottom: 0;
}
.theme-light .daily-memo-wrapper > .time-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  left: -24px;
  margin-top: -2px;
  flex-shrink: 0;
  width: 48px;
  height: 28px;
  border-radius: 6px;
  background-color: #eaeaea;
  color: #52504b;
  border: 2px solid white;
}
.theme-light .daily-memo-wrapper > .time-wrapper > .normal-text {
  margin: 0 auto;
  font-size: 11px;
  line-height: 24px;
}
.theme-light .daily-memo-wrapper > .memo-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  margin-left: -12px;
  padding: 0;
  font-size: 16px;
  margin-top: -3px;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .memo-content-text {
  margin-top: -14px;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .memo-content-text p > a {
  width: 20em;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .memo-content-text .tag-span {
  cursor: unset;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .memo-content-text .tag-span:hover {
  color: #5783f7;
  background-color: #eef3fe;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .images-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .images-container > img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  margin-bottom: 8px;
  margin-top: 16px;
}
.theme-light .daily-memo-wrapper > .memo-content-container > .images-container > img:last-child {
  margin-bottom: 0;
}
.theme-dark .daily-memo-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: calc(100% - 24px);
  margin-left: 24px;
  padding: 0;
  padding-bottom: 24px;
  border: none;
  border-left: 2px solid #808080;
}
.theme-dark .daily-memo-wrapper:last-child {
  border-left: none;
  padding-bottom: 0;
}
.theme-dark .daily-memo-wrapper > .time-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  left: -24px;
  margin-top: -2px;
  flex-shrink: 0;
  width: 48px;
  height: 28px;
  border-radius: 6px;
  background-color: #727171;
  color: #c7c4bb;
  border: 2px solid #727171;
}
.theme-dark .daily-memo-wrapper > .time-wrapper > .normal-text {
  margin: 0 auto;
  font-size: 11px;
  line-height: 24px;
}
.theme-dark .daily-memo-wrapper > .memo-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  margin-left: -12px;
  padding: 0;
  font-size: 16px;
  margin-top: -3px;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .memo-content-text {
  margin-top: -14px;
  text-overflow: clip;
  -webkit-line-clamp: 2;
  /*限制在一个块元素显示的文本的行数*/
  -webkit-box-orient: vertical;
  overflow: hidden;
  width: 80%;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  -webkit-text-overflow: ellipsis;
  -moz-text-overflow: ellipsis;
  white-space: nowrap;
  /*规定段落中的文本不进行换行*/
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .memo-content-text p > a.link {
  width: 20em;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .memo-content-text .tag-span {
  cursor: unset;
  padding-left: 4px;
  padding-right: 6px;
  margin-left: 4px;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .memo-content-text .tag-span:hover {
  color: #bbbec7;
  background-color: #616161;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .images-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .images-container > img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  margin-bottom: 8px;
  margin-top: 16px;
}
.theme-dark .daily-memo-wrapper > .memo-content-container > .images-container > img:last-child {
  margin-bottom: 0;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .date-picker-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 16px;
}
.theme-light .date-picker-wrapper > .date-picker-header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.theme-light .date-picker-wrapper > .date-picker-header > .btn-text {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-light .date-picker-wrapper > .date-picker-header > .btn-text > .icon-img {
  width: 100%;
  height: auto;
}
.theme-light .date-picker-wrapper > .date-picker-header > .btn-text:hover {
  background-color: #f8f8f8;
}
.theme-light .date-picker-wrapper > .date-picker-header > .normal-text {
  margin: 0 4px;
  line-height: 24px;
}
.theme-light .date-picker-wrapper > .date-picker-day-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 280px;
  flex-wrap: wrap;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .date-picker-day-header {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .date-picker-day-header > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  user-select: none;
  color: gray;
  font-size: 13px;
  margin: 2px 0;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 14px;
  user-select: none;
  cursor: pointer;
  margin: 2px;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .day-item:hover {
  background-color: #f8f8f8;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .day-item.current {
  background-color: #eef3fe;
  font-size: 16px;
  color: #5783f7;
  font-weight: bold;
}
.theme-light .date-picker-wrapper > .date-picker-day-container > .day-item.null {
  background-color: unset;
  cursor: unset;
}
.theme-light .editor-date-picker {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: unset;
  background-color: white;
  border: 1px dashed #2f3437;
}
.theme-light .editor-date-picker > .date-picker-header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.theme-light .editor-date-picker > .date-picker-header > .btn-text {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-light .editor-date-picker > .date-picker-header > .btn-text > .icon-img {
  width: 100%;
  height: auto;
}
.theme-light .editor-date-picker > .date-picker-header > .btn-text:hover {
  background-color: #f8f8f8;
}
.theme-light .editor-date-picker > .date-picker-header > .normal-text {
  margin: 0 4px;
  line-height: 24px;
}
.theme-light .editor-date-picker > .date-picker-day-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 280px;
  flex-wrap: wrap;
}
.theme-light .editor-date-picker > .date-picker-day-container > .date-picker-day-header {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.theme-light .editor-date-picker > .date-picker-day-container > .date-picker-day-header > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  user-select: none;
  color: gray;
  font-size: 13px;
  margin: 2px 0;
}
.theme-light .editor-date-picker > .date-picker-day-container > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 14px;
  user-select: none;
  cursor: pointer;
  margin: 2px;
}
.theme-light .editor-date-picker > .date-picker-day-container > .day-item:hover {
  background-color: #f8f8f8;
}
.theme-light .editor-date-picker > .date-picker-day-container > .day-item.current {
  background-color: #eef3fe;
  font-size: 16px;
  color: #5783f7;
  font-weight: bold;
}
.theme-light .editor-date-picker > .date-picker-day-container > .day-item.null {
  background-color: unset;
  cursor: unset;
}
.theme-dark .date-picker-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 16px;
}
.theme-dark .date-picker-wrapper > .date-picker-header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.theme-dark .date-picker-wrapper > .date-picker-header > .btn-text {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-dark .date-picker-wrapper > .date-picker-header > .btn-text > .icon-img {
  width: 100%;
  height: auto;
  fill: #cdcdcd;
}
.theme-dark .date-picker-wrapper > .date-picker-header > .btn-text:hover {
  background-color: #808080;
}
.theme-dark .date-picker-wrapper > .date-picker-header > .normal-text {
  margin: 0 4px;
  line-height: 24px;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 280px;
  flex-wrap: wrap;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .date-picker-day-header {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .date-picker-day-header > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  user-select: none;
  color: #b8b8b8;
  font-size: 13px;
  margin: 2px 0;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 14px;
  user-select: none;
  cursor: pointer;
  margin: 2px;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .day-item:hover {
  background-color: #808080;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .day-item.current {
  background-color: #616161;
  font-size: 16px;
  color: #5783f7;
  font-weight: bold;
}
.theme-dark .date-picker-wrapper > .date-picker-day-container > .day-item.null {
  background-color: unset;
  cursor: unset;
}
.theme-dark .editor-date-picker {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: unset;
  background-color: #2f3437;
  border: 1px dashed #cacdcf;
}
.theme-dark .editor-date-picker > .date-picker-header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.theme-dark .editor-date-picker > .date-picker-header > .btn-text {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-dark .editor-date-picker > .date-picker-header > .btn-text > .icon-img {
  width: 100%;
  height: auto;
  fill: #cdcdcd;
}
.theme-dark .editor-date-picker > .date-picker-header > .btn-text:hover {
  background-color: #808080;
}
.theme-dark .editor-date-picker > .date-picker-header > .normal-text {
  margin: 0 4px;
  line-height: 24px;
}
.theme-dark .editor-date-picker > .date-picker-day-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 280px;
  flex-wrap: wrap;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .date-picker-day-header {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .date-picker-day-header > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  user-select: none;
  color: #b8b8b8;
  font-size: 13px;
  margin: 2px 0;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .day-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 14px;
  user-select: none;
  cursor: pointer;
  margin: 2px;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .day-item:hover {
  background-color: #808080;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .day-item.current {
  background-color: #616161;
  font-size: 16px;
  color: #5783f7;
  font-weight: bold;
}
.theme-dark .editor-date-picker > .date-picker-day-container > .day-item.null {
  background-color: unset;
  cursor: unset;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .daily-memo-diary-dialog > .dialog-container {
  width: 440px;
  max-width: 100%;
  padding: 0;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  padding: 24px;
  margin-bottom: 0;
  padding-bottom: 0;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text:last-child {
  margin-right: 0;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text > .icon-img {
  width: 100%;
  height: auto;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text:hover {
  background-color: lightgray;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text.share-btn {
  padding: 2px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 440px;
  max-width: 100%;
  height: auto;
  padding: 24px 24px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: auto;
  padding-bottom: 24px;
  z-index: 1;
  user-select: none;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .year-text {
  margin: auto;
  font-weight: bold;
  color: gray;
  text-align: center;
  line-height: 24px;
  margin-bottom: 12px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: auto;
  width: 96px;
  height: 96px;
  border-radius: 32px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  z-index: 1;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .month-text,
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .day-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 24px;
  font-size: 14px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .month-text {
  background-color: #1337a3;
  color: white;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .date-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding-top: 4px;
  height: 48px;
  font-size: 44px;
  font-weight: bold;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .day-text {
  font-size: 12px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-picker {
  margin: 0 auto;
  border: 1px solid lightgray;
  border-radius: 8px;
  margin-bottom: 24px;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .tip-container {
  margin: auto;
  padding: 16px 0;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .tip-container > .tip-text {
  font-style: italic;
}
.theme-light .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .dailymemos-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
}
@media only screen and (max-width: 875px) {
  .theme-light .dialog-wrapper.daily-memo-diary-dialog {
    padding: 0;
    scrollbar-width: none;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar {
    width: 0;
    height: 0;
    cursor: pointer;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar-thumb {
    width: 0;
    height: 0;
    border-radius: 8px;
    background-color: #d5d5d5;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar {
    display: none;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog > .dialog-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 16px;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog > .dialog-container > .dialog-header-container {
    padding-top: 32px;
  }
  .theme-light .dialog-wrapper.daily-memo-diary-dialog > .dialog-container::-webkit-scrollbar {
    display: none;
  }
}
.theme-dark .daily-memo-diary-dialog > .dialog-container {
  width: 440px;
  max-width: 100%;
  padding: 0;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  padding: 24px;
  margin-bottom: 0;
  padding-bottom: 0;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text:last-child {
  margin-right: 0;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text > .icon-img {
  width: 100%;
  height: auto;
  fill: #cdcdcd;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text:hover {
  background-color: #383838;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-header-container > .header-wrapper > .btns-container > .btn-text.share-btn {
  padding: 2px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 440px;
  max-width: 100%;
  height: auto;
  padding: 24px 24px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: auto;
  padding-bottom: 24px;
  z-index: 1;
  user-select: none;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .year-text {
  margin: auto;
  font-weight: bold;
  color: #a1a1a1;
  text-align: center;
  line-height: 24px;
  margin-bottom: 12px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: auto;
  width: 96px;
  height: 96px;
  border-radius: 32px;
  box-shadow: 0 0 8px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #d2d1cd;
  color: black;
  text-align: center;
  z-index: 1;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .month-text,
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .day-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 24px;
  font-size: 14px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .month-text {
  background-color: #2c395a;
  color: white;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;
  margin-top: -1px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .date-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding-top: 4px;
  height: 48px;
  font-size: 44px;
  font-weight: bold;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-card-container > .date-container > .day-text {
  font-size: 12px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .date-picker {
  margin: 0 auto;
  border: 1px solid #333333;
  border-radius: 8px;
  margin-bottom: 24px;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .tip-container {
  margin: auto;
  padding: 16px 0;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .tip-container > .tip-text {
  font-style: italic;
}
.theme-dark .daily-memo-diary-dialog > .dialog-container > .dialog-content-container > .dailymemos-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
}
@media only screen and (max-width: 875px) {
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog {
    padding: 0;
    scrollbar-width: none;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar {
    width: 0;
    height: 0;
    cursor: pointer;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar-thumb {
    width: 0;
    height: 0;
    border-radius: 8px;
    background-color: #d5d5d5;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar-thumb:hover {
    background-color: #ccc;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog::-webkit-scrollbar {
    display: none;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog > .dialog-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 16px;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog > .dialog-container > .dialog-header-container {
    padding-top: 32px;
  }
  .theme-dark .dialog-wrapper.daily-memo-diary-dialog > .dialog-container::-webkit-scrollbar {
    display: none;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .user-banner-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 160px;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  flex-wrap: nowrap;
  margin-bottom: 4px;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .username-text {
  max-width: calc(100% - 32px);
  font-weight: bold;
  font-size: 18px;
  line-height: 36px;
  color: #37352f;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  margin-right: auto;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn {
  flex-shrink: 0;
  user-select: none;
  border: none;
  background-color: unset;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn.menu-popup-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 40px;
  margin-right: -8px;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn.menu-popup-btn > .icon-img {
  width: 20px;
  height: auto;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 24px;
  width: 100%;
  user-select: none;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .amount-text {
  font-weight: bold;
  font-size: 28px;
  line-height: 1.8;
  color: #37352f;
  opacity: 0.8;
}
.theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .type-text {
  color: gray;
  font-size: 12px;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] .user-banner-container {
    height: 170px;
    z-index: 1;
    padding-top: 16px !important;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container {
    padding: 0 16px;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn {
    width: 60px;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .username-text {
    font-size: 22px;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container {
    padding: 0 16px;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .amount-text {
    font-size: 32px;
  }
  .theme-light div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .type-text {
    font-size: 14px;
  }
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container {
  height: 170px;
  z-index: 1;
  padding-top: 16px !important;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container {
  padding: 0 16px;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container > .action-btn {
  width: 60px;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container > .username-text {
  font-size: 22px;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container {
  padding: 0 16px;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container > .status-text > .amount-text {
  font-size: 32px;
}
.theme-light div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container > .status-text > .type-text {
  font-size: 14px;
}
.theme-dark div[data-type='memos_view'] .user-banner-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 160px;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  flex-wrap: nowrap;
  margin-bottom: 4px;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .username-text {
  max-width: calc(100% - 32px);
  font-weight: bold;
  font-size: 18px;
  line-height: 36px;
  color: #d2d1cd;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  margin-right: auto;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn {
  flex-shrink: 0;
  user-select: none;
  border: none;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn.menu-popup-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 40px;
  margin-right: -8px;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn.menu-popup-btn > .icon-img {
  width: 20px;
  height: auto;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 24px;
  width: 100%;
  user-select: none;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .amount-text {
  font-weight: bold;
  font-size: 28px;
  line-height: 1.8;
  color: #d2d1cd;
  opacity: 0.8;
}
.theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .type-text {
  color: #ececec;
  font-size: 12px;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
@media only screen and (max-width: 875px) {
  .theme-dark div[data-type='memos_view'] .user-banner-container {
    height: 170px;
    z-index: 1;
    padding-top: 16px !important;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container {
    padding: 0 16px;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .action-btn {
    width: 60px;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .userinfo-header-container > .username-text {
    font-size: 22px;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container {
    padding: 0 16px;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .amount-text {
    font-size: 32px;
  }
  .theme-dark div[data-type='memos_view'] .user-banner-container > .status-text-container > .status-text > .type-text {
    font-size: 14px;
  }
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container {
  height: 170px;
  z-index: 1;
  padding-top: 16px !important;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container {
  padding: 0 16px;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container > .action-btn {
  width: 60px;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .userinfo-header-container > .username-text {
  font-size: 22px;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container {
  padding: 0 16px;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container > .status-text > .amount-text {
  font-size: 32px;
}
.theme-dark div[data-type='memos_view'].mobile-view .user-banner-container > .status-text-container > .status-text > .type-text {
  font-size: 14px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .selector-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  height: 28px;
}
.theme-light .selector-wrapper > .current-value-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  border: 1px solid #e4e4e4;
  border-radius: 4px;
  padding: 0 8px;
  padding-right: 4px;
  background-color: white;
  cursor: pointer;
  user-select: none;
}
.theme-light .selector-wrapper > .current-value-container:hover,
.theme-light .selector-wrapper > .current-value-container.active {
  background-color: #f8f8f8;
}
.theme-light .selector-wrapper > .current-value-container > .value-text {
  margin-right: 0px;
  font-size: 13px;
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: calc(100% - 20px);
}
.theme-light .selector-wrapper > .current-value-container > .arrow-text {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 16px;
  flex-shrink: 0;
}
.theme-light .selector-wrapper > .current-value-container > .arrow-text > .icon-img {
  width: 16px;
  height: auto;
  opacity: 0.6;
  transform: rotate(90deg);
}
.theme-light .selector-wrapper > .items-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  top: 100%;
  left: 0;
  width: auto;
  min-width: calc(100% + 16px);
  max-height: 256px;
  padding: 4px;
  overflow: auto;
  margin-top: 2px;
  margin-left: -8px;
  z-index: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  scrollbar-width: none;
}
.theme-light .selector-wrapper > .items-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .selector-wrapper > .items-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .selector-wrapper > .items-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .selector-wrapper > .items-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light .selector-wrapper > .items-wrapper > .item-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding-left: 12px;
  line-height: 30px;
  white-space: nowrap;
  font-size: 13px;
  cursor: pointer;
  border-radius: 4px;
  user-select: none;
}
.theme-light .selector-wrapper > .items-wrapper > .item-container:hover {
  background-color: #f8f8f8;
}
.theme-light .selector-wrapper > .items-wrapper > .item-container.selected {
  color: #55bb8e;
}
.theme-dark .selector-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  height: 28px;
}
.theme-dark .selector-wrapper > .current-value-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  border: 1px solid #353535;
  border-radius: 4px;
  padding: 0 8px;
  padding-right: 4px;
  background-color: #000000;
  cursor: pointer;
  user-select: none;
}
.theme-dark .selector-wrapper > .current-value-container:hover,
.theme-dark .selector-wrapper > .current-value-container.active {
  background-color: #808080;
}
.theme-dark .selector-wrapper > .current-value-container > .value-text {
  margin-right: 0px;
  font-size: 13px;
  line-height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: calc(100% - 20px);
}
.theme-dark .selector-wrapper > .current-value-container > .arrow-text {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 16px;
  flex-shrink: 0;
}
.theme-dark .selector-wrapper > .current-value-container > .arrow-text > .icon-img {
  width: 16px;
  height: auto;
  opacity: 0.6;
  transform: rotate(90deg);
  fill: #cdcdcd;
}
.theme-dark .selector-wrapper > .items-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  top: 100%;
  left: 0;
  width: auto;
  min-width: calc(100% + 16px);
  max-height: 256px;
  padding: 4px;
  overflow: auto;
  margin-top: 2px;
  margin-left: -8px;
  z-index: 1;
  background-color: #000000;
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(255, 255, 255, 0.2);
  scrollbar-width: none;
}
.theme-dark .selector-wrapper > .items-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .selector-wrapper > .items-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .selector-wrapper > .items-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .selector-wrapper > .items-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark .selector-wrapper > .items-wrapper > .item-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding-left: 12px;
  line-height: 30px;
  white-space: nowrap;
  font-size: 13px;
  cursor: pointer;
  border-radius: 4px;
  user-select: none;
}
.theme-dark .selector-wrapper > .items-wrapper > .item-container:hover {
  background-color: #808080;
}
.theme-dark .selector-wrapper > .items-wrapper > .item-container.selected {
  color: #d24c42;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .create-query-dialog > .dialog-container {
  width: 420px;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  margin-top: 8px;
  padding: 4px 0;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .normal-text {
  display: block;
  flex-shrink: 0;
  width: 40px;
  margin-right: 12px;
  text-align: right;
  color: gray;
  font-size: 13px;
  line-height: 32px;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .title-input {
  width: 100%;
  padding: 0 8px;
  font-size: 13px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
  resize: none;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .filters-wrapper {
  width: calc(100% - 56px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .filters-wrapper > .create-filter-btn {
  color: #55bb8e;
  font-size: 13px;
  line-height: 32px;
  cursor: pointer;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 0;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .tip-text {
  font-size: 13px;
  color: gray;
  margin-right: 8px;
  white-space: nowrap;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn {
  padding: 6px 16px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn:hover {
  opacity: 0.8;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.disabled {
  color: lightgray;
  cursor: not-allowed;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.save-btn {
  background-color: #55bb8e;
  color: white;
}
.theme-light .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.save-btn.requesting {
  cursor: wait;
  opacity: 0.8;
}
.theme-light .memo-filter-input-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  flex-shrink: 0;
}
.theme-light .memo-filter-input-wrapper:first-of-type {
  margin-top: 0;
}
.theme-light .memo-filter-input-wrapper > .selector-wrapper {
  margin-right: 4px;
  height: 34px;
  flex-grow: 0;
  flex-shrink: 0;
}
.theme-light .memo-filter-input-wrapper > .selector-wrapper.relation-selector {
  width: 48px;
  margin-left: -52px;
}
.theme-light .memo-filter-input-wrapper > .selector-wrapper.type-selector {
  width: 62px;
}
.theme-light .memo-filter-input-wrapper > .selector-wrapper.operator-selector {
  width: 62px;
}
.theme-light .memo-filter-input-wrapper > .selector-wrapper.value-selector {
  flex-grow: 1;
  max-width: calc(100% - 152px);
}
.theme-light .memo-filter-input-wrapper > input.value-inputer {
  max-width: calc(100% - 152px);
  height: 34px;
  padding: 0 8px;
  flex-shrink: 0;
  flex-grow: 1;
  margin-right: 4px;
  border-radius: 4px;
  border: 1px solid #e4e4e4;
  background-color: transparent;
}
.theme-light .memo-filter-input-wrapper > input.value-inputer:hover {
  background-color: #f8f8f8;
}
.theme-light .memo-filter-input-wrapper > .remove-btn {
  width: 16px;
  height: auto;
  cursor: pointer;
  opacity: 0.8;
}
.theme-light .memo-filter-input-wrapper > .remove-btn:hover {
  opacity: 0.6;
}
@media only screen and (max-width: 875px) {
  .theme-light .dialog-wrapper.create-query-dialog {
    padding: 24px 16px;
    padding-top: 64px;
    justify-content: unset;
    overflow-x: hidden;
  }
  .theme-light .dialog-wrapper.create-query-dialog::-webkit-scrollbar {
    display: none;
  }
}
.theme-dark .create-query-dialog > .dialog-container {
  width: 420px;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  margin-top: 8px;
  padding: 4px 0;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .normal-text {
  display: block;
  flex-shrink: 0;
  width: 40px;
  margin-right: 12px;
  text-align: right;
  color: #afafaf;
  font-size: 13px;
  line-height: 32px;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .title-input {
  width: 100%;
  padding: 0 8px;
  font-size: 13px;
  line-height: 32px;
  border-radius: 4px;
  border: 1px solid #353535;
  resize: none;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .filters-wrapper {
  width: calc(100% - 56px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-content-container > .form-item-container > .filters-wrapper > .create-filter-btn {
  color: #d24c42;
  font-size: 13px;
  line-height: 32px;
  cursor: pointer;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 0;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .tip-text {
  font-size: 13px;
  color: #bbbbbb;
  margin-right: 8px;
  white-space: nowrap;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn {
  padding: 6px 16px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn:hover {
  opacity: 0.8;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.disabled {
  color: #303030;
  cursor: not-allowed;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.save-btn {
  background-color: #940b01;
  color: #000000;
}
.theme-dark .create-query-dialog > .dialog-container > .dialog-footer-container > .btns-container > .btn.save-btn.requesting {
  cursor: wait;
  opacity: 0.8;
}
.theme-dark .memo-filter-input-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  flex-shrink: 0;
}
.theme-dark .memo-filter-input-wrapper:first-of-type {
  margin-top: 0;
}
.theme-dark .memo-filter-input-wrapper > .selector-wrapper {
  margin-right: 4px;
  height: 34px;
  flex-grow: 0;
  flex-shrink: 0;
}
.theme-dark .memo-filter-input-wrapper > .selector-wrapper.relation-selector {
  width: 48px;
  margin-left: -52px;
}
.theme-dark .memo-filter-input-wrapper > .selector-wrapper.type-selector {
  width: 62px;
}
.theme-dark .memo-filter-input-wrapper > .selector-wrapper.operator-selector {
  width: 62px;
}
.theme-dark .memo-filter-input-wrapper > .selector-wrapper.value-selector {
  flex-grow: 1;
  max-width: calc(100% - 152px);
}
.theme-dark .memo-filter-input-wrapper > input.value-inputer {
  max-width: calc(100% - 152px);
  height: 34px;
  padding: 0 8px;
  flex-shrink: 0;
  flex-grow: 1;
  margin-right: 4px;
  border-radius: 4px;
  border: 1px solid #353535;
  background-color: transparent;
}
.theme-dark .memo-filter-input-wrapper > input.value-inputer:hover {
  background-color: #808080;
}
.theme-dark .memo-filter-input-wrapper > .remove-btn {
  width: 16px;
  height: auto;
  cursor: pointer;
  opacity: 0.8;
  filter: invert(0.8);
}
.theme-dark .memo-filter-input-wrapper > .remove-btn:hover {
  opacity: 0.6;
}
@media only screen and (max-width: 875px) {
  .theme-dark .dialog-wrapper.create-query-dialog {
    padding: 24px 16px;
    padding-top: 64px;
    justify-content: unset;
    overflow-x: hidden;
  }
  .theme-dark .dialog-wrapper.create-query-dialog::-webkit-scrollbar {
    display: none;
  }
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .queries-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 0 8px;
  height: auto;
  flex-wrap: nowrap;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .queries-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .queries-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .queries-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .queries-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 16px;
  margin-bottom: 4px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .title-text > * {
  font-size: 12px;
  line-height: 24px;
  color: #37352f;
  opacity: 0.5;
  font-weight: bold;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .title-text > .btn {
  display: none;
  padding: 0 4px;
  font-size: 18px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .title-text:hover > .btn,
.theme-light div[data-type='memos_view'] .queries-wrapper > .title-text:active > .btn {
  display: block;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .create-query-btn-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  margin-bottom: 12px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .create-query-btn-container > .btn {
  display: flex;
  padding: 4px 8px;
  border: 1px dashed #1337a3;
  border-radius: 8px;
  font-size: 13px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .create-query-btn-container > .btn:hover {
  background-color: #1337a3;
  color: white;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  flex-wrap: nowrap;
  margin-bottom: 8px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  margin-top: 4px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container:hover {
  background-color: #e4e4e4;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container:hover > .btns-container {
  display: flex;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active {
  background-color: #55bb8e !important;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active > .query-text-container {
  font-weight: bold;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active > .query-text-container > * {
  color: white;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  max-width: calc(100% - 24px);
  color: #37352f;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 20px;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container > .icon-text {
  display: block;
  width: 16px;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container > .query-text {
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  display: none;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btn > .icon-img {
  width: 18px;
  height: auto;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  right: 0;
  width: auto;
  height: auto;
  padding: 8px;
  transform: translateY(60px);
  z-index: 1;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 86px;
  height: auto;
  white-space: nowrap;
  border-radius: 6px;
  padding: 4px;
  background-color: white;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn {
  width: 100%;
  padding: 6px 0;
  padding-left: 12px;
  border-radius: 4px;
  font-size: 13px;
  height: unset;
  line-height: unset;
  text-align: left;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn:hover {
  background-color: #f8f8f8;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn.delete-btn {
  color: #d28653;
}
.theme-light div[data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-light div[data-type='memos_view'].mobile-view .queries-container {
  height: auto;
}
.theme-light div[data-type='memos_view'].mobile-view .queries-container:last-child {
  flex-grow: 1;
}
.theme-light div[data-type='memos_view'].mobile-view .queries-container > .title-text {
  font-size: 13px;
  margin-bottom: 4px;
}
.theme-light div[data-type='memos_view'].mobile-view .queries-container > .query-item-container {
  font-size: 15px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 0 8px;
  height: auto;
  flex-wrap: nowrap;
  scrollbar-width: none;
}
.theme-dark [data-type='memos_view'] .queries-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark [data-type='memos_view'] .queries-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark [data-type='memos_view'] .queries-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark [data-type='memos_view'] .queries-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 16px;
  margin-bottom: 4px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .title-text > * {
  font-size: 12px;
  line-height: 24px;
  color: #d2d1cd;
  opacity: 0.5;
  font-weight: bold;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .title-text > .btn {
  display: none;
  padding: 0 4px;
  font-size: 18px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .title-text:hover > .btn,
.theme-dark [data-type='memos_view'] .queries-wrapper > .title-text:active > .btn {
  display: block;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .create-query-btn-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 8px;
  margin-bottom: 12px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .create-query-btn-container > .btn {
  display: flex;
  padding: 4px 8px;
  border: 1px dashed #2c395a;
  border-radius: 8px;
  font-size: 13px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .create-query-btn-container > .btn:hover {
  background-color: #2c395a;
  color: white;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  flex-wrap: nowrap;
  margin-bottom: 8px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  margin-top: 4px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container:hover {
  background-color: #353535;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container:hover > .btns-container {
  display: flex;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active {
  background-color: #940b01 !important;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active > .query-text-container {
  font-weight: bold;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container.active > .query-text-container > * {
  color: #727272;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  max-width: calc(100% - 24px);
  color: #37352f;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 20px;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container > .icon-text {
  display: block;
  width: 16px;
  flex-shrink: 0;
  color: #d2d1cd;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .query-text-container > .query-text {
  flex-shrink: 0;
  color: #d2d1cd;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  display: none;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btn > .icon-img {
  width: 18px;
  height: auto;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: absolute;
  right: 0;
  width: auto;
  height: auto;
  padding: 8px;
  transform: translateY(60px);
  z-index: 1;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 86px;
  height: auto;
  white-space: nowrap;
  border-radius: 6px;
  padding: 4px;
  background-color: #646464;
  box-shadow: 0 0 8px 0 rgba(187, 187, 187, 0.2);
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn {
  width: 100%;
  padding: 6px 0;
  padding-left: 12px;
  border-radius: 4px;
  font-size: 13px;
  height: unset;
  line-height: unset;
  text-align: left;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn:hover {
  background-color: #808080;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn.delete-btn {
  color: #940b01;
}
.theme-dark [data-type='memos_view'] .queries-wrapper > .queries-container > .query-item-container > .btns-container > .action-btns-wrapper > .action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-dark div[data-type='memos_view'].mobile-view .queries-container {
  height: auto;
}
.theme-dark div[data-type='memos_view'].mobile-view .queries-container:last-child {
  flex-grow: 1;
}
.theme-dark div[data-type='memos_view'].mobile-view .queries-container > .title-text {
  font-size: 13px;
  margin-bottom: 4px;
}
.theme-dark div[data-type='memos_view'].mobile-view .queries-container > .query-item-container {
  font-size: 15px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .tags-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 0 8px;
  height: auto;
  flex-wrap: nowrap;
  padding-bottom: 16px;
  flex-grow: 1;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .title-text {
  width: 100%;
  padding: 4px 16px;
  font-size: 12px;
  line-height: 24px;
  color: #37352f;
  opacity: 0.5;
  margin-bottom: 4px;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  flex-wrap: nowrap;
  margin-bottom: 8px;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .subtags-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100% - 18px);
  min-width: 80px;
  height: auto;
  margin-top: 4px;
  margin-left: 18px;
  border-left: 2px solid #e4e4e4;
  padding-left: 6px;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .subtags-container > .tag-item-container:first-child {
  margin-top: 0;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  margin-top: 4px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container:hover {
  background-color: #e4e4e4;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container.active > .tag-text-container > * {
  color: #55bb8e;
  font-weight: bold;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  max-width: calc(100% - 24px);
  color: #37352f;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 20px;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container > .icon-text {
  display: block;
  width: 16px;
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container > .tag-text {
  flex-shrink: 0;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  transition: all 0.1s linear;
  transform: rotate(0);
  margin-right: -8px;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  opacity: 0.8;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn.shown {
  transform: rotate(90deg);
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container > .tag-tip-container {
  width: 100%;
  margin-top: 8px;
  padding-left: 16px;
  font-size: 12px;
  line-height: 1.6;
  color: gray;
}
.theme-light div[data-type='memos_view'] .tags-wrapper > .tags-container > .tag-tip-container > .code-text {
  color: #5783f7;
  padding: 4px;
  margin: 0 2px;
  white-space: pre-line;
  background-color: #eef3fe;
  border-radius: 4px;
}
.theme-light .rename-tag-dialog > .dialog-container {
  width: 320px;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .tag-text {
  margin-bottom: 8px;
  font-size: 14px;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .text-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid lightgray;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text {
  font-size: 14px;
  margin-left: 12px;
  cursor: pointer;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text:hover {
  opacity: 0.8;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text.cancel-btn {
  color: #52504b;
}
.theme-light .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text.confirm-btn {
  background-color: #55bb8e;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
}
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper,
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate {
  background-color: white;
}
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper > .tags-container,
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate > .tags-container {
  height: auto;
}
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper > .tags-container:last-child,
.theme-light div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate > .tags-container:last-child {
  flex-grow: 1;
}
.theme-light .mobile-view .rename-tag-dialog,
.theme-light .mobile-view .rename-tag-dialog-mobile-emulate {
  padding-top: 64px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 0 8px;
  height: auto;
  flex-wrap: nowrap;
  padding-bottom: 16px;
  flex-grow: 1;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .title-text {
  width: 100%;
  padding: 4px 16px;
  font-size: 12px;
  line-height: 24px;
  color: #d2d1cd;
  opacity: 0.5;
  margin-bottom: 4px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  flex-wrap: nowrap;
  margin-bottom: 8px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .subtags-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: calc(100% - 18px);
  min-width: 80px;
  height: auto;
  margin-top: 4px;
  margin-left: 18px;
  border-left: 2px solid #353535;
  padding-left: 6px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .subtags-container > .tag-item-container:first-child {
  margin-top: 0;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 16px;
  margin-top: 4px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container:hover {
  background-color: #353535;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container.active > .tag-text-container > * {
  color: #457560;
  font-weight: bold;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  max-width: calc(100% - 24px);
  color: #d2d1cd;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 20px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container > .icon-text {
  display: block;
  width: 16px;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .tag-text-container > .tag-text {
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  transition: all 0.1s linear;
  transform: rotate(0);
  margin-right: -8px;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  color: #d2d1cd;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container .tag-item-container > .btns-container > .action-btn.shown {
  transform: rotate(90deg);
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container > .tag-tip-container {
  width: 100%;
  margin-top: 8px;
  padding-left: 16px;
  font-size: 12px;
  line-height: 1.6;
  color: #bbbbbb;
}
.theme-dark div[data-type='memos_view'] .tags-wrapper > .tags-container > .tag-tip-container > .code-text {
  color: #bbbec7;
  padding: 4px;
  margin: 0 2px;
  white-space: pre-line;
  background-color: #616161;
  border-radius: 4px;
}
.theme-dark .rename-tag-dialog > .dialog-container {
  width: 320px;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .tag-text {
  margin-bottom: 8px;
  font-size: 14px;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .text-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #505050;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text {
  font-size: 14px;
  margin-left: 12px;
  cursor: pointer;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text:hover {
  opacity: 0.8;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text.cancel-btn {
  color: #c7c4bb;
}
.theme-dark .rename-tag-dialog > .dialog-container > .dialog-content-container > .btns-container > .btn-text.confirm-btn {
  background-color: #457560;
  color: #000000;
  padding: 4px 12px;
  border-radius: 4px;
}
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper,
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate {
  background-color: #000000;
}
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper > .tags-container,
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate > .tags-container {
  height: auto;
}
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper > .tags-container:last-child,
.theme-dark div[data-type='memos_view'].mobile-view .tags-wrapper-mobile-emulate > .tags-container:last-child {
  flex-grow: 1;
}
.theme-dark .mobile-view .rename-tag-dialog,
.theme-dark .mobile-view .rename-tag-dialog-mobile-emulate {
  padding-top: 64px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 122px;
  flex-wrap: wrap;
  padding-right: 24px;
  padding-bottom: 12px;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper:hover > .day-tip-text-container {
  visibility: visible;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 24px;
  height: 100%;
  padding-bottom: 2px;
  flex-wrap: wrap;
  visibility: hidden;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
  font-size: 10px;
  line-height: 16px;
  padding-right: 2px;
  width: 100%;
  text-align: right;
  color: gray;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map {
  width: 192px;
  height: 100%;
  flex-wrap: wrap;
  display: grid;
  grid-template-rows: repeat(7, 1fr);
  grid-template-columns: repeat(12, 1fr);
  grid-auto-flow: column;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
  display: block;
  width: 13px;
  height: 13px;
  background-color: #eaeaea;
  border-radius: 2px;
  margin-bottom: 2px;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.null {
  background-color: transparent;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L1-bg {
  background-color: #9be9a8;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L2-bg {
  background-color: #40c463;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L3-bg {
  background-color: #30a14e;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L4-bg {
  background-color: #216e39;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.today {
  border: 1px solid black;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container {
  position: absolute;
  left: 0;
  top: 0;
  margin-left: 9px;
  transform: translateX(-50%);
  margin-top: -36px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.6;
  z-index: 2;
  user-select: none;
  white-space: nowrap;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container > .date-text {
  color: lightgray;
}
.theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container::before {
  content: '';
  position: absolute;
  bottom: -4px;
  left: calc(50% - 6px);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.8);
}
@media only screen and (max-width: 875px) {
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper {
    height: 160px;
    padding: 8px 0 !important;
    padding-top: 12px !important;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container {
    visibility: visible;
    width: 48px;
    padding-bottom: 4px;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
    padding-right: 6px;
    font-size: 12px;
    line-height: unset !important;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map {
    width: 240px;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
    width: 16px;
    height: 16px;
    margin-bottom: 4px;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container {
    margin-top: -32px;
    margin-left: 16px;
    font-size: 10px;
  }
  .theme-light div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container::before {
    left: calc(50% - 4px);
  }
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper {
  height: 160px;
  padding: 8px 0 !important;
  padding-top: 12px !important;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .day-tip-text-container {
  visibility: visible;
  width: 48px;
  padding-bottom: 4px;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
  padding-right: 6px;
  font-size: 12px;
  line-height: unset !important;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-heat-map {
  width: 240px;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
  width: 16px;
  height: 16px;
  margin-bottom: 4px;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-detail-container {
  margin-top: -32px;
  margin-left: 16px;
  font-size: 10px;
}
.theme-light div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-detail-container::before {
  left: calc(50% - 4px);
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 122px;
  flex-wrap: wrap;
  padding-right: 24px;
  padding-bottom: 12px;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper:hover > .day-tip-text-container {
  visibility: visible;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 24px;
  height: 100%;
  padding-bottom: 2px;
  flex-wrap: wrap;
  visibility: hidden;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
  font-size: 10px;
  line-height: 16px;
  padding-right: 2px;
  width: 100%;
  text-align: right;
  color: gray;
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map {
  width: 192px;
  height: 100%;
  flex-wrap: wrap;
  display: grid;
  grid-template-rows: repeat(7, 1fr);
  grid-template-columns: repeat(12, 1fr);
  grid-auto-flow: column;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
  display: block;
  width: 13px;
  height: 13px;
  background-color: #d8d8d8;
  border-radius: 2px;
  margin-bottom: 2px;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.null {
  background-color: transparent;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L1-bg {
  background-color: #f75205;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L2-bg {
  background-color: #e03a07;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L3-bg {
  background-color: #bf2104;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.stat-day-L4-bg {
  background-color: #940b01;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container.today {
  border: 1px solid #ffffff;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container {
  position: absolute;
  left: 0;
  top: 0;
  margin-left: 9px;
  transform: translateX(-50%);
  margin-top: -36px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.6;
  z-index: 2;
  user-select: none;
  white-space: nowrap;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container > .date-text {
  color: lightgray;
}
.theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container::before {
  content: '';
  position: absolute;
  bottom: -4px;
  left: calc(50% - 6px);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(0, 0, 0, 0.8);
}
@media only screen and (max-width: 875px) {
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper {
    height: 160px;
    padding: 8px 0 !important;
    padding-top: 12px !important;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container {
    visibility: visible;
    width: 48px;
    padding-bottom: 4px;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
    padding-right: 6px;
    font-size: 12px;
    line-height: unset !important;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map {
    width: 240px;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
    width: 16px;
    height: 16px;
    margin-bottom: 4px;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container {
    margin-top: -32px;
    margin-left: 16px;
    font-size: 10px;
  }
  .theme-dark div[data-type='memos_view'] .usage-heat-map-wrapper > .usage-detail-container::before {
    left: calc(50% - 4px);
  }
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper {
  height: 160px;
  padding: 8px 0 !important;
  padding-top: 12px !important;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .day-tip-text-container {
  visibility: visible;
  width: 48px;
  padding-bottom: 4px;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .day-tip-text-container > .tip-text {
  padding-right: 6px;
  font-size: 12px;
  line-height: unset !important;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-heat-map {
  width: 240px;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-heat-map > .stat-container {
  width: 16px;
  height: 16px;
  margin-bottom: 4px;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-detail-container {
  margin-top: -32px;
  margin-left: 16px;
  font-size: 10px;
}
.theme-dark div[data-type='memos_view'].mobile-view .usage-heat-map-wrapper > .usage-detail-container::before {
  left: calc(50% - 4px);
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light .memos-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
  scrollbar-width: none;
}
.theme-light .memos-sidebar-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light .memos-sidebar-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light .memos-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light .memos-sidebar-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light .memos-sidebar-wrapper > * {
  flex-shrink: 0;
}
.theme-light .memos-sidebar-wrapper-display,
.theme-dark .memos-sidebar-wrapper-display {
  display: none;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
}
.mobile-show-sidebar .mobile-view #page-wrapper > .memos-sidebar-wrapper {
  transform: translateX(0);
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
}
.theme-light .mobile-view .memos-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  padding: 0;
  background-color: white;
  transition: all 0.4s ease;
  transform: translateX(-320px);
}
.theme-light .mobile-view .memos-sidebar-wrapper > * {
  width: 320px;
  max-width: 95%;
  flex-shrink: 0;
  padding-left: 32px;
}
.theme-dark .memos-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 240px;
  height: 100%;
  padding: 16px 0;
  overflow-x: hidden;
  overflow-y: auto;
  flex-shrink: 0;
  scrollbar-width: none;
}
.theme-dark .memos-sidebar-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark .memos-sidebar-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark .memos-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark .memos-sidebar-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark .memos-sidebar-wrapper > * {
  flex-shrink: 0;
}
.theme-dark .mobile-view .memos-sidebar-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  z-index: 99;
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  padding: 0;
  background-color: #000000;
  transition: all 0.4s ease;
  transform: translateX(-320px);
}
.theme-dark .mobile-view .memos-sidebar-wrapper > * {
  width: 320px;
  max-width: 95%;
  flex-shrink: 0;
  padding-left: 32px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] #root {
  background-color: #f6f5f4;
}
div[data-type='memos_view'] .view-content {
  overflow-y: hidden;
}
div[data-type='memos_view'] #page-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 848px;
  max-width: 100%;
  height: 100%;
  margin: auto;
  transform: translateX(-16px);
  margin-top: -15px;
}
div[data-type='memos_view'] #page-wrapper > .content-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  margin-top: 15px;
  padding-left: 10px;
  width: 600px;
  height: 100%;
  gap: 8px;
}
div[data-type='memos_view'] #page-wrapper > .content-wrapper-padding-fix {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  margin-top: 0px;
  width: 600px;
  height: 100%;
  overflow-y: hidden;
  padding-left: 34px;
  gap: 8px;
}
div[data-type='memos_view'].mobile-view body.mobile-show-sidebar #page-wrapper > .content-wrapper {
  transform: translateX(320px);
}
div[data-type='memos_view'].mobile-view #page-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  padding: 0;
  transform: translateX(0);
  margin-top: -10px;
}
div[data-type='memos_view'].mobile-view #page-wrapper > .content-wrapper {
  width: 100%;
  height: 100%;
  margin-left: 0;
  padding-top: 0;
  transition: all 0.4s ease;
  transform: translateX(0);
}
.workspace-leaf-content[data-type="memos_view"] .react-transform-wrapper {
  overflow: unset;
}
.Control-box {
  position: absolute;
  display: flex;
  justify-content: center;
  left: 50%;
  margin-bottom: 10px;
  height: 40px;
  z-index: 20;
}
.Control-box .button {
  margin-left: 10px;
  width: 2em;
}
.controlPanel {
  position: absolute;
  z-index: 2;
  transform: translate(10px, 10px);
  max-width: calc(100% - 20px);
}
.controlBtn {
  padding: 6px 12px;
  background: white;
  border: 1px solid grey;
  border-radius: 5px;
  margin-right: 10px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  cursor: pointer;
}
.controlBtn:focus {
  filter: brightness(90%);
}
.controlBtn:hover {
  filter: brightness(120%);
}
.controlBtn:active {
  opacity: 0.9;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-editor-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid #e4e4e4;
}
.theme-light div[data-type='memos_view'] .memo-editor-wrapper.edit-ing {
  border-color: #5783f7;
}
.theme-light div[data-type='memos_view'] .memo-editor-wrapper > .tip-text {
  font-size: 12px;
  line-height: 20px;
  margin-top: 0px;
  color: #cac8c4;
}
.theme-light div[data-type='memos_view'] .memo-editor-wrapper > .memo-editor {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: white;
}
.theme-light div[data-type='memos_view'] .memo-editor-wrapper > .date-picker {
  position: absolute;
  z-index: 20;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-editor-wrapper {
  width: calc(100% - 24px);
  margin: auto;
}
.theme-light .mobile-view img.memo-show-editor-button {
  position: fixed;
  z-index: 10;
  filter: opacity(30%);
}
.theme-dark div[data-type='memos_view'] .memo-editor-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: #3b3b3b;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid #353535;
}
.theme-dark div[data-type='memos_view'] .memo-editor-wrapper.edit-ing {
  border-color: #3c3c3c;
}
.theme-dark div[data-type='memos_view'] .memo-editor-wrapper > .tip-text {
  font-size: 12px;
  line-height: 20px;
  margin-top: 0px;
  color: #5e5b56;
}
.theme-dark div[data-type='memos_view'] .memo-editor-wrapper > .memo-editor {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  width: 100%;
  height: auto;
  background-color: #383838;
}
.theme-dark div[data-type='memos_view'] .memo-editor-wrapper > .date-picker {
  position: absolute;
  z-index: 20;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-editor-wrapper {
  width: calc(100% - 24px);
  margin: auto;
}
.theme-dark .mobile-view img.memo-show-editor-button {
  position: fixed;
  z-index: 10;
  transition: visibility 0s linear 0.5s, opacity 0.5s linear;
  filter: invert(0.8) opacity(65%);
}
:root {
  --rdp-cell-size: 40px;
  --rdp-accent-color: #0000ff;
  --rdp-background-color: #e7edff;
  /* Switch to dark colors for dark themes */
  --rdp-accent-color-dark: #3003e1;
  --rdp-background-color-dark: #180270;
  /* Outline border for focused elements */
  --rdp-outline: 2px solid var(--rdp-accent-color);
  /* Outline border for focused and selected elements */
  --rdp-outline-selected: 2px solid rgba(0, 0, 0, 0.75);
  --rdp-dark-cell-size: 40px;
  --rdp-dark-accent-color: #494949;
  --rdp-dark-background-color: #838383;
  /* Switch to dark colors for dark themes */
  --rdp-dark-accent-color-dark: #050505;
  --rdp-dark-background-color-dark: #7e7e7e;
  /* Outline border for focused elements */
  --rdp-dark-outline: 2px solid var(--rdp-accent-color);
  /* Outline border for focused and selected elements */
  --rdp-dark-outline-selected: 2px solid rgba(255, 255, 255, 0.75);
}
.theme-light .rdp {
  margin: 1em;
  border-style: solid;
  border-width: 1px;
  border-color: #9b9b9b;
  overflow: auto;
}
/* Hide elements for devices that are not screen readers */
.rdp-vhidden {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  background: transparent;
  border: 0;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  position: absolute !important;
  top: 0;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  border: 0 !important;
}
/* Buttons */
.rdp-button_reset {
  appearance: none;
  position: relative;
  margin: 0;
  padding: 0;
  cursor: default;
  color: inherit;
  outline: none;
  background: none;
  font: inherit;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.rdp-button {
  border: 2px solid transparent;
}
.rdp-button[disabled] {
  opacity: 0.25;
}
.rdp-button:not([disabled]) {
  cursor: pointer;
}
.rdp-button:focus:not([disabled]),
.rdp-button:active:not([disabled]) {
  color: inherit;
  border: var(--rdp-outline);
  background-color: var(--rdp-background-color);
}
.rdp-button:hover:not([disabled]) {
  background-color: var(--rdp-background-color);
}
.rdp-months {
  display: flex;
  background: white;
  padding: 4px;
}
.rdp-month {
  margin: 0 1em;
}
.rdp-month:first-child {
  margin-left: 0;
}
.rdp-month:last-child {
  margin-right: 0;
}
.rdp-table {
  margin: 0;
  max-width: calc(var(--rdp-cell-size) * 7);
  border-collapse: collapse;
}
.rdp-with_weeknumber .rdp-table {
  max-width: calc(var(--rdp-cell-size) * 8);
  border-collapse: collapse;
}
.rdp-caption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  text-align: left;
}
.rdp-multiple_months .rdp-caption {
  position: relative;
  display: block;
  text-align: center;
}
.rdp-caption_dropdowns {
  position: relative;
  display: inline-flex;
}
.rdp-caption_label {
  position: relative;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  margin: 0;
  padding: 0 0.25em;
  white-space: nowrap;
  color: currentColor;
  border: 0;
  border: 2px solid transparent;
  font-family: inherit;
  font-size: 140%;
  font-weight: bold;
}
.rdp-nav {
  white-space: nowrap;
}
.rdp-multiple_months .rdp-caption_start .rdp-nav {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.rdp-multiple_months .rdp-caption_end .rdp-nav {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.rdp-nav_button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  padding: 0.25em;
  border-radius: 100%;
}
/* ---------- */
/* Dropdowns  */
/* ---------- */
.rdp-dropdown_year,
.rdp-dropdown_month {
  position: relative;
  display: inline-flex;
  align-items: center;
}
.rdp-dropdown {
  appearance: none;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 0;
  cursor: inherit;
  opacity: 0;
  border: none;
  background-color: transparent;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.rdp-dropdown[disabled] {
  opacity: unset;
  color: unset;
}
.rdp-dropdown:focus:not([disabled]) + .rdp-caption_label,
.rdp-dropdown:active:not([disabled]) + .rdp-caption_label {
  border: var(--rdp-outline);
  border-radius: 6px;
  background-color: var(--rdp-background-color);
}
.rdp-dropdown_icon {
  margin: 0 0 0 5px;
}
.rdp-head {
  border: 0;
}
.rdp-head_row,
.rdp-row {
  height: 100%;
}
.rdp-head_cell {
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 0.75em;
  font-weight: 700;
  text-align: center;
  height: 100%;
  height: var(--rdp-cell-size);
  padding: 0;
}
.rdp-tbody {
  border: 0;
}
.rdp-foot {
  margin: 0.5em;
}
.rdp-cell {
  width: var(--rdp-cell-size);
  height: 100%;
  height: var(--rdp-cell-size);
  padding: 0;
  text-align: center;
}
.rdp-weeknumber {
  font-size: 0.75em;
}
.rdp-weeknumber,
.rdp-day {
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: var(--rdp-cell-size);
  max-width: var(--rdp-cell-size);
  height: var(--rdp-cell-size);
  margin: 0;
  border: 2px solid transparent;
  border-radius: 100%;
}
.rdp-day_today:not(.rdp-day_outside) {
  font-weight: bold;
}
.rdp-day_selected:not([disabled]),
.rdp-day_selected:focus:not([disabled]),
.rdp-day_selected:active:not([disabled]),
.rdp-day_selected:hover:not([disabled]) {
  color: white;
  background-color: var(--rdp-accent-color);
}
.rdp-day_selected:focus:not([disabled]) {
  border: var(--rdp-outline-selected);
}
.rdp:not([dir='rtl']) .rdp-day_range_start:not(.rdp-day_range_end) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rdp:not([dir='rtl']) .rdp-day_range_end:not(.rdp-day_range_start) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rdp[dir='rtl'] .rdp-day_range_start:not(.rdp-day_range_end) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.rdp[dir='rtl'] .rdp-day_range_end:not(.rdp-day_range_start) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.rdp-day_range_end.rdp-day_range_start {
  border-radius: 100%;
}
.rdp-day_range_middle {
  border-radius: 0;
}
.theme-dark .rdp {
  margin: 1em;
  border-style: solid;
  border-width: 1px;
  border-color: #9b9b9b;
  overflow: auto;
  color: #dddddd;
  /* Hide elements for devices that are not screen readers */
  /* Buttons */
  /* ---------- */
  /* Dropdowns  */
  /* ---------- */
}
.theme-dark .rdp .rdp-vhidden {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  background: transparent;
  border: 0;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  position: absolute !important;
  top: 0;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  border: 0 !important;
}
.theme-dark .rdp .rdp-button_reset {
  appearance: none;
  position: relative;
  margin: 0;
  padding: 0;
  cursor: default;
  color: inherit;
  outline: none;
  background: none;
  font: inherit;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.theme-dark .rdp .rdp-button {
  border: 2px solid transparent;
}
.theme-dark .rdp .rdp-button[disabled] {
  opacity: 0.25;
}
.theme-dark .rdp .rdp-button:not([disabled]) {
  cursor: pointer;
}
.theme-dark .rdp .rdp-button:focus:not([disabled]),
.theme-dark .rdp .rdp-button:active:not([disabled]) {
  color: inherit;
  border: var(--rdp-dark-outline);
  background-color: var(--rdp-dark-background-color);
}
.theme-dark .rdp .rdp-button:hover:not([disabled]) {
  background-color: var(--rdp-dark-background-color);
}
.theme-dark .rdp .rdp-months {
  display: flex;
  background: #252525;
  padding: 4px;
}
.theme-dark .rdp .rdp-month {
  margin: 0 1em;
}
.theme-dark .rdp .rdp-month:first-child {
  margin-left: 0;
}
.theme-dark .rdp .rdp-month:last-child {
  margin-right: 0;
}
.theme-dark .rdp .rdp-table {
  margin: 0;
  max-width: calc(var(--rdp-dark-cell-size) * 7);
  border-collapse: collapse;
}
.theme-dark .rdp .rdp-with_weeknumber .rdp-table {
  max-width: calc(var(--rdp-dark-cell-size) * 8);
  border-collapse: collapse;
}
.theme-dark .rdp .rdp-caption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  text-align: left;
}
.theme-dark .rdp .rdp-multiple_months .rdp-caption {
  position: relative;
  display: block;
  text-align: center;
}
.theme-dark .rdp .rdp-caption_dropdowns {
  position: relative;
  display: inline-flex;
}
.theme-dark .rdp .rdp-caption_label {
  position: relative;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  margin: 0;
  padding: 0 0.25em;
  white-space: nowrap;
  color: currentColor;
  border: 0;
  border: 2px solid transparent;
  font-family: inherit;
  font-size: 140%;
  font-weight: bold;
}
.theme-dark .rdp .rdp-nav {
  white-space: nowrap;
}
.theme-dark .rdp .rdp-multiple_months .rdp-caption_start .rdp-nav {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.theme-dark .rdp .rdp-multiple_months .rdp-caption_end .rdp-nav {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
.theme-dark .rdp .rdp-nav_button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--rdp-dark-cell-size);
  height: var(--rdp-dark-cell-size);
  padding: 0.25em;
  border-radius: 100%;
}
.theme-dark .rdp .rdp-dropdown_year,
.theme-dark .rdp .rdp-dropdown_month {
  position: relative;
  display: inline-flex;
  align-items: center;
}
.theme-dark .rdp .rdp-dropdown {
  appearance: none;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  margin: 0;
  padding: 0;
  cursor: inherit;
  opacity: 0;
  border: none;
  background-color: transparent;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.theme-dark .rdp .rdp-dropdown[disabled] {
  opacity: unset;
  color: unset;
}
.theme-dark .rdp .rdp-dropdown:focus:not([disabled]) + .rdp-caption_label,
.theme-dark .rdp .rdp-dropdown:active:not([disabled]) + .rdp-caption_label {
  border: var(--rdp-dark-outline);
  border-radius: 6px;
  background-color: var(--rdp-dark-background-color);
}
.theme-dark .rdp .rdp-dropdown_icon {
  margin: 0 0 0 5px;
}
.theme-dark .rdp .rdp-head {
  border: 0;
}
.theme-dark .rdp .rdp-head_row,
.theme-dark .rdp .rdp-row {
  height: 100%;
}
.theme-dark .rdp .rdp-head_cell {
  vertical-align: middle;
  text-transform: uppercase;
  font-size: 0.75em;
  font-weight: 700;
  text-align: center;
  height: 100%;
  height: var(--rdp-dark-cell-size);
  padding: 0;
}
.theme-dark .rdp .rdp-tbody {
  border: 0;
}
.theme-dark .rdp .rdp-foot {
  margin: 0.5em;
}
.theme-dark .rdp .rdp-cell {
  width: var(--rdp-dark-cell-size);
  height: 100%;
  height: var(--rdp-dark-cell-size);
  padding: 0;
  text-align: center;
}
.theme-dark .rdp .rdp-weeknumber {
  font-size: 0.75em;
}
.theme-dark .rdp .rdp-weeknumber,
.theme-dark .rdp .rdp-day {
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: var(--rdp-dark-cell-size);
  max-width: var(--rdp-dark-cell-size);
  height: var(--rdp-dark-cell-size);
  margin: 0;
  border: 2px solid transparent;
  border-radius: 100%;
}
.theme-dark .rdp .rdp-day_today:not(.rdp-day_outside) {
  font-weight: bold;
}
.theme-dark .rdp .rdp-day_selected:not([disabled]),
.theme-dark .rdp .rdp-day_selected:focus:not([disabled]),
.theme-dark .rdp .rdp-day_selected:active:not([disabled]),
.theme-dark .rdp .rdp-day_selected:hover:not([disabled]) {
  color: white;
  background-color: var(--rdp-dark-accent-color);
}
.theme-dark .rdp .rdp-day_selected:focus:not([disabled]) {
  border: var(--rdp-dark-outline-selected);
}
.theme-dark .rdp .rdp:not([dir='rtl']) .rdp-day_range_start:not(.rdp-day_range_end) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.theme-dark .rdp .rdp:not([dir='rtl']) .rdp-day_range_end:not(.rdp-day_range_start) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.theme-dark .rdp .rdp[dir='rtl'] .rdp-day_range_start:not(.rdp-day_range_end) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.theme-dark .rdp .rdp[dir='rtl'] .rdp-day_range_end:not(.rdp-day_range_start) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.theme-dark .rdp .rdp-day_range_end.rdp-day_range_start {
  border-radius: 100%;
}
.theme-dark .rdp .rdp-day_range_middle {
  border-radius: 0;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .search-bar-container {
  width: 160px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .search-bar-inputer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fcfcfc;
  width: 100%;
  height: 40px;
  padding: 4px 16px;
  border-radius: 8px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .search-bar-inputer > .icon-img {
  margin-right: 8px;
  width: 14px;
  height: auto;
  opacity: 0.6;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .search-bar-inputer > .text-input {
  width: 100%;
  font-size: 15px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .search-bar-inputer > .text-input:hover {
  box-shadow: 0 0 0 1px var(--background-modifier-border-hover);
}
.theme-light div[data-type='memos_view'] .search-bar-container > .search-bar-inputer:hover + .quickly-action-wrapper {
  display: flex;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper {
  display: none;
  position: absolute;
  top: 52px;
  right: -7px;
  z-index: 12;
  padding: 8px;
  width: 320px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  background-color: white;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .title-text {
  color: gray;
  font-size: 12px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  font-size: 13px;
  margin-top: 8px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .section-text {
  color: gray;
  margin-right: 4px;
  flex-shrink: 0;
  line-height: 26px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  user-select: none;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  line-height: 26px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item {
  cursor: pointer;
  padding: 0 4px;
  border-radius: 6px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item:hover {
  background-color: #f8f8f8;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item.selected {
  background-color: #55bb8e;
  color: white;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .split-text {
  color: lightgray;
  margin: 0 2px;
}
.theme-light div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper:hover {
  display: flex;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container {
  width: 120px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #fcfcfc;
  height: 40px;
  padding: 4px 16px;
  border-radius: 8px;
  width: 120%;
  margin-left: -35px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer > .icon-img {
  margin-right: 8px;
  width: 14px;
  height: auto;
  opacity: 0.6;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer > .text-input {
  width: 100%;
  font-size: 15px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer > .text-input:hover {
  box-shadow: 0 0 0 1px var(--background-modifier-border-hover);
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer:hover + .quickly-action-wrapper {
  display: flex;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper {
  display: none;
  position: absolute;
  top: 42px;
  z-index: 12;
  padding-right: 20px;
  padding-left: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
  width: 320px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  background-color: white;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .title-text {
  color: gray;
  font-size: 12px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  font-size: 13px;
  margin-top: 8px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .section-text {
  color: gray;
  margin-right: 4px;
  flex-shrink: 0;
  line-height: 26px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  user-select: none;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  line-height: 26px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item {
  cursor: pointer;
  padding: 0 4px;
  border-radius: 6px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item:hover {
  background-color: #f8f8f8;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item.selected {
  background-color: #55bb8e;
  color: white;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .split-text {
  color: lightgray;
  margin: 0 2px;
}
.theme-light div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper:hover {
  display: flex;
}
.theme-dark div[data-type='memos_view'] .search-bar-container {
  width: 160px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .search-bar-inputer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #302e2e;
  width: 100%;
  height: 40px;
  padding: 8px 16px;
  border-radius: 8px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .search-bar-inputer > .icon-img {
  margin-right: 8px;
  width: 14px;
  height: auto;
  opacity: 0.8;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .search-bar-inputer > .text-input {
  width: 100%;
  font-size: 15px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .search-bar-inputer:hover + .quickly-action-wrapper {
  display: flex;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper {
  display: none;
  position: absolute;
  top: 52px;
  right: -7px;
  z-index: 12;
  padding: 8px;
  width: 320px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  background-color: #000000;
  padding: 8px 16px;
  border-radius: 8px;
  margin-top: -8px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .title-text {
  color: #cccccc;
  font-size: 12px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  font-size: 13px;
  margin-top: 8px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .section-text {
  color: #dfdfdf;
  margin-right: 4px;
  flex-shrink: 0;
  line-height: 26px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  user-select: none;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  line-height: 26px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item {
  cursor: pointer;
  padding: 0 4px;
  border-radius: 6px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item:hover {
  background-color: #808080;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item.selected {
  background-color: #457560;
  color: #000000;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .split-text {
  color: #686868;
  margin: 0 2px;
}
.theme-dark div[data-type='memos_view'] .search-bar-container > .quickly-action-wrapper:hover {
  display: flex;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container {
  width: 120px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  background-color: #302e2e;
  height: 40px;
  padding: 8px 16px;
  border-radius: 8px;
  width: 120%;
  margin-left: -35px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer > .icon-img {
  margin-right: 8px;
  width: 14px;
  height: auto;
  opacity: 0.8;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer > .text-input {
  width: 100%;
  font-size: 15px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .search-bar-inputer:hover + .quickly-action-wrapper {
  display: flex;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper {
  display: none;
  position: absolute;
  top: 42px;
  z-index: 2;
  padding: 8px;
  width: 320px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  background-color: #000000;
  padding: 8px 16px;
  border-radius: 8px;
  margin-top: -8px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .title-text {
  color: #cccccc;
  font-size: 12px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  font-size: 13px;
  margin-top: 8px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .section-text {
  color: #dfdfdf;
  margin-right: 4px;
  flex-shrink: 0;
  line-height: 26px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  user-select: none;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  line-height: 26px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item {
  cursor: pointer;
  padding: 0 4px;
  border-radius: 6px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item:hover {
  background-color: #808080;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .type-item.selected {
  background-color: #457560;
  color: #000000;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper > .quickly-action-container > .types-container > .values-container > div .split-text {
  color: #686868;
  margin: 0 2px;
}
.theme-dark div[data-type='memos_view'].mobile-view .search-bar-container > .quickly-action-wrapper:hover {
  display: flex;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .section-header-container,
div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
div[data-type='memos_view'] .section-header-container > .title-text,
div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #37352f;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
div[data-type='memos_view'] .section-header-container > .btns-container,
div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
div[data-type='memos_view'].mobile-view .section-header-container,
div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
.theme-dark div[data-type='memos_view'] .section-header-container,
.theme-dark div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #d2d1cd;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .btns-container,
.theme-dark div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text {
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .filter-query-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
  padding: 4px 12px;
  padding-bottom: 4px;
  font-size: 13px;
  line-height: 1.8;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .filter-query {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .filter-query > .tip-text {
  padding: 2px 0;
  margin-left: -6px;
  margin-right: 3px;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container {
  padding: 2px 8px;
  padding-left: 4px;
  margin-right: 6px;
  cursor: pointer;
  background-color: #e4e4e4;
  border-radius: 4px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container > .icon-text {
  letter-spacing: 2px;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container:hover {
  text-decoration: line-through;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .copy-memo {
  padding-right: 6px;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .copy-memo > .icon-img {
  width: 20px;
  height: auto;
}
.theme-light div[data-type='memos_view'] .filter-query-container > .copy-memo:hover {
  opacity: 0.8;
  filter: contrast(1) brightness(1) invert(0.5);
}
.theme-light div[data-type='memos_view'].mobile-view .filter-query-container {
  padding-left: 20px;
}
.theme-dark div[data-type='memos_view'] .filter-query-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
  padding: 4px 12px;
  padding-bottom: 4px;
  font-size: 13px;
  line-height: 1.8;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .filter-query {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .filter-query > .tip-text {
  padding: 2px 0;
  margin-left: -6px;
  margin-right: 3px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container {
  padding: 2px 8px;
  padding-left: 4px;
  margin-right: 6px;
  cursor: pointer;
  background-color: #cacdcf;
  border-radius: 4px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container > .icon-text {
  letter-spacing: 2px;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .filter-query > .filter-item-container:hover {
  text-decoration: line-through;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .copy-memo {
  padding-right: 6px;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .copy-memo > .icon-img {
  width: 20px;
  height: auto;
  opacity: 0.8;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .filter-query-container > .copy-memo:hover {
  opacity: 0.8;
  filter: contrast(1) brightness(1) invert(0.9);
}
.theme-dark div[data-type='memos_view'].mobile-view .filter-query-container {
  padding-left: 20px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
  width: 100%;
  overflow-y: scroll;
  gap: 8px;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper > .status-text-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  margin-bottom: 16px;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper > .status-text-container.completed {
  margin-bottom: 64px;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper > .status-text-container.invisible {
  visibility: hidden;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper > .status-text-container > .status-text {
  font-size: 13px;
  color: gray;
}
.theme-light div[data-type='memos_view'] .memolist-wrapper.completed {
  padding-bottom: 80px;
}
div[data-type='memos_view'].mobile-view .memolist-wrapper {
  padding: 0 12px;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
  width: 100%;
  overflow-y: scroll;
  gap: 8px;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper > .status-text-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  margin-bottom: 16px;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper > .status-text-container.completed {
  margin-bottom: 64px;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper > .status-text-container.invisible {
  visibility: hidden;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper > .status-text-container > .status-text {
  font-size: 13px;
  color: #ffffff;
}
.theme-dark div[data-type='memos_view'] .memolist-wrapper.completed {
  padding-bottom: 80px;
}
.theme-dark div[data-type='memos_view'].mobile-view .memolist-wrapper {
  padding: 0 12px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .section-header-container,
div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
div[data-type='memos_view'] .section-header-container > .title-text,
div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #37352f;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
div[data-type='memos_view'] .section-header-container > .btns-container,
div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
div[data-type='memos_view'].mobile-view .section-header-container,
div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
.theme-dark div[data-type='memos_view'] .section-header-container,
.theme-dark div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #d2d1cd;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .btns-container,
.theme-dark div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text {
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .section-header-container {
  width: 100%;
  height: 40px;
  margin-bottom: 0;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .section-header-container > .title-text {
  font-weight: bold;
  font-size: 15px;
  color: #37352f;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .tip-text-container {
  width: 100%;
  height: 128px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
  width: 100%;
  overflow-y: scroll;
  padding-bottom: 64px;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container > .memo-wrapper > .memo-content-text {
  font-size: 15px;
  line-height: 24px;
}
.theme-light div[data-type='memos_view'].mobile-view .deleted-memos-container {
  padding: 0 12px;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  scrollbar-width: none;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper > .section-header-container {
  width: 100%;
  height: 58px;
  margin-bottom: 0;
}
.theme-light div[data-type='memos_view'].mobile-view .memo-trash-wrapper > .section-header-container > .title-text {
  font-weight: bold;
  font-size: 15px;
  color: #37352f;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .section-header-container {
  width: 100%;
  height: 40px;
  margin-bottom: 0;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .section-header-container > .title-text {
  font-weight: bold;
  font-size: 18px;
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .tip-text-container {
  width: 100%;
  height: 128px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
  font-size: 15px;
  width: 100%;
  overflow-y: scroll;
  padding-bottom: 64px;
  color: #d2d1cd;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'] .memo-trash-wrapper > .deleted-memos-container > .memo-wrapper > .memo-content-text {
  font-size: 15px;
  line-height: 24px;
}
.theme-dark div[data-type='memos_view'].mobile-view .deleted-memos-container {
  padding: 0 12px;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  scrollbar-width: none;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper > .section-header-container {
  width: 100%;
  height: 58px;
  margin-bottom: 0;
}
.theme-dark div[data-type='memos_view'].mobile-view .memo-trash-wrapper > .section-header-container > .title-text {
  font-weight: bold;
  font-size: 15px;
  color: #d2d1cd;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 12px 18px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid transparent;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper:hover {
  border-color: #e4e4e4;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 24px;
  margin-bottom: 0;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .time-text {
  font-size: 12px;
  line-height: 24px;
  color: #a8a8a8;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  flex-wrap: nowrap;
  top: calc(100% - 14px);
  right: -16px;
  width: auto;
  height: auto;
  padding: 12px;
  z-index: 1;
  display: none;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper:hover {
  display: flex;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  width: 112px;
  height: auto;
  line-height: 18px;
  padding: 4px;
  white-space: nowrap;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn {
  width: 100%;
  padding: 8px 0;
  padding-left: 24px;
  border-radius: 4px;
  height: unset;
  line-height: unset;
  justify-content: flex-start;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn {
  color: #d28653;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 28px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn:hover {
  background-color: #f8f8f8;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn {
  width: 28px;
  cursor: unset;
  margin-right: -6px;
  opacity: 0.8;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn > .icon-img {
  width: 16px;
  height: 16px;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn:hover {
  background-color: unset;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn:hover + .more-action-btns-wrapper {
  display: flex;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-content-text {
  width: 100%;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 4px;
  scrollbar-width: none;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img:hover {
  border-color: lightgray;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-light div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  padding: 12px 18px;
  background-color: #303030;
  border-radius: 8px;
  border: 1px solid transparent;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper:hover {
  border-color: #353535;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 24px;
  margin-bottom: 0;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .time-text {
  font-size: 12px;
  line-height: 24px;
  color: #d6d6d6;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  flex-shrink: 0;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  flex-wrap: nowrap;
  top: calc(100% - 14px);
  right: -16px;
  width: auto;
  height: auto;
  padding: 12px;
  z-index: 1;
  display: none;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper:hover {
  display: flex;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container {
  width: 112px;
  height: auto;
  line-height: 18px;
  padding: 4px;
  white-space: nowrap;
  border-radius: 8px;
  background-color: #181818;
  z-index: 1;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn {
  width: 100%;
  padding: 8px 0;
  padding-left: 24px;
  border-radius: 4px;
  height: unset;
  line-height: unset;
  justify-content: flex-start;
  color: #d2d1cd;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn {
  color: #940b01;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container > .more-action-btns-wrapper > .more-action-btns-container > .btn.delete-btn.final-confirm {
  font-weight: bold;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 28px;
  font-size: 13px;
  border-radius: 4px;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn:hover {
  background-color: #808080;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn {
  width: 28px;
  cursor: unset;
  margin-right: -6px;
  opacity: 0.8;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn > .icon-img {
  width: 16px;
  height: 16px;
  fill: #cdcdcd;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn:hover {
  background-color: unset;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-top-wrapper > .btns-container .btn.more-action-btn:hover + .more-action-btns-wrapper {
  display: flex;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .memo-content-text {
  width: 100%;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 8px;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 4px;
  scrollbar-width: none;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar {
  width: 0;
  height: 2px;
  cursor: pointer;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 2px;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img {
  margin-right: 8px;
  width: auto;
  height: 128px;
  flex-shrink: 0;
  flex-grow: 0;
  overflow-y: hidden;
  scrollbar-width: none;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img::-webkit-scrollbar {
  display: none;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img:hover {
  border-color: #444444;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img:last-child {
  margin-right: 0;
}
.theme-dark div[data-type="memos_view"] .memo-trash-wrapper .memo-wrapper > .images-wrapper > .memo-img > img {
  width: auto;
  max-height: 128px;
  border-radius: 8px;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
.preferences-section-container > .demo-content-container {
  padding: 16px;
  border-radius: 8px;
  border: 2px solid #e4e4e4;
  margin: 12px 0;
}
.preferences-section-container > .form-label {
  height: 28px;
  cursor: pointer;
}
.preferences-section-container > .form-label > .icon-img {
  width: 16px;
  height: 16px;
  margin: 0 8px;
}
.preferences-section-container > .form-label:hover {
  opacity: 0.8;
}
.preferences-section-container > .btn-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  margin: 4px 0;
}
.preferences-section-container > .btn-container .btn {
  height: 28px;
  padding: 0 12px;
  margin-right: 8px;
  border: 1px solid gray;
  border-radius: 8px;
  cursor: pointer;
}
.preferences-section-container > .btn-container .btn:hover {
  opacity: 0.8;
}
.mono-font-family {
  font-family: 'ubuntu-mono', SFMono-Regular, Menlo, Consolas, 'PT Mono', 'Liberation Mono', Courier, monospace;
}
.hide-scroll-bar {
  scrollbar-width: none;
}
.hide-scroll-bar::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
.hide-scroll-bar::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
.hide-scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
.hide-scroll-bar::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .section-header-container,
div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
div[data-type='memos_view'] .section-header-container > .title-text,
div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #37352f;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
div[data-type='memos_view'] .section-header-container > .btns-container,
div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
div[data-type='memos_view'].mobile-view .section-header-container,
div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
}
.theme-dark div[data-type='memos_view'] .section-header-container,
.theme-dark div[data-type='memos_view'] .memos-header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  flex-wrap: nowrap;
  margin-top: 16px;
  flex-shrink: 0;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  line-height: 40px;
  color: #d2d1cd;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  cursor: pointer;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-right: 4px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'] .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
.theme-dark div[data-type='memos_view'] .section-header-container > .btns-container,
.theme-dark div[data-type='memos_view'] .memos-header-container > .btns-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container {
  height: auto;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0 12px;
  padding-bottom: 8px;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text {
  color: #d2d1cd;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 24px;
  margin-right: -8px;
  margin-left: -20px;
  flex-shrink: 0;
  background-color: unset;
}
.theme-dark div[data-type='memos_view'].mobile-view .section-header-container > .title-text > .action-btn > .icon-img,
.theme-dark div[data-type='memos_view'].mobile-view .memos-header-container > .title-text > .action-btn > .icon-img {
  width: 18px;
  height: 18px;
  fill: #cdcdcd;
}
div[data-type='memos_view'] .preference-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow-y: scroll;
  scrollbar-width: none;
}
div[data-type='memos_view'] .preference-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .preference-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
div[data-type='memos_view'] .preference-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
div[data-type='memos_view'] .preference-wrapper::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .preference-wrapper > .section-header-container {
  width: 100%;
  height: 40px;
  margin-bottom: 0;
}
div[data-type='memos_view'] .preference-wrapper > .section-header-container > .title-text {
  font-weight: bold;
  font-size: 18px;
  color: #37352f;
}
div[data-type='memos_view'] .preference-wrapper > .tip-text-container {
  width: 100%;
  height: 128px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
  width: 100%;
  overflow-y: scroll;
  padding-bottom: 64px;
  scrollbar-width: none;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper::-webkit-scrollbar {
  width: 0;
  height: 0;
  cursor: pointer;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
  border-radius: 8px;
  background-color: #d5d5d5;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper::-webkit-scrollbar {
  display: none;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper > .section-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  background-color: white;
  margin: 8px 0;
  padding: 16px;
  padding-bottom: 8px;
  border-radius: 8px;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper > .section-container > .title-text {
  font-size: 15px;
  color: #37352f;
  font-weight: bold;
  margin-bottom: 8px;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper > .section-container > .form-label {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 8px;
}
div[data-type='memos_view'] .preference-wrapper > .sections-wrapper > .section-container > .form-label > .normal-text {
  flex-shrink: 0;
}
@media only screen and (max-width: 875px) {
  div[data-type='memos_view'] .sections-wrapper {
    padding: 0 12px;
  }
}
