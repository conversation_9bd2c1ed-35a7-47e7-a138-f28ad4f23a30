---
cssclass: rightlane
---

```dataviewjs
const today = window.moment().format("YYYY年MM月DD日");
const todayFile = dv.page(today);

if (todayFile) {
    // 获取文件内容
    const fileContent = await dv.io.load(todayFile.file.path);
    
    // 查找#日程标题后的内容
    const lines = fileContent.split('\n');
    let scheduleStartIndex = -1;
    let scheduleEndIndex = lines.length;
    
    // 找到#日程标题的位置
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('# 日程') || lines[i].includes('#日程')) {
            scheduleStartIndex = i + 1;
            break;
        }
    }
    
    // 找到下一个标题的位置（作为结束位置）
    if (scheduleStartIndex !== -1) {
        for (let i = scheduleStartIndex; i < lines.length; i++) {
            if (lines[i].startsWith('#') && !lines[i].includes('日程')) {
                scheduleEndIndex = i;
                break;
            }
        }
        
        // 提取日程内容
        const scheduleContent = lines.slice(scheduleStartIndex, scheduleEndIndex)
            .filter(line => line.trim() !== '') // 过滤空行
            .join('\n');
        
        if (scheduleContent.trim()) {
            dv.paragraph(scheduleContent);
        } else {
            dv.paragraph("今天暂无日程安排");
        }
    } else {
        dv.paragraph("未找到日程标题");
    }
} else {
    dv.paragraph(`未找到今天的日程文件：${today}`);
}
```