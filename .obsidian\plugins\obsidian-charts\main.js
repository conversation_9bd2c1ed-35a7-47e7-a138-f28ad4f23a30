/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository (https://github.com/phibr0/obsidian-dictionary)
*/

var qm=Object.create;var $i=Object.defineProperty,Gm=Object.defineProperties,Um=Object.getOwnPropertyDescriptor,Km=Object.getOwnPropertyDescriptors,Zm=Object.getOwnPropertyNames,Lc=Object.getOwnPropertySymbols,Jm=Object.getPrototypeOf,Fc=Object.prototype.hasOwnProperty,Qm=Object.prototype.propertyIsEnumerable;var Ic=(n,t,e)=>t in n?$i(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,ji=(n,t)=>{for(var e in t||(t={}))Fc.call(t,e)&&Ic(n,e,t[e]);if(Lc)for(var e of Lc(t))Qm.call(t,e)&&Ic(n,e,t[e]);return n},zi=(n,t)=>Gm(n,Km(t)),$c=n=>$i(n,"__esModule",{value:!0});var Bo=(n,t)=>()=>(t||n((t={exports:{}}).exports,t),t.exports),tb=(n,t)=>{$c(n);for(var e in t)$i(n,e,{get:t[e],enumerable:!0})},eb=(n,t,e)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Zm(t))!Fc.call(n,i)&&i!=="default"&&$i(n,i,{get:()=>t[i],enumerable:!(e=Um(t,i))||e.enumerable});return n},ze=n=>eb($c($i(n!=null?qm(Jm(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);var $u=Bo((ja,za)=>{(function(n,t){typeof ja=="object"&&typeof za!="undefined"?za.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis!="undefined"?globalThis:n||self,n.chroma=t())})(ja,function(){"use strict";for(var n=function(u,d,m){return d===void 0&&(d=0),m===void 0&&(m=1),u<d?d:u>m?m:u},t=n,e=function(u){u._clipped=!1,u._unclipped=u.slice(0);for(var d=0;d<=3;d++)d<3?((u[d]<0||u[d]>255)&&(u._clipped=!0),u[d]=t(u[d],0,255)):d===3&&(u[d]=t(u[d],0,1));return u},i={},s=0,r=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];s<r.length;s+=1){var o=r[s];i["[object "+o+"]"]=o.toLowerCase()}var a=function(u){return i[Object.prototype.toString.call(u)]||"object"},l=a,c=function(u,d){return d===void 0&&(d=null),u.length>=3?Array.prototype.slice.call(u):l(u[0])=="object"&&d?d.split("").filter(function(m){return u[0][m]!==void 0}).map(function(m){return u[0][m]}):u[0]},h=a,f=function(u){if(u.length<2)return null;var d=u.length-1;return h(u[d])=="string"?u[d].toLowerCase():null},g=Math.PI,p={clip_rgb:e,limit:n,type:a,unpack:c,last:f,PI:g,TWOPI:g*2,PITHIRD:g/3,DEG2RAD:g/180,RAD2DEG:180/g},b={format:{},autodetect:[]},x=p.last,S=p.clip_rgb,C=p.type,T=b,I=function(){for(var d=[],m=arguments.length;m--;)d[m]=arguments[m];var _=this;if(C(d[0])==="object"&&d[0].constructor&&d[0].constructor===this.constructor)return d[0];var M=x(d),P=!1;if(!M){P=!0,T.sorted||(T.autodetect=T.autodetect.sort(function(B,q){return q.p-B.p}),T.sorted=!0);for(var w=0,E=T.autodetect;w<E.length;w+=1){var L=E[w];if(M=L.test.apply(L,d),M)break}}if(T.format[M]){var $=T.format[M].apply(null,P?d:d.slice(0,-1));_._rgb=S($)}else throw new Error("unknown format: "+d);_._rgb.length===3&&_._rgb.push(1)};I.prototype.toString=function(){return C(this.hex)=="function"?this.hex():"["+this._rgb.join(",")+"]"};var A=I,F=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(F.Color,[null].concat(u)))};F.Color=A,F.version="2.4.2";var j=F,N=p.unpack,G=Math.max,U=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=N(u,"rgb"),_=m[0],M=m[1],P=m[2];_=_/255,M=M/255,P=P/255;var w=1-G(_,G(M,P)),E=w<1?1/(1-w):0,L=(1-_-w)*E,$=(1-M-w)*E,B=(1-P-w)*E;return[L,$,B,w]},nt=U,lt=p.unpack,rt=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=lt(u,"cmyk");var m=u[0],_=u[1],M=u[2],P=u[3],w=u.length>4?u[4]:1;return P===1?[0,0,0,w]:[m>=1?0:255*(1-m)*(1-P),_>=1?0:255*(1-_)*(1-P),M>=1?0:255*(1-M)*(1-P),w]},Pt=rt,zt=j,et=A,Ot=b,St=p.unpack,re=p.type,_e=nt;et.prototype.cmyk=function(){return _e(this._rgb)},zt.cmyk=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(et,[null].concat(u,["cmyk"])))},Ot.format.cmyk=Pt,Ot.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=St(u,"cmyk"),re(u)==="array"&&u.length===4)return"cmyk"}});var ot=p.unpack,Lt=p.last,Bt=function(u){return Math.round(u*100)/100},Gt=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=ot(u,"hsla"),_=Lt(u)||"lsa";return m[0]=Bt(m[0]||0),m[1]=Bt(m[1]*100)+"%",m[2]=Bt(m[2]*100)+"%",_==="hsla"||m.length>3&&m[3]<1?(m[3]=m.length>3?m[3]:1,_="hsla"):m.length=3,_+"("+m.join(",")+")"},Qt=Gt,k=p.unpack,v=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=k(u,"rgba");var m=u[0],_=u[1],M=u[2];m/=255,_/=255,M/=255;var P=Math.min(m,_,M),w=Math.max(m,_,M),E=(w+P)/2,L,$;return w===P?(L=0,$=Number.NaN):L=E<.5?(w-P)/(w+P):(w-P)/(2-w-P),m==w?$=(_-M)/(w-P):_==w?$=2+(M-m)/(w-P):M==w&&($=4+(m-_)/(w-P)),$*=60,$<0&&($+=360),u.length>3&&u[3]!==void 0?[$,L,E,u[3]]:[$,L,E]},y=v,R=p.unpack,D=p.last,O=Qt,Z=y,V=Math.round,J=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=R(u,"rgba"),_=D(u)||"rgb";return _.substr(0,3)=="hsl"?O(Z(m),_):(m[0]=V(m[0]),m[1]=V(m[1]),m[2]=V(m[2]),(_==="rgba"||m.length>3&&m[3]<1)&&(m[3]=m.length>3?m[3]:1,_="rgba"),_+"("+m.slice(0,_==="rgb"?3:4).join(",")+")")},tt=J,xt=p.unpack,Ht=Math.round,Et=function(){for(var u,d=[],m=arguments.length;m--;)d[m]=arguments[m];d=xt(d,"hsl");var _=d[0],M=d[1],P=d[2],w,E,L;if(M===0)w=E=L=P*255;else{var $=[0,0,0],B=[0,0,0],q=P<.5?P*(1+M):P+M-P*M,H=2*P-q,Q=_/360;$[0]=Q+1/3,$[1]=Q,$[2]=Q-1/3;for(var K=0;K<3;K++)$[K]<0&&($[K]+=1),$[K]>1&&($[K]-=1),6*$[K]<1?B[K]=H+(q-H)*6*$[K]:2*$[K]<1?B[K]=q:3*$[K]<2?B[K]=H+(q-H)*(2/3-$[K])*6:B[K]=H;u=[Ht(B[0]*255),Ht(B[1]*255),Ht(B[2]*255)],w=u[0],E=u[1],L=u[2]}return d.length>3?[w,E,L,d[3]]:[w,E,L,1]},Yt=Et,Xt=Yt,ue=b,we=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,ye=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,En=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,un=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,An=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,je=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,Ai=Math.round,Rn=function(u){u=u.toLowerCase().trim();var d;if(ue.format.named)try{return ue.format.named(u)}catch(K){}if(d=u.match(we)){for(var m=d.slice(1,4),_=0;_<3;_++)m[_]=+m[_];return m[3]=1,m}if(d=u.match(ye)){for(var M=d.slice(1,5),P=0;P<4;P++)M[P]=+M[P];return M}if(d=u.match(En)){for(var w=d.slice(1,4),E=0;E<3;E++)w[E]=Ai(w[E]*2.55);return w[3]=1,w}if(d=u.match(un)){for(var L=d.slice(1,5),$=0;$<3;$++)L[$]=Ai(L[$]*2.55);return L[3]=+L[3],L}if(d=u.match(An)){var B=d.slice(1,4);B[1]*=.01,B[2]*=.01;var q=Xt(B);return q[3]=1,q}if(d=u.match(je)){var H=d.slice(1,4);H[1]*=.01,H[2]*=.01;var Q=Xt(H);return Q[3]=+d[4],Q}};Rn.test=function(u){return we.test(u)||ye.test(u)||En.test(u)||un.test(u)||An.test(u)||je.test(u)};var ei=Rn,As=j,Ri=A,ni=b,qe=p.type,Ln=tt,Fn=ei;Ri.prototype.css=function(u){return Ln(this._rgb,u)},As.css=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Ri,[null].concat(u,["css"])))},ni.format.css=Fn,ni.autodetect.push({p:5,test:function(u){for(var d=[],m=arguments.length-1;m-- >0;)d[m]=arguments[m+1];if(!d.length&&qe(u)==="string"&&Fn.test(u))return"css"}});var ii=A,Rs=j,fn=b,oe=p.unpack;fn.format.gl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=oe(u,"rgba");return m[0]*=255,m[1]*=255,m[2]*=255,m},Rs.gl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(ii,[null].concat(u,["gl"])))},ii.prototype.gl=function(){var u=this._rgb;return[u[0]/255,u[1]/255,u[2]/255,u[3]]};var mt=p.unpack,te=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=mt(u,"rgb"),_=m[0],M=m[1],P=m[2],w=Math.min(_,M,P),E=Math.max(_,M,P),L=E-w,$=L*100/255,B=w/(255-L)*100,q;return L===0?q=Number.NaN:(_===E&&(q=(M-P)/L),M===E&&(q=2+(P-_)/L),P===E&&(q=4+(_-M)/L),q*=60,q<0&&(q+=360)),[q,$,B]},Ge=te,pe=p.unpack,Ls=Math.floor,rd=function(){for(var u,d,m,_,M,P,w=[],E=arguments.length;E--;)w[E]=arguments[E];w=pe(w,"hcg");var L=w[0],$=w[1],B=w[2],q,H,Q;B=B*255;var K=$*255;if($===0)q=H=Q=B;else{L===360&&(L=0),L>360&&(L-=360),L<0&&(L+=360),L/=60;var ct=Ls(L),pt=L-ct,_t=B*(1-$),Mt=_t+K*(1-pt),ae=_t+K*pt,ne=_t+K;switch(ct){case 0:u=[ne,ae,_t],q=u[0],H=u[1],Q=u[2];break;case 1:d=[Mt,ne,_t],q=d[0],H=d[1],Q=d[2];break;case 2:m=[_t,ne,ae],q=m[0],H=m[1],Q=m[2];break;case 3:_=[_t,Mt,ne],q=_[0],H=_[1],Q=_[2];break;case 4:M=[ae,_t,ne],q=M[0],H=M[1],Q=M[2];break;case 5:P=[ne,_t,Mt],q=P[0],H=P[1],Q=P[2];break}}return[q,H,Q,w.length>3?w[3]:1]},od=rd,ad=p.unpack,ld=p.type,cd=j,wl=A,kl=b,hd=Ge;wl.prototype.hcg=function(){return hd(this._rgb)},cd.hcg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(wl,[null].concat(u,["hcg"])))},kl.format.hcg=od,kl.autodetect.push({p:1,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=ad(u,"hcg"),ld(u)==="array"&&u.length===3)return"hcg"}});var ud=p.unpack,fd=p.last,Fs=Math.round,dd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=ud(u,"rgba"),_=m[0],M=m[1],P=m[2],w=m[3],E=fd(u)||"auto";w===void 0&&(w=1),E==="auto"&&(E=w<1?"rgba":"rgb"),_=Fs(_),M=Fs(M),P=Fs(P);var L=_<<16|M<<8|P,$="000000"+L.toString(16);$=$.substr($.length-6);var B="0"+Fs(w*255).toString(16);switch(B=B.substr(B.length-2),E.toLowerCase()){case"rgba":return"#"+$+B;case"argb":return"#"+B+$;default:return"#"+$}},Sl=dd,pd=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,gd=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,md=function(u){if(u.match(pd)){(u.length===4||u.length===7)&&(u=u.substr(1)),u.length===3&&(u=u.split(""),u=u[0]+u[0]+u[1]+u[1]+u[2]+u[2]);var d=parseInt(u,16),m=d>>16,_=d>>8&255,M=d&255;return[m,_,M,1]}if(u.match(gd)){(u.length===5||u.length===9)&&(u=u.substr(1)),u.length===4&&(u=u.split(""),u=u[0]+u[0]+u[1]+u[1]+u[2]+u[2]+u[3]+u[3]);var P=parseInt(u,16),w=P>>24&255,E=P>>16&255,L=P>>8&255,$=Math.round((P&255)/255*100)/100;return[w,E,L,$]}throw new Error("unknown hex color: "+u)},Ml=md,bd=j,Cl=A,vd=p.type,Pl=b,_d=Sl;Cl.prototype.hex=function(u){return _d(this._rgb,u)},bd.hex=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Cl,[null].concat(u,["hex"])))},Pl.format.hex=Ml,Pl.autodetect.push({p:4,test:function(u){for(var d=[],m=arguments.length-1;m-- >0;)d[m]=arguments[m+1];if(!d.length&&vd(u)==="string"&&[3,4,5,6,7,8,9].indexOf(u.length)>=0)return"hex"}});var yd=p.unpack,Tl=p.TWOPI,xd=Math.min,wd=Math.sqrt,kd=Math.acos,Sd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=yd(u,"rgb"),_=m[0],M=m[1],P=m[2];_/=255,M/=255,P/=255;var w,E=xd(_,M,P),L=(_+M+P)/3,$=L>0?1-E/L:0;return $===0?w=NaN:(w=(_-M+(_-P))/2,w/=wd((_-M)*(_-M)+(_-P)*(M-P)),w=kd(w),P>M&&(w=Tl-w),w/=Tl),[w*360,$,L]},Md=Sd,Cd=p.unpack,fo=p.limit,si=p.TWOPI,po=p.PITHIRD,ri=Math.cos,Pd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=Cd(u,"hsi");var m=u[0],_=u[1],M=u[2],P,w,E;return isNaN(m)&&(m=0),isNaN(_)&&(_=0),m>360&&(m-=360),m<0&&(m+=360),m/=360,m<1/3?(E=(1-_)/3,P=(1+_*ri(si*m)/ri(po-si*m))/3,w=1-(E+P)):m<2/3?(m-=1/3,P=(1-_)/3,w=(1+_*ri(si*m)/ri(po-si*m))/3,E=1-(P+w)):(m-=2/3,w=(1-_)/3,E=(1+_*ri(si*m)/ri(po-si*m))/3,P=1-(w+E)),P=fo(M*P*3),w=fo(M*w*3),E=fo(M*E*3),[P*255,w*255,E*255,u.length>3?u[3]:1]},Td=Pd,Dd=p.unpack,Od=p.type,Ed=j,Dl=A,Ol=b,Ad=Md;Dl.prototype.hsi=function(){return Ad(this._rgb)},Ed.hsi=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Dl,[null].concat(u,["hsi"])))},Ol.format.hsi=Td,Ol.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Dd(u,"hsi"),Od(u)==="array"&&u.length===3)return"hsi"}});var Rd=p.unpack,Ld=p.type,Fd=j,El=A,Al=b,Id=y;El.prototype.hsl=function(){return Id(this._rgb)},Fd.hsl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(El,[null].concat(u,["hsl"])))},Al.format.hsl=Yt,Al.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Rd(u,"hsl"),Ld(u)==="array"&&u.length===3)return"hsl"}});var $d=p.unpack,jd=Math.min,zd=Math.max,Bd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=$d(u,"rgb");var m=u[0],_=u[1],M=u[2],P=jd(m,_,M),w=zd(m,_,M),E=w-P,L,$,B;return B=w/255,w===0?(L=Number.NaN,$=0):($=E/w,m===w&&(L=(_-M)/E),_===w&&(L=2+(M-m)/E),M===w&&(L=4+(m-_)/E),L*=60,L<0&&(L+=360)),[L,$,B]},Nd=Bd,Hd=p.unpack,Wd=Math.floor,Vd=function(){for(var u,d,m,_,M,P,w=[],E=arguments.length;E--;)w[E]=arguments[E];w=Hd(w,"hsv");var L=w[0],$=w[1],B=w[2],q,H,Q;if(B*=255,$===0)q=H=Q=B;else{L===360&&(L=0),L>360&&(L-=360),L<0&&(L+=360),L/=60;var K=Wd(L),ct=L-K,pt=B*(1-$),_t=B*(1-$*ct),Mt=B*(1-$*(1-ct));switch(K){case 0:u=[B,Mt,pt],q=u[0],H=u[1],Q=u[2];break;case 1:d=[_t,B,pt],q=d[0],H=d[1],Q=d[2];break;case 2:m=[pt,B,Mt],q=m[0],H=m[1],Q=m[2];break;case 3:_=[pt,_t,B],q=_[0],H=_[1],Q=_[2];break;case 4:M=[Mt,pt,B],q=M[0],H=M[1],Q=M[2];break;case 5:P=[B,pt,_t],q=P[0],H=P[1],Q=P[2];break}}return[q,H,Q,w.length>3?w[3]:1]},Yd=Vd,Xd=p.unpack,qd=p.type,Gd=j,Rl=A,Ll=b,Ud=Nd;Rl.prototype.hsv=function(){return Ud(this._rgb)},Gd.hsv=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(Rl,[null].concat(u,["hsv"])))},Ll.format.hsv=Yd,Ll.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Xd(u,"hsv"),qd(u)==="array"&&u.length===3)return"hsv"}});var Is={Kn:18,Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452},oi=Is,Kd=p.unpack,Fl=Math.pow,Zd=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=Kd(u,"rgb"),_=m[0],M=m[1],P=m[2],w=Jd(_,M,P),E=w[0],L=w[1],$=w[2],B=116*L-16;return[B<0?0:B,500*(E-L),200*(L-$)]},go=function(u){return(u/=255)<=.04045?u/12.92:Fl((u+.055)/1.055,2.4)},mo=function(u){return u>oi.t3?Fl(u,1/3):u/oi.t2+oi.t0},Jd=function(u,d,m){u=go(u),d=go(d),m=go(m);var _=mo((.4124564*u+.3575761*d+.1804375*m)/oi.Xn),M=mo((.2126729*u+.7151522*d+.072175*m)/oi.Yn),P=mo((.0193339*u+.119192*d+.9503041*m)/oi.Zn);return[_,M,P]},Il=Zd,ai=Is,Qd=p.unpack,tp=Math.pow,ep=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=Qd(u,"lab");var m=u[0],_=u[1],M=u[2],P,w,E,L,$,B;return w=(m+16)/116,P=isNaN(_)?w:w+_/500,E=isNaN(M)?w:w-M/200,w=ai.Yn*vo(w),P=ai.Xn*vo(P),E=ai.Zn*vo(E),L=bo(3.2404542*P-1.5371385*w-.4985314*E),$=bo(-.969266*P+1.8760108*w+.041556*E),B=bo(.0556434*P-.2040259*w+1.0572252*E),[L,$,B,u.length>3?u[3]:1]},bo=function(u){return 255*(u<=.00304?12.92*u:1.055*tp(u,1/2.4)-.055)},vo=function(u){return u>ai.t1?u*u*u:ai.t2*(u-ai.t0)},$l=ep,np=p.unpack,ip=p.type,sp=j,jl=A,zl=b,rp=Il;jl.prototype.lab=function(){return rp(this._rgb)},sp.lab=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(jl,[null].concat(u,["lab"])))},zl.format.lab=$l,zl.autodetect.push({p:2,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=np(u,"lab"),ip(u)==="array"&&u.length===3)return"lab"}});var op=p.unpack,ap=p.RAD2DEG,lp=Math.sqrt,cp=Math.atan2,hp=Math.round,up=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=op(u,"lab"),_=m[0],M=m[1],P=m[2],w=lp(M*M+P*P),E=(cp(P,M)*ap+360)%360;return hp(w*1e4)===0&&(E=Number.NaN),[_,w,E]},Bl=up,fp=p.unpack,dp=Il,pp=Bl,gp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=fp(u,"rgb"),_=m[0],M=m[1],P=m[2],w=dp(_,M,P),E=w[0],L=w[1],$=w[2];return pp(E,L,$)},mp=gp,bp=p.unpack,vp=p.DEG2RAD,_p=Math.sin,yp=Math.cos,xp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=bp(u,"lch"),_=m[0],M=m[1],P=m[2];return isNaN(P)&&(P=0),P=P*vp,[_,yp(P)*M,_p(P)*M]},Nl=xp,wp=p.unpack,kp=Nl,Sp=$l,Mp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=wp(u,"lch");var m=u[0],_=u[1],M=u[2],P=kp(m,_,M),w=P[0],E=P[1],L=P[2],$=Sp(w,E,L),B=$[0],q=$[1],H=$[2];return[B,q,H,u.length>3?u[3]:1]},Hl=Mp,Cp=p.unpack,Pp=Hl,Tp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=Cp(u,"hcl").reverse();return Pp.apply(void 0,m)},Dp=Tp,Op=p.unpack,Ep=p.type,Wl=j,$s=A,_o=b,Vl=mp;$s.prototype.lch=function(){return Vl(this._rgb)},$s.prototype.hcl=function(){return Vl(this._rgb).reverse()},Wl.lch=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply($s,[null].concat(u,["lch"])))},Wl.hcl=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply($s,[null].concat(u,["hcl"])))},_o.format.lch=Hl,_o.format.hcl=Dp,["lch","hcl"].forEach(function(u){return _o.autodetect.push({p:2,test:function(){for(var d=[],m=arguments.length;m--;)d[m]=arguments[m];if(d=Op(d,u),Ep(d)==="array"&&d.length===3)return u}})});var Ap={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflower:"#6495ed",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},Yl=Ap,Rp=A,Xl=b,Lp=p.type,Li=Yl,Fp=Ml,Ip=Sl;Rp.prototype.name=function(){for(var u=Ip(this._rgb,"rgb"),d=0,m=Object.keys(Li);d<m.length;d+=1){var _=m[d];if(Li[_]===u)return _.toLowerCase()}return u},Xl.format.named=function(u){if(u=u.toLowerCase(),Li[u])return Fp(Li[u]);throw new Error("unknown color name: "+u)},Xl.autodetect.push({p:5,test:function(u){for(var d=[],m=arguments.length-1;m-- >0;)d[m]=arguments[m+1];if(!d.length&&Lp(u)==="string"&&Li[u.toLowerCase()])return"named"}});var $p=p.unpack,jp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=$p(u,"rgb"),_=m[0],M=m[1],P=m[2];return(_<<16)+(M<<8)+P},zp=jp,Bp=p.type,Np=function(u){if(Bp(u)=="number"&&u>=0&&u<=16777215){var d=u>>16,m=u>>8&255,_=u&255;return[d,m,_,1]}throw new Error("unknown num color: "+u)},Hp=Np,Wp=j,ql=A,Gl=b,Vp=p.type,Yp=zp;ql.prototype.num=function(){return Yp(this._rgb)},Wp.num=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(ql,[null].concat(u,["num"])))},Gl.format.num=Hp,Gl.autodetect.push({p:5,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u.length===1&&Vp(u[0])==="number"&&u[0]>=0&&u[0]<=16777215)return"num"}});var Xp=j,yo=A,Ul=b,Kl=p.unpack,Zl=p.type,Jl=Math.round;yo.prototype.rgb=function(u){return u===void 0&&(u=!0),u===!1?this._rgb.slice(0,3):this._rgb.slice(0,3).map(Jl)},yo.prototype.rgba=function(u){return u===void 0&&(u=!0),this._rgb.slice(0,4).map(function(d,m){return m<3?u===!1?d:Jl(d):d})},Xp.rgb=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(yo,[null].concat(u,["rgb"])))},Ul.format.rgb=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=Kl(u,"rgba");return m[3]===void 0&&(m[3]=1),m},Ul.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=Kl(u,"rgba"),Zl(u)==="array"&&(u.length===3||u.length===4&&Zl(u[3])=="number"&&u[3]>=0&&u[3]<=1))return"rgb"}});var js=Math.log,qp=function(u){var d=u/100,m,_,M;return d<66?(m=255,_=d<6?0:-155.25485562709179-.44596950469579133*(_=d-2)+104.49216199393888*js(_),M=d<20?0:-254.76935184120902+.8274096064007395*(M=d-10)+115.67994401066147*js(M)):(m=351.97690566805693+.114206453784165*(m=d-55)-40.25366309332127*js(m),_=325.4494125711974+.07943456536662342*(_=d-50)-28.0852963507957*js(_),M=255),[m,_,M,1]},Ql=qp,Gp=Ql,Up=p.unpack,Kp=Math.round,Zp=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];for(var m=Up(u,"rgb"),_=m[0],M=m[2],P=1e3,w=4e4,E=.4,L;w-P>E;){L=(w+P)*.5;var $=Gp(L);$[2]/$[0]>=M/_?w=L:P=L}return Kp(L)},Jp=Zp,xo=j,zs=A,wo=b,Qp=Jp;zs.prototype.temp=zs.prototype.kelvin=zs.prototype.temperature=function(){return Qp(this._rgb)},xo.temp=xo.kelvin=xo.temperature=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(zs,[null].concat(u,["temp"])))},wo.format.temp=wo.format.kelvin=wo.format.temperature=Ql;var tg=p.unpack,ko=Math.cbrt,eg=Math.pow,ng=Math.sign,ig=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=tg(u,"rgb"),_=m[0],M=m[1],P=m[2],w=[So(_/255),So(M/255),So(P/255)],E=w[0],L=w[1],$=w[2],B=ko(.4122214708*E+.5363325363*L+.0514459929*$),q=ko(.2119034982*E+.6806995451*L+.1073969566*$),H=ko(.0883024619*E+.2817188376*L+.6299787005*$);return[.2104542553*B+.793617785*q-.0040720468*H,1.9779984951*B-2.428592205*q+.4505937099*H,.0259040371*B+.7827717662*q-.808675766*H]},tc=ig;function So(u){var d=Math.abs(u);return d<.04045?u/12.92:(ng(u)||1)*eg((d+.055)/1.055,2.4)}var sg=p.unpack,Bs=Math.pow,rg=Math.sign,og=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=sg(u,"lab");var m=u[0],_=u[1],M=u[2],P=Bs(m+.3963377774*_+.2158037573*M,3),w=Bs(m-.1055613458*_-.0638541728*M,3),E=Bs(m-.0894841775*_-1.291485548*M,3);return[255*Mo(4.0767416621*P-3.3077115913*w+.2309699292*E),255*Mo(-1.2684380046*P+2.6097574011*w-.3413193965*E),255*Mo(-.0041960863*P-.7034186147*w+1.707614701*E),u.length>3?u[3]:1]},ec=og;function Mo(u){var d=Math.abs(u);return d>.0031308?(rg(u)||1)*(1.055*Bs(d,1/2.4)-.055):u*12.92}var ag=p.unpack,lg=p.type,cg=j,nc=A,ic=b,hg=tc;nc.prototype.oklab=function(){return hg(this._rgb)},cg.oklab=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(nc,[null].concat(u,["oklab"])))},ic.format.oklab=ec,ic.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=ag(u,"oklab"),lg(u)==="array"&&u.length===3)return"oklab"}});var ug=p.unpack,fg=tc,dg=Bl,pg=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];var m=ug(u,"rgb"),_=m[0],M=m[1],P=m[2],w=fg(_,M,P),E=w[0],L=w[1],$=w[2];return dg(E,L,$)},gg=pg,mg=p.unpack,bg=Nl,vg=ec,_g=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];u=mg(u,"lch");var m=u[0],_=u[1],M=u[2],P=bg(m,_,M),w=P[0],E=P[1],L=P[2],$=vg(w,E,L),B=$[0],q=$[1],H=$[2];return[B,q,H,u.length>3?u[3]:1]},yg=_g,xg=p.unpack,wg=p.type,kg=j,sc=A,rc=b,Sg=gg;sc.prototype.oklch=function(){return Sg(this._rgb)},kg.oklch=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];return new(Function.prototype.bind.apply(sc,[null].concat(u,["oklch"])))},rc.format.oklch=yg,rc.autodetect.push({p:3,test:function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];if(u=xg(u,"oklch"),wg(u)==="array"&&u.length===3)return"oklch"}});var oc=A,Mg=p.type;oc.prototype.alpha=function(u,d){return d===void 0&&(d=!1),u!==void 0&&Mg(u)==="number"?d?(this._rgb[3]=u,this):new oc([this._rgb[0],this._rgb[1],this._rgb[2],u],"rgb"):this._rgb[3]};var Cg=A;Cg.prototype.clipped=function(){return this._rgb._clipped||!1};var In=A,Pg=Is;In.prototype.darken=function(u){u===void 0&&(u=1);var d=this,m=d.lab();return m[0]-=Pg.Kn*u,new In(m,"lab").alpha(d.alpha(),!0)},In.prototype.brighten=function(u){return u===void 0&&(u=1),this.darken(-u)},In.prototype.darker=In.prototype.darken,In.prototype.brighter=In.prototype.brighten;var Tg=A;Tg.prototype.get=function(u){var d=u.split("."),m=d[0],_=d[1],M=this[m]();if(_){var P=m.indexOf(_)-(m.substr(0,2)==="ok"?2:0);if(P>-1)return M[P];throw new Error("unknown channel "+_+" in mode "+m)}else return M};var li=A,Dg=p.type,Og=Math.pow,Eg=1e-7,Ag=20;li.prototype.luminance=function(u){if(u!==void 0&&Dg(u)==="number"){if(u===0)return new li([0,0,0,this._rgb[3]],"rgb");if(u===1)return new li([255,255,255,this._rgb[3]],"rgb");var d=this.luminance(),m="rgb",_=Ag,M=function(w,E){var L=w.interpolate(E,.5,m),$=L.luminance();return Math.abs(u-$)<Eg||!_--?L:$>u?M(w,L):M(L,E)},P=(d>u?M(new li([0,0,0]),this):M(this,new li([255,255,255]))).rgb();return new li(P.concat([this._rgb[3]]))}return Rg.apply(void 0,this._rgb.slice(0,3))};var Rg=function(u,d,m){return u=Co(u),d=Co(d),m=Co(m),.2126*u+.7152*d+.0722*m},Co=function(u){return u/=255,u<=.03928?u/12.92:Og((u+.055)/1.055,2.4)},xe={},ac=A,lc=p.type,Ns=xe,cc=function(u,d,m){m===void 0&&(m=.5);for(var _=[],M=arguments.length-3;M-- >0;)_[M]=arguments[M+3];var P=_[0]||"lrgb";if(!Ns[P]&&!_.length&&(P=Object.keys(Ns)[0]),!Ns[P])throw new Error("interpolation mode "+P+" is not defined");return lc(u)!=="object"&&(u=new ac(u)),lc(d)!=="object"&&(d=new ac(d)),Ns[P](u,d,m).alpha(u.alpha()+m*(d.alpha()-u.alpha()))},hc=A,Lg=cc;hc.prototype.mix=hc.prototype.interpolate=function(u,d){d===void 0&&(d=.5);for(var m=[],_=arguments.length-2;_-- >0;)m[_]=arguments[_+2];return Lg.apply(void 0,[this,u,d].concat(m))};var uc=A;uc.prototype.premultiply=function(u){u===void 0&&(u=!1);var d=this._rgb,m=d[3];return u?(this._rgb=[d[0]*m,d[1]*m,d[2]*m,m],this):new uc([d[0]*m,d[1]*m,d[2]*m,m],"rgb")};var Po=A,Fg=Is;Po.prototype.saturate=function(u){u===void 0&&(u=1);var d=this,m=d.lch();return m[1]+=Fg.Kn*u,m[1]<0&&(m[1]=0),new Po(m,"lch").alpha(d.alpha(),!0)},Po.prototype.desaturate=function(u){return u===void 0&&(u=1),this.saturate(-u)};var fc=A,dc=p.type;fc.prototype.set=function(u,d,m){m===void 0&&(m=!1);var _=u.split("."),M=_[0],P=_[1],w=this[M]();if(P){var E=M.indexOf(P)-(M.substr(0,2)==="ok"?2:0);if(E>-1){if(dc(d)=="string")switch(d.charAt(0)){case"+":w[E]+=+d;break;case"-":w[E]+=+d;break;case"*":w[E]*=+d.substr(1);break;case"/":w[E]/=+d.substr(1);break;default:w[E]=+d}else if(dc(d)==="number")w[E]=d;else throw new Error("unsupported value for Color.set");var L=new fc(w,M);return m?(this._rgb=L._rgb,this):L}throw new Error("unknown channel "+P+" in mode "+M)}else return w};var Ig=A,$g=function(u,d,m){var _=u._rgb,M=d._rgb;return new Ig(_[0]+m*(M[0]-_[0]),_[1]+m*(M[1]-_[1]),_[2]+m*(M[2]-_[2]),"rgb")};xe.rgb=$g;var jg=A,To=Math.sqrt,ci=Math.pow,zg=function(u,d,m){var _=u._rgb,M=_[0],P=_[1],w=_[2],E=d._rgb,L=E[0],$=E[1],B=E[2];return new jg(To(ci(M,2)*(1-m)+ci(L,2)*m),To(ci(P,2)*(1-m)+ci($,2)*m),To(ci(w,2)*(1-m)+ci(B,2)*m),"rgb")};xe.lrgb=zg;var Bg=A,Ng=function(u,d,m){var _=u.lab(),M=d.lab();return new Bg(_[0]+m*(M[0]-_[0]),_[1]+m*(M[1]-_[1]),_[2]+m*(M[2]-_[2]),"lab")};xe.lab=Ng;var pc=A,hi=function(u,d,m,_){var M,P,w,E;_==="hsl"?(w=u.hsl(),E=d.hsl()):_==="hsv"?(w=u.hsv(),E=d.hsv()):_==="hcg"?(w=u.hcg(),E=d.hcg()):_==="hsi"?(w=u.hsi(),E=d.hsi()):_==="lch"||_==="hcl"?(_="hcl",w=u.hcl(),E=d.hcl()):_==="oklch"&&(w=u.oklch().reverse(),E=d.oklch().reverse());var L,$,B,q,H,Q;(_.substr(0,1)==="h"||_==="oklch")&&(M=w,L=M[0],B=M[1],H=M[2],P=E,$=P[0],q=P[1],Q=P[2]);var K,ct,pt,_t;return!isNaN(L)&&!isNaN($)?($>L&&$-L>180?_t=$-(L+360):$<L&&L-$>180?_t=$+360-L:_t=$-L,ct=L+m*_t):isNaN(L)?isNaN($)?ct=Number.NaN:(ct=$,(H==1||H==0)&&_!="hsv"&&(K=q)):(ct=L,(Q==1||Q==0)&&_!="hsv"&&(K=B)),K===void 0&&(K=B+m*(q-B)),pt=H+m*(Q-H),_==="oklch"?new pc([pt,K,ct],_):new pc([ct,K,pt],_)},Hg=hi,gc=function(u,d,m){return Hg(u,d,m,"lch")};xe.lch=gc,xe.hcl=gc;var Wg=A,Vg=function(u,d,m){var _=u.num(),M=d.num();return new Wg(_+m*(M-_),"num")};xe.num=Vg;var Yg=hi,Xg=function(u,d,m){return Yg(u,d,m,"hcg")};xe.hcg=Xg;var qg=hi,Gg=function(u,d,m){return qg(u,d,m,"hsi")};xe.hsi=Gg;var Ug=hi,Kg=function(u,d,m){return Ug(u,d,m,"hsl")};xe.hsl=Kg;var Zg=hi,Jg=function(u,d,m){return Zg(u,d,m,"hsv")};xe.hsv=Jg;var Qg=A,tm=function(u,d,m){var _=u.oklab(),M=d.oklab();return new Qg(_[0]+m*(M[0]-_[0]),_[1]+m*(M[1]-_[1]),_[2]+m*(M[2]-_[2]),"oklab")};xe.oklab=tm;var em=hi,nm=function(u,d,m){return em(u,d,m,"oklch")};xe.oklch=nm;var Do=A,im=p.clip_rgb,Oo=Math.pow,Eo=Math.sqrt,Ao=Math.PI,mc=Math.cos,bc=Math.sin,sm=Math.atan2,rm=function(u,d,m){d===void 0&&(d="lrgb"),m===void 0&&(m=null);var _=u.length;m||(m=Array.from(new Array(_)).map(function(){return 1}));var M=_/m.reduce(function(ct,pt){return ct+pt});if(m.forEach(function(ct,pt){m[pt]*=M}),u=u.map(function(ct){return new Do(ct)}),d==="lrgb")return om(u,m);for(var P=u.shift(),w=P.get(d),E=[],L=0,$=0,B=0;B<w.length;B++)if(w[B]=(w[B]||0)*m[0],E.push(isNaN(w[B])?0:m[0]),d.charAt(B)==="h"&&!isNaN(w[B])){var q=w[B]/180*Ao;L+=mc(q)*m[0],$+=bc(q)*m[0]}var H=P.alpha()*m[0];u.forEach(function(ct,pt){var _t=ct.get(d);H+=ct.alpha()*m[pt+1];for(var Mt=0;Mt<w.length;Mt++)if(!isNaN(_t[Mt]))if(E[Mt]+=m[pt+1],d.charAt(Mt)==="h"){var ae=_t[Mt]/180*Ao;L+=mc(ae)*m[pt+1],$+=bc(ae)*m[pt+1]}else w[Mt]+=_t[Mt]*m[pt+1]});for(var Q=0;Q<w.length;Q++)if(d.charAt(Q)==="h"){for(var K=sm($/E[Q],L/E[Q])/Ao*180;K<0;)K+=360;for(;K>=360;)K-=360;w[Q]=K}else w[Q]=w[Q]/E[Q];return H/=_,new Do(w,d).alpha(H>.99999?1:H,!0)},om=function(u,d){for(var m=u.length,_=[0,0,0,0],M=0;M<u.length;M++){var P=u[M],w=d[M]/m,E=P._rgb;_[0]+=Oo(E[0],2)*w,_[1]+=Oo(E[1],2)*w,_[2]+=Oo(E[2],2)*w,_[3]+=E[3]*w}return _[0]=Eo(_[0]),_[1]=Eo(_[1]),_[2]=Eo(_[2]),_[3]>.9999999&&(_[3]=1),new Do(im(_))},Pe=j,ui=p.type,am=Math.pow,Ro=function(u){var d="rgb",m=Pe("#ccc"),_=0,M=[0,1],P=[],w=[0,0],E=!1,L=[],$=!1,B=0,q=1,H=!1,Q={},K=!0,ct=1,pt=function(W){if(W=W||["#fff","#000"],W&&ui(W)==="string"&&Pe.brewer&&Pe.brewer[W.toLowerCase()]&&(W=Pe.brewer[W.toLowerCase()]),ui(W)==="array"){W.length===1&&(W=[W[0],W[0]]),W=W.slice(0);for(var it=0;it<W.length;it++)W[it]=Pe(W[it]);P.length=0;for(var ft=0;ft<W.length;ft++)P.push(ft/(W.length-1))}return ge(),L=W},_t=function(W){if(E!=null){for(var it=E.length-1,ft=0;ft<it&&W>=E[ft];)ft++;return ft-1}return 0},Mt=function(W){return W},ae=function(W){return W},ne=function(W,it){var ft,ht;if(it==null&&(it=!1),isNaN(W)||W===null)return m;if(it)ht=W;else if(E&&E.length>2){var le=_t(W);ht=le/(E.length-2)}else q!==B?ht=(W-B)/(q-B):ht=1;ht=ae(ht),it||(ht=Mt(ht)),ct!==1&&(ht=am(ht,ct)),ht=w[0]+ht*(1-w[0]-w[1]),ht=Math.min(1,Math.max(0,ht));var It=Math.floor(ht*1e4);if(K&&Q[It])ft=Q[It];else{if(ui(L)==="array")for(var yt=0;yt<P.length;yt++){var Tt=P[yt];if(ht<=Tt){ft=L[yt];break}if(ht>=Tt&&yt===P.length-1){ft=L[yt];break}if(ht>Tt&&ht<P[yt+1]){ht=(ht-Tt)/(P[yt+1]-Tt),ft=Pe.interpolate(L[yt],L[yt+1],ht,d);break}}else ui(L)==="function"&&(ft=L(ht));K&&(Q[It]=ft)}return ft},ge=function(){return Q={}};pt(u);var bt=function(W){var it=Pe(ne(W));return $&&it[$]?it[$]():it};return bt.classes=function(W){if(W!=null){if(ui(W)==="array")E=W,M=[W[0],W[W.length-1]];else{var it=Pe.analyze(M);W===0?E=[it.min,it.max]:E=Pe.limits(it,"e",W)}return bt}return E},bt.domain=function(W){if(!arguments.length)return M;B=W[0],q=W[W.length-1],P=[];var it=L.length;if(W.length===it&&B!==q)for(var ft=0,ht=Array.from(W);ft<ht.length;ft+=1){var le=ht[ft];P.push((le-B)/(q-B))}else{for(var It=0;It<it;It++)P.push(It/(it-1));if(W.length>2){var yt=W.map(function(Dt,At){return At/(W.length-1)}),Tt=W.map(function(Dt){return(Dt-B)/(q-B)});Tt.every(function(Dt,At){return yt[At]===Dt})||(ae=function(Dt){if(Dt<=0||Dt>=1)return Dt;for(var At=0;Dt>=Tt[At+1];)At++;var De=(Dt-Tt[At])/(Tt[At+1]-Tt[At]),gn=yt[At]+De*(yt[At+1]-yt[At]);return gn})}}return M=[B,q],bt},bt.mode=function(W){return arguments.length?(d=W,ge(),bt):d},bt.range=function(W,it){return pt(W),bt},bt.out=function(W){return $=W,bt},bt.spread=function(W){return arguments.length?(_=W,bt):_},bt.correctLightness=function(W){return W==null&&(W=!0),H=W,ge(),H?Mt=function(it){for(var ft=ne(0,!0).lab()[0],ht=ne(1,!0).lab()[0],le=ft>ht,It=ne(it,!0).lab()[0],yt=ft+(ht-ft)*it,Tt=It-yt,Dt=0,At=1,De=20;Math.abs(Tt)>.01&&De-- >0;)(function(){return le&&(Tt*=-1),Tt<0?(Dt=it,it+=(At-it)*.5):(At=it,it+=(Dt-it)*.5),It=ne(it,!0).lab()[0],Tt=It-yt})();return it}:Mt=function(it){return it},bt},bt.padding=function(W){return W!=null?(ui(W)==="number"&&(W=[W,W]),w=W,bt):w},bt.colors=function(W,it){arguments.length<2&&(it="hex");var ft=[];if(arguments.length===0)ft=L.slice(0);else if(W===1)ft=[bt(.5)];else if(W>1){var ht=M[0],le=M[1]-ht;ft=lm(0,W,!1).map(function(At){return bt(ht+At/(W-1)*le)})}else{u=[];var It=[];if(E&&E.length>2)for(var yt=1,Tt=E.length,Dt=1<=Tt;Dt?yt<Tt:yt>Tt;Dt?yt++:yt--)It.push((E[yt-1]+E[yt])*.5);else It=M;ft=It.map(function(At){return bt(At)})}return Pe[it]&&(ft=ft.map(function(At){return At[it]()})),ft},bt.cache=function(W){return W!=null?(K=W,bt):K},bt.gamma=function(W){return W!=null?(ct=W,bt):ct},bt.nodata=function(W){return W!=null?(m=Pe(W),bt):m},bt};function lm(u,d,m){for(var _=[],M=u<d,P=m?M?d+1:d-1:d,w=u;M?w<P:w>P;M?w++:w--)_.push(w);return _}var Fi=A,cm=Ro,hm=function(u){for(var d=[1,1],m=1;m<u;m++){for(var _=[1],M=1;M<=d.length;M++)_[M]=(d[M]||0)+d[M-1];d=_}return d},um=function(u){var d,m,_,M,P,w,E;if(u=u.map(function(H){return new Fi(H)}),u.length===2)d=u.map(function(H){return H.lab()}),P=d[0],w=d[1],M=function(H){var Q=[0,1,2].map(function(K){return P[K]+H*(w[K]-P[K])});return new Fi(Q,"lab")};else if(u.length===3)m=u.map(function(H){return H.lab()}),P=m[0],w=m[1],E=m[2],M=function(H){var Q=[0,1,2].map(function(K){return(1-H)*(1-H)*P[K]+2*(1-H)*H*w[K]+H*H*E[K]});return new Fi(Q,"lab")};else if(u.length===4){var L;_=u.map(function(H){return H.lab()}),P=_[0],w=_[1],E=_[2],L=_[3],M=function(H){var Q=[0,1,2].map(function(K){return(1-H)*(1-H)*(1-H)*P[K]+3*(1-H)*(1-H)*H*w[K]+3*(1-H)*H*H*E[K]+H*H*H*L[K]});return new Fi(Q,"lab")}}else if(u.length>=5){var $,B,q;$=u.map(function(H){return H.lab()}),q=u.length-1,B=hm(q),M=function(H){var Q=1-H,K=[0,1,2].map(function(ct){return $.reduce(function(pt,_t,Mt){return pt+B[Mt]*Math.pow(Q,q-Mt)*Math.pow(H,Mt)*_t[ct]},0)});return new Fi(K,"lab")}}else throw new RangeError("No point in running bezier with only one color.");return M},fm=function(u){var d=um(u);return d.scale=function(){return cm(d)},d},Lo=j,Te=function(u,d,m){if(!Te[m])throw new Error("unknown blend mode "+m);return Te[m](u,d)},dn=function(u){return function(d,m){var _=Lo(m).rgb(),M=Lo(d).rgb();return Lo.rgb(u(_,M))}},pn=function(u){return function(d,m){var _=[];return _[0]=u(d[0],m[0]),_[1]=u(d[1],m[1]),_[2]=u(d[2],m[2]),_}},dm=function(u){return u},pm=function(u,d){return u*d/255},gm=function(u,d){return u>d?d:u},mm=function(u,d){return u>d?u:d},bm=function(u,d){return 255*(1-(1-u/255)*(1-d/255))},vm=function(u,d){return d<128?2*u*d/255:255*(1-2*(1-u/255)*(1-d/255))},_m=function(u,d){return 255*(1-(1-d/255)/(u/255))},ym=function(u,d){return u===255?255:(u=255*(d/255)/(1-u/255),u>255?255:u)};Te.normal=dn(pn(dm)),Te.multiply=dn(pn(pm)),Te.screen=dn(pn(bm)),Te.overlay=dn(pn(vm)),Te.darken=dn(pn(gm)),Te.lighten=dn(pn(mm)),Te.dodge=dn(pn(ym)),Te.burn=dn(pn(_m));for(var xm=Te,Fo=p.type,wm=p.clip_rgb,km=p.TWOPI,Sm=Math.pow,Mm=Math.sin,Cm=Math.cos,vc=j,Pm=function(u,d,m,_,M){u===void 0&&(u=300),d===void 0&&(d=-1.5),m===void 0&&(m=1),_===void 0&&(_=1),M===void 0&&(M=[0,1]);var P=0,w;Fo(M)==="array"?w=M[1]-M[0]:(w=0,M=[M,M]);var E=function(L){var $=km*((u+120)/360+d*L),B=Sm(M[0]+w*L,_),q=P!==0?m[0]+L*P:m,H=q*B*(1-B)/2,Q=Cm($),K=Mm($),ct=B+H*(-.14861*Q+1.78277*K),pt=B+H*(-.29227*Q-.90649*K),_t=B+H*(1.97294*Q);return vc(wm([ct*255,pt*255,_t*255,1]))};return E.start=function(L){return L==null?u:(u=L,E)},E.rotations=function(L){return L==null?d:(d=L,E)},E.gamma=function(L){return L==null?_:(_=L,E)},E.hue=function(L){return L==null?m:(m=L,Fo(m)==="array"?(P=m[1]-m[0],P===0&&(m=m[1])):P=0,E)},E.lightness=function(L){return L==null?M:(Fo(L)==="array"?(M=L,w=L[1]-L[0]):(M=[L,L],w=0),E)},E.scale=function(){return vc.scale(E)},E.hue(m),E},Tm=A,Dm="0123456789abcdef",Om=Math.floor,Em=Math.random,Am=function(){for(var u="#",d=0;d<6;d++)u+=Dm.charAt(Om(Em()*16));return new Tm(u,"hex")},Io=a,_c=Math.log,Rm=Math.pow,Lm=Math.floor,Fm=Math.abs,yc=function(u,d){d===void 0&&(d=null);var m={min:Number.MAX_VALUE,max:Number.MAX_VALUE*-1,sum:0,values:[],count:0};return Io(u)==="object"&&(u=Object.values(u)),u.forEach(function(_){d&&Io(_)==="object"&&(_=_[d]),_!=null&&!isNaN(_)&&(m.values.push(_),m.sum+=_,_<m.min&&(m.min=_),_>m.max&&(m.max=_),m.count+=1)}),m.domain=[m.min,m.max],m.limits=function(_,M){return xc(m,_,M)},m},xc=function(u,d,m){d===void 0&&(d="equal"),m===void 0&&(m=7),Io(u)=="array"&&(u=yc(u));var _=u.min,M=u.max,P=u.values.sort(function(jo,zo){return jo-zo});if(m===1)return[_,M];var w=[];if(d.substr(0,1)==="c"&&(w.push(_),w.push(M)),d.substr(0,1)==="e"){w.push(_);for(var E=1;E<m;E++)w.push(_+E/m*(M-_));w.push(M)}else if(d.substr(0,1)==="l"){if(_<=0)throw new Error("Logarithmic scales are only possible for values > 0");var L=Math.LOG10E*_c(_),$=Math.LOG10E*_c(M);w.push(_);for(var B=1;B<m;B++)w.push(Rm(10,L+B/m*($-L)));w.push(M)}else if(d.substr(0,1)==="q"){w.push(_);for(var q=1;q<m;q++){var H=(P.length-1)*q/m,Q=Lm(H);if(Q===H)w.push(P[Q]);else{var K=H-Q;w.push(P[Q]*(1-K)+P[Q+1]*K)}}w.push(M)}else if(d.substr(0,1)==="k"){var ct,pt=P.length,_t=new Array(pt),Mt=new Array(m),ae=!0,ne=0,ge=null;ge=[],ge.push(_);for(var bt=1;bt<m;bt++)ge.push(_+bt/m*(M-_));for(ge.push(M);ae;){for(var W=0;W<m;W++)Mt[W]=0;for(var it=0;it<pt;it++)for(var ft=P[it],ht=Number.MAX_VALUE,le=void 0,It=0;It<m;It++){var yt=Fm(ge[It]-ft);yt<ht&&(ht=yt,le=It),Mt[le]++,_t[it]=le}for(var Tt=new Array(m),Dt=0;Dt<m;Dt++)Tt[Dt]=null;for(var At=0;At<pt;At++)ct=_t[At],Tt[ct]===null?Tt[ct]=P[At]:Tt[ct]+=P[At];for(var De=0;De<m;De++)Tt[De]*=1/Mt[De];ae=!1;for(var gn=0;gn<m;gn++)if(Tt[gn]!==ge[gn]){ae=!0;break}ge=Tt,ne++,ne>200&&(ae=!1)}for(var mn={},fi=0;fi<m;fi++)mn[fi]=[];for(var di=0;di<pt;di++)ct=_t[di],mn[ct].push(P[di]);for(var Ke=[],$n=0;$n<m;$n++)Ke.push(mn[$n][0]),Ke.push(mn[$n][mn[$n].length-1]);Ke=Ke.sort(function(jo,zo){return jo-zo}),w.push(Ke[0]);for(var Ii=1;Ii<Ke.length;Ii+=2){var jn=Ke[Ii];!isNaN(jn)&&w.indexOf(jn)===-1&&w.push(jn)}}return w},wc={analyze:yc,limits:xc},kc=A,Im=function(u,d){u=new kc(u),d=new kc(d);var m=u.luminance(),_=d.luminance();return m>_?(m+.05)/(_+.05):(_+.05)/(m+.05)},Sc=A,Ue=Math.sqrt,Ut=Math.pow,$m=Math.min,jm=Math.max,Mc=Math.atan2,Cc=Math.abs,Hs=Math.cos,Pc=Math.sin,zm=Math.exp,Tc=Math.PI,Bm=function(u,d,m,_,M){m===void 0&&(m=1),_===void 0&&(_=1),M===void 0&&(M=1);var P=function(jn){return 360*jn/(2*Tc)},w=function(jn){return 2*Tc*jn/360};u=new Sc(u),d=new Sc(d);var E=Array.from(u.lab()),L=E[0],$=E[1],B=E[2],q=Array.from(d.lab()),H=q[0],Q=q[1],K=q[2],ct=(L+H)/2,pt=Ue(Ut($,2)+Ut(B,2)),_t=Ue(Ut(Q,2)+Ut(K,2)),Mt=(pt+_t)/2,ae=.5*(1-Ue(Ut(Mt,7)/(Ut(Mt,7)+Ut(25,7)))),ne=$*(1+ae),ge=Q*(1+ae),bt=Ue(Ut(ne,2)+Ut(B,2)),W=Ue(Ut(ge,2)+Ut(K,2)),it=(bt+W)/2,ft=P(Mc(B,ne)),ht=P(Mc(K,ge)),le=ft>=0?ft:ft+360,It=ht>=0?ht:ht+360,yt=Cc(le-It)>180?(le+It+360)/2:(le+It)/2,Tt=1-.17*Hs(w(yt-30))+.24*Hs(w(2*yt))+.32*Hs(w(3*yt+6))-.2*Hs(w(4*yt-63)),Dt=It-le;Dt=Cc(Dt)<=180?Dt:It<=le?Dt+360:Dt-360,Dt=2*Ue(bt*W)*Pc(w(Dt)/2);var At=H-L,De=W-bt,gn=1+.015*Ut(ct-50,2)/Ue(20+Ut(ct-50,2)),mn=1+.045*it,fi=1+.015*it*Tt,di=30*zm(-Ut((yt-275)/25,2)),Ke=2*Ue(Ut(it,7)/(Ut(it,7)+Ut(25,7))),$n=-Ke*Pc(2*w(di)),Ii=Ue(Ut(At/(m*gn),2)+Ut(De/(_*mn),2)+Ut(Dt/(M*fi),2)+$n*(De/(_*mn))*(Dt/(M*fi)));return jm(0,$m(100,Ii))},Dc=A,Nm=function(u,d,m){m===void 0&&(m="lab"),u=new Dc(u),d=new Dc(d);var _=u.get(m),M=d.get(m),P=0;for(var w in _){var E=(_[w]||0)-(M[w]||0);P+=E*E}return Math.sqrt(P)},Hm=A,Wm=function(){for(var u=[],d=arguments.length;d--;)u[d]=arguments[d];try{return new(Function.prototype.bind.apply(Hm,[null].concat(u))),!0}catch(m){return!1}},Oc=j,Ec=Ro,Vm={cool:function(){return Ec([Oc.hsl(180,1,.9),Oc.hsl(250,.7,.4)])},hot:function(){return Ec(["#000","#f00","#ff0","#fff"]).mode("rgb")}},Ws={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},$o=0,Ac=Object.keys(Ws);$o<Ac.length;$o+=1){var Rc=Ac[$o];Ws[Rc.toLowerCase()]=Ws[Rc]}var Ym=Ws,ee=j;ee.average=rm,ee.bezier=fm,ee.blend=xm,ee.cubehelix=Pm,ee.mix=ee.interpolate=cc,ee.random=Am,ee.scale=Ro,ee.analyze=wc.analyze,ee.contrast=Im,ee.deltaE=Bm,ee.distance=Nm,ee.limits=wc.limits,ee.valid=Wm,ee.scales=Vm,ee.colors=Yl,ee.brewer=Ym;var Xm=ee;return Xm})});var Nu=Bo((Hr,Bu)=>{(function(n,t){typeof Hr=="object"&&typeof Bu!="undefined"?t(Hr):typeof define=="function"&&define.amd?define("@ts-stack/markdown",["exports"],t):(n=typeof globalThis!="undefined"?globalThis:n||self,t((n["ts-stack"]=n["ts-stack"]||{},n["ts-stack"].markdown={})))})(Hr,function(n){"use strict";var t=function(){function k(v,y){y===void 0&&(y=""),this.source=v.source,this.flags=y}return k.prototype.setGroup=function(v,y){var R=typeof y=="string"?y:y.source;return R=R.replace(/(^|[^\[])\^/g,"$1"),this.source=this.source.replace(v,R),this},k.prototype.getRegexp=function(){return new RegExp(this.source,this.flags)},k}();var e=/[&<>"']/,i=/[&<>"']/g,s={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},r=/[<>"']|&(?!#?\w+;)/,o=/[<>"']|&(?!#?\w+;)/g;function a(k,v){if(v){if(e.test(k))return k.replace(i,function(y){return s[y]})}else if(r.test(k))return k.replace(o,function(y){return s[y]});return k}function l(k){return k.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,function(v,y){return y=y.toLowerCase(),y==="colon"?":":y.charAt(0)==="#"?y.charAt(1)==="x"?String.fromCharCode(parseInt(y.substring(2),16)):String.fromCharCode(+y.substring(1)):""})}n.TokenType=void 0,function(k){k[k.space=1]="space",k[k.text=2]="text",k[k.paragraph=3]="paragraph",k[k.heading=4]="heading",k[k.listStart=5]="listStart",k[k.listEnd=6]="listEnd",k[k.looseItemStart=7]="looseItemStart",k[k.looseItemEnd=8]="looseItemEnd",k[k.listItemStart=9]="listItemStart",k[k.listItemEnd=10]="listItemEnd",k[k.blockquoteStart=11]="blockquoteStart",k[k.blockquoteEnd=12]="blockquoteEnd",k[k.code=13]="code",k[k.table=14]="table",k[k.html=15]="html",k[k.hr=16]="hr"}(n.TokenType||(n.TokenType={}));var c=function(){function k(){this.gfm=!0,this.tables=!0,this.breaks=!1,this.pedantic=!1,this.sanitize=!1,this.mangle=!0,this.smartLists=!1,this.silent=!1,this.langPrefix="lang-",this.smartypants=!1,this.headerPrefix="",this.xhtml=!1,this.escape=a,this.unescape=l}return k}();var h=function(k,v){return h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,R){y.__proto__=R}||function(y,R){for(var D in R)Object.prototype.hasOwnProperty.call(R,D)&&(y[D]=R[D])},h(k,v)};function f(k,v){if(typeof v!="function"&&v!==null)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");h(k,v);function y(){this.constructor=k}k.prototype=v===null?Object.create(v):(y.prototype=v.prototype,new y)}var g=function(){return g=Object.assign||function(v){for(var y,R=1,D=arguments.length;R<D;R++){y=arguments[R];for(var O in y)Object.prototype.hasOwnProperty.call(y,O)&&(v[O]=y[O])}return v},g.apply(this,arguments)};function p(k,v){var y={};for(var R in k)Object.prototype.hasOwnProperty.call(k,R)&&v.indexOf(R)<0&&(y[R]=k[R]);if(k!=null&&typeof Object.getOwnPropertySymbols=="function")for(var D=0,R=Object.getOwnPropertySymbols(k);D<R.length;D++)v.indexOf(R[D])<0&&Object.prototype.propertyIsEnumerable.call(k,R[D])&&(y[R[D]]=k[R[D]]);return y}function b(k,v,y,R){var D=arguments.length,O=D<3?v:R===null?R=Object.getOwnPropertyDescriptor(v,y):R,Z;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(k,v,y,R);else for(var V=k.length-1;V>=0;V--)(Z=k[V])&&(O=(D<3?Z(O):D>3?Z(v,y,O):Z(v,y))||O);return D>3&&O&&Object.defineProperty(v,y,O),O}function x(k,v){return function(y,R){v(y,R,k)}}function S(k,v){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(k,v)}function C(k,v,y,R){function D(O){return O instanceof y?O:new y(function(Z){Z(O)})}return new(y||(y=Promise))(function(O,Z){function V(xt){try{tt(R.next(xt))}catch(Ht){Z(Ht)}}function J(xt){try{tt(R.throw(xt))}catch(Ht){Z(Ht)}}function tt(xt){xt.done?O(xt.value):D(xt.value).then(V,J)}tt((R=R.apply(k,v||[])).next())})}function T(k,v){var y={label:0,sent:function(){if(O[0]&1)throw O[1];return O[1]},trys:[],ops:[]},R,D,O,Z;return Z={next:V(0),throw:V(1),return:V(2)},typeof Symbol=="function"&&(Z[Symbol.iterator]=function(){return this}),Z;function V(tt){return function(xt){return J([tt,xt])}}function J(tt){if(R)throw new TypeError("Generator is already executing.");for(;y;)try{if(R=1,D&&(O=tt[0]&2?D.return:tt[0]?D.throw||((O=D.return)&&O.call(D),0):D.next)&&!(O=O.call(D,tt[1])).done)return O;switch(D=0,O&&(tt=[tt[0]&2,O.value]),tt[0]){case 0:case 1:O=tt;break;case 4:return y.label++,{value:tt[1],done:!1};case 5:y.label++,D=tt[1],tt=[0];continue;case 7:tt=y.ops.pop(),y.trys.pop();continue;default:if(O=y.trys,!(O=O.length>0&&O[O.length-1])&&(tt[0]===6||tt[0]===2)){y=0;continue}if(tt[0]===3&&(!O||tt[1]>O[0]&&tt[1]<O[3])){y.label=tt[1];break}if(tt[0]===6&&y.label<O[1]){y.label=O[1],O=tt;break}if(O&&y.label<O[2]){y.label=O[2],y.ops.push(tt);break}O[2]&&y.ops.pop(),y.trys.pop();continue}tt=v.call(k,y)}catch(xt){tt=[6,xt],D=0}finally{R=O=0}if(tt[0]&5)throw tt[1];return{value:tt[0]?tt[1]:void 0,done:!0}}}var I=Object.create?function(k,v,y,R){R===void 0&&(R=y),Object.defineProperty(k,R,{enumerable:!0,get:function(){return v[y]}})}:function(k,v,y,R){R===void 0&&(R=y),k[R]=v[y]};function A(k,v){for(var y in k)y!=="default"&&!Object.prototype.hasOwnProperty.call(v,y)&&I(v,k,y)}function F(k){var v=typeof Symbol=="function"&&Symbol.iterator,y=v&&k[v],R=0;if(y)return y.call(k);if(k&&typeof k.length=="number")return{next:function(){return k&&R>=k.length&&(k=void 0),{value:k&&k[R++],done:!k}}};throw new TypeError(v?"Object is not iterable.":"Symbol.iterator is not defined.")}function j(k,v){var y=typeof Symbol=="function"&&k[Symbol.iterator];if(!y)return k;var R=y.call(k),D,O=[],Z;try{for(;(v===void 0||v-- >0)&&!(D=R.next()).done;)O.push(D.value)}catch(V){Z={error:V}}finally{try{D&&!D.done&&(y=R.return)&&y.call(R)}finally{if(Z)throw Z.error}}return O}function N(){for(var k=[],v=0;v<arguments.length;v++)k=k.concat(j(arguments[v]));return k}function G(){for(var k=0,v=0,y=arguments.length;v<y;v++)k+=arguments[v].length;for(var R=Array(k),D=0,v=0;v<y;v++)for(var O=arguments[v],Z=0,V=O.length;Z<V;Z++,D++)R[D]=O[Z];return R}function U(k,v){for(var y=0,R=v.length,D=k.length;y<R;y++,D++)k[D]=v[y];return k}function nt(k){return this instanceof nt?(this.v=k,this):new nt(k)}function lt(k,v,y){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var R=y.apply(k,v||[]),D,O=[];return D={},Z("next"),Z("throw"),Z("return"),D[Symbol.asyncIterator]=function(){return this},D;function Z(Et){R[Et]&&(D[Et]=function(Yt){return new Promise(function(Xt,ue){O.push([Et,Yt,Xt,ue])>1||V(Et,Yt)})})}function V(Et,Yt){try{J(R[Et](Yt))}catch(Xt){Ht(O[0][3],Xt)}}function J(Et){Et.value instanceof nt?Promise.resolve(Et.value.v).then(tt,xt):Ht(O[0][2],Et)}function tt(Et){V("next",Et)}function xt(Et){V("throw",Et)}function Ht(Et,Yt){Et(Yt),O.shift(),O.length&&V(O[0][0],O[0][1])}}function rt(k){var v,y;return v={},R("next"),R("throw",function(D){throw D}),R("return"),v[Symbol.iterator]=function(){return this},v;function R(D,O){v[D]=k[D]?function(Z){return(y=!y)?{value:nt(k[D](Z)),done:D==="return"}:O?O(Z):Z}:O}}function Pt(k){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var v=k[Symbol.asyncIterator],y;return v?v.call(k):(k=typeof F=="function"?F(k):k[Symbol.iterator](),y={},R("next"),R("throw"),R("return"),y[Symbol.asyncIterator]=function(){return this},y);function R(O){y[O]=k[O]&&function(Z){return new Promise(function(V,J){Z=k[O](Z),D(V,J,Z.done,Z.value)})}}function D(O,Z,V,J){Promise.resolve(J).then(function(tt){O({value:tt,done:V})},Z)}}function zt(k,v){return Object.defineProperty?Object.defineProperty(k,"raw",{value:v}):k.raw=v,k}var et=Object.create?function(k,v){Object.defineProperty(k,"default",{enumerable:!0,value:v})}:function(k,v){k.default=v};function Ot(k){if(k&&k.__esModule)return k;var v={};if(k!=null)for(var y in k)y!=="default"&&Object.prototype.hasOwnProperty.call(k,y)&&I(v,k,y);return et(v,k),v}function St(k){return k&&k.__esModule?k:{default:k}}function re(k,v){if(!v.has(k))throw new TypeError("attempted to get private field on non-instance");return v.get(k)}function _e(k,v,y){if(!v.has(k))throw new TypeError("attempted to set private field on non-instance");return v.set(k,y),y}var ot=function(){function k(v){this.options=v||Gt.options}return k.prototype.code=function(v,y,R,D){if(this.options.highlight){var O=this.options.highlight(v,y);O!=null&&O!==v&&(R=!0,v=O)}var Z=R?v:this.options.escape(v,!0);if(!y)return`
<pre><code>`+Z+`
</code></pre>
`;var V=this.options.langPrefix+this.options.escape(y,!0);return`
<pre><code class="`+V+'">'+Z+`
</code></pre>
`},k.prototype.blockquote=function(v){return`<blockquote>
`+v+`</blockquote>
`},k.prototype.html=function(v){return v},k.prototype.heading=function(v,y,R){var D=this.options.headerPrefix+R.toLowerCase().replace(/[^\w]+/g,"-");return"<h"+y+' id="'+D+'">'+v+"</h"+y+`>
`},k.prototype.hr=function(){return this.options.xhtml?`<hr/>
`:`<hr>
`},k.prototype.list=function(v,y){var R=y?"ol":"ul";return`
<`+R+`>
`+v+"</"+R+`>
`},k.prototype.listitem=function(v){return"<li>"+v+`</li>
`},k.prototype.paragraph=function(v){return"<p>"+v+`</p>
`},k.prototype.table=function(v,y){return`
<table>
<thead>
`+v+`</thead>
<tbody>
`+y+`</tbody>
</table>
`},k.prototype.tablerow=function(v){return`<tr>
`+v+`</tr>
`},k.prototype.tablecell=function(v,y){var R=y.header?"th":"td",D=y.align?"<"+R+' style="text-align:'+y.align+'">':"<"+R+">";return D+v+"</"+R+`>
`},k.prototype.strong=function(v){return"<strong>"+v+"</strong>"},k.prototype.em=function(v){return"<em>"+v+"</em>"},k.prototype.codespan=function(v){return"<code>"+v+"</code>"},k.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},k.prototype.del=function(v){return"<del>"+v+"</del>"},k.prototype.link=function(v,y,R){if(this.options.sanitize){var D=void 0;try{D=decodeURIComponent(this.options.unescape(v)).replace(/[^\w:]/g,"").toLowerCase()}catch(Z){return R}if(D.indexOf("javascript:")===0||D.indexOf("vbscript:")===0||D.indexOf("data:")===0)return R}var O='<a href="'+v+'"';return y&&(O+=' title="'+y+'"'),O+=">"+R+"</a>",O},k.prototype.image=function(v,y,R){var D='<img src="'+v+'" alt="'+R+'"';return y&&(D+=' title="'+y+'"'),D+=this.options.xhtml?"/>":">",D},k.prototype.text=function(v){return v},k}();var Lt=function(){function k(v,y,R,D){if(R===void 0&&(R=Gt.options),this.staticThis=v,this.links=y,this.options=R,this.renderer=D||this.options.renderer||new ot(this.options),!this.links)throw new Error("InlineLexer requires 'links' parameter.");this.setRules()}return k.output=function(v,y,R){var D=new this(this,y,R);return D.output(v)},k.getRulesBase=function(){if(this.rulesBase)return this.rulesBase;var v={escape:/^\\([\\`*{}\[\]()#+\-.!_>])/,autolink:/^<([^ <>]+(@|:\/)[^ <>]+)>/,tag:/^<!--[\s\S]*?-->|^<\/?\w+(?:"[^"]*"|'[^']*'|[^<'">])*?>/,link:/^!?\[(inside)\]\(href\)/,reflink:/^!?\[(inside)\]\s*\[([^\]]*)\]/,nolink:/^!?\[((?:\[[^\]]*\]|[^\[\]])*)\]/,strong:/^__([\s\S]+?)__(?!_)|^\*\*([\s\S]+?)\*\*(?!\*)/,em:/^\b_((?:[^_]|__)+?)_\b|^\*((?:\*\*|[\s\S])+?)\*(?!\*)/,code:/^(`+)([\s\S]*?[^`])\1(?!`)/,br:/^ {2,}\n(?!\s*$)/,text:/^[\s\S]+?(?=[\\<!\[_*`]| {2,}\n|$)/,_inside:/(?:\[[^\]]*\]|[^\[\]]|\](?=[^\[]*\]))*/,_href:/\s*<?([\s\S]*?)>?(?:\s+['"]([\s\S]*?)['"])?\s*/};return v.link=new t(v.link).setGroup("inside",v._inside).setGroup("href",v._href).getRegexp(),v.reflink=new t(v.reflink).setGroup("inside",v._inside).getRegexp(),this.rulesBase=v},k.getRulesPedantic=function(){return this.rulesPedantic?this.rulesPedantic:this.rulesPedantic=Object.assign(Object.assign({},this.getRulesBase()),{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/})},k.getRulesGfm=function(){if(this.rulesGfm)return this.rulesGfm;var v=this.getRulesBase(),y=new t(v.escape).setGroup("])","~|])").getRegexp(),R=new t(v.text).setGroup("]|","~]|").setGroup("|","|https?://|").getRegexp();return this.rulesGfm=Object.assign(Object.assign({},v),{escape:y,url:/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,del:/^~~(?=\S)([\s\S]*?\S)~~/,text:R})},k.getRulesBreaks=function(){if(this.rulesBreaks)return this.rulesBreaks;var v=this.getRulesGfm(),y=this.getRulesGfm();return this.rulesBreaks=Object.assign(Object.assign({},y),{br:new t(v.br).setGroup("{2,}","*").getRegexp(),text:new t(y.text).setGroup("{2,}","*").getRegexp()})},k.prototype.setRules=function(){this.options.gfm?this.options.breaks?this.rules=this.staticThis.getRulesBreaks():this.rules=this.staticThis.getRulesGfm():this.options.pedantic?this.rules=this.staticThis.getRulesPedantic():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=this.rules.url!==void 0},k.prototype.output=function(v){v=v;for(var y,R="";v;){if(y=this.rules.escape.exec(v)){v=v.substring(y[0].length),R+=y[1];continue}if(y=this.rules.autolink.exec(v)){var D=void 0,O=void 0;v=v.substring(y[0].length),y[2]==="@"?(D=this.options.escape(y[1].charAt(6)===":"?this.mangle(y[1].substring(7)):this.mangle(y[1])),O=this.mangle("mailto:")+D):(D=this.options.escape(y[1]),O=D),R+=this.renderer.link(O,null,D);continue}if(!this.inLink&&this.hasRulesGfm&&(y=this.rules.url.exec(v))){var D=void 0,O=void 0;v=v.substring(y[0].length),D=this.options.escape(y[1]),O=D,R+=this.renderer.link(O,null,D);continue}if(y=this.rules.tag.exec(v)){!this.inLink&&/^<a /i.test(y[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(y[0])&&(this.inLink=!1),v=v.substring(y[0].length),R+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(y[0]):this.options.escape(y[0]):y[0];continue}if(y=this.rules.link.exec(v)){v=v.substring(y[0].length),this.inLink=!0,R+=this.outputLink(y,{href:y[2],title:y[3]}),this.inLink=!1;continue}if((y=this.rules.reflink.exec(v))||(y=this.rules.nolink.exec(v))){v=v.substring(y[0].length);var Z=(y[2]||y[1]).replace(/\s+/g," "),V=this.links[Z.toLowerCase()];if(!V||!V.href){R+=y[0].charAt(0),v=y[0].substring(1)+v;continue}this.inLink=!0,R+=this.outputLink(y,V),this.inLink=!1;continue}if(y=this.rules.strong.exec(v)){v=v.substring(y[0].length),R+=this.renderer.strong(this.output(y[2]||y[1]));continue}if(y=this.rules.em.exec(v)){v=v.substring(y[0].length),R+=this.renderer.em(this.output(y[2]||y[1]));continue}if(y=this.rules.code.exec(v)){v=v.substring(y[0].length),R+=this.renderer.codespan(this.options.escape(y[2].trim(),!0));continue}if(y=this.rules.br.exec(v)){v=v.substring(y[0].length),R+=this.renderer.br();continue}if(this.hasRulesGfm&&(y=this.rules.del.exec(v))){v=v.substring(y[0].length),R+=this.renderer.del(this.output(y[1]));continue}if(y=this.rules.text.exec(v)){v=v.substring(y[0].length),R+=this.renderer.text(this.options.escape(this.smartypants(y[0])));continue}if(v)throw new Error("Infinite loop on byte: "+v.charCodeAt(0))}return R},k.prototype.outputLink=function(v,y){var R=this.options.escape(y.href),D=y.title?this.options.escape(y.title):null;return v[0].charAt(0)!=="!"?this.renderer.link(R,D,this.output(v[1])):this.renderer.image(R,D,this.options.escape(v[1]))},k.prototype.smartypants=function(v){return this.options.smartypants?v.replace(/---/g,"\u2014").replace(/--/g,"\u2013").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1\u2018").replace(/'/g,"\u2019").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1\u201C").replace(/"/g,"\u201D").replace(/\.{3}/g,"\u2026"):v},k.prototype.mangle=function(v){if(!this.options.mangle)return v;for(var y="",R=v.length,D=0;D<R;D++){var O=void 0;Math.random()>.5&&(O="x"+v.charCodeAt(D).toString(16)),y+="&#"+O+";"}return y},k}();Lt.rulesBase=null,Lt.rulesPedantic=null,Lt.rulesGfm=null,Lt.rulesBreaks=null;var Bt=function(){function k(v){this.simpleRenderers=[],this.line=0,this.tokens=[],this.token=null,this.options=v||Gt.options,this.renderer=this.options.renderer||new ot(this.options)}return k.parse=function(v,y,R){var D=new this(R);return D.parse(y,v)},k.prototype.parse=function(v,y){this.inlineLexer=new Lt(Lt,v,this.options,this.renderer),this.tokens=y.reverse();for(var R="";this.next();)R+=this.tok();return R},k.prototype.debug=function(v,y){this.inlineLexer=new Lt(Lt,v,this.options,this.renderer),this.tokens=y.reverse();for(var R="";this.next();){var D=this.tok();this.token.line=this.line+=D.split(`
`).length-1,R+=D}return R},k.prototype.next=function(){return this.token=this.tokens.pop()},k.prototype.getNextElement=function(){return this.tokens[this.tokens.length-1]},k.prototype.parseText=function(){for(var v=this.token.text,y;(y=this.getNextElement())&&y.type==n.TokenType.text;)v+=`
`+this.next().text;return this.inlineLexer.output(v)},k.prototype.tok=function(){var v,y;switch(this.token.type){case n.TokenType.space:return"";case n.TokenType.paragraph:return this.renderer.paragraph(this.inlineLexer.output(this.token.text));case n.TokenType.text:return this.options.isNoP?this.parseText():this.renderer.paragraph(this.parseText());case n.TokenType.heading:return this.renderer.heading(this.inlineLexer.output(this.token.text),this.token.depth,this.token.text);case n.TokenType.listStart:{for(var R="",D=this.token.ordered;this.next().type!=n.TokenType.listEnd;)R+=this.tok();return this.renderer.list(R,D)}case n.TokenType.listItemStart:{for(var R="";this.next().type!=n.TokenType.listItemEnd;)R+=this.token.type==n.TokenType.text?this.parseText():this.tok();return this.renderer.listitem(R)}case n.TokenType.looseItemStart:{for(var R="";this.next().type!=n.TokenType.listItemEnd;)R+=this.tok();return this.renderer.listitem(R)}case n.TokenType.code:return this.renderer.code(this.token.text,this.token.lang,this.token.escaped,this.token.meta);case n.TokenType.table:{var O="",R="",Z=void 0;Z="";for(var V=0;V<this.token.header.length;V++){var J={header:!0,align:this.token.align[V]},tt=this.inlineLexer.output(this.token.header[V]);Z+=this.renderer.tablecell(tt,J)}O+=this.renderer.tablerow(Z);try{for(var xt=F(this.token.cells),Ht=xt.next();!Ht.done;Ht=xt.next()){var Et=Ht.value;Z="";for(var Yt=0;Yt<Et.length;Yt++)Z+=this.renderer.tablecell(this.inlineLexer.output(Et[Yt]),{header:!1,align:this.token.align[Yt]});R+=this.renderer.tablerow(Z)}}catch(ye){v={error:ye}}finally{try{Ht&&!Ht.done&&(y=xt.return)&&y.call(xt)}finally{if(v)throw v.error}}return this.renderer.table(O,R)}case n.TokenType.blockquoteStart:{for(var R="";this.next().type!=n.TokenType.blockquoteEnd;)R+=this.tok();return this.renderer.blockquote(R)}case n.TokenType.hr:return this.renderer.hr();case n.TokenType.html:{var Xt=!this.token.pre&&!this.options.pedantic?this.inlineLexer.output(this.token.text):this.token.text;return this.renderer.html(Xt)}default:{if(this.simpleRenderers.length){for(var V=0;V<this.simpleRenderers.length;V++)if(this.token.type=="simpleRule"+(V+1))return this.simpleRenderers[V].call(this.renderer,this.token.execArr)}var ue='Token with "'+this.token.type+'" type was not found.';if(this.options.silent)console.log(ue);else throw new Error(ue)}}},k}();var Gt=function(){function k(){}return k.setOptions=function(v){return Object.assign(this.options,v),this},k.setBlockRule=function(v,y){return y===void 0&&(y=function(){return""}),Qt.simpleRules.push(v),this.simpleRenderers.push(y),this},k.parse=function(v,y){y===void 0&&(y=this.options);try{var R=this.callBlockLexer(v,y),D=R.tokens,O=R.links;return this.callParser(D,O,y)}catch(Z){return this.callMe(Z)}},k.debug=function(v,y){y===void 0&&(y=this.options);var R=this.callBlockLexer(v,y),D=R.tokens,O=R.links,Z=D.slice(),V=new Bt(y);V.simpleRenderers=this.simpleRenderers;var J=V.debug(O,D);return Z=Z.map(function(tt){tt.type=n.TokenType[tt.type]||tt.type;var xt=tt.line;return delete tt.line,xt?Object.assign({line:xt},tt):tt}),{tokens:Z,links:O,result:J}},k.callBlockLexer=function(v,y){if(v===void 0&&(v=""),typeof v!="string")throw new Error("Expected that the 'src' parameter would have a 'string' type, got '"+typeof v+"'");return v=v.replace(/\r\n|\r/g,`
`).replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,`
`).replace(/^ +$/gm,""),Qt.lex(v,y,!0)},k.callParser=function(v,y,R){if(this.simpleRenderers.length){var D=new Bt(R);return D.simpleRenderers=this.simpleRenderers,D.parse(y,v)}else return Bt.parse(v,y,R)},k.callMe=function(v){if(v.message+=`
Please report this to https://github.com/ts-stack/markdown`,this.options.silent)return"<p>An error occured:</p><pre>"+this.options.escape(v.message+"",!0)+"</pre>";throw v},k}();Gt.options=new c,Gt.simpleRenderers=[];var Qt=function(){function k(v,y){this.staticThis=v,this.links={},this.tokens=[],this.options=y||Gt.options,this.setRules()}return k.lex=function(v,y,R,D){var O=new this(this,y);return O.getTokens(v,R,D)},k.getRulesBase=function(){if(this.rulesBase)return this.rulesBase;var v={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,hr:/^( *[-*_]){3,} *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *#* *(?:\n+|$)/,lheading:/^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/,blockquote:/^( *>[^\n]+(\n[^\n]+)*\n*)+/,list:/^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:/^ *(?:comment *(?:\n|\s*$)|closed *(?:\n{2,}|\s*$)|closing *(?:\n{2,}|\s*$))/,def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +["(]([^\n]+)[")])? *(?:\n+|$)/,paragraph:/^((?:[^\n]+\n?(?!hr|heading|lheading|blockquote|tag|def))+)\n*/,text:/^[^\n]+/,bullet:/(?:[*+-]|\d+\.)/,item:/^( *)(bull) [^\n]*(?:\n(?!\1bull )[^\n]*)*/};v.item=new t(v.item,"gm").setGroup(/bull/g,v.bullet).getRegexp(),v.list=new t(v.list).setGroup(/bull/g,v.bullet).setGroup("hr","\\n+(?=\\1?(?:[-*_] *){3,}(?:\\n+|$))").setGroup("def","\\n+(?="+v.def.source+")").getRegexp();var y="(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:/|[^\\w\\s@]*@)\\b";return v.html=new t(v.html).setGroup("comment",/<!--[\s\S]*?-->/).setGroup("closed",/<(tag)[\s\S]+?<\/\1>/).setGroup("closing",/<tag(?:"[^"]*"|'[^']*'|[^'">])*?>/).setGroup(/tag/g,y).getRegexp(),v.paragraph=new t(v.paragraph).setGroup("hr",v.hr).setGroup("heading",v.heading).setGroup("lheading",v.lheading).setGroup("blockquote",v.blockquote).setGroup("tag","<"+y).setGroup("def",v.def).getRegexp(),this.rulesBase=v},k.getRulesGfm=function(){if(this.rulesGfm)return this.rulesGfm;var v=this.getRulesBase(),y=Object.assign(Object.assign({},v),{fences:/^ *(`{3,}|~{3,})[ \.]*((\S+)? *[^\n]*)\n([\s\S]*?)\s*\1 *(?:\n+|$)/,paragraph:/^/,heading:/^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/}),R=y.fences.source.replace("\\1","\\2"),D=v.list.source.replace("\\1","\\3");return y.paragraph=new t(v.paragraph).setGroup("(?!","(?!"+R+"|"+D+"|").getRegexp(),this.rulesGfm=y},k.getRulesTable=function(){return this.rulesTables?this.rulesTables:this.rulesTables=Object.assign(Object.assign({},this.getRulesGfm()),{nptable:/^ *(\S.*\|.*)\n *([-:]+ *\|[-| :]*)\n((?:.*\|.*(?:\n|$))*)\n*/,table:/^ *\|(.+)\n *\|( *[-:]+[-| :]*)\n((?: *\|.*(?:\n|$))*)\n*/})},k.prototype.setRules=function(){this.options.gfm?this.options.tables?this.rules=this.staticThis.getRulesTable():this.rules=this.staticThis.getRulesGfm():this.rules=this.staticThis.getRulesBase(),this.hasRulesGfm=this.rules.fences!==void 0,this.hasRulesTables=this.rules.table!==void 0},k.prototype.getTokens=function(v,y,R){var D=v,O;t:for(;D;){if((O=this.rules.newline.exec(D))&&(D=D.substring(O[0].length),O[0].length>1&&this.tokens.push({type:n.TokenType.space})),O=this.rules.code.exec(D)){D=D.substring(O[0].length);var Z=O[0].replace(/^ {4}/gm,"");this.tokens.push({type:n.TokenType.code,text:this.options.pedantic?Z:Z.replace(/\n+$/,"")});continue}if(this.hasRulesGfm&&(O=this.rules.fences.exec(D))){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.code,meta:O[2],lang:O[3],text:O[4]||""});continue}if(O=this.rules.heading.exec(D)){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.heading,depth:O[1].length,text:O[2]});continue}if(y&&this.hasRulesTables&&(O=this.rules.nptable.exec(D))){D=D.substring(O[0].length);for(var V={type:n.TokenType.table,header:O[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:O[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]},J=0;J<V.align.length;J++)/^ *-+: *$/.test(V.align[J])?V.align[J]="right":/^ *:-+: *$/.test(V.align[J])?V.align[J]="center":/^ *:-+ *$/.test(V.align[J])?V.align[J]="left":V.align[J]=null;for(var tt=O[3].replace(/\n$/,"").split(`
`),J=0;J<tt.length;J++)V.cells[J]=tt[J].split(/ *\| */);this.tokens.push(V);continue}if(O=this.rules.lheading.exec(D)){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.heading,depth:O[2]==="="?1:2,text:O[1]});continue}if(O=this.rules.hr.exec(D)){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.hr});continue}if(O=this.rules.blockquote.exec(D)){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.blockquoteStart});var xt=O[0].replace(/^ *> ?/gm,"");this.getTokens(xt),this.tokens.push({type:n.TokenType.blockquoteEnd});continue}if(O=this.rules.list.exec(D)){D=D.substring(O[0].length);var Ht=O[2];this.tokens.push({type:n.TokenType.listStart,ordered:Ht.length>1});for(var xt=O[0].match(this.rules.item),Et=xt.length,Yt=!1,Xt=void 0,ue=void 0,we=void 0,J=0;J<Et;J++){var V=xt[J];Xt=V.length,V=V.replace(/^ *([*+-]|\d+\.) +/,""),V.indexOf(`
 `)!==-1&&(Xt-=V.length,V=this.options.pedantic?V.replace(/^ {1,4}/gm,""):V.replace(new RegExp("^ {1,"+Xt+"}","gm"),"")),this.options.smartLists&&J!==Et-1&&(ue=this.staticThis.getRulesBase().bullet.exec(xt[J+1])[0],Ht!==ue&&!(Ht.length>1&&ue.length>1)&&(D=xt.slice(J+1).join(`
`)+D,J=Et-1)),we=Yt||/\n\n(?!\s*$)/.test(V),J!==Et-1&&(Yt=V.charAt(V.length-1)===`
`,we||(we=Yt)),this.tokens.push({type:we?n.TokenType.looseItemStart:n.TokenType.listItemStart}),this.getTokens(V,!1,R),this.tokens.push({type:n.TokenType.listItemEnd})}this.tokens.push({type:n.TokenType.listEnd});continue}if(O=this.rules.html.exec(D)){D=D.substring(O[0].length);var ye=O[1],En=ye==="pre"||ye==="script"||ye==="style";this.tokens.push({type:this.options.sanitize?n.TokenType.paragraph:n.TokenType.html,pre:!this.options.sanitizer&&En,text:O[0]});continue}if(y&&(O=this.rules.def.exec(D))){D=D.substring(O[0].length),this.links[O[1].toLowerCase()]={href:O[2],title:O[3]};continue}if(y&&this.hasRulesTables&&(O=this.rules.table.exec(D))){D=D.substring(O[0].length);for(var V={type:n.TokenType.table,header:O[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:O[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:[]},J=0;J<V.align.length;J++)/^ *-+: *$/.test(V.align[J])?V.align[J]="right":/^ *:-+: *$/.test(V.align[J])?V.align[J]="center":/^ *:-+ *$/.test(V.align[J])?V.align[J]="left":V.align[J]=null;for(var tt=O[3].replace(/(?: *\| *)?\n$/,"").split(`
`),J=0;J<tt.length;J++)V.cells[J]=tt[J].replace(/^ *\| *| *\| *$/g,"").split(/ *\| */);this.tokens.push(V);continue}if(this.staticThis.simpleRules.length){for(var un=this.staticThis.simpleRules,J=0;J<un.length;J++)if(O=un[J].exec(D)){D=D.substring(O[0].length);var An="simpleRule"+(J+1);this.tokens.push({type:An,execArr:O});continue t}}if(y&&(O=this.rules.paragraph.exec(D))){D=D.substring(O[0].length),O[1].slice(-1)===`
`?this.tokens.push({type:n.TokenType.paragraph,text:O[1].slice(0,-1)}):this.tokens.push({type:this.tokens.length>0?n.TokenType.paragraph:n.TokenType.text,text:O[1]});continue}if(O=this.rules.text.exec(D)){D=D.substring(O[0].length),this.tokens.push({type:n.TokenType.text,text:O[0]});continue}if(D)throw new Error("Infinite loop on byte: "+D.charCodeAt(0)+(", near text '"+D.slice(0,30)+"...'"))}return{tokens:this.tokens,links:this.links}},k}();Qt.simpleRules=[],Qt.rulesBase=null,Qt.rulesGfm=null,Qt.rulesTables=null,n.BlockLexer=Qt,n.ExtendRegexp=t,n.InlineLexer=Lt,n.Marked=Gt,n.MarkedOptions=c,n.Parser=Bt,n.Renderer=ot,n.escape=a,n.unescape=l,Object.defineProperty(n,"__esModule",{value:!0})})});var Hu=Bo(wi=>{"use strict";var sy=wi&&wi.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,s){i.__proto__=s}||function(i,s){for(var r in s)s.hasOwnProperty(r)&&(i[r]=s[r])},n(t,e)};return function(t,e){n(t,e);function i(){this.constructor=t}t.prototype=e===null?Object.create(e):(i.prototype=e.prototype,new i)}}();Object.defineProperty(wi,"__esModule",{value:!0});wi.Extractor=void 0;var Ba=Nu(),ry=function(n){sy(t,n);function t(e,i){var s=n.call(this)||this;return s.lowercaseKeys=i??!1,s.reset(e),s}return Object.defineProperty(t.prototype,"tables",{get:function(){return this.extractedTables},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"objects",{get:function(){var e=this;return this.extractedTables.map(function(i){return t.tableToObject(i,e.lowercaseKeys)})},enumerable:!1,configurable:!0}),t.prototype.reset=function(e){e===void 0&&(e="rows"),this.mode=e,this.currentRow=[],this.currentTable=[],this.extractedTables=[]},t.prototype.table=function(e,i){return this.extractedTables.push(this.mode==="rows"?this.currentTable:t.transposeTable(this.currentTable)),this.currentTable=[],n.prototype.table.call(this,e,i)},t.prototype.tablerow=function(e){return this.currentTable.push(this.currentRow),this.currentRow=[],n.prototype.tablerow.call(this,e)},t.prototype.tablecell=function(e,i){return this.currentRow.push(e),n.prototype.tablecell.call(this,e,i)},t.transposeTable=function(e){for(var i=[],s=e.length,r=e[0].length,o=0;o<r;o++){i.push([]);for(var a=0;a<s;a++)i[o].push(e[a][o])}return i},t.tableToObject=function(e,i){var s=e.shift().slice(1),r={};return e.forEach(function(o){var a=o.shift(),l={};o.forEach(function(c,h){l[i?s[h].toLowerCase():s[h]]=c}),r[i?a.toLowerCase():a]=l}),r},t.createExtractor=function(e,i,s){var r=new t(i,s);return Ba.Marked.setOptions({renderer:r}),Ba.Marked.parse(e),r},t.extractObject=function(e,i,s){var r=t.extractAllObjects(e,i,s);return r.length>0?r[0]:null},t.extractAllObjects=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects},t.extractTable=function(e,i,s){var r=t.extractAllTables(e,i,s);return r.length>0?r[0]:null},t.extractAllTables=function(e,i,s){var r=t.createExtractor(e,i,s);return r.tables},t.extract=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects.length>0?JSON.stringify(r.objects[0]):null},t.extractAll=function(e,i,s){var r=t.createExtractor(e,i,s);return r.objects.map(function(o){return JSON.stringify(o)})},t}(Ba.Renderer);wi.Extractor=ry});tb(exports,{default:()=>xl});var $e=ze(require("obsidian"));function Oe(){}var No=function(){let n=0;return function(){return n++}}();function vt(n){return n===null||typeof n=="undefined"}function gt(n){if(Array.isArray&&Array.isArray(n))return!0;let t=Object.prototype.toString.call(n);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function at(n){return n!==null&&Object.prototype.toString.call(n)==="[object Object]"}var $t=n=>(typeof n=="number"||n instanceof Number)&&isFinite(+n);function me(n,t){return $t(n)?n:t}function st(n,t){return typeof n=="undefined"?t:n}var Ho=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100:n/t,Vs=(n,t)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100*t:+n;function wt(n,t,e){if(n&&typeof n.call=="function")return n.apply(e,t)}function Ct(n,t,e,i){let s,r,o;if(gt(n))if(r=n.length,i)for(s=r-1;s>=0;s--)t.call(e,n[s],s);else for(s=0;s<r;s++)t.call(e,n[s],s);else if(at(n))for(o=Object.keys(n),r=o.length,s=0;s<r;s++)t.call(e,n[o[s]],o[s])}function pi(n,t){let e,i,s,r;if(!n||!t||n.length!==t.length)return!1;for(e=0,i=n.length;e<i;++e)if(s=n[e],r=t[e],s.datasetIndex!==r.datasetIndex||s.index!==r.index)return!1;return!0}function Bi(n){if(gt(n))return n.map(Bi);if(at(n)){let t=Object.create(null),e=Object.keys(n),i=e.length,s=0;for(;s<i;++s)t[e[s]]=Bi(n[e[s]]);return t}return n}function jc(n){return["__proto__","prototype","constructor"].indexOf(n)===-1}function zc(n,t,e,i){if(!jc(n))return;let s=t[n],r=e[n];at(s)&&at(r)?zn(s,r,i):t[n]=Bi(r)}function zn(n,t,e){let i=gt(t)?t:[t],s=i.length;if(!at(n))return n;e=e||{};let r=e.merger||zc;for(let o=0;o<s;++o){if(t=i[o],!at(t))continue;let a=Object.keys(t);for(let l=0,c=a.length;l<c;++l)r(a[l],n,t,e)}return n}function Bn(n,t){return zn(n,t,{merger:Bc})}function Bc(n,t,e){if(!jc(n))return;let i=t[n],s=e[n];at(i)&&at(s)?Bn(i,s):Object.prototype.hasOwnProperty.call(t,n)||(t[n]=Bi(s))}var Nc={"":n=>n,x:n=>n.x,y:n=>n.y};function Be(n,t){return(Nc[t]||(Nc[t]=nb(t)))(n)}function nb(n){let t=Hc(n);return e=>{for(let i of t){if(i==="")break;e=e&&e[i]}return e}}function Hc(n){let t=n.split("."),e=[],i="";for(let s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(e.push(i),i="");return e}function Ni(n){return n.charAt(0).toUpperCase()+n.slice(1)}var Nt=n=>typeof n!="undefined",ce=n=>typeof n=="function",Ys=(n,t)=>{if(n.size!==t.size)return!1;for(let e of n)if(!t.has(e))return!1;return!0};function Wo(n){return n.type==="mouseup"||n.type==="click"||n.type==="contextmenu"}var dt=Math.PI,kt=2*dt,Wc=kt+dt,Hi=Number.POSITIVE_INFINITY,Wi=dt/180,Rt=dt/2,ke=dt/4,gi=dt*2/3,be=Math.log10,Se=Math.sign;function Xs(n){let t=Math.round(n);n=Nn(n,t,n/1e3)?t:n;let e=Math.pow(10,Math.floor(be(n))),i=n/e;return(i<=1?1:i<=2?2:i<=5?5:10)*e}function Vo(n){let t=[],e=Math.sqrt(n),i;for(i=1;i<e;i++)n%i==0&&(t.push(i),t.push(n/i));return e===(e|0)&&t.push(e),t.sort((s,r)=>s-r).pop(),t}function Ee(n){return!isNaN(parseFloat(n))&&isFinite(n)}function Nn(n,t,e){return Math.abs(n-t)<e}function Yo(n,t){let e=Math.round(n);return e-t<=n&&e+t>=n}function qs(n,t,e){let i,s,r;for(i=0,s=n.length;i<s;i++)r=n[i][e],isNaN(r)||(t.min=Math.min(t.min,r),t.max=Math.max(t.max,r))}function Wt(n){return n*(dt/180)}function Hn(n){return n*(180/dt)}function Gs(n){if(!$t(n))return;let t=1,e=0;for(;Math.round(n*t)/t!==n;)t*=10,e++;return e}function Us(n,t){let e=t.x-n.x,i=t.y-n.y,s=Math.sqrt(e*e+i*i),r=Math.atan2(i,e);return r<-.5*dt&&(r+=kt),{angle:r,distance:s}}function Ze(n,t){return Math.sqrt(Math.pow(t.x-n.x,2)+Math.pow(t.y-n.y,2))}function Vc(n,t){return(n-t+Wc)%kt-dt}function fe(n){return(n%kt+kt)%kt}function Wn(n,t,e,i){let s=fe(n),r=fe(t),o=fe(e),a=fe(r-s),l=fe(o-s),c=fe(s-r),h=fe(s-o);return s===r||s===o||i&&r===o||a>l&&c<h}function Kt(n,t,e){return Math.max(t,Math.min(e,n))}function Xo(n){return Kt(n,-32768,32767)}function Ae(n,t,e,i=1e-6){return n>=Math.min(t,e)-i&&n<=Math.max(t,e)+i}function Vi(n,t,e){e=e||(o=>n[o]<t);let i=n.length-1,s=0,r;for(;i-s>1;)r=s+i>>1,e(r)?s=r:i=r;return{lo:s,hi:i}}var Re=(n,t,e,i)=>Vi(n,e,i?s=>n[s][t]<=e:s=>n[s][t]<e),qo=(n,t,e)=>Vi(n,e,i=>n[i][t]>=e);function Go(n,t,e){let i=0,s=n.length;for(;i<s&&n[i]<t;)i++;for(;s>i&&n[s-1]>e;)s--;return i>0||s<n.length?n.slice(i,s):n}var Yc=["push","pop","shift","splice","unshift"];function Uo(n,t){if(n._chartjs){n._chartjs.listeners.push(t);return}Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Yc.forEach(e=>{let i="_onData"+Ni(e),s=n[e];Object.defineProperty(n,e,{configurable:!0,enumerable:!1,value(...r){let o=s.apply(this,r);return n._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...r)}),o}})})}function Ks(n,t){let e=n._chartjs;if(!e)return;let i=e.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(Yc.forEach(r=>{delete n[r]}),delete n._chartjs)}function Zs(n){let t=new Set,e,i;for(e=0,i=n.length;e<i;++e)t.add(n[e]);return t.size===i?n:Array.from(t)}var Js=function(){return typeof window=="undefined"?function(n){return n()}:window.requestAnimationFrame}();function Qs(n,t,e){let i=e||(o=>Array.prototype.slice.call(o)),s=!1,r=[];return function(...o){r=i(o),s||(s=!0,Js.call(window,()=>{s=!1,n.apply(t,r)}))}}function Ko(n,t){let e;return function(...i){return t?(clearTimeout(e),e=setTimeout(n,t,i)):n.apply(this,i),t}}var Yi=n=>n==="start"?"left":n==="end"?"right":"center",ie=(n,t,e)=>n==="start"?t:n==="end"?e:(t+e)/2,Zo=(n,t,e,i)=>n===(i?"left":"right")?e:n==="center"?(t+e)/2:t;function tr(n,t,e){let i=t.length,s=0,r=i;if(n._sorted){let{iScale:o,_parsed:a}=n,l=o.axis,{min:c,max:h,minDefined:f,maxDefined:g}=o.getUserBounds();f&&(s=Kt(Math.min(Re(a,o.axis,c).lo,e?i:Re(t,l,o.getPixelForValue(c)).lo),0,i-1)),g?r=Kt(Math.max(Re(a,o.axis,h,!0).hi+1,e?0:Re(t,l,o.getPixelForValue(h),!0).hi+1),s,i)-s:r=i-s}return{start:s,count:r}}function er(n){let{xScale:t,yScale:e,_scaleRanges:i}=n,s={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!i)return n._scaleRanges=s,!0;let r=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==e.min||i.ymax!==e.max;return Object.assign(i,s),r}var nr=n=>n===0||n===1,Xc=(n,t,e)=>-(Math.pow(2,10*(n-=1))*Math.sin((n-t)*kt/e)),qc=(n,t,e)=>Math.pow(2,-10*n)*Math.sin((n-t)*kt/e)+1,Vn={linear:n=>n,easeInQuad:n=>n*n,easeOutQuad:n=>-n*(n-2),easeInOutQuad:n=>(n/=.5)<1?.5*n*n:-.5*(--n*(n-2)-1),easeInCubic:n=>n*n*n,easeOutCubic:n=>(n-=1)*n*n+1,easeInOutCubic:n=>(n/=.5)<1?.5*n*n*n:.5*((n-=2)*n*n+2),easeInQuart:n=>n*n*n*n,easeOutQuart:n=>-((n-=1)*n*n*n-1),easeInOutQuart:n=>(n/=.5)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2),easeInQuint:n=>n*n*n*n*n,easeOutQuint:n=>(n-=1)*n*n*n*n+1,easeInOutQuint:n=>(n/=.5)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2),easeInSine:n=>-Math.cos(n*Rt)+1,easeOutSine:n=>Math.sin(n*Rt),easeInOutSine:n=>-.5*(Math.cos(dt*n)-1),easeInExpo:n=>n===0?0:Math.pow(2,10*(n-1)),easeOutExpo:n=>n===1?1:-Math.pow(2,-10*n)+1,easeInOutExpo:n=>nr(n)?n:n<.5?.5*Math.pow(2,10*(n*2-1)):.5*(-Math.pow(2,-10*(n*2-1))+2),easeInCirc:n=>n>=1?n:-(Math.sqrt(1-n*n)-1),easeOutCirc:n=>Math.sqrt(1-(n-=1)*n),easeInOutCirc:n=>(n/=.5)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1),easeInElastic:n=>nr(n)?n:Xc(n,.075,.3),easeOutElastic:n=>nr(n)?n:qc(n,.075,.3),easeInOutElastic(n){let t=.1125,e=.45;return nr(n)?n:n<.5?.5*Xc(n*2,t,e):.5+.5*qc(n*2-1,t,e)},easeInBack(n){let t=1.70158;return n*n*((t+1)*n-t)},easeOutBack(n){let t=1.70158;return(n-=1)*n*((t+1)*n+t)+1},easeInOutBack(n){let t=1.70158;return(n/=.5)<1?.5*(n*n*(((t*=1.525)+1)*n-t)):.5*((n-=2)*n*(((t*=1.525)+1)*n+t)+2)},easeInBounce:n=>1-Vn.easeOutBounce(1-n),easeOutBounce(n){let t=7.5625,e=2.75;return n<1/e?t*n*n:n<2/e?t*(n-=1.5/e)*n+.75:n<2.5/e?t*(n-=2.25/e)*n+.9375:t*(n-=2.625/e)*n+.984375},easeInOutBounce:n=>n<.5?Vn.easeInBounce(n*2)*.5:Vn.easeOutBounce(n*2-1)*.5+.5};function Xi(n){return n+.5|0}var bn=(n,t,e)=>Math.max(Math.min(n,e),t);function qi(n){return bn(Xi(n*2.55),0,255)}function vn(n){return bn(Xi(n*255),0,255)}function Je(n){return bn(Xi(n/2.55)/100,0,1)}function Gc(n){return bn(Xi(n*100),0,100)}var Me={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Jo=[..."0123456789ABCDEF"],ib=n=>Jo[n&15],sb=n=>Jo[(n&240)>>4]+Jo[n&15],ir=n=>(n&240)>>4==(n&15),rb=n=>ir(n.r)&&ir(n.g)&&ir(n.b)&&ir(n.a);function ob(n){var t=n.length,e;return n[0]==="#"&&(t===4||t===5?e={r:255&Me[n[1]]*17,g:255&Me[n[2]]*17,b:255&Me[n[3]]*17,a:t===5?Me[n[4]]*17:255}:(t===7||t===9)&&(e={r:Me[n[1]]<<4|Me[n[2]],g:Me[n[3]]<<4|Me[n[4]],b:Me[n[5]]<<4|Me[n[6]],a:t===9?Me[n[7]]<<4|Me[n[8]]:255})),e}var ab=(n,t)=>n<255?t(n):"";function lb(n){var t=rb(n)?ib:sb;return n?"#"+t(n.r)+t(n.g)+t(n.b)+ab(n.a,t):void 0}var cb=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Uc(n,t,e){let i=t*Math.min(e,1-e),s=(r,o=(r+n/30)%12)=>e-i*Math.max(Math.min(o-3,9-o,1),-1);return[s(0),s(8),s(4)]}function hb(n,t,e){let i=(s,r=(s+n/60)%6)=>e-e*t*Math.max(Math.min(r,4-r,1),0);return[i(5),i(3),i(1)]}function ub(n,t,e){let i=Uc(n,1,.5),s;for(t+e>1&&(s=1/(t+e),t*=s,e*=s),s=0;s<3;s++)i[s]*=1-t-e,i[s]+=t;return i}function fb(n,t,e,i,s){return n===s?(t-e)/i+(t<e?6:0):t===s?(e-n)/i+2:(n-t)/i+4}function Qo(n){let t=255,e=n.r/t,i=n.g/t,s=n.b/t,r=Math.max(e,i,s),o=Math.min(e,i,s),a=(r+o)/2,l,c,h;return r!==o&&(h=r-o,c=a>.5?h/(2-r-o):h/(r+o),l=fb(e,i,s,h,r),l=l*60+.5),[l|0,c||0,a]}function ta(n,t,e,i){return(Array.isArray(t)?n(t[0],t[1],t[2]):n(t,e,i)).map(vn)}function ea(n,t,e){return ta(Uc,n,t,e)}function db(n,t,e){return ta(ub,n,t,e)}function pb(n,t,e){return ta(hb,n,t,e)}function Kc(n){return(n%360+360)%360}function gb(n){let t=cb.exec(n),e=255,i;if(!t)return;t[5]!==i&&(e=t[6]?qi(+t[5]):vn(+t[5]));let s=Kc(+t[2]),r=+t[3]/100,o=+t[4]/100;return t[1]==="hwb"?i=db(s,r,o):t[1]==="hsv"?i=pb(s,r,o):i=ea(s,r,o),{r:i[0],g:i[1],b:i[2],a:e}}function mb(n,t){var e=Qo(n);e[0]=Kc(e[0]+t),e=ea(e),n.r=e[0],n.g=e[1],n.b=e[2]}function bb(n){if(!n)return;let t=Qo(n),e=t[0],i=Gc(t[1]),s=Gc(t[2]);return n.a<255?`hsla(${e}, ${i}%, ${s}%, ${Je(n.a)})`:`hsl(${e}, ${i}%, ${s}%)`}var Zc={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Jc={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function vb(){let n={},t=Object.keys(Jc),e=Object.keys(Zc),i,s,r,o,a;for(i=0;i<t.length;i++){for(o=a=t[i],s=0;s<e.length;s++)r=e[s],a=a.replace(r,Zc[r]);r=parseInt(Jc[o],16),n[a]=[r>>16&255,r>>8&255,r&255]}return n}var sr;function _b(n){sr||(sr=vb(),sr.transparent=[0,0,0,0]);let t=sr[n.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}var yb=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function xb(n){let t=yb.exec(n),e=255,i,s,r;if(!!t){if(t[7]!==i){let o=+t[7];e=t[8]?qi(o):bn(o*255,0,255)}return i=+t[1],s=+t[3],r=+t[5],i=255&(t[2]?qi(i):bn(i,0,255)),s=255&(t[4]?qi(s):bn(s,0,255)),r=255&(t[6]?qi(r):bn(r,0,255)),{r:i,g:s,b:r,a:e}}}function wb(n){return n&&(n.a<255?`rgba(${n.r}, ${n.g}, ${n.b}, ${Je(n.a)})`:`rgb(${n.r}, ${n.g}, ${n.b})`)}var na=n=>n<=.0031308?n*12.92:Math.pow(n,1/2.4)*1.055-.055,mi=n=>n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4);function kb(n,t,e){let i=mi(Je(n.r)),s=mi(Je(n.g)),r=mi(Je(n.b));return{r:vn(na(i+e*(mi(Je(t.r))-i))),g:vn(na(s+e*(mi(Je(t.g))-s))),b:vn(na(r+e*(mi(Je(t.b))-r))),a:n.a+e*(t.a-n.a)}}function rr(n,t,e){if(n){let i=Qo(n);i[t]=Math.max(0,Math.min(i[t]+i[t]*e,t===0?360:1)),i=ea(i),n.r=i[0],n.g=i[1],n.b=i[2]}}function Qc(n,t){return n&&Object.assign(t||{},n)}function th(n){var t={r:0,g:0,b:0,a:255};return Array.isArray(n)?n.length>=3&&(t={r:n[0],g:n[1],b:n[2],a:255},n.length>3&&(t.a=vn(n[3]))):(t=Qc(n,{r:0,g:0,b:0,a:1}),t.a=vn(t.a)),t}function Sb(n){return n.charAt(0)==="r"?xb(n):gb(n)}var Gi=class{constructor(t){if(t instanceof Gi)return t;let e=typeof t,i;e==="object"?i=th(t):e==="string"&&(i=ob(t)||_b(t)||Sb(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=Qc(this._rgb);return t&&(t.a=Je(t.a)),t}set rgb(t){this._rgb=th(t)}rgbString(){return this._valid?wb(this._rgb):void 0}hexString(){return this._valid?lb(this._rgb):void 0}hslString(){return this._valid?bb(this._rgb):void 0}mix(t,e){if(t){let i=this.rgb,s=t.rgb,r,o=e===r?.5:e,a=2*o-1,l=i.a-s.a,c=((a*l==-1?a:(a+l)/(1+a*l))+1)/2;r=1-c,i.r=255&c*i.r+r*s.r+.5,i.g=255&c*i.g+r*s.g+.5,i.b=255&c*i.b+r*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=kb(this._rgb,t._rgb,e)),this}clone(){return new Gi(this.rgb)}alpha(t){return this._rgb.a=vn(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=Xi(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return rr(this._rgb,2,t),this}darken(t){return rr(this._rgb,2,-t),this}saturate(t){return rr(this._rgb,1,t),this}desaturate(t){return rr(this._rgb,1,-t),this}rotate(t){return mb(this._rgb,t),this}};function eh(n){return new Gi(n)}function ia(n){if(n&&typeof n=="object"){let t=n.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function or(n){return ia(n)?n:eh(n)}function ar(n){return ia(n)?n:eh(n).saturate(.5).darken(.1).hexString()}var _n=Object.create(null),lr=Object.create(null);function Ui(n,t){if(!t)return n;let e=t.split(".");for(let i=0,s=e.length;i<s;++i){let r=e[i];n=n[r]||(n[r]=Object.create(null))}return n}function sa(n,t,e){return typeof t=="string"?zn(Ui(n,t),e):zn(Ui(n,""),t)}var nh=class{constructor(t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,i)=>ar(i.backgroundColor),this.hoverBorderColor=(e,i)=>ar(i.borderColor),this.hoverColor=(e,i)=>ar(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t)}set(t,e){return sa(this,t,e)}get(t){return Ui(this,t)}describe(t,e){return sa(lr,t,e)}override(t,e){return sa(_n,t,e)}route(t,e,i,s){let r=Ui(this,t),o=Ui(this,i),a="_"+e;Object.defineProperties(r,{[a]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let l=this[a],c=o[s];return at(l)?Object.assign({},c,l):st(l,c)},set(l){this[a]=l}}})}},ut=new nh({_scriptable:n=>!n.startsWith("on"),_indexable:n=>n!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}});function ih(n){return!n||vt(n.size)||vt(n.family)?null:(n.style?n.style+" ":"")+(n.weight?n.weight+" ":"")+n.size+"px "+n.family}function bi(n,t,e,i,s){let r=t[s];return r||(r=t[s]=n.measureText(s).width,e.push(s)),r>i&&(i=r),i}function ra(n,t,e,i){i=i||{};let s=i.data=i.data||{},r=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(s=i.data={},r=i.garbageCollect=[],i.font=t),n.save(),n.font=t;let o=0,a=e.length,l,c,h,f,g;for(l=0;l<a;l++)if(f=e[l],f!=null&&gt(f)!==!0)o=bi(n,s,r,o,f);else if(gt(f))for(c=0,h=f.length;c<h;c++)g=f[c],g!=null&&!gt(g)&&(o=bi(n,s,r,o,g));n.restore();let p=r.length/2;if(p>e.length){for(l=0;l<p;l++)delete s[r[l]];r.splice(0,p)}return o}function Qe(n,t,e){let i=n.currentDevicePixelRatio,s=e!==0?Math.max(e/2,.5):0;return Math.round((t-s)*i)/i+s}function cr(n,t){t=t||n.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,n.width,n.height),t.restore()}function Ki(n,t,e,i){hr(n,t,e,i,null)}function hr(n,t,e,i,s){let r,o,a,l,c,h,f=t.pointStyle,g=t.rotation,p=t.radius,b=(g||0)*Wi;if(f&&typeof f=="object"&&(r=f.toString(),r==="[object HTMLImageElement]"||r==="[object HTMLCanvasElement]")){n.save(),n.translate(e,i),n.rotate(b),n.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),n.restore();return}if(!(isNaN(p)||p<=0)){switch(n.beginPath(),f){default:s?n.ellipse(e,i,s/2,p,0,0,kt):n.arc(e,i,p,0,kt),n.closePath();break;case"triangle":n.moveTo(e+Math.sin(b)*p,i-Math.cos(b)*p),b+=gi,n.lineTo(e+Math.sin(b)*p,i-Math.cos(b)*p),b+=gi,n.lineTo(e+Math.sin(b)*p,i-Math.cos(b)*p),n.closePath();break;case"rectRounded":c=p*.516,l=p-c,o=Math.cos(b+ke)*l,a=Math.sin(b+ke)*l,n.arc(e-o,i-a,c,b-dt,b-Rt),n.arc(e+a,i-o,c,b-Rt,b),n.arc(e+o,i+a,c,b,b+Rt),n.arc(e-a,i+o,c,b+Rt,b+dt),n.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=s?s/2:l,n.rect(e-h,i-l,2*h,2*l);break}b+=ke;case"rectRot":o=Math.cos(b)*p,a=Math.sin(b)*p,n.moveTo(e-o,i-a),n.lineTo(e+a,i-o),n.lineTo(e+o,i+a),n.lineTo(e-a,i+o),n.closePath();break;case"crossRot":b+=ke;case"cross":o=Math.cos(b)*p,a=Math.sin(b)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o);break;case"star":o=Math.cos(b)*p,a=Math.sin(b)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o),b+=ke,o=Math.cos(b)*p,a=Math.sin(b)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a),n.moveTo(e+a,i-o),n.lineTo(e-a,i+o);break;case"line":o=s?s/2:Math.cos(b)*p,a=Math.sin(b)*p,n.moveTo(e-o,i-a),n.lineTo(e+o,i+a);break;case"dash":n.moveTo(e,i),n.lineTo(e+Math.cos(b)*p,i+Math.sin(b)*p);break}n.fill(),t.borderWidth>0&&n.stroke()}}function Yn(n,t,e){return e=e||.5,!t||n&&n.x>t.left-e&&n.x<t.right+e&&n.y>t.top-e&&n.y<t.bottom+e}function yn(n,t){n.save(),n.beginPath(),n.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),n.clip()}function xn(n){n.restore()}function oa(n,t,e,i,s){if(!t)return n.lineTo(e.x,e.y);if(s==="middle"){let r=(t.x+e.x)/2;n.lineTo(r,t.y),n.lineTo(r,e.y)}else s==="after"!=!!i?n.lineTo(t.x,e.y):n.lineTo(e.x,t.y);n.lineTo(e.x,e.y)}function aa(n,t,e,i){if(!t)return n.lineTo(e.x,e.y);n.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?e.cp2x:e.cp1x,i?e.cp2y:e.cp1y,e.x,e.y)}function tn(n,t,e,i,s,r={}){let o=gt(t)?t:[t],a=r.strokeWidth>0&&r.strokeColor!=="",l,c;for(n.save(),n.font=s.string,Mb(n,r),l=0;l<o.length;++l)c=o[l],a&&(r.strokeColor&&(n.strokeStyle=r.strokeColor),vt(r.strokeWidth)||(n.lineWidth=r.strokeWidth),n.strokeText(c,e,i,r.maxWidth)),n.fillText(c,e,i,r.maxWidth),Cb(n,e,i,c,r),i+=s.lineHeight;n.restore()}function Mb(n,t){t.translation&&n.translate(t.translation[0],t.translation[1]),vt(t.rotation)||n.rotate(t.rotation),t.color&&(n.fillStyle=t.color),t.textAlign&&(n.textAlign=t.textAlign),t.textBaseline&&(n.textBaseline=t.textBaseline)}function Cb(n,t,e,i,s){if(s.strikethrough||s.underline){let r=n.measureText(i),o=t-r.actualBoundingBoxLeft,a=t+r.actualBoundingBoxRight,l=e-r.actualBoundingBoxAscent,c=e+r.actualBoundingBoxDescent,h=s.strikethrough?(l+c)/2:c;n.strokeStyle=n.fillStyle,n.beginPath(),n.lineWidth=s.decorationWidth||2,n.moveTo(o,h),n.lineTo(a,h),n.stroke()}}function en(n,t){let{x:e,y:i,w:s,h:r,radius:o}=t;n.arc(e+o.topLeft,i+o.topLeft,o.topLeft,-Rt,dt,!0),n.lineTo(e,i+r-o.bottomLeft),n.arc(e+o.bottomLeft,i+r-o.bottomLeft,o.bottomLeft,dt,Rt,!0),n.lineTo(e+s-o.bottomRight,i+r),n.arc(e+s-o.bottomRight,i+r-o.bottomRight,o.bottomRight,Rt,0,!0),n.lineTo(e+s,i+o.topRight),n.arc(e+s-o.topRight,i+o.topRight,o.topRight,0,-Rt,!0),n.lineTo(e+o.topLeft,i)}var Pb=new RegExp(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/),Tb=new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);function sh(n,t){let e=(""+n).match(Pb);if(!e||e[1]==="normal")return t*1.2;switch(n=+e[2],e[3]){case"px":return n;case"%":n/=100;break}return t*n}var Db=n=>+n||0;function Zi(n,t){let e={},i=at(t),s=i?Object.keys(t):t,r=at(n)?i?o=>st(n[o],n[t[o]]):o=>n[o]:()=>n;for(let o of s)e[o]=Db(r(o));return e}function ur(n){return Zi(n,{top:"y",right:"x",bottom:"y",left:"x"})}function Le(n){return Zi(n,["topLeft","topRight","bottomLeft","bottomRight"])}function Vt(n){let t=ur(n);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function jt(n,t){n=n||{},t=t||ut.font;let e=st(n.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let i=st(n.style,t.style);i&&!(""+i).match(Tb)&&(console.warn('Invalid font style specified: "'+i+'"'),i="");let s={family:st(n.family,t.family),lineHeight:sh(st(n.lineHeight,t.lineHeight),e),size:e,style:i,weight:st(n.weight,t.weight),string:""};return s.string=ih(s),s}function Xn(n,t,e,i){let s=!0,r,o,a;for(r=0,o=n.length;r<o;++r)if(a=n[r],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),s=!1),e!==void 0&&gt(a)&&(a=a[e%a.length],s=!1),a!==void 0))return i&&!s&&(i.cacheable=!1),a}function la(n,t,e){let{min:i,max:s}=n,r=Vs(t,(s-i)/2),o=(a,l)=>e&&a===0?0:a+l;return{min:o(i,-Math.abs(r)),max:o(s,r)}}function Ne(n,t){return Object.assign(Object.create(n),t)}function Ji(n,t=[""],e=n,i,s=()=>n[0]){Nt(i)||(i=lh("_fallback",n));let r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:n,_rootScopes:e,_fallback:i,_getTarget:s,override:o=>Ji([o,...n],t,e,i)};return new Proxy(r,{deleteProperty(o,a){return delete o[a],delete o._keys,delete n[0][a],!0},get(o,a){return rh(o,a,()=>$b(a,t,n,o))},getOwnPropertyDescriptor(o,a){return Reflect.getOwnPropertyDescriptor(o._scopes[0],a)},getPrototypeOf(){return Reflect.getPrototypeOf(n[0])},has(o,a){return ch(o).includes(a)},ownKeys(o){return ch(o)},set(o,a,l){let c=o._storage||(o._storage=s());return o[a]=c[a]=l,delete o._keys,!0}})}function wn(n,t,e,i){let s={_cacheable:!1,_proxy:n,_context:t,_subProxy:e,_stack:new Set,_descriptors:fr(n,i),setContext:r=>wn(n,r,e,i),override:r=>wn(n.override(r),t,e,i)};return new Proxy(s,{deleteProperty(r,o){return delete r[o],delete n[o],!0},get(r,o,a){return rh(r,o,()=>Eb(r,o,a))},getOwnPropertyDescriptor(r,o){return r._descriptors.allKeys?Reflect.has(n,o)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(n,o)},getPrototypeOf(){return Reflect.getPrototypeOf(n)},has(r,o){return Reflect.has(n,o)},ownKeys(){return Reflect.ownKeys(n)},set(r,o,a){return n[o]=a,delete r[o],!0}})}function fr(n,t={scriptable:!0,indexable:!0}){let{_scriptable:e=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=n;return{allKeys:s,scriptable:e,indexable:i,isScriptable:ce(e)?e:()=>e,isIndexable:ce(i)?i:()=>i}}var Ob=(n,t)=>n?n+Ni(t):t,ca=(n,t)=>at(t)&&n!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function rh(n,t,e){if(Object.prototype.hasOwnProperty.call(n,t))return n[t];let i=e();return n[t]=i,i}function Eb(n,t,e){let{_proxy:i,_context:s,_subProxy:r,_descriptors:o}=n,a=i[t];return ce(a)&&o.isScriptable(t)&&(a=Ab(t,a,n,e)),gt(a)&&a.length&&(a=Rb(t,a,n,o.isIndexable)),ca(t,a)&&(a=wn(a,s,r&&r[t],o)),a}function Ab(n,t,e,i){let{_proxy:s,_context:r,_subProxy:o,_stack:a}=e;if(a.has(n))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+n);return a.add(n),t=t(r,o||i),a.delete(n),ca(n,t)&&(t=ha(s._scopes,s,n,t)),t}function Rb(n,t,e,i){let{_proxy:s,_context:r,_subProxy:o,_descriptors:a}=e;if(Nt(r.index)&&i(n))t=t[r.index%t.length];else if(at(t[0])){let l=t,c=s._scopes.filter(h=>h!==l);t=[];for(let h of l){let f=ha(c,s,n,h);t.push(wn(f,r,o&&o[n],a))}}return t}function oh(n,t,e){return ce(n)?n(t,e):n}var Lb=(n,t)=>n===!0?t:typeof n=="string"?Be(t,n):void 0;function Fb(n,t,e,i,s){for(let r of t){let o=Lb(e,r);if(o){n.add(o);let a=oh(o._fallback,e,s);if(Nt(a)&&a!==e&&a!==i)return a}else if(o===!1&&Nt(i)&&e!==i)return null}return!1}function ha(n,t,e,i){let s=t._rootScopes,r=oh(t._fallback,e,i),o=[...n,...s],a=new Set;a.add(i);let l=ah(a,o,e,r||e,i);return l===null||Nt(r)&&r!==e&&(l=ah(a,o,r,l,i),l===null)?!1:Ji(Array.from(a),[""],s,r,()=>Ib(t,e,i))}function ah(n,t,e,i,s){for(;e;)e=Fb(n,t,e,i,s);return e}function Ib(n,t,e){let i=n._getTarget();t in i||(i[t]={});let s=i[t];return gt(s)&&at(e)?e:s}function $b(n,t,e,i){let s;for(let r of t)if(s=lh(Ob(r,n),e),Nt(s))return ca(n,s)?ha(e,i,n,s):s}function lh(n,t){for(let e of t){if(!e)continue;let i=e[n];if(Nt(i))return i}}function ch(n){let t=n._keys;return t||(t=n._keys=jb(n._scopes)),t}function jb(n){let t=new Set;for(let e of n)for(let i of Object.keys(e).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}function dr(n,t,e,i){let{iScale:s}=n,{key:r="r"}=this._parsing,o=new Array(i),a,l,c,h;for(a=0,l=i;a<l;++a)c=a+e,h=t[c],o[a]={r:s.parse(Be(h,r),c)};return o}var zb=Number.EPSILON||1e-14,vi=(n,t)=>t<n.length&&!n[t].skip&&n[t],hh=n=>n==="x"?"y":"x";function uh(n,t,e,i){let s=n.skip?t:n,r=t,o=e.skip?t:e,a=Ze(r,s),l=Ze(o,r),c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let f=i*c,g=i*h;return{previous:{x:r.x-f*(o.x-s.x),y:r.y-f*(o.y-s.y)},next:{x:r.x+g*(o.x-s.x),y:r.y+g*(o.y-s.y)}}}function Bb(n,t,e){let i=n.length,s,r,o,a,l,c=vi(n,0);for(let h=0;h<i-1;++h)if(l=c,c=vi(n,h+1),!(!l||!c)){if(Nn(t[h],0,zb)){e[h]=e[h+1]=0;continue}s=e[h]/t[h],r=e[h+1]/t[h],a=Math.pow(s,2)+Math.pow(r,2),!(a<=9)&&(o=3/Math.sqrt(a),e[h]=s*o*t[h],e[h+1]=r*o*t[h])}}function Nb(n,t,e="x"){let i=hh(e),s=n.length,r,o,a,l=vi(n,0);for(let c=0;c<s;++c){if(o=a,a=l,l=vi(n,c+1),!a)continue;let h=a[e],f=a[i];o&&(r=(h-o[e])/3,a[`cp1${e}`]=h-r,a[`cp1${i}`]=f-r*t[c]),l&&(r=(l[e]-h)/3,a[`cp2${e}`]=h+r,a[`cp2${i}`]=f+r*t[c])}}function fh(n,t="x"){let e=hh(t),i=n.length,s=Array(i).fill(0),r=Array(i),o,a,l,c=vi(n,0);for(o=0;o<i;++o)if(a=l,l=c,c=vi(n,o+1),!!l){if(c){let h=c[t]-l[t];s[o]=h!==0?(c[e]-l[e])/h:0}r[o]=a?c?Se(s[o-1])!==Se(s[o])?0:(s[o-1]+s[o])/2:s[o-1]:s[o]}Bb(n,s,r),Nb(n,r,t)}function pr(n,t,e){return Math.max(Math.min(n,e),t)}function Hb(n,t){let e,i,s,r,o,a=Yn(n[0],t);for(e=0,i=n.length;e<i;++e)o=r,r=a,a=e<i-1&&Yn(n[e+1],t),!!r&&(s=n[e],o&&(s.cp1x=pr(s.cp1x,t.left,t.right),s.cp1y=pr(s.cp1y,t.top,t.bottom)),a&&(s.cp2x=pr(s.cp2x,t.left,t.right),s.cp2y=pr(s.cp2y,t.top,t.bottom)))}function ua(n,t,e,i,s){let r,o,a,l;if(t.spanGaps&&(n=n.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")fh(n,s);else{let c=i?n[n.length-1]:n[0];for(r=0,o=n.length;r<o;++r)a=n[r],l=uh(c,a,n[Math.min(r+1,o-(i?0:1))%o],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&Hb(n,e)}function gr(){return typeof window!="undefined"&&typeof document!="undefined"}function Qi(n){let t=n.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function mr(n,t,e){let i;return typeof n=="string"?(i=parseInt(n,10),n.indexOf("%")!==-1&&(i=i/100*t.parentNode[e])):i=n,i}var br=n=>window.getComputedStyle(n,null);function dh(n,t){return br(n).getPropertyValue(t)}var Wb=["top","right","bottom","left"];function qn(n,t,e){let i={};e=e?"-"+e:"";for(let s=0;s<4;s++){let r=Wb[s];i[r]=parseFloat(n[t+"-"+r+e])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}var Vb=(n,t,e)=>(n>0||t>0)&&(!e||!e.shadowRoot);function Yb(n,t){let e=n.touches,i=e&&e.length?e[0]:n,{offsetX:s,offsetY:r}=i,o=!1,a,l;if(Vb(s,r,n.target))a=s,l=r;else{let c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,o=!0}return{x:a,y:l,box:o}}function nn(n,t){if("native"in n)return n;let{canvas:e,currentDevicePixelRatio:i}=t,s=br(e),r=s.boxSizing==="border-box",o=qn(s,"padding"),a=qn(s,"border","width"),{x:l,y:c,box:h}=Yb(n,e),f=o.left+(h&&a.left),g=o.top+(h&&a.top),{width:p,height:b}=t;return r&&(p-=o.width+a.width,b-=o.height+a.height),{x:Math.round((l-f)/p*e.width/i),y:Math.round((c-g)/b*e.height/i)}}function Xb(n,t,e){let i,s;if(t===void 0||e===void 0){let r=Qi(n);if(!r)t=n.clientWidth,e=n.clientHeight;else{let o=r.getBoundingClientRect(),a=br(r),l=qn(a,"border","width"),c=qn(a,"padding");t=o.width-c.width-l.width,e=o.height-c.height-l.height,i=mr(a.maxWidth,r,"clientWidth"),s=mr(a.maxHeight,r,"clientHeight")}}return{width:t,height:e,maxWidth:i||Hi,maxHeight:s||Hi}}var fa=n=>Math.round(n*10)/10;function da(n,t,e,i){let s=br(n),r=qn(s,"margin"),o=mr(s.maxWidth,n,"clientWidth")||Hi,a=mr(s.maxHeight,n,"clientHeight")||Hi,l=Xb(n,t,e),{width:c,height:h}=l;if(s.boxSizing==="content-box"){let f=qn(s,"border","width"),g=qn(s,"padding");c-=g.width+f.width,h-=g.height+f.height}return c=Math.max(0,c-r.width),h=Math.max(0,i?Math.floor(c/i):h-r.height),c=fa(Math.min(c,o,l.maxWidth)),h=fa(Math.min(h,a,l.maxHeight)),c&&!h&&(h=fa(c/2)),{width:c,height:h}}function vr(n,t,e){let i=t||1,s=Math.floor(n.height*i),r=Math.floor(n.width*i);n.height=s/i,n.width=r/i;let o=n.canvas;return o.style&&(e||!o.style.height&&!o.style.width)&&(o.style.height=`${n.height}px`,o.style.width=`${n.width}px`),n.currentDevicePixelRatio!==i||o.height!==s||o.width!==r?(n.currentDevicePixelRatio=i,o.height=s,o.width=r,n.ctx.setTransform(i,0,0,i,0,0),!0):!1}var pa=function(){let n=!1;try{let t={get passive(){return n=!0,!1}};window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(t){}return n}();function _r(n,t){let e=dh(n,t),i=e&&e.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function sn(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}}function ga(n,t,e,i){return{x:n.x+e*(t.x-n.x),y:i==="middle"?e<.5?n.y:t.y:i==="after"?e<1?n.y:t.y:e>0?t.y:n.y}}function ma(n,t,e,i){let s={x:n.cp2x,y:n.cp2y},r={x:t.cp1x,y:t.cp1y},o=sn(n,s,e),a=sn(s,r,e),l=sn(r,t,e),c=sn(o,a,e),h=sn(a,l,e);return sn(c,h,e)}var ph=new Map;function qb(n,t){t=t||{};let e=n+JSON.stringify(t),i=ph.get(e);return i||(i=new Intl.NumberFormat(n,t),ph.set(e,i)),i}function Gn(n,t,e){return qb(t,e).format(n)}var Gb=function(n,t){return{x(e){return n+n+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,i){return e-i},leftForLtr(e,i){return e-i}}},Ub=function(){return{x(n){return n},setWidth(n){},textAlign(n){return n},xPlus(n,t){return n+t},leftForLtr(n,t){return n}}};function kn(n,t,e){return n?Gb(t,e):Ub()}function yr(n,t){let e,i;(t==="ltr"||t==="rtl")&&(e=n.canvas.style,i=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),n.prevTextDirection=i)}function xr(n,t){t!==void 0&&(delete n.prevTextDirection,n.canvas.style.setProperty("direction",t[0],t[1]))}function gh(n){return n==="angle"?{between:Wn,compare:Vc,normalize:fe}:{between:Ae,compare:(t,e)=>t-e,normalize:t=>t}}function mh({start:n,end:t,count:e,loop:i,style:s}){return{start:n%e,end:t%e,loop:i&&(t-n+1)%e==0,style:s}}function Kb(n,t,e){let{property:i,start:s,end:r}=e,{between:o,normalize:a}=gh(i),l=t.length,{start:c,end:h,loop:f}=n,g,p;if(f){for(c+=l,h+=l,g=0,p=l;g<p&&o(a(t[c%l][i]),s,r);++g)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:f,style:n.style}}function wr(n,t,e){if(!e)return[n];let{property:i,start:s,end:r}=e,o=t.length,{compare:a,between:l,normalize:c}=gh(i),{start:h,end:f,loop:g,style:p}=Kb(n,t,e),b=[],x=!1,S=null,C,T,I,A=()=>l(s,I,C)&&a(s,I)!==0,F=()=>a(r,C)===0||l(r,I,C),j=()=>x||A(),N=()=>!x||F();for(let G=h,U=h;G<=f;++G)T=t[G%o],!T.skip&&(C=c(T[i]),C!==I&&(x=l(C,s,r),S===null&&j()&&(S=a(C,s)===0?G:U),S!==null&&N()&&(b.push(mh({start:S,end:G,loop:g,count:o,style:p})),S=null),U=G,I=C));return S!==null&&b.push(mh({start:S,end:f,loop:g,count:o,style:p})),b}function kr(n,t){let e=[],i=n.segments;for(let s=0;s<i.length;s++){let r=wr(i[s],n.points,t);r.length&&e.push(...r)}return e}function Zb(n,t,e,i){let s=0,r=t-1;if(e&&!i)for(;s<t&&!n[s].skip;)s++;for(;s<t&&n[s].skip;)s++;for(s%=t,e&&(r+=s);r>s&&n[r%t].skip;)r--;return r%=t,{start:s,end:r}}function Jb(n,t,e,i){let s=n.length,r=[],o=t,a=n[t],l;for(l=t+1;l<=e;++l){let c=n[l%s];c.skip||c.stop?a.skip||(i=!1,r.push({start:t%s,end:(l-1)%s,loop:i}),t=o=c.stop?l:null):(o=l,a.skip&&(t=l)),a=c}return o!==null&&r.push({start:t%s,end:o%s,loop:i}),r}function ba(n,t){let e=n.points,i=n.options.spanGaps,s=e.length;if(!s)return[];let r=!!n._loop,{start:o,end:a}=Zb(e,s,r,i);if(i===!0)return bh(n,[{start:o,end:a,loop:r}],e,t);let l=a<o?a+s:a,c=!!n._fullLoop&&o===0&&a===s-1;return bh(n,Jb(e,o,l,c),e,t)}function bh(n,t,e,i){return!i||!i.setContext||!e?t:Qb(n,t,e,i)}function Qb(n,t,e,i){let s=n._chart.getContext(),r=vh(n.options),{_datasetIndex:o,options:{spanGaps:a}}=n,l=e.length,c=[],h=r,f=t[0].start,g=f;function p(b,x,S,C){let T=a?-1:1;if(b!==x){for(b+=l;e[b%l].skip;)b-=T;for(;e[x%l].skip;)x+=T;b%l!=x%l&&(c.push({start:b%l,end:x%l,loop:S,style:C}),h=C,f=x%l)}}for(let b of t){f=a?f:b.start;let x=e[f%l],S;for(g=f+1;g<=b.end;g++){let C=e[g%l];S=vh(i.setContext(Ne(s,{type:"segment",p0:x,p1:C,p0DataIndex:(g-1)%l,p1DataIndex:g%l,datasetIndex:o}))),t0(S,h)&&p(f,g-1,b.loop,h),x=C,h=S}f<g-1&&p(f,g-1,b.loop,h)}return c}function vh(n){return{backgroundColor:n.backgroundColor,borderCapStyle:n.borderCapStyle,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderJoinStyle:n.borderJoinStyle,borderWidth:n.borderWidth,borderColor:n.borderColor}}function t0(n,t){return t&&JSON.stringify(n)!==JSON.stringify(t)}var _h=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let r=e.listeners[s],o=e.duration;r.forEach(a=>a({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Js.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;let r=i.items,o=r.length-1,a=!1,l;for(;o>=0;--o)l=r[o],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(r[o]=r[r.length-1],r.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),r.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=r.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);!e||(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}},rn=new _h,yh="transparent",e0={boolean(n,t,e){return e>.5?t:n},color(n,t,e){let i=or(n||yh),s=i.valid&&or(t||yh);return s&&s.valid?s.mix(i,e).hexString():t},number(n,t,e){return n+(t-n)*e}},xh=class{constructor(t,e,i,s){let r=e[i];s=Xn([t.to,s,r,t.from]);let o=Xn([t.from,r,s]);this._active=!0,this._fn=t.fn||e0[t.type||typeof o],this._easing=Vn[t.easing]||Vn.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],r=i-this._start,o=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=Xn([t.to,e,s,t.from]),this._from=Xn([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e=t-this._start,i=this._duration,s=this._prop,r=this._from,o=this._loop,a=this._to,l;if(this._active=r!==a&&(o||e<i),!this._active){this._target[s]=a,this._notify(!0);return}if(e<0){this._target[s]=r;return}l=e/i%2,l=o&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(r,a,l)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}},n0=["x","y","borderWidth","radius","tension"],i0=["color","borderColor","backgroundColor"];ut.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0});var s0=Object.keys(ut.animation);ut.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:n=>n!=="onProgress"&&n!=="onComplete"&&n!=="fn"});ut.set("animations",{colors:{type:"color",properties:i0},numbers:{type:"number",properties:n0}});ut.describe("animations",{_fallback:"animation"});ut.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:n=>n|0}}}});var ts=class{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!at(t))return;let e=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{let s=t[i];if(!at(s))return;let r={};for(let o of s0)r[o]=s[o];(gt(s.properties)&&s.properties||[i]).forEach(o=>{(o===i||!e.has(o))&&e.set(o,r)})})}_animateOptions(t,e){let i=e.options,s=o0(t,i);if(!s)return[];let r=this._createAnimations(s,i);return i.$shared&&r0(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){let i=this._properties,s=[],r=t.$animations||(t.$animations={}),o=Object.keys(e),a=Date.now(),l;for(l=o.length-1;l>=0;--l){let c=o[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,e));continue}let h=e[c],f=r[c],g=i.get(c);if(f)if(g&&f.active()){f.update(g,h,a);continue}else f.cancel();if(!g||!g.duration){t[c]=h;continue}r[c]=f=new xh(g,t,c,h),s.push(f)}return s}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}let i=this._createAnimations(t,e);if(i.length)return rn.add(this._chart,i),!0}};function r0(n,t){let e=[],i=Object.keys(t);for(let s=0;s<i.length;s++){let r=n[i[s]];r&&r.active()&&e.push(r.wait())}return Promise.all(e)}function o0(n,t){if(!t)return;let e=n.options;if(!e){n.options=t;return}return e.$shared&&(n.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function wh(n,t){let e=n&&n.options||{},i=e.reverse,s=e.min===void 0?t:0,r=e.max===void 0?t:0;return{start:i?r:s,end:i?s:r}}function a0(n,t,e){if(e===!1)return!1;let i=wh(n,e),s=wh(t,e);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function l0(n){let t,e,i,s;return at(n)?(t=n.top,e=n.right,i=n.bottom,s=n.left):t=e=i=s=n,{top:t,right:e,bottom:i,left:s,disabled:n===!1}}function kh(n,t){let e=[],i=n._getSortedDatasetMetas(t),s,r;for(s=0,r=i.length;s<r;++s)e.push(i[s].index);return e}function Sh(n,t,e,i={}){let s=n.keys,r=i.mode==="single",o,a,l,c;if(t!==null){for(o=0,a=s.length;o<a;++o){if(l=+s[o],l===e){if(i.all)continue;break}c=n.values[l],$t(c)&&(r||t===0||Se(t)===Se(c))&&(t+=c)}return t}}function c0(n){let t=Object.keys(n),e=new Array(t.length),i,s,r;for(i=0,s=t.length;i<s;++i)r=t[i],e[i]={x:r,y:n[r]};return e}function Mh(n,t){let e=n&&n.options.stacked;return e||e===void 0&&t.stack!==void 0}function h0(n,t,e){return`${n.id}.${t.id}.${e.stack||e.type}`}function u0(n){let{min:t,max:e,minDefined:i,maxDefined:s}=n.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?e:Number.POSITIVE_INFINITY}}function f0(n,t,e){let i=n[t]||(n[t]={});return i[e]||(i[e]={})}function Ch(n,t,e,i){for(let s of t.getMatchingVisibleMetas(i).reverse()){let r=n[s.index];if(e&&r>0||!e&&r<0)return s.index}return null}function Ph(n,t){let{chart:e,_cachedMeta:i}=n,s=e._stacks||(e._stacks={}),{iScale:r,vScale:o,index:a}=i,l=r.axis,c=o.axis,h=h0(r,o,i),f=t.length,g;for(let p=0;p<f;++p){let b=t[p],{[l]:x,[c]:S}=b,C=b._stacks||(b._stacks={});g=C[c]=f0(s,h,x),g[a]=S,g._top=Ch(g,o,!0,i.type),g._bottom=Ch(g,o,!1,i.type)}}function va(n,t){let e=n.scales;return Object.keys(e).filter(i=>e[i].axis===t).shift()}function d0(n,t){return Ne(n,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function p0(n,t,e){return Ne(n,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function es(n,t){let e=n.controller.index,i=n.vScale&&n.vScale.axis;if(!!i){t=t||n._parsed;for(let s of t){let r=s._stacks;if(!r||r[i]===void 0||r[i][e]===void 0)return;delete r[i][e]}}}var _a=n=>n==="reset"||n==="none",Th=(n,t)=>t?n:Object.assign({},n),g0=(n,t,e)=>n&&!t.hidden&&t._stacked&&{keys:kh(e,!0),values:null},Ce=class{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Mh(t.vScale,t),this.addElements()}updateIndex(t){this.index!==t&&es(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(f,g,p,b)=>f==="x"?g:f==="r"?b:p,r=e.xAxisID=st(i.xAxisID,va(t,"x")),o=e.yAxisID=st(i.yAxisID,va(t,"y")),a=e.rAxisID=st(i.rAxisID,va(t,"r")),l=e.indexAxis,c=e.iAxisID=s(l,r,o,a),h=e.vAxisID=s(l,o,r,a);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&Ks(this._data,this),t._stacked&&es(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(at(e))this._data=c0(e);else if(i!==e){if(i){Ks(i,this);let s=this._cachedMeta;es(s),s._parsed=[]}e&&Object.isExtensible(e)&&Uo(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let r=e._stacked;e._stacked=Mh(e.vScale,e),e.stack!==i.stack&&(s=!0,es(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&Ph(this,e._parsed)}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let{_cachedMeta:i,_data:s}=this,{iScale:r,_stacked:o}=i,a=r.axis,l=t===0&&e===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],h,f,g;if(this._parsing===!1)i._parsed=s,i._sorted=!0,g=s;else{gt(s[t])?g=this.parseArrayData(i,s,t,e):at(s[t])?g=this.parseObjectData(i,s,t,e):g=this.parsePrimitiveData(i,s,t,e);let p=()=>f[a]===null||c&&f[a]<c[a];for(h=0;h<e;++h)i._parsed[h+t]=f=g[h],l&&(p()&&(l=!1),c=f);i._sorted=l}o&&Ph(this,g)}parsePrimitiveData(t,e,i,s){let{iScale:r,vScale:o}=t,a=r.axis,l=o.axis,c=r.getLabels(),h=r===o,f=new Array(s),g,p,b;for(g=0,p=s;g<p;++g)b=g+i,f[g]={[a]:h||r.parse(c[b],b),[l]:o.parse(e[b],b)};return f}parseArrayData(t,e,i,s){let{xScale:r,yScale:o}=t,a=new Array(s),l,c,h,f;for(l=0,c=s;l<c;++l)h=l+i,f=e[h],a[l]={x:r.parse(f[0],h),y:o.parse(f[1],h)};return a}parseObjectData(t,e,i,s){let{xScale:r,yScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s),h,f,g,p;for(h=0,f=s;h<f;++h)g=h+i,p=e[g],c[h]={x:r.parse(Be(p,a),g),y:o.parse(Be(p,l),g)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,r=this._cachedMeta,o=e[t.axis],a={keys:kh(s,!0),values:e._stacks[t.axis]};return Sh(a,o,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let r=i[e.axis],o=r===null?NaN:r,a=s&&i._stacks[e.axis];s&&a&&(s.values=a,o=Sh(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){let i=this._cachedMeta,s=i._parsed,r=i._sorted&&t===i.iScale,o=s.length,a=this._getOtherScale(t),l=g0(e,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:f}=u0(a),g,p;function b(){p=s[g];let x=p[a.axis];return!$t(p[t.axis])||h>x||f<x}for(g=0;g<o&&!(!b()&&(this.updateRangeFromParsed(c,t,p,l),r));++g);if(r){for(g=o-1;g>=0;--g)if(!b()){this.updateRangeFromParsed(c,t,p,l);break}}return c}getAllParsedValues(t){let e=this._cachedMeta._parsed,i=[],s,r,o;for(s=0,r=e.length;s<r;++s)o=e[s][t.axis],$t(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){let e=this._cachedMeta;this.update(t||"default"),e._clip=l0(st(this.options.clip,a0(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],r=e.chartArea,o=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop,h;for(i.dataset&&i.dataset.draw(t,r,a,l),h=a;h<a+l;++h){let f=s[h];f.hidden||(f.active&&c?o.push(f):f.draw(t,r))}for(h=0;h<o.length;++h)o[h].draw(t,r)}getStyle(t,e){let i=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){let s=this.getDataset(),r;if(t>=0&&t<this._cachedMeta.data.length){let o=this._cachedMeta.data[t];r=o.$context||(o.$context=p0(this.getContext(),t,o)),r.parsed=this.getParsed(t),r.raw=s.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=d0(this.chart.getContext(),this.index)),r.dataset=s,r.index=r.datasetIndex=this.index;return r.active=!!e,r.mode=i,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s=e==="active",r=this._cachedDataOpts,o=t+"-"+e,a=r[o],l=this.enableOptionSharing&&Nt(i);if(a)return Th(a,l);let c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),f=s?[`${t}Hover`,"hover",t,""]:[t,""],g=c.getOptionScopes(this.getDataset(),h),p=Object.keys(ut.elements[t]),b=()=>this.getContext(i,s),x=c.resolveNamedOptions(g,p,b,f);return x.$shared&&(x.$shared=l,r[o]=Object.freeze(Th(x,l))),x}_resolveAnimations(t,e,i){let s=this.chart,r=this._cachedDataOpts,o=`animation-${e}`,a=r[o];if(a)return a;let l;if(s.options.animation!==!1){let h=this.chart.config,f=h.datasetAnimationScopeKeys(this._type,e),g=h.getOptionScopes(this.getDataset(),f);l=h.createResolver(g,this.getContext(t,i,e))}let c=new ts(s,l&&l.animations);return l&&l._cacheable&&(r[o]=Object.freeze(c)),c}getSharedOptions(t){if(!!t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||_a(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),o=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:o}}updateElement(t,e,i,s){_a(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!_a(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];let s=i.length,r=e.length,o=Math.min(r,s);o&&this.parse(0,o),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){let s=this._cachedMeta,r=s.data,o=t+e,a,l=c=>{for(c.length+=e,a=c.length-1;a>=o;a--)c[a]=c[a-e]};for(l(r),a=t;a<o;++a)r[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,e),i&&this.updateElements(r,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&es(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}};Ce.defaults={};Ce.prototype.datasetElementType=null;Ce.prototype.dataElementType=null;function m0(n,t){if(!n._cache.$bar){let e=n.getMatchingVisibleMetas(t),i=[];for(let s=0,r=e.length;s<r;s++)i=i.concat(e[s].controller.getAllParsedValues(n));n._cache.$bar=Zs(i.sort((s,r)=>s-r))}return n._cache.$bar}function b0(n){let t=n.iScale,e=m0(t,n.type),i=t._length,s,r,o,a,l=()=>{o===32767||o===-32768||(Nt(a)&&(i=Math.min(i,Math.abs(o-a)||i)),a=o)};for(s=0,r=e.length;s<r;++s)o=t.getPixelForValue(e[s]),l();for(a=void 0,s=0,r=t.ticks.length;s<r;++s)o=t.getPixelForTick(s),l();return i}function v0(n,t,e,i){let s=e.barThickness,r,o;return vt(s)?(r=t.min*e.categoryPercentage,o=e.barPercentage):(r=s*i,o=1),{chunk:r/i,ratio:o,start:t.pixels[n]-r/2}}function _0(n,t,e,i){let s=t.pixels,r=s[n],o=n>0?s[n-1]:null,a=n<s.length-1?s[n+1]:null,l=e.categoryPercentage;o===null&&(o=r-(a===null?t.end-t.start:a-r)),a===null&&(a=r+r-o);let c=r-(r-Math.min(o,a))/2*l;return{chunk:Math.abs(a-o)/2*l/i,ratio:e.barPercentage,start:c}}function y0(n,t,e,i){let s=e.parse(n[0],i),r=e.parse(n[1],i),o=Math.min(s,r),a=Math.max(s,r),l=o,c=a;Math.abs(o)>Math.abs(a)&&(l=a,c=o),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:s,end:r,min:o,max:a}}function Dh(n,t,e,i){return gt(n)?y0(n,t,e,i):t[e.axis]=e.parse(n,i),t}function Oh(n,t,e,i){let s=n.iScale,r=n.vScale,o=s.getLabels(),a=s===r,l=[],c,h,f,g;for(c=e,h=e+i;c<h;++c)g=t[c],f={},f[s.axis]=a||s.parse(o[c],c),l.push(Dh(g,f,r,c));return l}function ya(n){return n&&n.barStart!==void 0&&n.barEnd!==void 0}function x0(n,t,e){return n!==0?Se(n):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function w0(n){let t,e,i,s,r;return n.horizontal?(t=n.base>n.x,e="left",i="right"):(t=n.base<n.y,e="bottom",i="top"),t?(s="end",r="start"):(s="start",r="end"),{start:e,end:i,reverse:t,top:s,bottom:r}}function k0(n,t,e,i){let s=t.borderSkipped,r={};if(!s){n.borderSkipped=r;return}if(s===!0){n.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:o,end:a,reverse:l,top:c,bottom:h}=w0(n);s==="middle"&&e&&(n.enableBorderRadius=!0,(e._top||0)===i?s=c:(e._bottom||0)===i?s=h:(r[Eh(h,o,a,l)]=!0,s=c)),r[Eh(s,o,a,l)]=!0,n.borderSkipped=r}function Eh(n,t,e,i){return i?(n=S0(n,t,e),n=Ah(n,e,t)):n=Ah(n,t,e),n}function S0(n,t,e){return n===t?e:n===e?t:n}function Ah(n,t,e){return n==="start"?t:n==="end"?e:n}function M0(n,{inflateAmount:t},e){n.inflateAmount=t==="auto"?e===1?.33:0:t}var ns=class extends Ce{parsePrimitiveData(t,e,i,s){return Oh(t,e,i,s)}parseArrayData(t,e,i,s){return Oh(t,e,i,s)}parseObjectData(t,e,i,s){let{iScale:r,vScale:o}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=r.axis==="x"?a:l,h=o.axis==="x"?a:l,f=[],g,p,b,x;for(g=i,p=i+s;g<p;++g)x=e[g],b={},b[r.axis]=r.parse(Be(x,c),g),f.push(Dh(Be(x,h),b,o,g));return f}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let e=this._cachedMeta,{iScale:i,vScale:s}=e,r=this.getParsed(t),o=r._custom,a=ya(o)?"["+o.start+", "+o.end+"]":""+s.getLabelForValue(r[s.axis]);return{label:""+i.getLabelForValue(r[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let r=s==="reset",{index:o,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:f,includeOptions:g}=this._getSharedOptions(e,s);for(let p=e;p<e+i;p++){let b=this.getParsed(p),x=r||vt(b[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(p),S=this._calculateBarIndexPixels(p,h),C=(b._stacks||{})[a.axis],T={horizontal:c,base:x.base,enableBorderRadius:!C||ya(b._custom)||o===C._top||o===C._bottom,x:c?x.head:S.center,y:c?S.center:x.head,height:c?S.size:Math.abs(x.size),width:c?Math.abs(x.size):S.size};g&&(T.options=f||this.resolveDataElementOptions(p,t[p].active?"active":s));let I=T.options||t[p].options;k0(T,I,C,o),M0(T,I,h.ratio),this.updateElement(t[p],p,T,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(l=>l.controller.options.grouped),r=i.options.stacked,o=[],a=l=>{let c=l.controller.getParsed(e),h=c&&c[l.vScale.axis];if(vt(h)||isNaN(h))return!0};for(let l of s)if(!(e!==void 0&&a(l))&&((r===!1||o.indexOf(l.stack)===-1||r===void 0&&l.stack===void 0)&&o.push(l.stack),l.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,i){let s=this._getStacks(t,i),r=e!==void 0?s.indexOf(e):-1;return r===-1?s.length-1:r}_getRuler(){let t=this.options,e=this._cachedMeta,i=e.iScale,s=[],r,o;for(r=0,o=e.data.length;r<o;++r)s.push(i.getPixelForValue(this.getParsed(r)[i.axis],r));let a=t.barThickness;return{min:a||b0(e),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:e,_stacked:i},options:{base:s,minBarLength:r}}=this,o=s||0,a=this.getParsed(t),l=a._custom,c=ya(l),h=a[e.axis],f=0,g=i?this.applyStack(e,a,i):h,p,b;g!==h&&(f=g-h,g=h),c&&(h=l.barStart,g=l.barEnd-l.barStart,h!==0&&Se(h)!==Se(l.barEnd)&&(f=0),f+=h);let x=!vt(s)&&!c?s:f,S=e.getPixelForValue(x);if(this.chart.getDataVisibility(t)?p=e.getPixelForValue(f+g):p=S,b=p-S,Math.abs(b)<r){b=x0(b,e,o)*r,h===o&&(S-=b/2);let C=e.getPixelForDecimal(0),T=e.getPixelForDecimal(1),I=Math.min(C,T),A=Math.max(C,T);S=Math.max(Math.min(S,A),I),p=S+b}if(S===e.getPixelForValue(o)){let C=Se(b)*e.getLineWidthForValue(o)/2;S+=C,b-=C}return{size:b,base:S,head:p,center:p+b/2}}_calculateBarIndexPixels(t,e){let i=e.scale,s=this.options,r=s.skipNull,o=st(s.maxBarThickness,1/0),a,l;if(e.grouped){let c=r?this._getStackCount(t):e.stackCount,h=s.barThickness==="flex"?_0(t,e,s,c):v0(t,e,s,c),f=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0);a=h.start+h.chunk*f+h.chunk/2,l=Math.min(o,h.chunk*h.ratio)}else a=i.getPixelForValue(this.getParsed(t)[i.axis],t),l=Math.min(o,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,r=0;for(;r<s;++r)this.getParsed(r)[e.axis]!==null&&i[r].draw(this._ctx)}};ns.id="bar";ns.defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};ns.overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};var is=class extends Ce{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let r=super.parsePrimitiveData(t,e,i,s);for(let o=0;o<r.length;o++)r[o]._custom=this.resolveDataElementOptions(o+i).radius;return r}parseArrayData(t,e,i,s){let r=super.parseArrayData(t,e,i,s);for(let o=0;o<r.length;o++){let a=e[i+o];r[o]._custom=st(a[2],this.resolveDataElementOptions(o+i).radius)}return r}parseObjectData(t,e,i,s){let r=super.parseObjectData(t,e,i,s);for(let o=0;o<r.length;o++){let a=e[i+o];r[o]._custom=st(a&&a.r&&+a.r,this.resolveDataElementOptions(o+i).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,{xScale:i,yScale:s}=e,r=this.getParsed(t),o=i.getLabelForValue(r.x),a=s.getLabelForValue(r.y),l=r._custom;return{label:e.label,value:"("+o+", "+a+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,s),h=o.axis,f=a.axis;for(let g=e;g<e+i;g++){let p=t[g],b=!r&&this.getParsed(g),x={},S=x[h]=r?o.getPixelForDecimal(.5):o.getPixelForValue(b[h]),C=x[f]=r?a.getBasePixel():a.getPixelForValue(b[f]);x.skip=isNaN(S)||isNaN(C),c&&(x.options=l||this.resolveDataElementOptions(g,p.active?"active":s),r&&(x.options.radius=0)),this.updateElement(p,g,x,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let r=s.radius;return e!=="active"&&(s.radius=0),s.radius+=st(i&&i._custom,r),s}};is.id="bubble";is.defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};is.overrides={scales:{x:{type:"linear"},y:{type:"linear"}},plugins:{tooltip:{callbacks:{title(){return""}}}}};function C0(n,t,e){let i=1,s=1,r=0,o=0;if(t<kt){let a=n,l=a+t,c=Math.cos(a),h=Math.sin(a),f=Math.cos(l),g=Math.sin(l),p=(I,A,F)=>Wn(I,a,l,!0)?1:Math.max(A,A*e,F,F*e),b=(I,A,F)=>Wn(I,a,l,!0)?-1:Math.min(A,A*e,F,F*e),x=p(0,c,f),S=p(Rt,h,g),C=b(dt,c,f),T=b(dt+Rt,h,g);i=(x-C)/2,s=(S-T)/2,r=-(x+C)/2,o=-(S+T)/2}return{ratioX:i,ratioY:s,offsetX:r,offsetY:o}}var Un=class extends Ce{constructor(t,e){super(t,e);this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let r=l=>+i[l];if(at(i[t])){let{key:l="value"}=this._parsing;r=c=>+Be(i[c],l)}let o,a;for(o=t,a=t+e;o<a;++o)s._parsed[o]=r(o)}}_getRotation(){return Wt(this.options.rotation-90)}_getCircumference(){return Wt(this.options.circumference)}_getRotationExtents(){let t=kt,e=-kt;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)){let s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),o=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+o)}return{rotation:t,circumference:e-t}}update(t){let e=this.chart,{chartArea:i}=e,s=this._cachedMeta,r=s.data,o=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-o)/2,0),l=Math.min(Ho(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:f}=this._getRotationExtents(),{ratioX:g,ratioY:p,offsetX:b,offsetY:x}=C0(f,h,l),S=(i.width-o)/g,C=(i.height-o)/p,T=Math.max(Math.min(S,C)/2,0),I=Vs(this.options.radius,T),A=Math.max(I*l,0),F=(I-A)/this._getVisibleDatasetWeightTotal();this.offsetX=b*I,this.offsetY=x*I,s.total=this.calculateTotal(),this.outerRadius=I-F*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-F*c,0),this.updateElements(r,0,r.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/kt)}updateElements(t,e,i,s){let r=s==="reset",o=this.chart,a=o.chartArea,c=o.options.animation,h=(a.left+a.right)/2,f=(a.top+a.bottom)/2,g=r&&c.animateScale,p=g?0:this.innerRadius,b=g?0:this.outerRadius,{sharedOptions:x,includeOptions:S}=this._getSharedOptions(e,s),C=this._getRotation(),T;for(T=0;T<e;++T)C+=this._circumference(T,r);for(T=e;T<e+i;++T){let I=this._circumference(T,r),A=t[T],F={x:h+this.offsetX,y:f+this.offsetY,startAngle:C,endAngle:C+I,circumference:I,outerRadius:b,innerRadius:p};S&&(F.options=x||this.resolveDataElementOptions(T,A.active?"active":s)),C+=I,this.updateElement(A,T,F,s)}}calculateTotal(){let t=this._cachedMeta,e=t.data,i=0,s;for(s=0;s<e.length;s++){let r=t._parsed[s];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(s)&&!e[s].hidden&&(i+=Math.abs(r))}return i}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?kt*(Math.abs(t)/e):0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Gn(e._parsed[t],i.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e=0,i=this.chart,s,r,o,a,l;if(!t){for(s=0,r=i.data.datasets.length;s<r;++s)if(i.isDatasetVisible(s)){o=i.getDatasetMeta(s),t=o.data,a=o.controller;break}}if(!t)return 0;for(s=0,r=t.length;s<r;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let r=this.resolveDataElementOptions(i);e=Math.max(e,r.offset||0,r.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(st(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}};Un.id="doughnut";Un.defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};Un.descriptors={_scriptable:n=>n!=="spacing",_indexable:n=>n!=="spacing"};Un.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(n){let t=n.data;if(t.labels.length&&t.datasets.length){let{labels:{pointStyle:e}}=n.legend.options;return t.labels.map((i,s)=>{let o=n.getDatasetMeta(0).controller.getStyle(s);return{text:i,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,lineWidth:o.borderWidth,pointStyle:e,hidden:!n.getDataVisibility(s),index:s}})}return[]}},onClick(n,t,e){e.chart.toggleDataVisibility(t.index),e.chart.update()}},tooltip:{callbacks:{title(){return""},label(n){let t=n.label,e=": "+n.formattedValue;return gt(t)?(t=t.slice(),t[0]+=e):t+=e,t}}}}};var ss=class extends Ce{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,o=this.chart._animationsDisabled,{start:a,count:l}=tr(e,s,o);this._drawStart=a,this._drawCount=l,er(e)&&(a=0,l=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;let c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:c},t),this.updateElements(s,a,l,t)}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:f}=this._getSharedOptions(e,s),g=o.axis,p=a.axis,{spanGaps:b,segment:x}=this.options,S=Ee(b)?b:Number.POSITIVE_INFINITY,C=this.chart._animationsDisabled||r||s==="none",T=e>0&&this.getParsed(e-1);for(let I=e;I<e+i;++I){let A=t[I],F=this.getParsed(I),j=C?A:{},N=vt(F[p]),G=j[g]=o.getPixelForValue(F[g],I),U=j[p]=r||N?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,F,l):F[p],I);j.skip=isNaN(G)||isNaN(U)||N,j.stop=I>0&&Math.abs(F[g]-T[g])>S,x&&(j.parsed=F,j.raw=c.data[I]),f&&(j.options=h||this.resolveDataElementOptions(I,A.active?"active":s)),C||this.updateElement(A,I,j,s),T=F}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;let r=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,r,o)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}};ss.id="line";ss.defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};ss.overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};var rs=class extends Ce{constructor(t,e){super(t,e);this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=Gn(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,i,s){return dr.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,s)=>{let r=this.getParsed(s).r;!isNaN(r)&&this.chart.getDataVisibility(s)&&(r<e.min&&(e.min=r),r>e.max&&(e.max=r))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.min(e.right-e.left,e.bottom-e.top),r=Math.max(s/2,0),o=Math.max(i.cutoutPercentage?r/100*i.cutoutPercentage:1,0),a=(r-o)/t.getVisibleDatasetCount();this.outerRadius=r-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,e,i,s){let r=s==="reset",o=this.chart,l=o.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,f=c.yCenter,g=c.getIndexAngle(0)-.5*dt,p=g,b,x=360/this.countVisibleElements();for(b=0;b<e;++b)p+=this._computeAngle(b,s,x);for(b=e;b<e+i;b++){let S=t[b],C=p,T=p+this._computeAngle(b,s,x),I=o.getDataVisibility(b)?c.getDistanceFromCenterForValue(this.getParsed(b).r):0;p=T,r&&(l.animateScale&&(I=0),l.animateRotate&&(C=T=g));let A={x:h,y:f,innerRadius:0,outerRadius:I,startAngle:C,endAngle:T,options:this.resolveDataElementOptions(b,S.active?"active":s)};this.updateElement(S,b,A,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?Wt(this.resolveDataElementOptions(t,e).angle||i):0}};rs.id="polarArea";rs.defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};rs.overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(n){let t=n.data;if(t.labels.length&&t.datasets.length){let{labels:{pointStyle:e}}=n.legend.options;return t.labels.map((i,s)=>{let o=n.getDatasetMeta(0).controller.getStyle(s);return{text:i,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,lineWidth:o.borderWidth,pointStyle:e,hidden:!n.getDataVisibility(s),index:s}})}return[]}},onClick(n,t,e){e.chart.toggleDataVisibility(t.index),e.chart.update()}},tooltip:{callbacks:{title(){return""},label(n){return n.chart.data.labels[n.dataIndex]+": "+n.formattedValue}}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};var Sr=class extends Un{};Sr.id="pie";Sr.defaults={cutout:0,rotation:0,circumference:360,radius:"100%"};var os=class extends Ce{getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return dr.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(i.points=s,t!=="resize"){let o=this.resolveDatasetElementOptions(t);this.options.showLine||(o.borderWidth=0);let a={_loop:!0,_fullLoop:r.length===s.length,options:o};this.updateElement(i,void 0,a,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let r=this._cachedMeta.rScale,o=s==="reset";for(let a=e;a<e+i;a++){let l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":s),h=r.getPointPositionForValue(a,this.getParsed(a).r),f=o?r.xCenter:h.x,g=o?r.yCenter:h.y,p={x:f,y:g,angle:h.angle,skip:isNaN(f)||isNaN(g),options:c};this.updateElement(l,a,p,s)}}};os.id="radar";os.defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};os.overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};var Zt=class{constructor(){this.x=void 0,this.y=void 0,this.active=!1,this.options=void 0,this.$animations=void 0}tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return Ee(this.x)&&Ee(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(r=>{s[r]=i[r]&&i[r].active()?i[r]._to:this[r]}),s}};Zt.defaults={};Zt.defaultRoutes=void 0;var Rh={values(n){return gt(n)?n:""+n},numeric(n,t,e){if(n===0)return"0";let i=this.chart.options.locale,s,r=n;if(e.length>1){let c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),r=P0(n,e)}let o=be(Math.abs(r)),a=Math.max(Math.min(-1*Math.floor(o),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Gn(n,i,l)},logarithmic(n,t,e){if(n===0)return"0";let i=n/Math.pow(10,Math.floor(be(n)));return i===1||i===2||i===5?Rh.numeric.call(this,n,t,e):""}};function P0(n,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&n!==Math.floor(n)&&(e=n-Math.floor(n)),e}var Mr={formatters:Rh};ut.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",grace:0,grid:{display:!0,lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(n,t)=>t.lineWidth,tickColor:(n,t)=>t.color,offset:!1,borderDash:[],borderDashOffset:0,borderWidth:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Mr.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}});ut.route("scale.ticks","color","","color");ut.route("scale.grid","color","","borderColor");ut.route("scale.grid","borderColor","","borderColor");ut.route("scale.title","color","","color");ut.describe("scale",{_fallback:!1,_scriptable:n=>!n.startsWith("before")&&!n.startsWith("after")&&n!=="callback"&&n!=="parser",_indexable:n=>n!=="borderDash"&&n!=="tickBorderDash"});ut.describe("scales",{_fallback:"scale"});ut.describe("scale.ticks",{_scriptable:n=>n!=="backdropPadding"&&n!=="callback",_indexable:n=>n!=="backdropPadding"});function T0(n,t){let e=n.options.ticks,i=e.maxTicksLimit||D0(n),s=e.major.enabled?E0(t):[],r=s.length,o=s[0],a=s[r-1],l=[];if(r>i)return A0(t,l,s,r/i),l;let c=O0(s,t,i);if(r>0){let h,f,g=r>1?Math.round((a-o)/(r-1)):null;for(Cr(t,l,c,vt(g)?0:o-g,o),h=0,f=r-1;h<f;h++)Cr(t,l,c,s[h],s[h+1]);return Cr(t,l,c,a,vt(g)?t.length:a+g),l}return Cr(t,l,c),l}function D0(n){let t=n.options.offset,e=n._tickSize(),i=n._length/e+(t?0:1),s=n._maxLength/e;return Math.floor(Math.min(i,s))}function O0(n,t,e){let i=R0(n),s=t.length/e;if(!i)return Math.max(s,1);let r=Vo(i);for(let o=0,a=r.length-1;o<a;o++){let l=r[o];if(l>s)return l}return Math.max(s,1)}function E0(n){let t=[],e,i;for(e=0,i=n.length;e<i;e++)n[e].major&&t.push(e);return t}function A0(n,t,e,i){let s=0,r=e[0],o;for(i=Math.ceil(i),o=0;o<n.length;o++)o===r&&(t.push(n[o]),s++,r=e[s*i])}function Cr(n,t,e,i,s){let r=st(i,0),o=Math.min(st(s,n.length),n.length),a=0,l,c,h;for(e=Math.ceil(e),s&&(l=s-i,e=l/Math.floor(l/e)),h=r;h<0;)a++,h=Math.round(r+a*e);for(c=Math.max(r,0);c<o;c++)c===h&&(t.push(n[c]),a++,h=Math.round(r+a*e))}function R0(n){let t=n.length,e,i;if(t<2)return!1;for(i=n[0],e=1;e<t;++e)if(n[e]-n[e-1]!==i)return!1;return i}var L0=n=>n==="left"?"right":n==="right"?"left":n,Lh=(n,t,e)=>t==="top"||t==="left"?n[t]+e:n[t]-e;function Fh(n,t){let e=[],i=n.length/t,s=n.length,r=0;for(;r<s;r+=i)e.push(n[Math.floor(r)]);return e}function F0(n,t,e){let i=n.ticks.length,s=Math.min(t,i-1),r=n._startPixel,o=n._endPixel,a=1e-6,l=n.getPixelForTick(s),c;if(!(e&&(i===1?c=Math.max(l-r,o-l):t===0?c=(n.getPixelForTick(1)-l)/2:c=(l-n.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<r-a||l>o+a)))return l}function I0(n,t){Ct(n,e=>{let i=e.gc,s=i.length/2,r;if(s>t){for(r=0;r<s;++r)delete e.data[i[r]];i.splice(0,s)}})}function as(n){return n.drawTicks?n.tickLength:0}function Ih(n,t){if(!n.display)return 0;let e=jt(n.font,t),i=Vt(n.padding);return(gt(n.text)?n.text.length:1)*e.lineHeight+i.height}function $0(n,t){return Ne(n,{scale:t,type:"scale"})}function j0(n,t,e){return Ne(n,{tick:e,index:t,type:"tick"})}function z0(n,t,e){let i=Yi(n);return(e&&t!=="right"||!e&&t==="right")&&(i=L0(i)),i}function B0(n,t,e,i){let{top:s,left:r,bottom:o,right:a,chart:l}=n,{chartArea:c,scales:h}=l,f=0,g,p,b,x=o-s,S=a-r;if(n.isHorizontal()){if(p=ie(i,r,a),at(e)){let C=Object.keys(e)[0],T=e[C];b=h[C].getPixelForValue(T)+x-t}else e==="center"?b=(c.bottom+c.top)/2+x-t:b=Lh(n,e,t);g=a-r}else{if(at(e)){let C=Object.keys(e)[0],T=e[C];p=h[C].getPixelForValue(T)-S+t}else e==="center"?p=(c.left+c.right)/2-S+t:p=Lh(n,e,t);b=ie(i,o,s),f=e==="left"?-Rt:Rt}return{titleX:p,titleY:b,maxWidth:g,rotation:f}}var Sn=class extends Zt{constructor(t){super();this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=me(t,Number.POSITIVE_INFINITY),e=me(e,Number.NEGATIVE_INFINITY),i=me(i,Number.POSITIVE_INFINITY),s=me(s,Number.NEGATIVE_INFINITY),{min:me(t,i),max:me(e,s),minDefined:$t(t),maxDefined:$t(e)}}getMinMax(t){let{min:e,max:i,minDefined:s,maxDefined:r}=this.getUserBounds(),o;if(s&&r)return{min:e,max:i};let a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)o=a[l].controller.getMinMax(this,t),s||(e=Math.min(e,o.min)),r||(i=Math.max(i,o.max));return e=r&&e>i?i:e,i=s&&e>i?e:i,{min:me(e,me(i,e)),max:me(i,me(e,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){wt(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:r,ticks:o}=this.options,a=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=la(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=a<this.ticks.length;this._convertTicksToLabels(l?Fh(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||o.source==="auto")&&(this.ticks=T0(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,i;this.isHorizontal()?(e=this.left,i=this.right):(e=this.top,i=this.bottom,t=!t),this._startPixel=e,this._endPixel=i,this._reversePixels=t,this._length=i-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){wt(this.options.afterUpdate,[this])}beforeSetDimensions(){wt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){wt(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),wt(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){wt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e=this.options.ticks,i,s,r;for(i=0,s=t.length;i<s;i++)r=t[i],r.label=wt(e.callback,[r.value,i,t],this)}afterTickToLabelConversion(){wt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){wt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t=this.options,e=t.ticks,i=this.ticks.length,s=e.minRotation||0,r=e.maxRotation,o=s,a,l,c;if(!this._isVisible()||!e.display||s>=r||i<=1||!this.isHorizontal()){this.labelRotation=s;return}let h=this._getLabelSizes(),f=h.widest.width,g=h.highest.height,p=Kt(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/i:p/(i-1),f+6>a&&(a=p/(i-(t.offset?.5:1)),l=this.maxHeight-as(t.grid)-e.padding-Ih(t.title,this.chart.options.font),c=Math.sqrt(f*f+g*g),o=Hn(Math.min(Math.asin(Kt((h.highest.height+6)/a,-1,1)),Math.asin(Kt(l/c,-1,1))-Math.asin(Kt(g/c,-1,1)))),o=Math.max(s,Math.min(r,o))),this.labelRotation=o}afterCalculateLabelRotation(){wt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){wt(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,o=this._isVisible(),a=this.isHorizontal();if(o){let l=Ih(s,e.options.font);if(a?(t.width=this.maxWidth,t.height=as(r)+l):(t.height=this.maxHeight,t.width=as(r)+l),i.display&&this.ticks.length){let{first:c,last:h,widest:f,highest:g}=this._getLabelSizes(),p=i.padding*2,b=Wt(this.labelRotation),x=Math.cos(b),S=Math.sin(b);if(a){let C=i.mirror?0:S*f.width+x*g.height;t.height=Math.min(this.maxHeight,t.height+C+p)}else{let C=i.mirror?0:x*f.width+S*g.height;t.width=Math.min(this.maxWidth,t.width+C+p)}this._calculatePadding(c,h,S,x)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:r,padding:o},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1),g=0,p=0;l?c?(g=s*t.width,p=i*e.height):(g=i*t.height,p=s*e.width):r==="start"?p=e.width:r==="end"?g=t.width:r!=="inner"&&(g=t.width/2,p=e.width/2),this.paddingLeft=Math.max((g-h+o)*this.width/(this.width-h),0),this.paddingRight=Math.max((p-f+o)*this.width/(this.width-f),0)}else{let h=e.height/2,f=t.height/2;r==="start"?(h=0,f=t.height):r==="end"&&(h=e.height,f=0),this.paddingTop=h+o,this.paddingBottom=f+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){wt(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,i;for(e=0,i=t.length;e<i;e++)vt(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=Fh(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length)}return t}_computeLabelSizes(t,e){let{ctx:i,_longestTextCache:s}=this,r=[],o=[],a=0,l=0,c,h,f,g,p,b,x,S,C,T,I;for(c=0;c<e;++c){if(g=t[c].label,p=this._resolveTickFontOptions(c),i.font=b=p.string,x=s[b]=s[b]||{data:{},gc:[]},S=p.lineHeight,C=T=0,!vt(g)&&!gt(g))C=bi(i,x.data,x.gc,C,g),T=S;else if(gt(g))for(h=0,f=g.length;h<f;++h)I=g[h],!vt(I)&&!gt(I)&&(C=bi(i,x.data,x.gc,C,I),T+=S);r.push(C),o.push(T),a=Math.max(C,a),l=Math.max(T,l)}I0(s,e);let A=r.indexOf(a),F=o.indexOf(l),j=N=>({width:r[N]||0,height:o[N]||0});return{first:j(0),last:j(e-1),widest:j(A),highest:j(F),widths:r,heights:o}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return Xo(this._alignToPixels?Qe(this.chart,e,0):e)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){let e=this.ticks||[];if(t>=0&&t<e.length){let i=e[t];return i.$context||(i.$context=j0(this.getContext(),t,i))}return this.$context||(this.$context=$0(this.chart.getContext(),this))}_tickSize(){let t=this.options.ticks,e=Wt(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),o=t.autoSkipPadding||0,a=r?r.widest.width+o:0,l=r?r.highest.height+o:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){let t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e=this.axis,i=this.chart,s=this.options,{grid:r,position:o}=s,a=r.offset,l=this.isHorizontal(),h=this.ticks.length+(a?1:0),f=as(r),g=[],p=r.setContext(this.getContext()),b=p.drawBorder?p.borderWidth:0,x=b/2,S=function(et){return Qe(i,et,b)},C,T,I,A,F,j,N,G,U,nt,lt,rt;if(o==="top")C=S(this.bottom),j=this.bottom-f,G=C-x,nt=S(t.top)+x,rt=t.bottom;else if(o==="bottom")C=S(this.top),nt=t.top,rt=S(t.bottom)-x,j=C+x,G=this.top+f;else if(o==="left")C=S(this.right),F=this.right-f,N=C-x,U=S(t.left)+x,lt=t.right;else if(o==="right")C=S(this.left),U=t.left,lt=S(t.right)-x,F=C+x,N=this.left+f;else if(e==="x"){if(o==="center")C=S((t.top+t.bottom)/2+.5);else if(at(o)){let et=Object.keys(o)[0],Ot=o[et];C=S(this.chart.scales[et].getPixelForValue(Ot))}nt=t.top,rt=t.bottom,j=C+x,G=j+f}else if(e==="y"){if(o==="center")C=S((t.left+t.right)/2);else if(at(o)){let et=Object.keys(o)[0],Ot=o[et];C=S(this.chart.scales[et].getPixelForValue(Ot))}F=C-x,N=F-f,U=t.left,lt=t.right}let Pt=st(s.ticks.maxTicksLimit,h),zt=Math.max(1,Math.ceil(h/Pt));for(T=0;T<h;T+=zt){let et=r.setContext(this.getContext(T)),Ot=et.lineWidth,St=et.color,re=et.borderDash||[],_e=et.borderDashOffset,ot=et.tickWidth,Lt=et.tickColor,Bt=et.tickBorderDash||[],Gt=et.tickBorderDashOffset;I=F0(this,T,a),I!==void 0&&(A=Qe(i,I,Ot),l?F=N=U=lt=A:j=G=nt=rt=A,g.push({tx1:F,ty1:j,tx2:N,ty2:G,x1:U,y1:nt,x2:lt,y2:rt,width:Ot,color:St,borderDash:re,borderDashOffset:_e,tickWidth:ot,tickColor:Lt,tickBorderDash:Bt,tickBorderDashOffset:Gt}))}return this._ticksLength=h,this._borderValue=C,g}_computeLabelItems(t){let e=this.axis,i=this.options,{position:s,ticks:r}=i,o=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:f}=r,g=as(i.grid),p=g+h,b=f?-h:p,x=-Wt(this.labelRotation),S=[],C,T,I,A,F,j,N,G,U,nt,lt,rt,Pt="middle";if(s==="top")j=this.bottom-b,N=this._getXAxisLabelAlignment();else if(s==="bottom")j=this.top+b,N=this._getXAxisLabelAlignment();else if(s==="left"){let et=this._getYAxisLabelAlignment(g);N=et.textAlign,F=et.x}else if(s==="right"){let et=this._getYAxisLabelAlignment(g);N=et.textAlign,F=et.x}else if(e==="x"){if(s==="center")j=(t.top+t.bottom)/2+p;else if(at(s)){let et=Object.keys(s)[0],Ot=s[et];j=this.chart.scales[et].getPixelForValue(Ot)+p}N=this._getXAxisLabelAlignment()}else if(e==="y"){if(s==="center")F=(t.left+t.right)/2-p;else if(at(s)){let et=Object.keys(s)[0],Ot=s[et];F=this.chart.scales[et].getPixelForValue(Ot)}N=this._getYAxisLabelAlignment(g).textAlign}e==="y"&&(l==="start"?Pt="top":l==="end"&&(Pt="bottom"));let zt=this._getLabelSizes();for(C=0,T=a.length;C<T;++C){I=a[C],A=I.label;let et=r.setContext(this.getContext(C));G=this.getPixelForTick(C)+r.labelOffset,U=this._resolveTickFontOptions(C),nt=U.lineHeight,lt=gt(A)?A.length:1;let Ot=lt/2,St=et.color,re=et.textStrokeColor,_e=et.textStrokeWidth,ot=N;o?(F=G,N==="inner"&&(C===T-1?ot=this.options.reverse?"left":"right":C===0?ot=this.options.reverse?"right":"left":ot="center"),s==="top"?c==="near"||x!==0?rt=-lt*nt+nt/2:c==="center"?rt=-zt.highest.height/2-Ot*nt+nt:rt=-zt.highest.height+nt/2:c==="near"||x!==0?rt=nt/2:c==="center"?rt=zt.highest.height/2-Ot*nt:rt=zt.highest.height-lt*nt,f&&(rt*=-1)):(j=G,rt=(1-lt)*nt/2);let Lt;if(et.showLabelBackdrop){let Bt=Vt(et.backdropPadding),Gt=zt.heights[C],Qt=zt.widths[C],k=j+rt-Bt.top,v=F-Bt.left;switch(Pt){case"middle":k-=Gt/2;break;case"bottom":k-=Gt;break}switch(N){case"center":v-=Qt/2;break;case"right":v-=Qt;break}Lt={left:v,top:k,width:Qt+Bt.width,height:Gt+Bt.height,color:et.backdropColor}}S.push({rotation:x,label:A,font:U,color:St,strokeColor:re,strokeWidth:_e,textOffset:rt,textAlign:ot,textBaseline:Pt,translation:[F,j],backdrop:Lt})}return S}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-Wt(this.labelRotation))return t==="top"?"left":"right";let s="center";return e.align==="start"?s="left":e.align==="end"?s="right":e.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){let{position:e,ticks:{crossAlign:i,mirror:s,padding:r}}=this.options,o=this._getLabelSizes(),a=t+r,l=o.widest.width,c,h;return e==="left"?s?(h=this.right+r,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?s?(h=this.left+r,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,o),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let s=this.ticks.findIndex(r=>r.value===t);return s>=0?e.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){let e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),r,o,a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(e.display)for(r=0,o=s.length;r<o;++r){let l=s[r];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:t,ctx:e,options:{grid:i}}=this,s=i.setContext(this.getContext()),r=i.drawBorder?s.borderWidth:0;if(!r)return;let o=i.setContext(this.getContext(0)).lineWidth,a=this._borderValue,l,c,h,f;this.isHorizontal()?(l=Qe(t,this.left,r)-r/2,c=Qe(t,this.right,o)+o/2,h=f=a):(h=Qe(t,this.top,r)-r/2,f=Qe(t,this.bottom,o)+o/2,l=c=a),e.save(),e.lineWidth=s.borderWidth,e.strokeStyle=s.borderColor,e.beginPath(),e.moveTo(l,h),e.lineTo(c,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;let i=this.ctx,s=this._computeLabelArea();s&&yn(i,s);let r=this._labelItems||(this._labelItems=this._computeLabelItems(t)),o,a;for(o=0,a=r.length;o<a;++o){let l=r[o],c=l.font,h=l.label;l.backdrop&&(i.fillStyle=l.backdrop.color,i.fillRect(l.backdrop.left,l.backdrop.top,l.backdrop.width,l.backdrop.height));let f=l.textOffset;tn(i,h,0,f,c,l)}s&&xn(i)}drawTitle(){let{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;let r=jt(i.font),o=Vt(i.padding),a=i.align,l=r.lineHeight/2;e==="bottom"||e==="center"||at(e)?(l+=o.bottom,gt(i.text)&&(l+=r.lineHeight*(i.text.length-1))):l+=o.top;let{titleX:c,titleY:h,maxWidth:f,rotation:g}=B0(this,l,e,a);tn(t,i.text,0,0,r,{color:i.color,maxWidth:f,rotation:g,textAlign:z0(a,e,s),textBaseline:"middle",translation:[c,h]})}draw(t){!this._isVisible()||(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=st(t.grid&&t.grid.z,-1);return!this._isVisible()||this.draw!==Sn.prototype.draw?[{z:e,draw:s=>{this.draw(s)}}]:[{z:i,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i+1,draw:()=>{this.drawBorder()}},{z:e,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(t){let e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[],r,o;for(r=0,o=e.length;r<o;++r){let a=e[r];a[i]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){let e=this.options.ticks.setContext(this.getContext(t));return jt(e.font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}},ls=class{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){let e=Object.getPrototypeOf(t),i;W0(e)&&(i=this.register(e));let s=this.items,r=t.id,o=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+t);return r in s||(s[r]=t,N0(t,o,i),this.override&&ut.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in ut[s]&&(delete ut[s][i],this.override&&delete _n[i])}};function N0(n,t,e){let i=zn(Object.create(null),[e?ut.get(e):{},ut.get(t),n.defaults]);ut.set(t,i),n.defaultRoutes&&H0(t,n.defaultRoutes),n.descriptors&&ut.describe(t,n.descriptors)}function H0(n,t){Object.keys(t).forEach(e=>{let i=e.split("."),s=i.pop(),r=[n].concat(i).join("."),o=t[e].split("."),a=o.pop(),l=o.join(".");ut.route(r,s,l,a)})}function W0(n){return"id"in n&&"defaults"in n}var $h=class{constructor(){this.controllers=new ls(Ce,"datasets",!0),this.elements=new ls(Zt,"elements"),this.plugins=new ls(Object,"plugins"),this.scales=new ls(Sn,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(s=>{let r=i||this._getRegistryForType(s);i||r.isForType(s)||r===this.plugins&&s.id?this._exec(t,r,s):Ct(s,o=>{let a=i||this._getRegistryForType(o);this._exec(t,a,o)})})}_exec(t,e,i){let s=Ni(t);wt(i["before"+s],[],i),e[t](i),wt(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}},He=new $h,cs=class extends Ce{update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:r,count:o}=tr(e,i,s);if(this._drawStart=r,this._drawCount=o,er(e)&&(r=0,o=i.length),this.options.showLine){let{dataset:a,_dataset:l}=e;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;let c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:c},t)}this.updateElements(i,r,o,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=He.getElement("line")),super.addElements()}updateElements(t,e,i,s){let r=s==="reset",{iScale:o,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),f=this.getSharedOptions(h),g=this.includeOptions(s,f),p=o.axis,b=a.axis,{spanGaps:x,segment:S}=this.options,C=Ee(x)?x:Number.POSITIVE_INFINITY,T=this.chart._animationsDisabled||r||s==="none",I=e>0&&this.getParsed(e-1);for(let A=e;A<e+i;++A){let F=t[A],j=this.getParsed(A),N=T?F:{},G=vt(j[b]),U=N[p]=o.getPixelForValue(j[p],A),nt=N[b]=r||G?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,j,l):j[b],A);N.skip=isNaN(U)||isNaN(nt)||G,N.stop=A>0&&Math.abs(j[p]-I[p])>C,S&&(N.parsed=j,N.raw=c.data[A]),g&&(N.options=f||this.resolveDataElementOptions(A,F.active?"active":s)),T||this.updateElement(F,A,N,s),I=j}this.updateSharedOptions(f,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let a=0;for(let l=e.length-1;l>=0;--l)a=Math.max(a,e[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}let i=t.dataset,s=i.options&&i.options.borderWidth||0;if(!e.length)return s;let r=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(s,r,o)/2}};cs.id="scatter";cs.defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};cs.overrides={interaction:{mode:"point"},plugins:{tooltip:{callbacks:{title(){return""},label(n){return"("+n.label+", "+n.formattedValue+")"}}}},scales:{x:{type:"linear"},y:{type:"linear"}}};var V0=Object.freeze({__proto__:null,BarController:ns,BubbleController:is,DoughnutController:Un,LineController:ss,PolarAreaController:rs,PieController:Sr,RadarController:os,ScatterController:cs});function Kn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var Pr=class{constructor(t){this.options=t||{}}init(t){}formats(){return Kn()}parse(t,e){return Kn()}format(t,e){return Kn()}add(t,e,i){return Kn()}diff(t,e,i){return Kn()}startOf(t,e,i){return Kn()}endOf(t,e){return Kn()}};Pr.override=function(n){Object.assign(Pr.prototype,n)};var xa={_date:Pr};function Y0(n,t,e,i){let{controller:s,data:r,_sorted:o}=n,a=s._cachedMeta.iScale;if(a&&t===a.axis&&t!=="r"&&o&&r.length){let l=a._reversePixels?qo:Re;if(i){if(s._sharedOptions){let c=r[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){let f=l(r,t,e-h),g=l(r,t,e+h);return{lo:f.lo,hi:g.hi}}}}else return l(r,t,e)}return{lo:0,hi:r.length-1}}function hs(n,t,e,i,s){let r=n.getSortedVisibleDatasetMetas(),o=e[t];for(let a=0,l=r.length;a<l;++a){let{index:c,data:h}=r[a],{lo:f,hi:g}=Y0(r[a],t,o,s);for(let p=f;p<=g;++p){let b=h[p];b.skip||i(b,c,p)}}}function X0(n){let t=n.indexOf("x")!==-1,e=n.indexOf("y")!==-1;return function(i,s){let r=t?Math.abs(i.x-s.x):0,o=e?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(o,2))}}function wa(n,t,e,i,s){let r=[];return!s&&!n.isPointInArea(t)||hs(n,e,t,function(a,l,c){!s&&!Yn(a,n.chartArea,0)||a.inRange(t.x,t.y,i)&&r.push({element:a,datasetIndex:l,index:c})},!0),r}function q0(n,t,e,i){let s=[];function r(o,a,l){let{startAngle:c,endAngle:h}=o.getProps(["startAngle","endAngle"],i),{angle:f}=Us(o,{x:t.x,y:t.y});Wn(f,c,h)&&s.push({element:o,datasetIndex:a,index:l})}return hs(n,e,t,r),s}function G0(n,t,e,i,s,r){let o=[],a=X0(e),l=Number.POSITIVE_INFINITY;function c(h,f,g){let p=h.inRange(t.x,t.y,s);if(i&&!p)return;let b=h.getCenterPoint(s);if(!(!!r||n.isPointInArea(b))&&!p)return;let S=a(t,b);S<l?(o=[{element:h,datasetIndex:f,index:g}],l=S):S===l&&o.push({element:h,datasetIndex:f,index:g})}return hs(n,e,t,c),o}function ka(n,t,e,i,s,r){return!r&&!n.isPointInArea(t)?[]:e==="r"&&!i?q0(n,t,e,s):G0(n,t,e,i,s,r)}function jh(n,t,e,i,s){let r=[],o=e==="x"?"inXRange":"inYRange",a=!1;return hs(n,e,t,(l,c,h)=>{l[o](t[e],s)&&(r.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,s))}),i&&!a?[]:r}var U0={evaluateInteractionItems:hs,modes:{index(n,t,e,i){let s=nn(t,n),r=e.axis||"x",o=e.includeInvisible||!1,a=e.intersect?wa(n,s,r,i,o):ka(n,s,r,!1,i,o),l=[];return a.length?(n.getSortedVisibleDatasetMetas().forEach(c=>{let h=a[0].index,f=c.data[h];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:h})}),l):[]},dataset(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1,a=e.intersect?wa(n,s,r,i,o):ka(n,s,r,!1,i,o);if(a.length>0){let l=a[0].datasetIndex,c=n.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return wa(n,s,r,i,o)},nearest(n,t,e,i){let s=nn(t,n),r=e.axis||"xy",o=e.includeInvisible||!1;return ka(n,s,r,e.intersect,i,o)},x(n,t,e,i){let s=nn(t,n);return jh(n,s,"x",e.intersect,i)},y(n,t,e,i){let s=nn(t,n);return jh(n,s,"y",e.intersect,i)}}},zh=["left","top","right","bottom"];function us(n,t){return n.filter(e=>e.pos===t)}function Bh(n,t){return n.filter(e=>zh.indexOf(e.pos)===-1&&e.box.axis===t)}function fs(n,t){return n.sort((e,i)=>{let s=t?i:e,r=t?e:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function K0(n){let t=[],e,i,s,r,o,a;for(e=0,i=(n||[]).length;e<i;++e)s=n[e],{position:r,options:{stack:o,stackWeight:a=1}}=s,t.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:o&&r+o,stackWeight:a});return t}function Z0(n){let t={};for(let e of n){let{stack:i,pos:s,stackWeight:r}=e;if(!i||!zh.includes(s))continue;let o=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=r}return t}function J0(n,t){let e=Z0(n),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t,r,o,a;for(r=0,o=n.length;r<o;++r){a=n[r];let{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&t.availableWidth,a.height=s):(a.width=i,a.height=h?h*s:l&&t.availableHeight)}return e}function Q0(n){let t=K0(n),e=fs(t.filter(c=>c.box.fullSize),!0),i=fs(us(t,"left"),!0),s=fs(us(t,"right")),r=fs(us(t,"top"),!0),o=fs(us(t,"bottom")),a=Bh(t,"x"),l=Bh(t,"y");return{fullSize:e,leftAndTop:i.concat(r),rightAndBottom:s.concat(l).concat(o).concat(a),chartArea:us(t,"chartArea"),vertical:i.concat(s).concat(l),horizontal:r.concat(o).concat(a)}}function Nh(n,t,e,i){return Math.max(n[e],t[e])+Math.max(n[i],t[i])}function Hh(n,t){n.top=Math.max(n.top,t.top),n.left=Math.max(n.left,t.left),n.bottom=Math.max(n.bottom,t.bottom),n.right=Math.max(n.right,t.right)}function tv(n,t,e,i){let{pos:s,box:r}=e,o=n.maxPadding;if(!at(s)){e.size&&(n[s]-=e.size);let f=i[e.stack]||{size:0,count:1};f.size=Math.max(f.size,e.horizontal?r.height:r.width),e.size=f.size/f.count,n[s]+=e.size}r.getPadding&&Hh(o,r.getPadding());let a=Math.max(0,t.outerWidth-Nh(o,n,"left","right")),l=Math.max(0,t.outerHeight-Nh(o,n,"top","bottom")),c=a!==n.w,h=l!==n.h;return n.w=a,n.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function ev(n){let t=n.maxPadding;function e(i){let s=Math.max(t[i]-n[i],0);return n[i]+=s,s}n.y+=e("top"),n.x+=e("left"),e("right"),e("bottom")}function nv(n,t){let e=t.maxPadding;function i(s){let r={left:0,top:0,right:0,bottom:0};return s.forEach(o=>{r[o]=Math.max(t[o],e[o])}),r}return i(n?["left","right"]:["top","bottom"])}function ds(n,t,e,i){let s=[],r,o,a,l,c,h;for(r=0,o=n.length,c=0;r<o;++r){a=n[r],l=a.box,l.update(a.width||t.w,a.height||t.h,nv(a.horizontal,t));let{same:f,other:g}=tv(t,e,a,i);c|=f&&s.length,h=h||g,l.fullSize||s.push(a)}return c&&ds(s,t,e,i)||h}function Tr(n,t,e,i,s){n.top=e,n.left=t,n.right=t+i,n.bottom=e+s,n.width=i,n.height=s}function Wh(n,t,e,i){let s=e.padding,{x:r,y:o}=t;for(let a of n){let l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){let f=t.w*h,g=c.size||l.height;Nt(c.start)&&(o=c.start),l.fullSize?Tr(l,s.left,o,e.outerWidth-s.right-s.left,g):Tr(l,t.left+c.placed,o,f,g),c.start=o,c.placed+=f,o=l.bottom}else{let f=t.h*h,g=c.size||l.width;Nt(c.start)&&(r=c.start),l.fullSize?Tr(l,r,s.top,g,e.outerHeight-s.bottom-s.top):Tr(l,r,t.top+c.placed,g,f),c.start=r,c.placed+=f,r=l.right}}t.x=r,t.y=o}ut.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}});var he={addBox(n,t){n.boxes||(n.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},n.boxes.push(t)},removeBox(n,t){let e=n.boxes?n.boxes.indexOf(t):-1;e!==-1&&n.boxes.splice(e,1)},configure(n,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(n,t,e,i){if(!n)return;let s=Vt(n.options.layout.padding),r=Math.max(t-s.width,0),o=Math.max(e-s.height,0),a=Q0(n.boxes),l=a.vertical,c=a.horizontal;Ct(n.boxes,x=>{typeof x.beforeLayout=="function"&&x.beforeLayout()});let h=l.reduce((x,S)=>S.box.options&&S.box.options.display===!1?x:x+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:e,padding:s,availableWidth:r,availableHeight:o,vBoxMaxWidth:r/2/h,hBoxMaxHeight:o/2}),g=Object.assign({},s);Hh(g,Vt(i));let p=Object.assign({maxPadding:g,w:r,h:o,x:s.left,y:s.top},s),b=J0(l.concat(c),f);ds(a.fullSize,p,f,b),ds(l,p,f,b),ds(c,p,f,b)&&ds(l,p,f,b),ev(p),Wh(a.leftAndTop,p,f,b),p.x+=p.w,p.y+=p.h,Wh(a.rightAndBottom,p,f,b),n.chartArea={left:p.left,top:p.top,right:p.left+p.w,bottom:p.top+p.h,height:p.h,width:p.w},Ct(a.chartArea,x=>{let S=x.box;Object.assign(S,n.chartArea),S.update(p.w,p.h,{left:0,top:0,right:0,bottom:0})})}},Sa=class{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}},Vh=class extends Sa{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}},Dr="$chartjs",iv={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Yh=n=>n===null||n==="";function sv(n,t){let e=n.style,i=n.getAttribute("height"),s=n.getAttribute("width");if(n[Dr]={initial:{height:i,width:s,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Yh(s)){let r=_r(n,"width");r!==void 0&&(n.width=r)}if(Yh(i))if(n.style.height==="")n.height=n.width/(t||2);else{let r=_r(n,"height");r!==void 0&&(n.height=r)}return n}var Xh=pa?{passive:!0}:!1;function rv(n,t,e){n.addEventListener(t,e,Xh)}function ov(n,t,e){n.canvas.removeEventListener(t,e,Xh)}function av(n,t){let e=iv[n.type]||n.type,{x:i,y:s}=nn(n,t);return{type:e,chart:t,native:n,x:i!==void 0?i:null,y:s!==void 0?s:null}}function Or(n,t){for(let e of n)if(e===t||e.contains(t))return!0}function lv(n,t,e){let i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(let a of r)o=o||Or(a.addedNodes,i),o=o&&!Or(a.removedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}function cv(n,t,e){let i=n.canvas,s=new MutationObserver(r=>{let o=!1;for(let a of r)o=o||Or(a.removedNodes,i),o=o&&!Or(a.addedNodes,i);o&&e()});return s.observe(document,{childList:!0,subtree:!0}),s}var ps=new Map,qh=0;function Gh(){let n=window.devicePixelRatio;n!==qh&&(qh=n,ps.forEach((t,e)=>{e.currentDevicePixelRatio!==n&&t()}))}function hv(n,t){ps.size||window.addEventListener("resize",Gh),ps.set(n,t)}function uv(n){ps.delete(n),ps.size||window.removeEventListener("resize",Gh)}function fv(n,t,e){let i=n.canvas,s=i&&Qi(i);if(!s)return;let r=Qs((a,l)=>{let c=s.clientWidth;e(a,l),c<s.clientWidth&&e()},window),o=new ResizeObserver(a=>{let l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||r(c,h)});return o.observe(s),hv(n,r),o}function Ma(n,t,e){e&&e.disconnect(),t==="resize"&&uv(n)}function dv(n,t,e){let i=n.canvas,s=Qs(r=>{n.ctx!==null&&e(av(r,n))},n,r=>{let o=r[0];return[o,o.offsetX,o.offsetY]});return rv(i,t,s),s}var Uh=class extends Sa{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(sv(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[Dr])return!1;let i=e[Dr].initial;["height","width"].forEach(r=>{let o=i[r];vt(o)?e.removeAttribute(r):e.setAttribute(r,o)});let s=i.style||{};return Object.keys(s).forEach(r=>{e.style[r]=s[r]}),e.width=e.width,delete e[Dr],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),o={attach:lv,detach:cv,resize:fv}[e]||dv;s[e]=o(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:Ma,detach:Ma,resize:Ma}[e]||ov)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return da(t,e,i,s)}isAttached(t){let e=Qi(t);return!!(e&&e.isConnected)}};function pv(n){return!gr()||typeof OffscreenCanvas!="undefined"&&n instanceof OffscreenCanvas?Vh:Uh}var Kh=class{constructor(){this._init=[]}notify(t,e,i,s){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let r=s?this._descriptors(t).filter(s):this._descriptors(t),o=this._notify(r,t,e,i);return e==="afterDestroy"&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,s){s=s||{};for(let r of t){let o=r.plugin,a=o[i],l=[e,s,r.options];if(wt(a,l,o)===!1&&s.cancelable)return!1}return!0}invalidate(){vt(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=st(i.options&&i.options.plugins,{}),r=gv(i);return s===!1&&!e?[]:bv(t,r,s,e)}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(r,o)=>r.filter(a=>!o.some(l=>a.plugin.id===l.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}};function gv(n){let t={},e=[],i=Object.keys(He.plugins.items);for(let r=0;r<i.length;r++)e.push(He.getPlugin(i[r]));let s=n.plugins||[];for(let r=0;r<s.length;r++){let o=s[r];e.indexOf(o)===-1&&(e.push(o),t[o.id]=!0)}return{plugins:e,localIds:t}}function mv(n,t){return!t&&n===!1?null:n===!0?{}:n}function bv(n,{plugins:t,localIds:e},i,s){let r=[],o=n.getContext();for(let a of t){let l=a.id,c=mv(i[l],s);c!==null&&r.push({plugin:a,options:vv(n.config,{plugin:a,local:e[l]},c,o)})}return r}function vv(n,{plugin:t,local:e},i,s){let r=n.pluginScopeKeys(t),o=n.getOptionScopes(i,r);return e&&t.defaults&&o.push(t.defaults),n.createResolver(o,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ca(n,t){let e=ut.datasets[n]||{};return((t.datasets||{})[n]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function _v(n,t){let e=n;return n==="_index_"?e=t:n==="_value_"&&(e=t==="x"?"y":"x"),e}function yv(n,t){return n===t?"_index_":"_value_"}function xv(n){if(n==="top"||n==="bottom")return"x";if(n==="left"||n==="right")return"y"}function Pa(n,t){return n==="x"||n==="y"?n:t.axis||xv(t.position)||n.charAt(0).toLowerCase()}function wv(n,t){let e=_n[n.type]||{scales:{}},i=t.scales||{},s=Ca(n.type,t),r=Object.create(null),o=Object.create(null);return Object.keys(i).forEach(a=>{let l=i[a];if(!at(l))return console.error(`Invalid scale configuration for scale: ${a}`);if(l._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);let c=Pa(a,l),h=yv(c,s),f=e.scales||{};r[c]=r[c]||a,o[a]=Bn(Object.create(null),[{axis:c},l,f[c],f[h]])}),n.data.datasets.forEach(a=>{let l=a.type||n.type,c=a.indexAxis||Ca(l,t),f=(_n[l]||{}).scales||{};Object.keys(f).forEach(g=>{let p=_v(g,c),b=a[p+"AxisID"]||r[p]||p;o[b]=o[b]||Object.create(null),Bn(o[b],[{axis:p},i[b],f[g]])})}),Object.keys(o).forEach(a=>{let l=o[a];Bn(l,[ut.scales[l.type],ut.scale])}),o}function Zh(n){let t=n.options||(n.options={});t.plugins=st(t.plugins,{}),t.scales=wv(n,t)}function Jh(n){return n=n||{},n.datasets=n.datasets||[],n.labels=n.labels||[],n}function kv(n){return n=n||{},n.data=Jh(n.data),Zh(n),n}var Qh=new Map,tu=new Set;function Er(n,t){let e=Qh.get(n);return e||(e=t(),Qh.set(n,e),tu.add(e)),e}var gs=(n,t,e)=>{let i=Be(t,e);i!==void 0&&n.add(i)},eu=class{constructor(t){this._config=kv(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Jh(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),Zh(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Er(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Er(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Er(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return Er(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:r}=this,o=this._cachedScopes(t,i),a=o.get(e);if(a)return a;let l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(f=>gs(l,t,f))),h.forEach(f=>gs(l,s,f)),h.forEach(f=>gs(l,_n[r]||{},f)),h.forEach(f=>gs(l,ut,f)),h.forEach(f=>gs(l,lr,f))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),tu.has(e)&&o.set(e,c),c}chartOptionScopes(){let{options:t,type:e}=this;return[t,_n[e]||{},ut.datasets[e]||{},{type:e},ut,lr]}resolveNamedOptions(t,e,i,s=[""]){let r={$shared:!0},{resolver:o,subPrefixes:a}=nu(this._resolverCache,t,s),l=o;if(Mv(o,e)){r.$shared=!1,i=ce(i)?i():i;let c=this.createResolver(t,i,a);l=wn(o,i,c)}for(let c of e)r[c]=l[c];return r}createResolver(t,e,i=[""],s){let{resolver:r}=nu(this._resolverCache,t,i);return at(e)?wn(r,e,void 0,s):r}};function nu(n,t,e){let i=n.get(t);i||(i=new Map,n.set(t,i));let s=e.join(),r=i.get(s);return r||(r={resolver:Ji(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,r)),r}var Sv=n=>at(n)&&Object.getOwnPropertyNames(n).reduce((t,e)=>t||ce(n[e]),!1);function Mv(n,t){let{isScriptable:e,isIndexable:i}=fr(n);for(let s of t){let r=e(s),o=i(s),a=(o||r)&&n[s];if(r&&(ce(a)||Sv(a))||o&&gt(a))return!0}return!1}var Cv="3.9.1",Pv=["top","bottom","left","right","chartArea"];function iu(n,t){return n==="top"||n==="bottom"||Pv.indexOf(n)===-1&&t==="x"}function su(n,t){return function(e,i){return e[n]===i[n]?e[t]-i[t]:e[n]-i[n]}}function ru(n){let t=n.chart,e=t.options.animation;t.notifyPlugins("afterRender"),wt(e&&e.onComplete,[n],t)}function Tv(n){let t=n.chart,e=t.options.animation;wt(e&&e.onProgress,[n],t)}function ou(n){return gr()&&typeof n=="string"?n=document.getElementById(n):n&&n.length&&(n=n[0]),n&&n.canvas&&(n=n.canvas),n}var Ar={},au=n=>{let t=ou(n);return Object.values(Ar).filter(e=>e.canvas===t).pop()};function Dv(n,t,e){let i=Object.keys(n);for(let s of i){let r=+s;if(r>=t){let o=n[s];delete n[s],(e>0||r>t)&&(n[r+e]=o)}}}function Ov(n,t,e,i){return!e||n.type==="mouseout"?null:i?t:n}var se=class{constructor(t,e){let i=this.config=new eu(e),s=ou(t),r=au(s);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||pv(s)),this.platform.updateConfig(i);let a=this.platform.acquireContext(s,o.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=No(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Kh,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Ko(f=>this.update(f),o.resizeDelay||0),this._dataChanges=[],Ar[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}rn.listen(this,"complete",ru),rn.listen(this,"progress",Tv),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return vt(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():vr(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return cr(this.canvas,this.ctx),this}stop(){return rn.stop(this),this}resize(t,e){rn.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,t,e,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,!!vr(this,a,!0)&&(this.notifyPlugins("resize",{size:o}),wt(i.onResize,[this,o],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){let e=this.options.scales||{};Ct(e,(i,s)=>{i.id=s})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((o,a)=>(o[a]=!1,o),{}),r=[];e&&(r=r.concat(Object.keys(e).map(o=>{let a=e[o],l=Pa(o,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),Ct(r,o=>{let a=o.options,l=a.id,c=Pa(l,a),h=st(a.type,o.dtype);(a.position===void 0||iu(a.position,c)!==iu(o.dposition))&&(a.position=o.dposition),s[l]=!0;let f=null;if(l in i&&i[l].type===h)f=i[l];else{let g=He.getScale(h);f=new g({id:l,type:h,ctx:this.ctx,chart:this}),i[f.id]=f}f.init(a,t)}),Ct(s,(o,a)=>{o||delete i[a]}),Ct(i,o=>{he.configure(this,o,o.options),he.addBox(this,o)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((s,r)=>s.index-r.index),i>e){for(let s=e;s<i;++s)this._destroyDatasetMeta(s);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(su("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((i,s)=>{e.filter(r=>r===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){let t=[],e=this.data.datasets,i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){let r=e[i],o=this.getDatasetMeta(i),a=r.type||this.config.type;if(o.type&&o.type!==a&&(this._destroyDatasetMeta(i),o=this.getDatasetMeta(i)),o.type=a,o.indexAxis=r.indexAxis||Ca(a,this.options),o.order=r.order||0,o.index=i,o.label=""+r.label,o.visible=this.isDatasetVisible(i),o.controller)o.controller.updateIndex(i),o.controller.linkScales();else{let l=He.getController(a),{datasetElementType:c,dataElementType:h}=ut.datasets[a];Object.assign(l.prototype,{dataElementType:He.getElement(h),datasetElementType:c&&He.getElement(c)}),o.controller=new l(this,i),t.push(o.controller)}}return this._updateMetasets(),t}_resetElements(){Ct(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let c=0,h=this.data.datasets.length;c<h;c++){let{controller:f}=this.getDatasetMeta(c),g=!s&&r.indexOf(f)===-1;f.buildOrUpdateElements(g),o=Math.max(+f.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||Ct(r,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(su("z","_idx"));let{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){Ct(this.scales,t=>{he.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!Ys(e,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(let{method:i,start:s,count:r}of e){let o=i==="_removeElements"?-r:r;Dv(t,s,o)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=r=>new Set(t.filter(o=>o[0]===r).map((o,a)=>a+","+o.splice(1).join(","))),s=i(0);for(let r=1;r<e;r++)if(!Ys(s,i(r)))return;return Array.from(s).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;he.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],Ct(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,r)=>{s._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,i=this.data.datasets.length;e<i;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,ce(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(rn.has(this)?this.attached&&!rn.running(this)&&rn.start(this):(this.draw(),ru({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:i,height:s}=this._resizeBeforeDraw;this._resize(i,s),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e=this._sortedMetasets,i=[],s,r;for(s=0,r=e.length;s<r;++s){let o=e[s];(!t||o.visible)&&i.push(o)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i=t._clip,s=!i.disabled,r=this.chartArea,o={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",o)!==!1&&(s&&yn(e,{left:i.left===!1?0:r.left-i.left,right:i.right===!1?this.width:r.right+i.right,top:i.top===!1?0:r.top-i.top,bottom:i.bottom===!1?this.height:r.bottom+i.bottom}),t.controller.draw(),s&&xn(e),o.cancelable=!1,this.notifyPlugins("afterDatasetDraw",o))}isPointInArea(t){return Yn(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let r=U0.modes[e];return typeof r=="function"?r(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(r=>r&&r._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=Ne(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!e.hidden}setDatasetVisibility(t,e){let i=this.getDatasetMeta(t);i.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",r=this.getDatasetMeta(t),o=r.controller._resolveAnimations(void 0,s);Nt(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(r,{visible:i}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),rn.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),cr(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),this.notifyPlugins("destroy"),delete Ar[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(r,o)=>{e.addEventListener(this,r,o),t[r]=o},s=(r,o,a)=>{r.offsetX=o,r.offsetY=a,this._eventHandler(r)};Ct(this.options.events,r=>i(r,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,e=this.platform,i=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},r=(l,c)=>{this.canvas&&this.resize(l,c)},o,a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",r),i("detach",o)};o=()=>{this.attached=!1,s("resize",r),this._stop(),this._resize(0,0),i("attach",a)},e.isAttached(this.canvas)?a():o()}unbindEvents(){Ct(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},Ct(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s=i?"set":"remove",r,o,a,l;for(e==="dataset"&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){o=t[a];let c=o&&this.getDatasetMeta(o.datasetIndex).controller;c&&c[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:r,index:o})=>{let a=this.getDatasetMeta(r);if(!a)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:a.data[o],index:o}});!pi(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}_updateHoverStyles(t,e,i){let s=this.options.hover,r=(l,c)=>l.filter(h=>!c.some(f=>h.datasetIndex===f.datasetIndex&&h.index===f.index)),o=r(e,t),a=i?t:r(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=o=>(o.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;let r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){let{_active:s=[],options:r}=this,o=e,a=this._getActiveElements(t,s,i,o),l=Wo(t),c=Ov(t,this._lastEvent,i,l);i&&(this._lastEvent=null,wt(r.onHover,[t,a,this],this),l&&wt(r.onClick,[t,a,this],this));let h=!pi(a,s);return(h||e)&&(this._active=a,this._updateHoverStyles(a,s,e)),this._lastEvent=c,h}_getActiveElements(t,e,i,s){if(t.type==="mouseout")return[];if(!i)return e;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}},lu=()=>Ct(se.instances,n=>n._plugins.invalidate()),Mn=!0;Object.defineProperties(se,{defaults:{enumerable:Mn,value:ut},instances:{enumerable:Mn,value:Ar},overrides:{enumerable:Mn,value:_n},registry:{enumerable:Mn,value:He},version:{enumerable:Mn,value:Cv},getChart:{enumerable:Mn,value:au},register:{enumerable:Mn,value:(...n)=>{He.add(...n),lu()}},unregister:{enumerable:Mn,value:(...n)=>{He.remove(...n),lu()}}});function cu(n,t,e){let{startAngle:i,pixelMargin:s,x:r,y:o,outerRadius:a,innerRadius:l}=t,c=s/a;n.beginPath(),n.arc(r,o,a,i-c,e+c),l>s?(c=s/l,n.arc(r,o,l,e+c,i-c,!0)):n.arc(r,o,s,e+Rt,i-Rt),n.closePath(),n.clip()}function Ev(n){return Zi(n,["outerStart","outerEnd","innerStart","innerEnd"])}function Av(n,t,e,i){let s=Ev(n.options.borderRadius),r=(e-t)/2,o=Math.min(r,i*t/2),a=l=>{let c=(e-Math.min(r,l))*i/2;return Kt(l,0,Math.min(r,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:Kt(s.innerStart,0,o),innerEnd:Kt(s.innerEnd,0,o)}}function _i(n,t,e,i){return{x:e+n*Math.cos(t),y:i+n*Math.sin(t)}}function Ta(n,t,e,i,s,r){let{x:o,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,f=Math.max(t.outerRadius+i+e-c,0),g=h>0?h+i+e+c:0,p=0,b=s-l;if(i){let et=h>0?h-i:0,Ot=f>0?f-i:0,St=(et+Ot)/2,re=St!==0?b*St/(St+i):b;p=(b-re)/2}let x=Math.max(.001,b*f-e/dt)/f,S=(b-x)/2,C=l+S+p,T=s-S-p,{outerStart:I,outerEnd:A,innerStart:F,innerEnd:j}=Av(t,g,f,T-C),N=f-I,G=f-A,U=C+I/N,nt=T-A/G,lt=g+F,rt=g+j,Pt=C+F/lt,zt=T-j/rt;if(n.beginPath(),r){if(n.arc(o,a,f,U,nt),A>0){let St=_i(G,nt,o,a);n.arc(St.x,St.y,A,nt,T+Rt)}let et=_i(rt,T,o,a);if(n.lineTo(et.x,et.y),j>0){let St=_i(rt,zt,o,a);n.arc(St.x,St.y,j,T+Rt,zt+Math.PI)}if(n.arc(o,a,g,T-j/g,C+F/g,!0),F>0){let St=_i(lt,Pt,o,a);n.arc(St.x,St.y,F,Pt+Math.PI,C-Rt)}let Ot=_i(N,C,o,a);if(n.lineTo(Ot.x,Ot.y),I>0){let St=_i(N,U,o,a);n.arc(St.x,St.y,I,C-Rt,U)}}else{n.moveTo(o,a);let et=Math.cos(U)*f+o,Ot=Math.sin(U)*f+a;n.lineTo(et,Ot);let St=Math.cos(nt)*f+o,re=Math.sin(nt)*f+a;n.lineTo(St,re)}n.closePath()}function Rv(n,t,e,i,s){let{fullCircles:r,startAngle:o,circumference:a}=t,l=t.endAngle;if(r){Ta(n,t,e,i,o+kt,s);for(let c=0;c<r;++c)n.fill();isNaN(a)||(l=o+a%kt,a%kt==0&&(l+=kt))}return Ta(n,t,e,i,l,s),n.fill(),l}function Lv(n,t,e){let{x:i,y:s,startAngle:r,pixelMargin:o,fullCircles:a}=t,l=Math.max(t.outerRadius-o,0),c=t.innerRadius+o,h;for(e&&cu(n,t,r+kt),n.beginPath(),n.arc(i,s,c,r+kt,r,!0),h=0;h<a;++h)n.stroke();for(n.beginPath(),n.arc(i,s,l,r,r+kt),h=0;h<a;++h)n.stroke()}function Fv(n,t,e,i,s,r){let{options:o}=t,{borderWidth:a,borderJoinStyle:l}=o,c=o.borderAlign==="inner";!a||(c?(n.lineWidth=a*2,n.lineJoin=l||"round"):(n.lineWidth=a,n.lineJoin=l||"bevel"),t.fullCircles&&Lv(n,t,c),c&&cu(n,t,s),Ta(n,t,e,i,s,r),n.stroke())}var ms=class extends Zt{constructor(t){super();this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.getProps(["x","y"],i),{angle:r,distance:o}=Us(s,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),g=this.options.spacing/2,b=st(f,l-a)>=kt||Wn(r,a,l),x=Ae(o,c+g,h+g);return b&&x}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:r,innerRadius:o,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius","circumference"],t),{offset:l,spacing:c}=this.options,h=(s+r)/2,f=(o+a+c+l)/2;return{x:e+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/2,r=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=i>kt?Math.floor(i/kt):0,i===0||this.innerRadius<0||this.outerRadius<0)return;t.save();let a=0;if(s){a=s/2;let c=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(c)*a,Math.sin(c)*a),this.circumference>=dt&&(a=s)}t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor;let l=Rv(t,this,a,r,o);Fv(t,this,a,r,l,o),t.restore()}};ms.id="arc";ms.defaults={borderAlign:"center",borderColor:"#fff",borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};ms.defaultRoutes={backgroundColor:"backgroundColor"};function hu(n,t,e=t){n.lineCap=st(e.borderCapStyle,t.borderCapStyle),n.setLineDash(st(e.borderDash,t.borderDash)),n.lineDashOffset=st(e.borderDashOffset,t.borderDashOffset),n.lineJoin=st(e.borderJoinStyle,t.borderJoinStyle),n.lineWidth=st(e.borderWidth,t.borderWidth),n.strokeStyle=st(e.borderColor,t.borderColor)}function Iv(n,t,e){n.lineTo(e.x,e.y)}function $v(n){return n.stepped?oa:n.tension||n.cubicInterpolationMode==="monotone"?aa:Iv}function uu(n,t,e={}){let i=n.length,{start:s=0,end:r=i-1}=e,{start:o,end:a}=t,l=Math.max(s,o),c=Math.min(r,a),h=s<o&&r<o||s>a&&r>a;return{count:i,start:l,loop:t.loop,ilen:c<l&&!h?i+c-l:c-l}}function jv(n,t,e,i){let{points:s,options:r}=t,{count:o,start:a,loop:l,ilen:c}=uu(s,e,i),h=$v(r),{move:f=!0,reverse:g}=i||{},p,b,x;for(p=0;p<=c;++p)b=s[(a+(g?c-p:p))%o],!b.skip&&(f?(n.moveTo(b.x,b.y),f=!1):h(n,x,b,g,r.stepped),x=b);return l&&(b=s[(a+(g?c:0))%o],h(n,x,b,g,r.stepped)),!!l}function zv(n,t,e,i){let s=t.points,{count:r,start:o,ilen:a}=uu(s,e,i),{move:l=!0,reverse:c}=i||{},h=0,f=0,g,p,b,x,S,C,T=A=>(o+(c?a-A:A))%r,I=()=>{x!==S&&(n.lineTo(h,S),n.lineTo(h,x),n.lineTo(h,C))};for(l&&(p=s[T(0)],n.moveTo(p.x,p.y)),g=0;g<=a;++g){if(p=s[T(g)],p.skip)continue;let A=p.x,F=p.y,j=A|0;j===b?(F<x?x=F:F>S&&(S=F),h=(f*h+A)/++f):(I(),n.lineTo(A,F),b=j,f=0,x=S=F),C=F}I()}function Da(n){let t=n.options,e=t.borderDash&&t.borderDash.length;return!n._decimated&&!n._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?zv:jv}function Bv(n){return n.stepped?ga:n.tension||n.cubicInterpolationMode==="monotone"?ma:sn}function Nv(n,t,e,i){let s=t._path;s||(s=t._path=new Path2D,t.path(s,e,i)&&s.closePath()),hu(n,t.options),n.stroke(s)}function Hv(n,t,e,i){let{segments:s,options:r}=t,o=Da(t);for(let a of s)hu(n,r,a.style),n.beginPath(),o(n,t,a,{start:e,end:e+i-1})&&n.closePath(),n.stroke()}var Wv=typeof Path2D=="function";function Vv(n,t,e,i){Wv&&!t.options.segment?Nv(n,t,e,i):Hv(n,t,e,i)}var on=class extends Zt{constructor(t){super();this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;ua(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=ba(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i=this.options,s=t[e],r=this.points,o=kr(this,{property:e,start:s,end:s});if(!o.length)return;let a=[],l=Bv(i),c,h;for(c=0,h=o.length;c<h;++c){let{start:f,end:g}=o[c],p=r[f],b=r[g];if(p===b){a.push(p);continue}let x=Math.abs((s-p[e])/(b[e]-p[e])),S=l(p,b,x,i.stepped);S[e]=t[e],a.push(S)}return a.length===1?a[0]:a}pathSegment(t,e,i){return Da(this)(t,this,e,i)}path(t,e,i){let s=this.segments,r=Da(this),o=this._loop;e=e||0,i=i||this.points.length-e;for(let a of s)o&=r(t,this,a,{start:e,end:e+i-1});return!!o}draw(t,e,i,s){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),Vv(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}};on.id="line";on.defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};on.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};on.descriptors={_scriptable:!0,_indexable:n=>n!=="borderDash"&&n!=="fill"};function fu(n,t,e,i){let s=n.options,{[e]:r}=n.getProps([e],i);return Math.abs(t-r)<s.radius+s.hitRadius}var bs=class extends Zt{constructor(t){super();this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:r,y:o}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return fu(this,t,"x",e)}inYRange(t,e){return fu(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);let i=e&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;this.skip||i.radius<.1||!Yn(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,Ki(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}};bs.id="point";bs.defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};bs.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};function du(n,t){let{x:e,y:i,base:s,width:r,height:o}=n.getProps(["x","y","base","width","height"],t),a,l,c,h,f;return n.horizontal?(f=o/2,a=Math.min(e,s),l=Math.max(e,s),c=i-f,h=i+f):(f=r/2,a=e-f,l=e+f,c=Math.min(i,s),h=Math.max(i,s)),{left:a,top:c,right:l,bottom:h}}function Cn(n,t,e,i){return n?0:Kt(t,e,i)}function Yv(n,t,e){let i=n.options.borderWidth,s=n.borderSkipped,r=ur(i);return{t:Cn(s.top,r.top,0,e),r:Cn(s.right,r.right,0,t),b:Cn(s.bottom,r.bottom,0,e),l:Cn(s.left,r.left,0,t)}}function Xv(n,t,e){let{enableBorderRadius:i}=n.getProps(["enableBorderRadius"]),s=n.options.borderRadius,r=Le(s),o=Math.min(t,e),a=n.borderSkipped,l=i||at(s);return{topLeft:Cn(!l||a.top||a.left,r.topLeft,0,o),topRight:Cn(!l||a.top||a.right,r.topRight,0,o),bottomLeft:Cn(!l||a.bottom||a.left,r.bottomLeft,0,o),bottomRight:Cn(!l||a.bottom||a.right,r.bottomRight,0,o)}}function qv(n){let t=du(n),e=t.right-t.left,i=t.bottom-t.top,s=Yv(n,e/2,i/2),r=Xv(n,e/2,i/2);return{outer:{x:t.left,y:t.top,w:e,h:i,radius:r},inner:{x:t.left+s.l,y:t.top+s.t,w:e-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,r.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,r.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,r.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,r.bottomRight-Math.max(s.b,s.r))}}}}function Oa(n,t,e,i){let s=t===null,r=e===null,a=n&&!(s&&r)&&du(n,i);return a&&(s||Ae(t,a.left,a.right))&&(r||Ae(e,a.top,a.bottom))}function Gv(n){return n.topLeft||n.topRight||n.bottomLeft||n.bottomRight}function Uv(n,t){n.rect(t.x,t.y,t.w,t.h)}function Ea(n,t,e={}){let i=n.x!==e.x?-t:0,s=n.y!==e.y?-t:0,r=(n.x+n.w!==e.x+e.w?t:0)-i,o=(n.y+n.h!==e.y+e.h?t:0)-s;return{x:n.x+i,y:n.y+s,w:n.w+r,h:n.h+o,radius:n.radius}}var vs=class extends Zt{constructor(t){super();this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){let{inflateAmount:e,options:{borderColor:i,backgroundColor:s}}=this,{inner:r,outer:o}=qv(this),a=Gv(o.radius)?en:Uv;t.save(),(o.w!==r.w||o.h!==r.h)&&(t.beginPath(),a(t,Ea(o,e,r)),t.clip(),a(t,Ea(r,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),a(t,Ea(r,e)),t.fillStyle=s,t.fill(),t.restore()}inRange(t,e,i){return Oa(this,t,e,i)}inXRange(t,e){return Oa(this,t,null,e)}inYRange(t,e){return Oa(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+s)/2:e,y:r?i:(i+s)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}};vs.id="bar";vs.defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};vs.defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};var Kv=Object.freeze({__proto__:null,ArcElement:ms,LineElement:on,PointElement:bs,BarElement:vs});function Zv(n,t,e,i,s){let r=s.samples||i;if(r>=e)return n.slice(t,t+e);let o=[],a=(e-2)/(r-2),l=0,c=t+e-1,h=t,f,g,p,b,x;for(o[l++]=n[h],f=0;f<r-2;f++){let S=0,C=0,T,I=Math.floor((f+1)*a)+1+t,A=Math.min(Math.floor((f+2)*a)+1,e)+t,F=A-I;for(T=I;T<A;T++)S+=n[T].x,C+=n[T].y;S/=F,C/=F;let j=Math.floor(f*a)+1+t,N=Math.min(Math.floor((f+1)*a)+1,e)+t,{x:G,y:U}=n[h];for(p=b=-1,T=j;T<N;T++)b=.5*Math.abs((G-S)*(n[T].y-U)-(G-n[T].x)*(C-U)),b>p&&(p=b,g=n[T],x=T);o[l++]=g,h=x}return o[l++]=n[c],o}function Jv(n,t,e,i){let s=0,r=0,o,a,l,c,h,f,g,p,b,x,S=[],C=t+e-1,T=n[t].x,A=n[C].x-T;for(o=t;o<t+e;++o){a=n[o],l=(a.x-T)/A*i,c=a.y;let F=l|0;if(F===h)c<b?(b=c,f=o):c>x&&(x=c,g=o),s=(r*s+a.x)/++r;else{let j=o-1;if(!vt(f)&&!vt(g)){let N=Math.min(f,g),G=Math.max(f,g);N!==p&&N!==j&&S.push({...n[N],x:s}),G!==p&&G!==j&&S.push({...n[G],x:s})}o>0&&j!==p&&S.push(n[j]),S.push(a),h=F,r=0,b=x=c,f=g=p=o}}return S}function pu(n){if(n._decimated){let t=n._data;delete n._decimated,delete n._data,Object.defineProperty(n,"data",{value:t})}}function gu(n){n.data.datasets.forEach(t=>{pu(t)})}function Qv(n,t){let e=t.length,i=0,s,{iScale:r}=n,{min:o,max:a,minDefined:l,maxDefined:c}=r.getUserBounds();return l&&(i=Kt(Re(t,r.axis,o).lo,0,e-1)),c?s=Kt(Re(t,r.axis,a).hi+1,i,e)-i:s=e-i,{start:i,count:s}}var t_={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(n,t,e)=>{if(!e.enabled){gu(n);return}let i=n.width;n.data.datasets.forEach((s,r)=>{let{_data:o,indexAxis:a}=s,l=n.getDatasetMeta(r),c=o||s.data;if(Xn([a,n.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=n.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||n.options.parsing)return;let{start:f,count:g}=Qv(l,c),p=e.threshold||4*i;if(g<=p){pu(s);return}vt(o)&&(s._data=c,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(x){this._data=x}}));let b;switch(e.algorithm){case"lttb":b=Zv(c,f,g,i,e);break;case"min-max":b=Jv(c,f,g,i);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}s._decimated=b})},destroy(n){gu(n)}};function e_(n,t,e){let i=n.segments,s=n.points,r=t.points,o=[];for(let a of i){let{start:l,end:c}=a;c=Ra(l,c,s);let h=Aa(e,s[l],s[c],a.loop);if(!t.segments){o.push({source:a,target:h,start:s[l],end:s[c]});continue}let f=kr(t,h);for(let g of f){let p=Aa(e,r[g.start],r[g.end],g.loop),b=wr(a,s,p);for(let x of b)o.push({source:x,target:g,start:{[e]:mu(h,p,"start",Math.max)},end:{[e]:mu(h,p,"end",Math.min)}})}}return o}function Aa(n,t,e,i){if(i)return;let s=t[n],r=e[n];return n==="angle"&&(s=fe(s),r=fe(r)),{property:n,start:s,end:r}}function n_(n,t){let{x:e=null,y:i=null}=n||{},s=t.points,r=[];return t.segments.forEach(({start:o,end:a})=>{a=Ra(o,a,s);let l=s[o],c=s[a];i!==null?(r.push({x:l.x,y:i}),r.push({x:c.x,y:i})):e!==null&&(r.push({x:e,y:l.y}),r.push({x:e,y:c.y}))}),r}function Ra(n,t,e){for(;t>n;t--){let i=e[t];if(!isNaN(i.x)&&!isNaN(i.y))break}return t}function mu(n,t,e,i){return n&&t?i(n[e],t[e]):n?n[e]:t?t[e]:0}function bu(n,t){let e=[],i=!1;return gt(n)?(i=!0,e=n):e=n_(n,t),e.length?new on({points:e,options:{tension:0},_loop:i,_fullLoop:i}):null}function vu(n){return n&&n.fill!==!1}function i_(n,t,e){let s=n[t].fill,r=[t],o;if(!e)return s;for(;s!==!1&&r.indexOf(s)===-1;){if(!$t(s))return s;if(o=n[s],!o)return!1;if(o.visible)return s;r.push(s),s=o.fill}return!1}function s_(n,t,e){let i=l_(n);if(at(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return $t(s)&&Math.floor(s)===s?r_(i[0],t,s,e):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function r_(n,t,e,i){return(n==="-"||n==="+")&&(e=t+e),e===t||e<0||e>=i?!1:e}function o_(n,t){let e=null;return n==="start"?e=t.bottom:n==="end"?e=t.top:at(n)?e=t.getPixelForValue(n.value):t.getBasePixel&&(e=t.getBasePixel()),e}function a_(n,t,e){let i;return n==="start"?i=e:n==="end"?i=t.options.reverse?t.min:t.max:at(n)?i=n.value:i=t.getBaseValue(),i}function l_(n){let t=n.options,e=t.fill,i=st(e&&e.target,e);return i===void 0&&(i=!!t.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function c_(n){let{scale:t,index:e,line:i}=n,s=[],r=i.segments,o=i.points,a=h_(t,e);a.push(bu({x:null,y:t.bottom},i));for(let l=0;l<r.length;l++){let c=r[l];for(let h=c.start;h<=c.end;h++)u_(s,o[h],a)}return new on({points:s,options:{}})}function h_(n,t){let e=[],i=n.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){let r=i[s];if(r.index===t)break;r.hidden||e.unshift(r.dataset)}return e}function u_(n,t,e){let i=[];for(let s=0;s<e.length;s++){let r=e[s],{first:o,last:a,point:l}=f_(r,t,"x");if(!(!l||o&&a)){if(o)i.unshift(l);else if(n.push(l),!a)break}}n.push(...i)}function f_(n,t,e){let i=n.interpolate(t,e);if(!i)return{};let s=i[e],r=n.segments,o=n.points,a=!1,l=!1;for(let c=0;c<r.length;c++){let h=r[c],f=o[h.start][e],g=o[h.end][e];if(Ae(s,f,g)){a=s===f,l=s===g;break}}return{first:a,last:l,point:i}}var La=class{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:r,radius:o}=this;return e=e||{start:0,end:kt},t.arc(s,r,o,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}};function d_(n){let{chart:t,fill:e,line:i}=n;if($t(e))return p_(t,e);if(e==="stack")return c_(n);if(e==="shape")return!0;let s=g_(n);return s instanceof La?s:bu(s,i)}function p_(n,t){let e=n.getDatasetMeta(t);return e&&n.isDatasetVisible(t)?e.dataset:null}function g_(n){return(n.scale||{}).getPointPositionForValue?b_(n):m_(n)}function m_(n){let{scale:t={},fill:e}=n,i=o_(e,t);if($t(i)){let s=t.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function b_(n){let{scale:t,fill:e}=n,i=t.options,s=t.getLabels().length,r=i.reverse?t.max:t.min,o=a_(e,t,r),a=[];if(i.grid.circular){let l=t.getPointPositionForValue(0,r);return new La({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(o)})}for(let l=0;l<s;++l)a.push(t.getPointPositionForValue(l,o));return a}function Fa(n,t,e){let i=d_(t),{line:s,scale:r,axis:o}=t,a=s.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:f=c}=l||{};i&&s.points.length&&(yn(n,e),v_(n,{line:s,target:i,above:h,below:f,area:e,scale:r,axis:o}),xn(n))}function v_(n,t){let{line:e,target:i,above:s,below:r,area:o,scale:a}=t,l=e._loop?"angle":t.axis;n.save(),l==="x"&&r!==s&&(_u(n,i,o.top),yu(n,{line:e,target:i,color:s,scale:a,property:l}),n.restore(),n.save(),_u(n,i,o.bottom)),yu(n,{line:e,target:i,color:r,scale:a,property:l}),n.restore()}function _u(n,t,e){let{segments:i,points:s}=t,r=!0,o=!1;n.beginPath();for(let a of i){let{start:l,end:c}=a,h=s[l],f=s[Ra(l,c,s)];r?(n.moveTo(h.x,h.y),r=!1):(n.lineTo(h.x,e),n.lineTo(h.x,h.y)),o=!!t.pathSegment(n,a,{move:o}),o?n.closePath():n.lineTo(f.x,e)}n.lineTo(t.first().x,e),n.closePath(),n.clip()}function yu(n,t){let{line:e,target:i,property:s,color:r,scale:o}=t,a=e_(e,i,s);for(let{source:l,target:c,start:h,end:f}of a){let{style:{backgroundColor:g=r}={}}=l,p=i!==!0;n.save(),n.fillStyle=g,__(n,o,p&&Aa(s,h,f)),n.beginPath();let b=!!e.pathSegment(n,l),x;if(p){b?n.closePath():xu(n,i,f,s);let S=!!i.pathSegment(n,c,{move:b,reverse:!0});x=b&&S,x||xu(n,i,h,s)}n.closePath(),n.fill(x?"evenodd":"nonzero"),n.restore()}}function __(n,t,e){let{top:i,bottom:s}=t.chart.chartArea,{property:r,start:o,end:a}=e||{};r==="x"&&(n.beginPath(),n.rect(o,i,a-o,s-i),n.clip())}function xu(n,t,e,i){let s=t.interpolate(e,i);s&&n.lineTo(s.x,s.y)}var y_={id:"filler",afterDatasetsUpdate(n,t,e){let i=(n.data.datasets||[]).length,s=[],r,o,a,l;for(o=0;o<i;++o)r=n.getDatasetMeta(o),a=r.dataset,l=null,a&&a.options&&a instanceof on&&(l={visible:n.isDatasetVisible(o),index:o,fill:s_(a,o,i),chart:n,axis:r.controller.options.indexAxis,scale:r.vScale,line:a}),r.$filler=l,s.push(l);for(o=0;o<i;++o)l=s[o],!(!l||l.fill===!1)&&(l.fill=i_(s,o,e.propagate))},beforeDraw(n,t,e){let i=e.drawTime==="beforeDraw",s=n.getSortedVisibleDatasetMetas(),r=n.chartArea;for(let o=s.length-1;o>=0;--o){let a=s[o].$filler;!a||(a.line.updateControlPoints(r,a.axis),i&&a.fill&&Fa(n.ctx,a,r))}},beforeDatasetsDraw(n,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;let i=n.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){let r=i[s].$filler;vu(r)&&Fa(n.ctx,r,n.chartArea)}},beforeDatasetDraw(n,t,e){let i=t.meta.$filler;!vu(i)||e.drawTime!=="beforeDatasetDraw"||Fa(n.ctx,i,n.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},wu=(n,t)=>{let{boxHeight:e=t,boxWidth:i=t}=n;return n.usePointStyle&&(e=Math.min(e,t),i=n.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:e,itemHeight:Math.max(t,e)}},x_=(n,t)=>n!==null&&t!==null&&n.datasetIndex===t.datasetIndex&&n.index===t.index,Ia=class extends Zt{constructor(t){super();this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=wt(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(i=>t.filter(i,this.chart.data))),t.sort&&(e=e.sort((i,s)=>t.sort(i,s,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}let i=t.labels,s=jt(i.font),r=s.size,o=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=wu(i,r),c,h;e.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(o,r,a,l)+10):(h=this.maxHeight,c=this._fitCols(o,r,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:r,maxWidth:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+a,f=t;r.textAlign="left",r.textBaseline="middle";let g=-1,p=-h;return this.legendItems.forEach((b,x)=>{let S=i+e/2+r.measureText(b.text).width;(x===0||c[c.length-1]+S+2*a>o)&&(f+=h,c[c.length-(x>0?0:1)]=0,p+=h,g++),l[x]={left:0,top:p,row:g,width:S,height:s},c[c.length-1]+=S+a}),f}_fitCols(t,e,i,s){let{ctx:r,maxHeight:o,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=o-t,f=a,g=0,p=0,b=0,x=0;return this.legendItems.forEach((S,C)=>{let T=i+e/2+r.measureText(S.text).width;C>0&&p+s+2*a>h&&(f+=g+a,c.push({width:g,height:p}),b+=g+a,x++,g=p=0),l[C]={left:b,top:p,col:x,width:T,height:s},g=Math.max(g,T),p+=s+a}),f+=g,c.push({width:g,height:p}),f}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,o=kn(r,this.left,this.width);if(this.isHorizontal()){let a=0,l=ie(i,this.left+s,this.right-this.lineWidths[a]);for(let c of e)a!==c.row&&(a=c.row,l=ie(i,this.left+s,this.right-this.lineWidths[a])),c.top+=this.top+t+s,c.left=o.leftForLtr(o.x(l),c.width),l+=c.width+s}else{let a=0,l=ie(i,this.top+t+s,this.bottom-this.columnSizes[a].height);for(let c of e)c.col!==a&&(a=c.col,l=ie(i,this.top+t+s,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+s,c.left=o.leftForLtr(o.x(c.left),c.width),l+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let t=this.ctx;yn(t,this),this._draw(),xn(t)}}_draw(){let{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:r,labels:o}=t,a=ut.color,l=kn(t.rtl,this.left,this.width),c=jt(o.font),{color:h,padding:f}=o,g=c.size,p=g/2,b;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;let{boxWidth:x,boxHeight:S,itemHeight:C}=wu(o,g),T=function(N,G,U){if(isNaN(x)||x<=0||isNaN(S)||S<0)return;s.save();let nt=st(U.lineWidth,1);if(s.fillStyle=st(U.fillStyle,a),s.lineCap=st(U.lineCap,"butt"),s.lineDashOffset=st(U.lineDashOffset,0),s.lineJoin=st(U.lineJoin,"miter"),s.lineWidth=nt,s.strokeStyle=st(U.strokeStyle,a),s.setLineDash(st(U.lineDash,[])),o.usePointStyle){let lt={radius:S*Math.SQRT2/2,pointStyle:U.pointStyle,rotation:U.rotation,borderWidth:nt},rt=l.xPlus(N,x/2),Pt=G+p;hr(s,lt,rt,Pt,o.pointStyleWidth&&x)}else{let lt=G+Math.max((g-S)/2,0),rt=l.leftForLtr(N,x),Pt=Le(U.borderRadius);s.beginPath(),Object.values(Pt).some(zt=>zt!==0)?en(s,{x:rt,y:lt,w:x,h:S,radius:Pt}):s.rect(rt,lt,x,S),s.fill(),nt!==0&&s.stroke()}s.restore()},I=function(N,G,U){tn(s,U.text,N,G+C/2,c,{strikethrough:U.hidden,textAlign:l.textAlign(U.textAlign)})},A=this.isHorizontal(),F=this._computeTitleHeight();A?b={x:ie(r,this.left+f,this.right-i[0]),y:this.top+f+F,line:0}:b={x:this.left+f,y:ie(r,this.top+F+f,this.bottom-e[0].height),line:0},yr(this.ctx,t.textDirection);let j=C+f;this.legendItems.forEach((N,G)=>{s.strokeStyle=N.fontColor||h,s.fillStyle=N.fontColor||h;let U=s.measureText(N.text).width,nt=l.textAlign(N.textAlign||(N.textAlign=o.textAlign)),lt=x+p+U,rt=b.x,Pt=b.y;l.setWidth(this.width),A?G>0&&rt+lt+f>this.right&&(Pt=b.y+=j,b.line++,rt=b.x=ie(r,this.left+f,this.right-i[b.line])):G>0&&Pt+j>this.bottom&&(rt=b.x=rt+e[b.line].width+f,b.line++,Pt=b.y=ie(r,this.top+F+f,this.bottom-e[b.line].height));let zt=l.x(rt);T(zt,Pt,N),rt=Zo(nt,rt+x+p,A?rt+lt:this.right,t.rtl),I(l.x(rt),Pt,N),A?b.x+=lt+f:b.y+=j}),xr(this.ctx,t.textDirection)}drawTitle(){let t=this.options,e=t.title,i=jt(e.font),s=Vt(e.padding);if(!e.display)return;let r=kn(t.rtl,this.left,this.width),o=this.ctx,a=e.position,l=i.size/2,c=s.top+l,h,f=this.left,g=this.width;if(this.isHorizontal())g=Math.max(...this.lineWidths),h=this.top+c,f=ie(t.align,f,this.right-g);else{let b=this.columnSizes.reduce((x,S)=>Math.max(x,S.height),0);h=c+ie(t.align,this.top,this.bottom-b-t.labels.padding-this._computeTitleHeight())}let p=ie(a,f,f+g);o.textAlign=r.textAlign(Yi(a)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,tn(o,e.text,p,h,i)}_computeTitleHeight(){let t=this.options.title,e=jt(t.font),i=Vt(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(Ae(t,this.left,this.right)&&Ae(e,this.top,this.bottom)){for(r=this.legendHitBoxes,i=0;i<r.length;++i)if(s=r[i],Ae(t,s.left,s.left+s.width)&&Ae(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){let e=this.options;if(!w_(t.type,e))return;let i=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){let s=this._hoveredItem,r=x_(s,i);s&&!r&&wt(e.onLeave,[t,s,this],this),this._hoveredItem=i,i&&!r&&wt(e.onHover,[t,i,this],this)}else i&&wt(e.onClick,[t,i,this],this)}};function w_(n,t){return!!((n==="mousemove"||n==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(n==="click"||n==="mouseup"))}var k_={id:"legend",_element:Ia,start(n,t,e){let i=n.legend=new Ia({ctx:n.ctx,options:e,chart:n});he.configure(n,i,e),he.addBox(n,i)},stop(n){he.removeBox(n,n.legend),delete n.legend},beforeUpdate(n,t,e){let i=n.legend;he.configure(n,i,e),i.options=e},afterUpdate(n){let t=n.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(n,t){t.replay||n.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(n,t,e){let i=t.datasetIndex,s=e.chart;s.isDatasetVisible(i)?(s.hide(i),t.hidden=!0):(s.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:n=>n.chart.options.color,boxWidth:40,padding:10,generateLabels(n){let t=n.data.datasets,{labels:{usePointStyle:e,pointStyle:i,textAlign:s,color:r}}=n.legend.options;return n._getSortedDatasetMetas().map(o=>{let a=o.controller.getStyle(e?0:void 0),l=Vt(a.borderWidth);return{text:t[o.index].label,fillStyle:a.backgroundColor,fontColor:r,hidden:!o.visible,lineCap:a.borderCapStyle,lineDash:a.borderDash,lineDashOffset:a.borderDashOffset,lineJoin:a.borderJoinStyle,lineWidth:(l.width+l.height)/4,strokeStyle:a.borderColor,pointStyle:i||a.pointStyle,rotation:a.rotation,textAlign:s||a.textAlign,borderRadius:0,datasetIndex:o.index}},this)}},title:{color:n=>n.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:n=>!n.startsWith("on"),labels:{_scriptable:n=>!["generateLabels","filter","sort"].includes(n)}}},Rr=class extends Zt{constructor(t){super();this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=gt(i.text)?i.text.length:1;this._padding=Vt(i.padding);let r=s*jt(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){let t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){let{top:e,left:i,bottom:s,right:r,options:o}=this,a=o.align,l=0,c,h,f;return this.isHorizontal()?(h=ie(a,i,r),f=e+t,c=r-i):(o.position==="left"?(h=i+t,f=ie(a,s,e),l=dt*-.5):(h=r-t,f=ie(a,e,s),l=dt*.5),c=s-e),{titleX:h,titleY:f,maxWidth:c,rotation:l}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=jt(e.font),r=i.lineHeight/2+this._padding.top,{titleX:o,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(r);tn(t,e.text,0,0,i,{color:e.color,maxWidth:l,rotation:c,textAlign:Yi(e.align),textBaseline:"middle",translation:[o,a]})}};function S_(n,t){let e=new Rr({ctx:n.ctx,options:t,chart:n});he.configure(n,e,t),he.addBox(n,e),n.titleBlock=e}var M_={id:"title",_element:Rr,start(n,t,e){S_(n,e)},stop(n){let t=n.titleBlock;he.removeBox(n,t),delete n.titleBlock},beforeUpdate(n,t,e){let i=n.titleBlock;he.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Lr=new WeakMap,C_={id:"subtitle",start(n,t,e){let i=new Rr({ctx:n.ctx,options:e,chart:n});he.configure(n,i,e),he.addBox(n,i),Lr.set(n,i)},stop(n){he.removeBox(n,Lr.get(n)),Lr.delete(n)},beforeUpdate(n,t,e){let i=Lr.get(n);he.configure(n,i,e),i.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},_s={average(n){if(!n.length)return!1;let t,e,i=0,s=0,r=0;for(t=0,e=n.length;t<e;++t){let o=n[t].element;if(o&&o.hasValue()){let a=o.tooltipPosition();i+=a.x,s+=a.y,++r}}return{x:i/r,y:s/r}},nearest(n,t){if(!n.length)return!1;let e=t.x,i=t.y,s=Number.POSITIVE_INFINITY,r,o,a;for(r=0,o=n.length;r<o;++r){let l=n[r].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=Ze(t,c);h<s&&(s=h,a=l)}}if(a){let l=a.tooltipPosition();e=l.x,i=l.y}return{x:e,y:i}}};function We(n,t){return t&&(gt(t)?Array.prototype.push.apply(n,t):n.push(t)),n}function an(n){return(typeof n=="string"||n instanceof String)&&n.indexOf(`
`)>-1?n.split(`
`):n}function P_(n,t){let{element:e,datasetIndex:i,index:s}=t,r=n.getDatasetMeta(i).controller,{label:o,value:a}=r.getLabelAndValue(s);return{chart:n,label:o,parsed:r.getParsed(s),raw:n.data.datasets[i].data[s],formattedValue:a,dataset:r.getDataset(),dataIndex:s,datasetIndex:i,element:e}}function ku(n,t){let e=n.chart.ctx,{body:i,footer:s,title:r}=n,{boxWidth:o,boxHeight:a}=t,l=jt(t.bodyFont),c=jt(t.titleFont),h=jt(t.footerFont),f=r.length,g=s.length,p=i.length,b=Vt(t.padding),x=b.height,S=0,C=i.reduce((A,F)=>A+F.before.length+F.lines.length+F.after.length,0);if(C+=n.beforeBody.length+n.afterBody.length,f&&(x+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),C){let A=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;x+=p*A+(C-p)*l.lineHeight+(C-1)*t.bodySpacing}g&&(x+=t.footerMarginTop+g*h.lineHeight+(g-1)*t.footerSpacing);let T=0,I=function(A){S=Math.max(S,e.measureText(A).width+T)};return e.save(),e.font=c.string,Ct(n.title,I),e.font=l.string,Ct(n.beforeBody.concat(n.afterBody),I),T=t.displayColors?o+2+t.boxPadding:0,Ct(i,A=>{Ct(A.before,I),Ct(A.lines,I),Ct(A.after,I)}),T=0,e.font=h.string,Ct(n.footer,I),e.restore(),S+=b.width,{width:S,height:x}}function T_(n,t){let{y:e,height:i}=t;return e<i/2?"top":e>n.height-i/2?"bottom":"center"}function D_(n,t,e,i){let{x:s,width:r}=i,o=e.caretSize+e.caretPadding;if(n==="left"&&s+r+o>t.width||n==="right"&&s-r-o<0)return!0}function O_(n,t,e,i){let{x:s,width:r}=e,{width:o,chartArea:{left:a,right:l}}=n,c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=r/2?c="left":s>=o-r/2&&(c="right"),D_(c,n,t,e)&&(c="center"),c}function Su(n,t,e){let i=e.yAlign||t.yAlign||T_(n,e);return{xAlign:e.xAlign||t.xAlign||O_(n,t,e,i),yAlign:i}}function E_(n,t){let{x:e,width:i}=n;return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function A_(n,t,e){let{y:i,height:s}=n;return t==="top"?i+=e:t==="bottom"?i-=s+e:i-=s/2,i}function Mu(n,t,e,i){let{caretSize:s,caretPadding:r,cornerRadius:o}=n,{xAlign:a,yAlign:l}=e,c=s+r,{topLeft:h,topRight:f,bottomLeft:g,bottomRight:p}=Le(o),b=E_(t,a),x=A_(t,l,c);return l==="center"?a==="left"?b+=c:a==="right"&&(b-=c):a==="left"?b-=Math.max(h,g)+s:a==="right"&&(b+=Math.max(f,p)+s),{x:Kt(b,0,i.width-t.width),y:Kt(x,0,i.height-t.height)}}function Fr(n,t,e){let i=Vt(e.padding);return t==="center"?n.x+n.width/2:t==="right"?n.x+n.width-i.right:n.x+i.left}function Cu(n){return We([],an(n))}function R_(n,t,e){return Ne(n,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Pu(n,t){let e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?n.override(e):n}var Ir=class extends Zt{constructor(t){super();this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart||t._chart,this._chart=this.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new ts(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=R_(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){let{callbacks:i}=e,s=i.beforeTitle.apply(this,[t]),r=i.title.apply(this,[t]),o=i.afterTitle.apply(this,[t]),a=[];return a=We(a,an(s)),a=We(a,an(r)),a=We(a,an(o)),a}getBeforeBody(t,e){return Cu(e.callbacks.beforeBody.apply(this,[t]))}getBody(t,e){let{callbacks:i}=e,s=[];return Ct(t,r=>{let o={before:[],lines:[],after:[]},a=Pu(i,r);We(o.before,an(a.beforeLabel.call(this,r))),We(o.lines,a.label.call(this,r)),We(o.after,an(a.afterLabel.call(this,r))),s.push(o)}),s}getAfterBody(t,e){return Cu(e.callbacks.afterBody.apply(this,[t]))}getFooter(t,e){let{callbacks:i}=e,s=i.beforeFooter.apply(this,[t]),r=i.footer.apply(this,[t]),o=i.afterFooter.apply(this,[t]),a=[];return a=We(a,an(s)),a=We(a,an(r)),a=We(a,an(o)),a}_createItems(t){let e=this._active,i=this.chart.data,s=[],r=[],o=[],a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(P_(this.chart,e[l]));return t.filter&&(a=a.filter((h,f,g)=>t.filter(h,f,g,i))),t.itemSort&&(a=a.sort((h,f)=>t.itemSort(h,f,i))),Ct(a,h=>{let f=Pu(t.callbacks,h);s.push(f.labelColor.call(this,h)),r.push(f.labelPointStyle.call(this,h)),o.push(f.labelTextColor.call(this,h))}),this.labelColors=s,this.labelPointStyles=r,this.labelTextColors=o,this.dataPoints=a,a}update(t,e){let i=this.options.setContext(this.getContext()),s=this._active,r,o=[];if(!s.length)this.opacity!==0&&(r={opacity:0});else{let a=_s[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);let l=this._size=ku(this,i),c=Object.assign({},a,l),h=Su(this.chart,i,c),f=Mu(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,r={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=o,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){let{xAlign:s,yAlign:r}=this,{caretSize:o,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:f}=Le(a),{x:g,y:p}=t,{width:b,height:x}=e,S,C,T,I,A,F;return r==="center"?(A=p+x/2,s==="left"?(S=g,C=S-o,I=A+o,F=A-o):(S=g+b,C=S+o,I=A-o,F=A+o),T=S):(s==="left"?C=g+Math.max(l,h)+o:s==="right"?C=g+b-Math.max(c,f)-o:C=this.caretX,r==="top"?(I=p,A=I-o,S=C-o,T=C+o):(I=p+x,A=I+o,S=C+o,T=C-o),F=I),{x1:S,x2:C,x3:T,y1:I,y2:A,y3:F}}drawTitle(t,e,i){let s=this.title,r=s.length,o,a,l;if(r){let c=kn(i.rtl,this.x,this.width);for(t.x=Fr(this,i.titleAlign,i),e.textAlign=c.textAlign(i.titleAlign),e.textBaseline="middle",o=jt(i.titleFont),a=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,l=0;l<r;++l)e.fillText(s[l],c.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+a,l+1===r&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,e,i,s,r){let o=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c,boxPadding:h}=r,f=jt(r.bodyFont),g=Fr(this,"left",r),p=s.x(g),b=l<f.lineHeight?(f.lineHeight-l)/2:0,x=e.y+b;if(r.usePointStyle){let S={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},C=s.leftForLtr(p,c)+c/2,T=x+l/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,Ki(t,S,C,T),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,Ki(t,S,C,T)}else{t.lineWidth=at(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;let S=s.leftForLtr(p,c-h),C=s.leftForLtr(s.xPlus(p,1),c-h-2),T=Le(o.borderRadius);Object.values(T).some(I=>I!==0)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,en(t,{x:S,y:x,w:c,h:l,radius:T}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),en(t,{x:C,y:x+1,w:c-2,h:l-2,radius:T}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(S,x,c,l),t.strokeRect(S,x,c,l),t.fillStyle=o.backgroundColor,t.fillRect(C,x+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let{body:s}=this,{bodySpacing:r,bodyAlign:o,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,f=jt(i.bodyFont),g=f.lineHeight,p=0,b=kn(i.rtl,this.x,this.width),x=function(G){e.fillText(G,b.x(t.x+p),t.y+g/2),t.y+=g+r},S=b.textAlign(o),C,T,I,A,F,j,N;for(e.textAlign=o,e.textBaseline="middle",e.font=f.string,t.x=Fr(this,S,i),e.fillStyle=i.bodyColor,Ct(this.beforeBody,x),p=a&&S!=="right"?o==="center"?c/2+h:c+2+h:0,A=0,j=s.length;A<j;++A){for(C=s[A],T=this.labelTextColors[A],e.fillStyle=T,Ct(C.before,x),I=C.lines,a&&I.length&&(this._drawColorBox(e,t,A,b,i),g=Math.max(f.lineHeight,l)),F=0,N=I.length;F<N;++F)x(I[F]),g=f.lineHeight;Ct(C.after,x)}p=0,g=f.lineHeight,Ct(this.afterBody,x),t.y-=r}drawFooter(t,e,i){let s=this.footer,r=s.length,o,a;if(r){let l=kn(i.rtl,this.x,this.width);for(t.x=Fr(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=l.textAlign(i.footerAlign),e.textBaseline="middle",o=jt(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,a=0;a<r;++a)e.fillText(s[a],l.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:r,yAlign:o}=this,{x:a,y:l}=t,{width:c,height:h}=i,{topLeft:f,topRight:g,bottomLeft:p,bottomRight:b}=Le(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(a+f,l),o==="top"&&this.drawCaret(t,e,i,s),e.lineTo(a+c-g,l),e.quadraticCurveTo(a+c,l,a+c,l+g),o==="center"&&r==="right"&&this.drawCaret(t,e,i,s),e.lineTo(a+c,l+h-b),e.quadraticCurveTo(a+c,l+h,a+c-b,l+h),o==="bottom"&&this.drawCaret(t,e,i,s),e.lineTo(a+p,l+h),e.quadraticCurveTo(a,l+h,a,l+h-p),o==="center"&&r==="left"&&this.drawCaret(t,e,i,s),e.lineTo(a,l+f),e.quadraticCurveTo(a,l,a+f,l),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){let o=_s[t.position].call(this,this._active,this._eventPosition);if(!o)return;let a=this._size=ku(this,t),l=Object.assign({},o,this._size),c=Su(e,t,l),h=Mu(t,l,c,e);(s._to!==h.x||r._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=o.x,this.caretY=o.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;let o=Vt(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),yr(t,e.textDirection),r.y+=o.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),xr(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:a,index:l})=>{let c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),r=!pi(i,s),o=this._positionChanged(s,e);(r||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,r=this._active||[],o=this._getActiveElements(t,r,e,i),a=this._positionChanged(o,t),l=e||!pi(o,r)||a;return l&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,i,s){let r=this.options;if(t.type==="mouseout")return[];if(!s)return e;let o=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&o.reverse(),o}_positionChanged(t,e){let{caretX:i,caretY:s,options:r}=this,o=_s[r.position].call(this,t,e);return o!==!1&&(i!==o.x||s!==o.y)}};Ir.positioners=_s;var L_={id:"tooltip",_element:Ir,positioners:_s,afterInit(n,t,e){e&&(n.tooltip=new Ir({chart:n,options:e}))},beforeUpdate(n,t,e){n.tooltip&&n.tooltip.initialize(e)},reset(n,t,e){n.tooltip&&n.tooltip.initialize(e)},afterDraw(n){let t=n.tooltip;if(t&&t._willRender()){let e={tooltip:t};if(n.notifyPlugins("beforeTooltipDraw",e)===!1)return;t.draw(n.ctx),n.notifyPlugins("afterTooltipDraw",e)}},afterEvent(n,t){if(n.tooltip){let e=t.replay;n.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(n,t)=>t.bodyFont.size,boxWidth:(n,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:{beforeTitle:Oe,title(n){if(n.length>0){let t=n[0],e=t.chart.data.labels,i=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return e[t.dataIndex]}return""},afterTitle:Oe,beforeBody:Oe,beforeLabel:Oe,label(n){if(this&&this.options&&this.options.mode==="dataset")return n.label+": "+n.formattedValue||n.formattedValue;let t=n.dataset.label||"";t&&(t+=": ");let e=n.formattedValue;return vt(e)||(t+=e),t},labelColor(n){let e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(n){let e=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Oe,afterBody:Oe,beforeFooter:Oe,footer:Oe,afterFooter:Oe}},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:n=>n!=="filter"&&n!=="itemSort"&&n!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},F_=Object.freeze({__proto__:null,Decimation:t_,Filler:y_,Legend:k_,SubTitle:C_,Title:M_,Tooltip:L_}),I_=(n,t,e,i)=>(typeof t=="string"?(e=n.push(t)-1,i.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function $_(n,t,e,i){let s=n.indexOf(t);if(s===-1)return I_(n,t,e,i);let r=n.lastIndexOf(t);return s!==r?e:s}var j_=(n,t)=>n===null?null:Kt(Math.round(n),0,t),ys=class extends Sn{constructor(t){super(t);this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let i=this.getLabels();for(let{index:s,label:r}of e)i[s]===r&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(vt(t))return null;let i=this.getLabels();return e=isFinite(e)&&i[e]===t?e:$_(i,t,st(e,t),this._addedLabels),j_(e,i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],r=this.getLabels();r=t===0&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}};ys.id="category";ys.defaults={ticks:{callback:ys.prototype.getLabelForValue}};function z_(n,t){let e=[],i=1e-14,{bounds:s,step:r,min:o,max:a,precision:l,count:c,maxTicks:h,maxDigits:f,includeBounds:g}=n,p=r||1,b=h-1,{min:x,max:S}=t,C=!vt(o),T=!vt(a),I=!vt(c),A=(S-x)/(f+1),F=Xs((S-x)/b/p)*p,j,N,G,U;if(F<i&&!C&&!T)return[{value:x},{value:S}];U=Math.ceil(S/F)-Math.floor(x/F),U>b&&(F=Xs(U*F/b/p)*p),vt(l)||(j=Math.pow(10,l),F=Math.ceil(F*j)/j),s==="ticks"?(N=Math.floor(x/F)*F,G=Math.ceil(S/F)*F):(N=x,G=S),C&&T&&r&&Yo((a-o)/r,F/1e3)?(U=Math.round(Math.min((a-o)/F,h)),F=(a-o)/U,N=o,G=a):I?(N=C?o:N,G=T?a:G,U=c-1,F=(G-N)/U):(U=(G-N)/F,Nn(U,Math.round(U),F/1e3)?U=Math.round(U):U=Math.ceil(U));let nt=Math.max(Gs(F),Gs(N));j=Math.pow(10,vt(l)?nt:l),N=Math.round(N*j)/j,G=Math.round(G*j)/j;let lt=0;for(C&&(g&&N!==o?(e.push({value:o}),N<o&&lt++,Nn(Math.round((N+lt*F)*j)/j,o,Tu(o,A,n))&&lt++):N<o&&lt++);lt<U;++lt)e.push({value:Math.round((N+lt*F)*j)/j});return T&&g&&G!==a?e.length&&Nn(e[e.length-1].value,a,Tu(a,A,n))?e[e.length-1].value=a:e.push({value:a}):(!T||G===a)&&e.push({value:G}),e}function Tu(n,t,{horizontal:e,minRotation:i}){let s=Wt(i),r=(e?Math.sin(s):Math.cos(s))||.001,o=.75*t*(""+n).length;return Math.min(t/r,o)}var xs=class extends Sn{constructor(t){super(t);this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return vt(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:r}=this,o=l=>s=e?s:l,a=l=>r=i?r:l;if(t){let l=Se(s),c=Se(r);l<0&&c<0?a(0):l>0&&c>0&&o(0)}if(s===r){let l=1;(r>=Number.MAX_SAFE_INTEGER||s<=Number.MIN_SAFE_INTEGER)&&(l=Math.abs(r*.05)),a(r+l),t||o(s-l)}this.min=s,this.max=r}getTickLimit(){let t=this.options.ticks,{maxTicksLimit:e,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),e=e||11),e&&(s=Math.min(e,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit();i=Math.max(2,i);let s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},r=this._range||this,o=z_(s,r);return t.bounds==="ticks"&&qs(o,this,"value"),t.reverse?(o.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),o}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Gn(t,this.chart.options.locale,this.options.ticks.format)}},$r=class extends xs{determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=$t(t)?t:0,this.max=$t(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=Wt(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,r.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}};$r.id="linear";$r.defaults={ticks:{callback:Mr.formatters.numeric}};function Du(n){return n/Math.pow(10,Math.floor(be(n)))===1}function B_(n,t){let e=Math.floor(be(t.max)),i=Math.ceil(t.max/Math.pow(10,e)),s=[],r=me(n.min,Math.pow(10,Math.floor(be(t.min)))),o=Math.floor(be(r)),a=Math.floor(r/Math.pow(10,o)),l=o<0?Math.pow(10,Math.abs(o)):1;do s.push({value:r,major:Du(r)}),++a,a===10&&(a=1,++o,l=o>=0?1:l),r=Math.round(a*Math.pow(10,o)*l)/l;while(o<e||o===e&&a<i);let c=me(n.max,r);return s.push({value:c,major:Du(r)}),s}var jr=class extends Sn{constructor(t){super(t);this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=xs.prototype.parse.apply(this,[t,e]);if(i===0){this._zero=!0;return}return $t(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=$t(t)?Math.max(0,t):null,this.max=$t(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,r=l=>i=t?i:l,o=l=>s=e?s:l,a=(l,c)=>Math.pow(10,Math.floor(be(l))+c);i===s&&(i<=0?(r(1),o(10)):(r(a(i,-1)),o(a(s,1)))),i<=0&&r(a(s,-1)),s<=0&&o(a(i,1)),this._zero&&this.min!==this._suggestedMin&&i===a(this.min,0)&&r(a(i,-1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e={min:this._userMin,max:this._userMax},i=B_(e,this);return t.bounds==="ticks"&&qs(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":Gn(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=be(t),this._valueRange=be(this.max)-be(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(be(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}};jr.id="logarithmic";jr.defaults={ticks:{callback:Mr.formatters.logarithmic,major:{enabled:!0}}};function $a(n){let t=n.ticks;if(t.display&&n.display){let e=Vt(t.backdropPadding);return st(t.font&&t.font.size,ut.font.size)+e.height}return 0}function N_(n,t,e){return e=gt(e)?e:[e],{w:ra(n,t.string,e),h:e.length*t.lineHeight}}function Ou(n,t,e,i,s){return n===i||n===s?{start:t-e/2,end:t+e/2}:n<i||n>s?{start:t-e,end:t}:{start:t,end:t+e}}function H_(n){let t={l:n.left+n._padding.left,r:n.right-n._padding.right,t:n.top+n._padding.top,b:n.bottom-n._padding.bottom},e=Object.assign({},t),i=[],s=[],r=n._pointLabels.length,o=n.options.pointLabels,a=o.centerPointLabels?dt/r:0;for(let l=0;l<r;l++){let c=o.setContext(n.getPointLabelContext(l));s[l]=c.padding;let h=n.getPointPosition(l,n.drawingArea+s[l],a),f=jt(c.font),g=N_(n.ctx,f,n._pointLabels[l]);i[l]=g;let p=fe(n.getIndexAngle(l)+a),b=Math.round(Hn(p)),x=Ou(b,h.x,g.w,0,180),S=Ou(b,h.y,g.h,90,270);W_(e,t,p,x,S)}n.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),n._pointLabelItems=V_(n,i,s)}function W_(n,t,e,i,s){let r=Math.abs(Math.sin(e)),o=Math.abs(Math.cos(e)),a=0,l=0;i.start<t.l?(a=(t.l-i.start)/r,n.l=Math.min(n.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/r,n.r=Math.max(n.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/o,n.t=Math.min(n.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/o,n.b=Math.max(n.b,t.b+l))}function V_(n,t,e){let i=[],s=n._pointLabels.length,r=n.options,o=$a(r)/2,a=n.drawingArea,l=r.pointLabels.centerPointLabels?dt/s:0;for(let c=0;c<s;c++){let h=n.getPointPosition(c,a+o+e[c],l),f=Math.round(Hn(fe(h.angle+Rt))),g=t[c],p=q_(h.y,g.h,f),b=Y_(f),x=X_(h.x,g.w,b);i.push({x:h.x,y:p,textAlign:b,left:x,top:p,right:x+g.w,bottom:p+g.h})}return i}function Y_(n){return n===0||n===180?"center":n<180?"left":"right"}function X_(n,t,e){return e==="right"?n-=t:e==="center"&&(n-=t/2),n}function q_(n,t,e){return e===90||e===270?n-=t/2:(e>270||e<90)&&(n-=t),n}function G_(n,t){let{ctx:e,options:{pointLabels:i}}=n;for(let s=t-1;s>=0;s--){let r=i.setContext(n.getPointLabelContext(s)),o=jt(r.font),{x:a,y:l,textAlign:c,left:h,top:f,right:g,bottom:p}=n._pointLabelItems[s],{backdropColor:b}=r;if(!vt(b)){let x=Le(r.borderRadius),S=Vt(r.backdropPadding);e.fillStyle=b;let C=h-S.left,T=f-S.top,I=g-h+S.width,A=p-f+S.height;Object.values(x).some(F=>F!==0)?(e.beginPath(),en(e,{x:C,y:T,w:I,h:A,radius:x}),e.fill()):e.fillRect(C,T,I,A)}tn(e,n._pointLabels[s],a,l+o.lineHeight/2,o,{color:r.color,textAlign:c,textBaseline:"middle"})}}function Eu(n,t,e,i){let{ctx:s}=n;if(e)s.arc(n.xCenter,n.yCenter,t,0,kt);else{let r=n.getPointPosition(0,t);s.moveTo(r.x,r.y);for(let o=1;o<i;o++)r=n.getPointPosition(o,t),s.lineTo(r.x,r.y)}}function U_(n,t,e,i){let s=n.ctx,r=t.circular,{color:o,lineWidth:a}=t;!r&&!i||!o||!a||e<0||(s.save(),s.strokeStyle=o,s.lineWidth=a,s.setLineDash(t.borderDash),s.lineDashOffset=t.borderDashOffset,s.beginPath(),Eu(n,e,r,i),s.closePath(),s.stroke(),s.restore())}function K_(n,t,e){return Ne(n,{label:e,index:t,type:"pointLabel"})}var yi=class extends xs{constructor(t){super(t);this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=Vt($a(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=$t(t)&&!isNaN(t)?t:0,this.max=$t(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/$a(this.options))}generateTickLabels(t){xs.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,i)=>{let s=wt(this.options.pointLabels.callback,[e,i],this);return s||s===0?s:""}).filter((e,i)=>this.chart.getDataVisibility(i))}fit(){let t=this.options;t.display&&t.pointLabels.display?H_(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){let e=kt/(this._pointLabels.length||1),i=this.options.startAngle||0;return fe(t*e+Wt(i))}getDistanceFromCenterForValue(t){if(vt(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(vt(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){let i=e[t];return K_(this.getContext(),t,i)}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-Rt+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:r}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),Eu(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t=this.ctx,e=this.options,{angleLines:i,grid:s}=e,r=this._pointLabels.length,o,a,l;if(e.pointLabels.display&&G_(this,r),s.display&&this.ticks.forEach((c,h)=>{if(h!==0){a=this.getDistanceFromCenterForValue(c.value);let f=s.setContext(this.getContext(h-1));U_(this,f,a,r)}}),i.display){for(t.save(),o=r-1;o>=0;o--){let c=i.setContext(this.getPointLabelContext(o)),{color:h,lineWidth:f}=c;!f||!h||(t.lineWidth=f,t.strokeStyle=h,t.setLineDash(c.borderDash),t.lineDashOffset=c.borderDashOffset,a=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),l=this.getPointPosition(o,a),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(l.x,l.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){let t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;let s=this.getIndexAngle(0),r,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&!e.reverse)return;let c=i.setContext(this.getContext(l)),h=jt(c.font);if(r=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,o=t.measureText(a.label).width,t.fillStyle=c.backdropColor;let f=Vt(c.backdropPadding);t.fillRect(-o/2-f.left,-r-h.size/2-f.top,o+f.width,h.size+f.height)}tn(t,a.label,0,-r,h,{color:c.color})}),t.restore()}drawTitle(){}};yi.id="radialLinear";yi.defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Mr.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(n){return n},padding:5,centerPointLabels:!1}};yi.defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};yi.descriptors={angleLines:{_fallback:"grid"}};var zr={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},ve=Object.keys(zr);function Z_(n,t){return n-t}function Au(n,t){if(vt(t))return null;let e=n._adapter,{parser:i,round:s,isoWeekday:r}=n._parseOpts,o=t;return typeof i=="function"&&(o=i(o)),$t(o)||(o=typeof i=="string"?e.parse(o,i):e.parse(o)),o===null?null:(s&&(o=s==="week"&&(Ee(r)||r===!0)?e.startOf(o,"isoWeek",r):e.startOf(o,s)),+o)}function Ru(n,t,e,i){let s=ve.length;for(let r=ve.indexOf(n);r<s-1;++r){let o=zr[ve[r]],a=o.steps?o.steps:Number.MAX_SAFE_INTEGER;if(o.common&&Math.ceil((e-t)/(a*o.size))<=i)return ve[r]}return ve[s-1]}function J_(n,t,e,i,s){for(let r=ve.length-1;r>=ve.indexOf(e);r--){let o=ve[r];if(zr[o].common&&n._adapter.diff(s,i,o)>=t-1)return o}return ve[e?ve.indexOf(e):0]}function Q_(n){for(let t=ve.indexOf(n)+1,e=ve.length;t<e;++t)if(zr[ve[t]].common)return ve[t]}function Lu(n,t,e){if(!e)n[t]=!0;else if(e.length){let{lo:i,hi:s}=Vi(e,t),r=e[i]>=t?e[i]:e[s];n[r]=!0}}function ty(n,t,e,i){let s=n._adapter,r=+s.startOf(t[0].value,i),o=t[t.length-1].value,a,l;for(a=r;a<=o;a=+s.add(a,1,i))l=e[a],l>=0&&(t[l].major=!0);return t}function Fu(n,t,e){let i=[],s={},r=t.length,o,a;for(o=0;o<r;++o)a=t[o],s[a]=o,i.push({value:a,major:!1});return r===0||!e?i:ty(n,i,s,e)}var xi=class extends Sn{constructor(t){super(t);this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e){let i=t.time||(t.time={}),s=this._adapter=new xa._date(t.adapters.date);s.init(e),Bn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Au(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:r,minDefined:o,maxDefined:a}=this.getUserBounds();function l(c){!o&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(r=Math.max(r,c.max))}(!o||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=$t(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=$t(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let r=this.min,o=this.max,a=Go(s,r,o);return this._unit=e.unit||(i.autoSkip?Ru(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):J_(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:Q_(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),Fu(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t){let e=0,i=0,s,r;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?e=1-s:e=(this.getDecimalForValue(t[1])-s)/2,r=this.getDecimalForValue(t[t.length-1]),t.length===1?i=r:i=(r-this.getDecimalForValue(t[t.length-2]))/2);let o=t.length<3?.5:.25;e=Kt(e,0,o),i=Kt(i,0,o),this._offsets={start:e,end:i,factor:1/(e+1+i)}}_generate(){let t=this._adapter,e=this.min,i=this.max,s=this.options,r=s.time,o=r.unit||Ru(r.minUnit,e,i,this._getLabelCapacity(e)),a=st(r.stepSize,1),l=o==="week"?r.isoWeekday:!1,c=Ee(l)||l===!0,h={},f=e,g,p;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":o),t.diff(i,e,o)>1e5*a)throw new Error(e+" and "+i+" are too far apart with stepSize of "+a+" "+o);let b=s.ticks.source==="data"&&this.getDataTimestamps();for(g=f,p=0;g<i;g=+t.add(g,a,o),p++)Lu(h,g,b);return(g===i||s.bounds==="ticks"||p===1)&&Lu(h,g,b),Object.keys(h).sort((x,S)=>x-S).map(x=>+x)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}_tickFormatFunction(t,e,i,s){let r=this.options,o=r.time.displayFormats,a=this._unit,l=this._majorUnit,c=a&&o[a],h=l&&o[l],f=i[e],g=l&&h&&f&&f.major,p=this._adapter.format(t,s||(g?h:c)),b=r.ticks.callback;return b?wt(b,[p,e,i],this):p}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=Wt(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),o=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*o,h:i*o+a*r}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,Fu(this,[t],this._majorUnit),s),o=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,i;if(t.length)return t;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(e=0,i=s.length;e<i;++e)t=t.concat(s[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],e,i;if(t.length)return t;let s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(Au(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Zs(t.sort(Z_))}};xi.id="time";xi.defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",major:{enabled:!1}}};function Br(n,t,e){let i=0,s=n.length-1,r,o,a,l;e?(t>=n[i].pos&&t<=n[s].pos&&({lo:i,hi:s}=Re(n,"pos",t)),{pos:r,time:a}=n[i],{pos:o,time:l}=n[s]):(t>=n[i].time&&t<=n[s].time&&({lo:i,hi:s}=Re(n,"time",t)),{time:r,pos:a}=n[i],{time:o,pos:l}=n[s]);let c=o-r;return c?a+(l-a)*(t-r)/c:a}var Nr=class extends xi{constructor(t){super(t);this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Br(e,this.min),this._tableRange=Br(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let{min:e,max:i}=this,s=[],r=[],o,a,l,c,h;for(o=0,a=t.length;o<a;++o)c=t[o],c>=e&&c<=i&&s.push(c);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,a=s.length;o<a;++o)h=s[o+1],l=s[o-1],c=s[o],Math.round((h+l)/2)!==c&&r.push({time:c,pos:o/(a-1)});return r}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return e.length&&i.length?t=this.normalize(e.concat(i)):t=e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Br(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return Br(this._table,i*this._tableRange+this._minPos,!0)}};Nr.id="timeseries";Nr.defaults=xi.defaults;var ey=Object.freeze({__proto__:null,CategoryScale:ys,LinearScale:$r,LogarithmicScale:jr,RadialLinearScale:yi,TimeScale:xi,TimeSeriesScale:Nr}),Iu=[V0,Kv,F_,ey];var Fe=ze(require("obsidian"));var ny={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};xa._date.override(typeof Fe.moment=="function"?{_id:"moment",formats:function(){return ny},parse:function(n,t){return typeof n=="string"&&typeof t=="string"?n=(0,Fe.moment)(n,t):n instanceof Fe.moment||(n=(0,Fe.moment)(n)),n.isValid()?n.valueOf():null},format:function(n,t){return(0,Fe.moment)(n).format(t)},add:function(n,t,e){return(0,Fe.moment)(n).add(t,e).valueOf()},diff:function(n,t,e){return(0,Fe.moment)(n).diff((0,Fe.moment)(t),e)},startOf:function(n,t,e){return n=(0,Fe.moment)(n),t==="isoWeek"?(e=Math.trunc(Math.min(Math.max(0,e),6)),n.isoWeekday(e).startOf("day").valueOf()):n.startOf(t).valueOf()},endOf:function(n,t){return(0,Fe.moment)(n).endOf(t).valueOf()}}:{});var Gr=ze(require("obsidian"));var ju=ze($u());function ln(n,t=.25){if(typeof t!="number")throw"Provided alpha value is not a number";return n.map(e=>(0,ju.default)(e.trim()).alpha(t).hex())}function cn(n,t){var i,s;let e=t.createDiv({cls:"chart-error"});e.createEl("b",{text:"Couldn't render Chart:"}),e.createEl("pre").createEl("code",{text:(s=(i=n.toString)==null?void 0:i.call(n))!=null?s:n}),e.createEl("hr"),e.createEl("span").innerHTML="You might also want to look for further Errors in the Console: Press <kbd>CTRL</kbd> + <kbd>SHIFT</kbd> + <kbd>I</kbd> to open it."}function iy(n){for(var t=window.atob(n),e=t.length,i=new Uint8Array(e),s=0;s<e;s++)i[s]=t.charCodeAt(s);return i.buffer}async function zu(n,t,e,i,s){let r=await e.imageRenderer(n.getSelection(),s.imageSettings);console.log("image converted");let o=await t.vault.createBinary(await t.vault.getAvailablePathForAttachments(`Chart ${new Date().toDateString()}`,s.imageSettings.format.split("/").last(),i),iy(r));console.log("Image saved"),n.replaceSelection(t.fileManager.generateMarkdownLink(o,i.path))}var Wu=ze(require("obsidian")),Vu=ze(Hu());async function Na(n,t){let{labels:e,dataFields:i}=Ha(n.getSelection(),t),s=`\`\`\`chart
type: bar
labels: [${e}]
series:
${i.map(r=>`  - title: ${r.dataTitle}
    data: [${r.data}]`).join(`
`)}
width: 80%
beginAtZero: true
\`\`\``;n.replaceSelection(s)}function Ha(n,t,e){let i;try{i=Vu.Extractor.extractObject(n,t,!1)}catch(o){throw new Wu.Notice("Table malformed"),o}let s=Object.keys(Object.values(i)[0]),r=Object.keys(i).map(o=>({dataTitle:o,data:Object.values(i[o])}));return e&&(r=r.filter(o=>e.contains(o.dataTitle))),{labels:s,dataFields:r}}var Yu={modes:{point(n,t){return Wr(n,t,{intersect:!0})},nearest(n,t,e){return ly(n,t,e)},x(n,t,e){return Wr(n,t,{intersect:e.intersect,axis:"x"})},y(n,t,e){return Wr(n,t,{intersect:e.intersect,axis:"y"})}}};function Wa(n,t,e){return(Yu.modes[e.mode]||Yu.modes.nearest)(n,t,e)}function oy(n,t,e){return e!=="x"&&e!=="y"?n.inRange(t.x,t.y,"x",!0)||n.inRange(t.x,t.y,"y",!0):n.inRange(t.x,t.y,e,!0)}function ay(n,t,e){return e==="x"?{x:n.x,y:t.y}:e==="y"?{x:t.x,y:n.y}:t}function Wr(n,t,e){return n.visibleElements.filter(i=>e.intersect?i.inRange(t.x,t.y):oy(i,t,e.axis))}function ly(n,t,e){let i=Number.POSITIVE_INFINITY;return Wr(n,t,e).reduce((s,r)=>{let o=r.getCenterPoint(),a=ay(t,o,e.axis),l=Ze(t,a);return l<i?(s=[r],i=l):l===i&&s.push(r),s},[]).sort((s,r)=>s._index-r._index).slice(0,1)}var cy=(n,t)=>t>n||n.length>t.length&&n.slice(0,t.length)===t,Zn=.001,Vr=(n,t,e)=>Math.min(e,Math.max(t,n));function hy(n,t,e){for(let i of Object.keys(n))n[i]=Vr(n[i],t,e);return n}function uy(n,t,e,i){if(!n||!t||e<=0)return!1;let s=i/2;return Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2)<=Math.pow(e+s,2)}function Xu(n,{x:t,y:e,x2:i,y2:s},r,o){let a=o/2,l=n.x>=t-a-Zn&&n.x<=i+a+Zn,c=n.y>=e-a-Zn&&n.y<=s+a+Zn;return r==="x"?l:(r==="y"||l)&&c}function ki(n,t){let{centerX:e,centerY:i}=n.getProps(["centerX","centerY"],t);return{x:e,y:i}}function fy(n,t,e,i=!0){let s=e.split("."),r=0;for(let o of t.split(".")){let a=s[r++];if(parseInt(o,10)<parseInt(a,10))break;if(cy(a,o)){if(i)throw new Error(`${n} v${e} is not supported. v${t} or newer is required.`);return!1}}return!0}var qu=n=>typeof n=="string"&&n.endsWith("%"),Gu=n=>parseFloat(n)/100,Uu=n=>Vr(Gu(n),0,1);function Va(n,t){return t==="start"?0:t==="end"?n:qu(t)?Uu(t)*n:n/2}function Pn(n,t,e=!0){return typeof t=="number"?t:qu(t)?(e?Uu(t):Gu(t))*n:n}function dy(n,t){let{x:e,width:i}=n,s=t.textAlign;return s==="center"?e+i/2:s==="end"||s==="right"?e+i:e}function Ya(n,t="center"){return at(n)?{x:st(n.x,t),y:st(n.y,t)}:(n=st(n,t),{x:n,y:n})}function Ku(n){return n&&(Nt(n.xValue)||Nt(n.yValue))}function ws(n,t,e,i=!1){let s=e.init;if(s){if(s===!0)return Ju(t,i)}else return;return py(t,i,wt(s,[{chart:n,properties:t,options:e}]))}function Zu(n,t,e){let i=!1;return t.forEach(s=>{ce(n[s])?(i=!0,e[s]=n[s]):Nt(e[s])&&delete e[s]}),i}function Ju({centerX:n,centerY:t},e){return e?{centerX:n,centerY:t,radius:0,width:0,height:0}:{x:n,y:t,x2:n,y2:t,width:0,height:0}}function py(n,t,e){if(e===!0)return Ju(n,t);if(at(e))return e}var Xa=new Map,gy=n=>isNaN(n)||n<=0,my=n=>n.reduce(function(t,e){return t+=e.string,t},"");function Yr(n){if(n&&typeof n=="object"){let t=n.toString();return t==="[object HTMLImageElement]"||t==="[object HTMLCanvasElement]"}}function qa(n,{x:t,y:e},i){i&&(n.translate(t,e),n.rotate(Wt(i)),n.translate(-t,-e))}function Tn(n,t){if(t&&t.borderWidth)return n.lineCap=t.borderCapStyle,n.setLineDash(t.borderDash),n.lineDashOffset=t.borderDashOffset,n.lineJoin=t.borderJoinStyle,n.lineWidth=t.borderWidth,n.strokeStyle=t.borderColor,!0}function Si(n,t){n.shadowColor=t.backgroundShadowColor,n.shadowBlur=t.shadowBlur,n.shadowOffsetX=t.shadowOffsetX,n.shadowOffsetY=t.shadowOffsetY}function Ga(n,t){let e=t.content;if(Yr(e))return{width:Pn(e.width,t.width),height:Pn(e.height,t.height)};let i=t.font,s=gt(i)?i.map(l=>jt(l)):[jt(i)],r=t.textStrokeWidth,o=gt(e)?e:[e],a=o.join()+my(s)+r+(n._measureText?"-spriting":"");return Xa.has(a)||Xa.set(a,xy(n,o,s,r)),Xa.get(a)}function Qu(n,t,e){let{x:i,y:s,width:r,height:o}=t;n.save(),Si(n,e);let a=Tn(n,e);n.fillStyle=e.backgroundColor,n.beginPath(),en(n,{x:i,y:s,w:r,h:o,radius:hy(Le(e.borderRadius),0,Math.min(r,o)/2)}),n.closePath(),n.fill(),a&&(n.shadowColor=e.borderShadowColor,n.stroke()),n.restore()}function by(n,t,e){let i=e.content;if(Yr(i)){n.save(),n.globalAlpha=Sy(e.opacity,i.style.opacity),n.drawImage(i,t.x,t.y,t.width,t.height),n.restore();return}let s=gt(i)?i:[i],r=e.font,o=gt(r)?r.map(f=>jt(f)):[jt(r)],a=e.color,l=gt(a)?a:[a],c=dy(t,e),h=t.y+e.textStrokeWidth/2;n.save(),n.textBaseline="middle",n.textAlign=e.textAlign,vy(n,e)&&wy(n,{x:c,y:h},s,o),ky(n,{x:c,y:h},s,{fonts:o,colors:l}),n.restore()}function vy(n,t){if(t.textStrokeWidth>0)return n.lineJoin="round",n.miterLimit=2,n.lineWidth=t.textStrokeWidth,n.strokeStyle=t.textStrokeColor,!0}function _y(n,t,e,i){let{radius:s,options:r}=t,o=r.pointStyle,a=r.rotation,l=(a||0)*Wi;if(Yr(o)){n.save(),n.translate(e,i),n.rotate(l),n.drawImage(o,-o.width/2,-o.height/2,o.width,o.height),n.restore();return}gy(s)||yy(n,{x:e,y:i,radius:s,rotation:a,style:o,rad:l})}function yy(n,{x:t,y:e,radius:i,rotation:s,style:r,rad:o}){let a,l,c,h;switch(n.beginPath(),r){default:n.arc(t,e,i,0,kt),n.closePath();break;case"triangle":n.moveTo(t+Math.sin(o)*i,e-Math.cos(o)*i),o+=gi,n.lineTo(t+Math.sin(o)*i,e-Math.cos(o)*i),o+=gi,n.lineTo(t+Math.sin(o)*i,e-Math.cos(o)*i),n.closePath();break;case"rectRounded":h=i*.516,c=i-h,a=Math.cos(o+ke)*c,l=Math.sin(o+ke)*c,n.arc(t-a,e-l,h,o-dt,o-Rt),n.arc(t+l,e-a,h,o-Rt,o),n.arc(t+a,e+l,h,o,o+Rt),n.arc(t-l,e+a,h,o+Rt,o+dt),n.closePath();break;case"rect":if(!s){c=Math.SQRT1_2*i,n.rect(t-c,e-c,2*c,2*c);break}o+=ke;case"rectRot":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+l,e-a),n.lineTo(t+a,e+l),n.lineTo(t-l,e+a),n.closePath();break;case"crossRot":o+=ke;case"cross":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"star":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a),o+=ke,a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l),n.moveTo(t+l,e-a),n.lineTo(t-l,e+a);break;case"line":a=Math.cos(o)*i,l=Math.sin(o)*i,n.moveTo(t-a,e-l),n.lineTo(t+a,e+l);break;case"dash":n.moveTo(t,e),n.lineTo(t+Math.cos(o)*i,e+Math.sin(o)*i);break}n.fill()}function xy(n,t,e,i){n.save();let s=t.length,r=0,o=i;for(let a=0;a<s;a++){let l=e[Math.min(a,e.length-1)];n.font=l.string;let c=t[a];r=Math.max(r,n.measureText(c).width+i),o+=l.lineHeight}return n.restore(),{width:r,height:o}}function wy(n,{x:t,y:e},i,s){n.beginPath();let r=0;i.forEach(function(o,a){let l=s[Math.min(a,s.length-1)],c=l.lineHeight;n.font=l.string,n.strokeText(o,t,e+c/2+r),r+=c}),n.stroke()}function ky(n,{x:t,y:e},i,{fonts:s,colors:r}){let o=0;i.forEach(function(a,l){let c=r[Math.min(l,r.length-1)],h=s[Math.min(l,s.length-1)],f=h.lineHeight;n.beginPath(),n.font=h.string,n.fillStyle=c,n.fillText(a,t,e+f/2+o),o+=f,n.fill()})}function Sy(n,t){let e=Ee(n)?n:t;return Ee(e)?Vr(e,0,1):1}var tf={xScaleID:{min:"xMin",max:"xMax",start:"left",end:"right",startProp:"x",endProp:"x2"},yScaleID:{min:"yMin",max:"yMax",start:"bottom",end:"top",startProp:"y",endProp:"y2"}};function Mi(n,t,e){return t=typeof t=="number"?t:n.parse(t),$t(t)?n.getPixelForValue(t):e}function Jn(n,t,e){let i=t[e];if(i||e==="scaleID")return i;let s=e.charAt(0),r=Object.values(n).filter(o=>o.axis&&o.axis===s);return r.length?r[0].id:s}function ef(n,t){if(n){let e=n.options.reverse,i=Mi(n,t.min,e?t.end:t.start),s=Mi(n,t.max,e?t.start:t.end);return{start:i,end:s}}}function nf(n,t){let{chartArea:e,scales:i}=n,s=i[Jn(i,t,"xScaleID")],r=i[Jn(i,t,"yScaleID")],o=e.width/2,a=e.height/2;return s&&(o=Mi(s,t.xValue,s.left+s.width/2)),r&&(a=Mi(r,t.yValue,r.top+r.height/2)),{x:o,y:a}}function Ua(n,t){let e=n.scales,i=e[Jn(e,t,"xScaleID")],s=e[Jn(e,t,"yScaleID")];if(!i&&!s)return{};let{left:r,right:o}=i||n.chartArea,{top:a,bottom:l}=s||n.chartArea,c=of(i,{min:t.xMin,max:t.xMax,start:r,end:o});r=c.start,o=c.end;let h=of(s,{min:t.yMin,max:t.yMax,start:l,end:a});return a=h.start,l=h.end,{x:r,y:a,x2:o,y2:l,width:o-r,height:l-a,centerX:r+(o-r)/2,centerY:a+(l-a)/2}}function sf(n,t){if(!Ku(t)){let e=Ua(n,t),i=t.radius;(!i||isNaN(i))&&(i=Math.min(e.width,e.height)/2,t.radius=i);let s=i*2,r=e.centerX+t.xAdjust,o=e.centerY+t.yAdjust;return{x:r-i,y:o-i,x2:r+i,y2:o+i,centerX:r,centerY:o,width:s,height:s,radius:i}}return Cy(n,t)}function My(n,t){let{scales:e,chartArea:i}=n,s=e[t.scaleID],r={x:i.left,y:i.top,x2:i.right,y2:i.bottom};return s?Py(s,r,t):Ty(e,r,t),r}function rf(n,t,e){let i=Ua(n,t);return i.initProperties=ws(n,i,t,e),i.elements=[{type:"label",optionScope:"label",properties:Ey(n,i,t),initProperties:i.initProperties}],i}function Cy(n,t){let e=nf(n,t),i=t.radius*2;return{x:e.x-t.radius+t.xAdjust,y:e.y-t.radius+t.yAdjust,x2:e.x+t.radius+t.xAdjust,y2:e.y+t.radius+t.yAdjust,centerX:e.x+t.xAdjust,centerY:e.y+t.yAdjust,radius:t.radius,width:i,height:i}}function of(n,t){let e=ef(n,t)||t;return{start:Math.min(e.start,e.end),end:Math.max(e.start,e.end)}}function Py(n,t,e){let i=Mi(n,e.value,NaN),s=Mi(n,e.endValue,i);n.isHorizontal()?(t.x=i,t.x2=s):(t.y=i,t.y2=s)}function Ty(n,t,e){for(let i of Object.keys(tf)){let s=n[Jn(n,e,i)];if(s){let{min:r,max:o,start:a,end:l,startProp:c,endProp:h}=tf[i],f=ef(s,{min:e[r],max:e[o],start:s[a],end:s[l]});t[c]=f.start,t[h]=f.end}}}function Dy({properties:n,options:t},e,i,s){let{x:r,x2:o,width:a}=n;return af({start:r,end:o,size:a,borderWidth:t.borderWidth},{position:i.x,padding:{start:s.left,end:s.right},adjust:t.label.xAdjust,size:e.width})}function Oy({properties:n,options:t},e,i,s){let{y:r,y2:o,height:a}=n;return af({start:r,end:o,size:a,borderWidth:t.borderWidth},{position:i.y,padding:{start:s.top,end:s.bottom},adjust:t.label.yAdjust,size:e.height})}function af(n,t){let{start:e,end:i,borderWidth:s}=n,{position:r,padding:{start:o,end:a},adjust:l}=t,c=i-s-e-o-a-t.size;return e+s/2+l+Va(c,r)}function Ey(n,t,e){let i=e.label;i.backgroundColor="transparent",i.callout.display=!1;let s=Ya(i.position),r=Vt(i.padding),o=Ga(n.ctx,i),a=Dy({properties:t,options:e},o,s,r),l=Oy({properties:t,options:e},o,s,r),c=o.width+r.width,h=o.height+r.height;return{x:a,y:l,x2:a+c,y2:l+h,width:c,height:h,centerX:a+c/2,centerY:l+h/2,rotation:i.rotation}}function Qn(n,t,e){let i=Math.cos(e),s=Math.sin(e),r=t.x,o=t.y;return{x:r+i*(n.x-r)-s*(n.y-o),y:o+s*(n.x-r)+i*(n.y-o)}}var Ka=["enter","leave"],Za=Ka.concat("click");function Ay(n,t,e){t.listened=Zu(e,Za,t.listeners),t.moveListened=!1,t._getElements=Wa,Ka.forEach(i=>{ce(e[i])&&(t.moveListened=!0)}),(!t.listened||!t.moveListened)&&t.annotations.forEach(i=>{!t.listened&&ce(i.click)&&(t.listened=!0),t.moveListened||Ka.forEach(s=>{ce(i[s])&&(t.listened=!0,t.moveListened=!0)})})}function Ry(n,t,e){if(n.listened)switch(t.type){case"mousemove":case"mouseout":return Ly(n,t,e);case"click":return Fy(n,t,e)}}function Ly(n,t,e){if(!n.moveListened)return;let i;t.type==="mousemove"?i=Wa(n,t,e.interaction):i=[];let s=n.hovered;n.hovered=i;let r={state:n,event:t},o=lf(r,"leave",s,i);return lf(r,"enter",i,s)||o}function lf({state:n,event:t},e,i,s){let r;for(let o of i)s.indexOf(o)<0&&(r=cf(o.options[e]||n.listeners[e],o,t)||r);return r}function Fy(n,t,e){let i=n.listeners,s=Wa(n,t,e.interaction),r;for(let o of s)r=cf(o.options.click||i.click,o,t)||r;return r}function cf(n,t,e){return wt(n,[t.$context,e])===!0}var Xr=["afterDraw","beforeDraw"];function Iy(n,t,e){let i=t.visibleElements;t.hooked=Zu(e,Xr,t.hooks),t.hooked||i.forEach(s=>{t.hooked||Xr.forEach(r=>{ce(s.options[r])&&(t.hooked=!0)})})}function hf(n,t,e){if(n.hooked){let i=t.options[e]||n.hooks[e];return wt(i,[t.$context])}}function $y(n,t,e){let i=Hy(n.scales,t,e),s=uf(t,i,"min","suggestedMin");s=uf(t,i,"max","suggestedMax")||s,s&&ce(t.handleTickRangeOptions)&&t.handleTickRangeOptions()}function jy(n,t){for(let e of n)By(e,t)}function uf(n,t,e,i){if($t(t[e])&&!zy(n.options,e,i)){let s=n[e]!==t[e];return n[e]=t[e],s}}function zy(n,t,e){return Nt(n[t])||Nt(n[e])}function By(n,t){for(let e of["scaleID","xScaleID","yScaleID"]){let i=Jn(t,n,e);i&&!t[i]&&Ny(n,e)&&console.warn(`No scale found with id '${i}' for annotation '${n.id}'`)}}function Ny(n,t){if(t==="scaleID")return!0;let e=t.charAt(0);for(let i of["Min","Max","Value"])if(Nt(n[e+i]))return!0;return!1}function Hy(n,t,e){let i=t.axis,s=t.id,r=i+"ScaleID",o={min:st(t.min,Number.NEGATIVE_INFINITY),max:st(t.max,Number.POSITIVE_INFINITY)};for(let a of e)a.scaleID===s?ff(a,t,["value","endValue"],o):Jn(n,a,r)===s&&ff(a,t,[i+"Min",i+"Max",i+"Value"],o);return o}function ff(n,t,e,i){for(let s of e){let r=n[s];if(Nt(r)){let o=t.parse(r);i.min=Math.min(i.min,o),i.max=Math.max(i.max,o)}}}var ti=class extends Zt{inRange(t,e,i,s){let{x:r,y:o}=Qn({x:t,y:e},this.getCenterPoint(s),Wt(-this.options.rotation));return Xu({x:r,y:o},this.getProps(["x","y","x2","y2"],s),i,this.options.borderWidth)}getCenterPoint(t){return ki(this,t)}draw(t){t.save(),qa(t,this.getCenterPoint(),this.options.rotation),Qu(t,this,this.options),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return rf(t,e)}};ti.id="boxAnnotation";ti.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,label:{backgroundColor:"transparent",borderWidth:0,callout:{display:!1},color:"black",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,opacity:void 0,padding:6,position:"center",rotation:void 0,textAlign:"start",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};ti.defaultRoutes={borderColor:"color",backgroundColor:"color"};ti.descriptors={label:{_fallback:!0}};var df=["left","bottom","top","right"],Ci=class extends Zt{inRange(t,e,i,s){let{x:r,y:o}=Qn({x:t,y:e},this.getCenterPoint(s),Wt(-this.rotation));return Xu({x:r,y:o},this.getProps(["x","y","x2","y2"],s),i,this.options.borderWidth)}getCenterPoint(t){return ki(this,t)}draw(t){let e=this.options,i=!Nt(this._visible)||this._visible;!e.display||!e.content||!i||(t.save(),qa(t,this.getCenterPoint(),this.rotation),Vy(t,this),Qu(t,this,e),by(t,Zy(this),e),t.restore())}resolveElementProperties(t,e){let i;if(Ku(e))i=nf(t,e);else{let{centerX:a,centerY:l}=Ua(t,e);i={x:a,y:l}}let s=Vt(e.padding),r=Ga(t.ctx,e),o=Wy(i,r,e,s);return{initProperties:ws(t,o,e),pointX:i.x,pointY:i.y,...o,rotation:e.rotation}}};Ci.id="labelAnnotation";Ci.defaults={adjustScaleRange:!0,backgroundColor:"transparent",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:0,borderShadowColor:"transparent",borderWidth:0,callout:{borderCapStyle:"butt",borderColor:void 0,borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:1,display:!1,margin:5,position:"auto",side:5,start:"50%"},color:"black",content:null,display:!0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:void 0},height:void 0,init:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Ci.defaultRoutes={borderColor:"color"};function Wy(n,t,e,i){let s=t.width+i.width+e.borderWidth,r=t.height+i.height+e.borderWidth,o=Ya(e.position,"center"),a=pf(n.x,s,e.xAdjust,o.x),l=pf(n.y,r,e.yAdjust,o.y);return{x:a,y:l,x2:a+s,y2:l+r,width:s,height:r,centerX:a+s/2,centerY:l+r/2}}function pf(n,t,e=0,i){return n-Va(t,i)+e}function Vy(n,t){let{pointX:e,pointY:i,options:s}=t,r=s.callout,o=r&&r.display&&Uy(t,r);if(!o||Jy(t,r,o))return;if(n.save(),n.beginPath(),!Tn(n,r))return n.restore();let{separatorStart:l,separatorEnd:c}=Yy(t,o),{sideStart:h,sideEnd:f}=qy(t,o,l);(r.margin>0||s.borderWidth===0)&&(n.moveTo(l.x,l.y),n.lineTo(c.x,c.y)),n.moveTo(h.x,h.y),n.lineTo(f.x,f.y);let g=Qn({x:e,y:i},t.getCenterPoint(),Wt(-t.rotation));n.lineTo(g.x,g.y),n.stroke(),n.restore()}function Yy(n,t){let{x:e,y:i,x2:s,y2:r}=n,o=Xy(n,t),a,l;return t==="left"||t==="right"?(a={x:e+o,y:i},l={x:a.x,y:r}):(a={x:e,y:i+o},l={x:s,y:a.y}),{separatorStart:a,separatorEnd:l}}function Xy(n,t){let{width:e,height:i,options:s}=n,r=s.callout.margin+s.borderWidth/2;return t==="right"?e+r:t==="bottom"?i+r:-r}function qy(n,t,e){let{y:i,width:s,height:r,options:o}=n,a=o.callout.start,l=Gy(t,o.callout),c,h;return t==="left"||t==="right"?(c={x:e.x,y:i+Pn(r,a)},h={x:c.x+l,y:c.y}):(c={x:e.x+Pn(s,a),y:e.y},h={x:c.x,y:c.y+l}),{sideStart:c,sideEnd:h}}function Gy(n,t){let e=t.side;return n==="left"||n==="top"?-e:e}function Uy(n,t){let e=t.position;return df.includes(e)?e:Ky(n,t)}function Ky(n,t){let{x:e,y:i,x2:s,y2:r,width:o,height:a,pointX:l,pointY:c,centerX:h,centerY:f,rotation:g}=n,p={x:h,y:f},b=t.start,x=Pn(o,b),S=Pn(a,b),C=[e,e+x,e+x,s],T=[i+S,r,i,r],I=[];for(let A=0;A<4;A++){let F=Qn({x:C[A],y:T[A]},p,Wt(g));I.push({position:df[A],distance:Ze(F,{x:l,y:c})})}return I.sort((A,F)=>A.distance-F.distance)[0].position}function Zy({x:n,y:t,width:e,height:i,options:s}){let r=s.borderWidth/2,o=Vt(s.padding);return{x:n+o.left+r,y:t+o.top+r,width:e-o.left-o.right-s.borderWidth,height:i-o.top-o.bottom-s.borderWidth}}function Jy(n,t,e){let{pointX:i,pointY:s}=n,r=t.margin,o=i,a=s;return e==="left"?o+=r:e==="right"?o-=r:e==="top"?a+=r:e==="bottom"&&(a-=r),n.inRange(o,a)}var Ja=(n,t,e)=>({x:n.x+e*(t.x-n.x),y:n.y+e*(t.y-n.y)}),Qa=(n,t,e)=>Ja(t,e,Math.abs((n-t.y)/(e.y-t.y))).x,gf=(n,t,e)=>Ja(t,e,Math.abs((n-t.x)/(e.x-t.x))).y,ks=n=>n*n,Qy=(n,t,{x:e,y:i,x2:s,y2:r},o)=>o==="y"?{start:Math.min(i,r),end:Math.max(i,r),value:t}:{start:Math.min(e,s),end:Math.max(e,s),value:n},mf=(n,t,e,i)=>(1-i)*(1-i)*n+2*(1-i)*i*t+i*i*e,tl=(n,t,e,i)=>({x:mf(n.x,t.x,e.x,i),y:mf(n.y,t.y,e.y,i)}),bf=(n,t,e,i)=>2*(1-i)*(t-n)+2*i*(e-t),vf=(n,t,e,i)=>-Math.atan2(bf(n.x,t.x,e.x,i),bf(n.y,t.y,e.y,i))+.5*dt,Pi=class extends Zt{inRange(t,e,i,s){let r=this.options.borderWidth/2;if(i!=="x"&&i!=="y"){let o={mouseX:t,mouseY:e},{path:a,ctx:l}=this;if(a){Tn(l,this.options);let{chart:h}=this.$context,f=t*h.currentDevicePixelRatio,g=e*h.currentDevicePixelRatio,p=l.isPointInStroke(a,f,g)||el(this,o,s);return l.restore(),p}let c=ks(r);return ix(this,o,c,s)||el(this,o,s)}return tx(this,{mouseX:t,mouseY:e},i,{hBorderWidth:r,useFinalPosition:s})}getCenterPoint(t){return ki(this,t)}draw(t){let{x:e,y:i,x2:s,y2:r,cp:o,options:a}=this;if(t.save(),!Tn(t,a))return t.restore();Si(t,a);let l=Math.sqrt(Math.pow(s-e,2)+Math.pow(r-i,2));if(a.curve&&o)return ux(t,this,o,l),t.restore();let{startOpts:c,endOpts:h,startAdjust:f,endAdjust:g}=kf(this),p=Math.atan2(r-i,s-e);t.translate(e,i),t.rotate(p),t.beginPath(),t.moveTo(0+f,0),t.lineTo(l-g,0),t.shadowColor=a.borderShadowColor,t.stroke(),nl(t,0,f,c),nl(t,l,-g,h),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){let i=My(t,e),{x:s,y:r,x2:o,y2:a}=i,l=ex(i,t.chartArea),c=l?nx({x:s,y:r},{x:o,y:a},t.chartArea):{x:s,y:r,x2:o,y2:a,width:Math.abs(o-s),height:Math.abs(a-r)};if(c.centerX=(o+s)/2,c.centerY=(a+r)/2,c.initProperties=ws(t,c,e),e.curve){let f={x:c.x,y:c.y},g={x:c.x2,y:c.y2};c.cp=hx(c,e,Ze(f,g))}let h=sx(t,c,e.label);return h._visible=l,c.elements=[{type:"label",optionScope:"label",properties:h,initProperties:c.initProperties}],c}};Pi.id="lineAnnotation";var _f={backgroundColor:void 0,backgroundShadowColor:void 0,borderColor:void 0,borderDash:void 0,borderDashOffset:void 0,borderShadowColor:void 0,borderWidth:void 0,display:void 0,fill:void 0,length:void 0,shadowBlur:void 0,shadowOffsetX:void 0,shadowOffsetY:void 0,width:void 0};Pi.defaults={adjustScaleRange:!0,arrowHeads:{display:!1,end:Object.assign({},_f),fill:!1,length:12,start:Object.assign({},_f),width:6},borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:2,curve:!1,controlPoint:{y:"-50%"},display:!0,endValue:void 0,init:void 0,label:{backgroundColor:"rgba(0,0,0,0.8)",backgroundShadowColor:"transparent",borderCapStyle:"butt",borderColor:"black",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderRadius:6,borderShadowColor:"transparent",borderWidth:0,callout:Object.assign({},Ci.defaults.callout),color:"#fff",content:null,display:!1,drawTime:void 0,font:{family:void 0,lineHeight:void 0,size:void 0,style:void 0,weight:"bold"},height:void 0,opacity:void 0,padding:6,position:"center",rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,textAlign:"center",textStrokeColor:void 0,textStrokeWidth:0,width:void 0,xAdjust:0,yAdjust:0,z:void 0},scaleID:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,value:void 0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Pi.descriptors={arrowHeads:{start:{_fallback:!0},end:{_fallback:!0},_fallback:!0}};Pi.defaultRoutes={borderColor:"color"};function tx(n,{mouseX:t,mouseY:e},i,{hBorderWidth:s,useFinalPosition:r}){let o=Qy(t,e,n.getProps(["x","y","x2","y2"],r),i);return o.value>=o.start-s&&o.value<=o.end+s||el(n,{mouseX:t,mouseY:e},r,i)}function ex({x:n,y:t,x2:e,y2:i},{top:s,right:r,bottom:o,left:a}){return!(n<a&&e<a||n>r&&e>r||t<s&&i<s||t>o&&i>o)}function yf({x:n,y:t},e,{top:i,right:s,bottom:r,left:o}){return n<o&&(t=gf(o,{x:n,y:t},e),n=o),n>s&&(t=gf(s,{x:n,y:t},e),n=s),t<i&&(n=Qa(i,{x:n,y:t},e),t=i),t>r&&(n=Qa(r,{x:n,y:t},e),t=r),{x:n,y:t}}function nx(n,t,e){let{x:i,y:s}=yf(n,t,e),{x:r,y:o}=yf(t,n,e);return{x:i,y:s,x2:r,y2:o,width:Math.abs(r-i),height:Math.abs(o-s)}}function ix(n,{mouseX:t,mouseY:e},i=Zn,s){let{x:r,y:o,x2:a,y2:l}=n.getProps(["x","y","x2","y2"],s),c=a-r,h=l-o,f=ks(c)+ks(h),g=f===0?-1:((t-r)*c+(e-o)*h)/f,p,b;return g<0?(p=r,b=o):g>1?(p=a,b=l):(p=r+g*c,b=o+g*h),ks(t-p)+ks(e-b)<=i}function el(n,{mouseX:t,mouseY:e},i,s){let r=n.label;return r.options.display&&r.inRange(t,e,s,i)}function sx(n,t,e){let i=e.borderWidth,s=Vt(e.padding),r=Ga(n.ctx,e),o=r.width+s.width+i,a=r.height+s.height+i;return ox(t,e,{width:o,height:a,padding:s},n.chartArea)}function rx(n){let{x:t,y:e,x2:i,y2:s}=n,r=Math.atan2(s-e,i-t);return r>dt/2?r-dt:r<dt/-2?r+dt:r}function ox(n,t,e,i){let{width:s,height:r,padding:o}=e,{xAdjust:a,yAdjust:l}=t,c={x:n.x,y:n.y},h={x:n.x2,y:n.y2},f=t.rotation==="auto"?rx(n):Wt(t.rotation),g=ax(s,r,f),p=lx(n,t,{labelSize:g,padding:o},i),b=n.cp?tl(c,n.cp,h,p):Ja(c,h,p),x={size:g.w,min:i.left,max:i.right,padding:o.left},S={size:g.h,min:i.top,max:i.bottom,padding:o.top},C=wf(b.x,x)+a,T=wf(b.y,S)+l;return{x:C-s/2,y:T-r/2,x2:C+s/2,y2:T+r/2,centerX:C,centerY:T,pointX:b.x,pointY:b.y,width:s,height:r,rotation:Hn(f)}}function ax(n,t,e){let i=Math.cos(e),s=Math.sin(e);return{w:Math.abs(n*i)+Math.abs(t*s),h:Math.abs(n*s)+Math.abs(t*i)}}function lx(n,t,e,i){let s,r=cx(n,i);return t.position==="start"?s=xf({w:n.x2-n.x,h:n.y2-n.y},e,t,r):t.position==="end"?s=1-xf({w:n.x-n.x2,h:n.y-n.y2},e,t,r):s=Va(1,t.position),s}function xf(n,t,e,i){let{labelSize:s,padding:r}=t,o=n.w*i.dx,a=n.h*i.dy,l=o>0&&(s.w/2+r.left-i.x)/o,c=a>0&&(s.h/2+r.top-i.y)/a;return Vr(Math.max(l,c),0,.25)}function cx(n,t){let{x:e,x2:i,y:s,y2:r}=n,o=Math.min(s,r)-t.top,a=Math.min(e,i)-t.left,l=t.bottom-Math.max(s,r),c=t.right-Math.max(e,i);return{x:Math.min(a,c),y:Math.min(o,l),dx:a<=c?1:-1,dy:o<=l?1:-1}}function wf(n,t){let{size:e,min:i,max:s,padding:r}=t,o=e/2;return e>s-i?(s+i)/2:(i>=n-r-o&&(n=i+r+o),s<=n+r+o&&(n=s-r-o),n)}function kf(n){let t=n.options,e=t.arrowHeads&&t.arrowHeads.start,i=t.arrowHeads&&t.arrowHeads.end;return{startOpts:e,endOpts:i,startAdjust:Sf(n,e),endAdjust:Sf(n,i)}}function Sf(n,t){if(!t||!t.display)return 0;let{length:e,width:i}=t,s=n.options.borderWidth/2,r={x:e,y:i+s};return Math.abs(Qa(0,r,{x:0,y:s}))}function nl(n,t,e,i){if(!i||!i.display)return;let{length:s,width:r,fill:o,backgroundColor:a,borderColor:l}=i,c=Math.abs(t-s)+e;n.beginPath(),Si(n,i),Tn(n,i),n.moveTo(c,-r),n.lineTo(t+e,0),n.lineTo(c,r),o===!0?(n.fillStyle=a||l,n.closePath(),n.fill(),n.shadowColor="transparent"):n.shadowColor=i.borderShadowColor,n.stroke()}function hx(n,t,e){let{x:i,y:s,x2:r,y2:o,centerX:a,centerY:l}=n,c=Math.atan2(o-s,r-i),h=Ya(t.controlPoint,0),f={x:a+Pn(e,h.x,!1),y:l+Pn(e,h.y,!1)};return Qn(f,{x:a,y:l},c)}function Mf(n,{x:t,y:e},{angle:i,adjust:s},r){!r||!r.display||(n.save(),n.translate(t,e),n.rotate(i),nl(n,0,-s,r),n.restore())}function ux(n,t,e,i){let{x:s,y:r,x2:o,y2:a,options:l}=t,{startOpts:c,endOpts:h,startAdjust:f,endAdjust:g}=kf(t),p={x:s,y:r},b={x:o,y:a},x=vf(p,e,b,0),S=vf(p,e,b,1)-dt,C=tl(p,e,b,f/i),T=tl(p,e,b,1-g/i),I=new Path2D;n.beginPath(),I.moveTo(C.x,C.y),I.quadraticCurveTo(e.x,e.y,T.x,T.y),n.shadowColor=l.borderShadowColor,n.stroke(I),t.path=I,t.ctx=n,Mf(n,C,{angle:x,adjust:f},c),Mf(n,T,{angle:S,adjust:g},h)}var Ti=class extends Zt{inRange(t,e,i,s){let r=this.options.rotation,o=this.options.borderWidth;if(i!=="x"&&i!=="y")return fx({x:t,y:e},this.getProps(["width","height","centerX","centerY"],s),r,o);let{x:a,y:l,x2:c,y2:h}=this.getProps(["x","y","x2","y2"],s),f=o/2,g=i==="y"?{start:l,end:h}:{start:a,end:c},p=Qn({x:t,y:e},this.getCenterPoint(s),Wt(-r));return p[i]>=g.start-f-Zn&&p[i]<=g.end+f+Zn}getCenterPoint(t){return ki(this,t)}draw(t){let{width:e,height:i,centerX:s,centerY:r,options:o}=this;t.save(),qa(t,this.getCenterPoint(),o.rotation),Si(t,this.options),t.beginPath(),t.fillStyle=o.backgroundColor;let a=Tn(t,o);t.ellipse(s,r,i/2,e/2,dt/2,0,2*dt),t.fill(),a&&(t.shadowColor=o.borderShadowColor,t.stroke()),t.restore()}get label(){return this.elements&&this.elements[0]}resolveElementProperties(t,e){return rf(t,e,!0)}};Ti.id="ellipseAnnotation";Ti.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,label:Object.assign({},ti.defaults.label),rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xMax:void 0,xMin:void 0,xScaleID:void 0,yMax:void 0,yMin:void 0,yScaleID:void 0,z:0};Ti.defaultRoutes={borderColor:"color",backgroundColor:"color"};Ti.descriptors={label:{_fallback:!0}};function fx(n,t,e,i){let{width:s,height:r,centerX:o,centerY:a}=t,l=s/2,c=r/2;if(l<=0||c<=0)return!1;let h=Wt(e||0),f=i/2||0,g=Math.cos(h),p=Math.sin(h),b=Math.pow(g*(n.x-o)+p*(n.y-a),2),x=Math.pow(p*(n.x-o)-g*(n.y-a),2);return b/Math.pow(l+f,2)+x/Math.pow(c+f,2)<=1.0001}var Ss=class extends Zt{inRange(t,e,i,s){let{x:r,y:o,x2:a,y2:l,width:c}=this.getProps(["x","y","x2","y2","width"],s),h=this.options.borderWidth;if(i!=="x"&&i!=="y")return uy({x:t,y:e},this.getCenterPoint(s),c/2,h);let f=h/2,g=i==="y"?{start:o,end:l,value:e}:{start:r,end:a,value:t};return g.value>=g.start-f&&g.value<=g.end+f}getCenterPoint(t){return ki(this,t)}draw(t){let e=this.options,i=e.borderWidth;if(e.radius<.1)return;t.save(),t.fillStyle=e.backgroundColor,Si(t,e);let s=Tn(t,e);_y(t,this,this.centerX,this.centerY),s&&!Yr(e.pointStyle)&&(t.shadowColor=e.borderShadowColor,t.stroke()),t.restore(),e.borderWidth=i}resolveElementProperties(t,e){let i=sf(t,e);return i.initProperties=ws(t,i,e,!0),i}};Ss.id="pointAnnotation";Ss.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderDash:[],borderDashOffset:0,borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,pointStyle:"circle",radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Ss.defaultRoutes={borderColor:"color",backgroundColor:"color"};var Ms=class extends Zt{inRange(t,e,i,s){if(i!=="x"&&i!=="y")return this.options.radius>=.1&&this.elements.length>1&&px(this.elements,t,e,s);let r=Qn({x:t,y:e},this.getCenterPoint(s),Wt(-this.options.rotation)),o=this.elements.map(c=>i==="y"?c.bY:c.bX),a=Math.min(...o),l=Math.max(...o);return r[i]>=a&&r[i]<=l}getCenterPoint(t){return ki(this,t)}draw(t){let{elements:e,options:i}=this;t.save(),t.beginPath(),t.fillStyle=i.backgroundColor,Si(t,i);let s=Tn(t,i),r=!0;for(let o of e)r?(t.moveTo(o.x,o.y),r=!1):t.lineTo(o.x,o.y);t.closePath(),t.fill(),s&&(t.shadowColor=i.borderShadowColor,t.stroke()),t.restore()}resolveElementProperties(t,e){let i=sf(t,e),{sides:s,rotation:r}=e,o=[],a=2*dt/s,l=r*Wi;for(let c=0;c<s;c++,l+=a){let h=dx(i,e,l);h.initProperties=ws(t,i,e),o.push(h)}return i.elements=o,i}};Ms.id="polygonAnnotation";Ms.defaults={adjustScaleRange:!0,backgroundShadowColor:"transparent",borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderShadowColor:"transparent",borderWidth:1,display:!0,init:void 0,point:{radius:0},radius:10,rotation:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,sides:3,xAdjust:0,xMax:void 0,xMin:void 0,xScaleID:void 0,xValue:void 0,yAdjust:0,yMax:void 0,yMin:void 0,yScaleID:void 0,yValue:void 0,z:0};Ms.defaultRoutes={borderColor:"color",backgroundColor:"color"};function dx({centerX:n,centerY:t},{radius:e,borderWidth:i},s){let r=i/2,o=Math.sin(s),a=Math.cos(s),l={x:n+o*e,y:t-a*e};return{type:"point",optionScope:"point",properties:{x:l.x,y:l.y,centerX:l.x,centerY:l.y,bX:n+o*(e+r),bY:t-a*(e+r)}}}function px(n,t,e,i){let s=!1,r=n[n.length-1].getProps(["bX","bY"],i);for(let o of n){let a=o.getProps(["bX","bY"],i);a.bY>e!=r.bY>e&&t<(r.bX-a.bX)*(e-a.bY)/(r.bY-a.bY)+a.bX&&(s=!s),r=a}return s}var Dn={box:ti,ellipse:Ti,label:Ci,line:Pi,point:Ss,polygon:Ms};Object.keys(Dn).forEach(n=>{ut.describe(`elements.${Dn[n].id}`,{_fallback:"plugins.annotation.common"})});var gx={update:Object.assign},mx=Za.concat(Xr),Cf=(n,t)=>at(t)?rl(n,t):n,il=n=>n==="color"||n==="font";function sl(n="line"){return Dn[n]?n:(console.warn(`Unknown annotation type: '${n}', defaulting to 'line'`),"line")}function bx(n,t,e,i){let s=_x(n,e.animations,i),r=t.annotations,o=wx(t.elements,r);for(let a=0;a<r.length;a++){let l=r[a],c=Pf(o,a,l.type),h=l.setContext(xx(n,c,l)),f=c.resolveElementProperties(n,h);f.skip=vx(f),"elements"in f&&(yx(c,f.elements,h,s),delete f.elements),Nt(c.x)||Object.assign(c,f),Object.assign(c,f.initProperties),f.options=Tf(h),s.update(c,f)}}function vx(n){return isNaN(n.x)||isNaN(n.y)}function _x(n,t,e){return e==="reset"||e==="none"||e==="resize"?gx:new ts(n,t)}function yx(n,t,e,i){let s=n.elements||(n.elements=[]);s.length=t.length;for(let r=0;r<t.length;r++){let o=t[r],a=o.properties,l=Pf(s,r,o.type,o.initProperties),c=e[o.optionScope].override(o);a.options=Tf(c),i.update(l,a)}}function Pf(n,t,e,i){let s=Dn[sl(e)],r=n[t];return(!r||!(r instanceof s))&&(r=n[t]=new s,Object.assign(r,i)),r}function Tf(n){let t=Dn[sl(n.type)],e={};e.id=n.id,e.type=n.type,e.drawTime=n.drawTime,Object.assign(e,rl(n,t.defaults),rl(n,t.defaultRoutes));for(let i of mx)e[i]=n[i];return e}function rl(n,t){let e={};for(let i of Object.keys(t)){let s=t[i],r=n[i];il(i)&&gt(r)?e[i]=r.map(o=>Cf(o,s)):e[i]=Cf(r,s)}return e}function xx(n,t,e){return t.$context||(t.$context=Object.assign(Object.create(n.getContext()),{element:t,id:e.id,type:"annotation"}))}function wx(n,t){let e=t.length,i=n.length;if(i<e){let s=e-i;n.splice(i,0,...new Array(s))}else i>e&&n.splice(e,i-e);return n}var kx="2.2.1",On=new Map,Sx=Za.concat(Xr),Df={id:"annotation",version:kx,beforeRegister(){fy("chart.js","3.7",se.version)},afterRegister(){se.register(Dn)},afterUnregister(){se.unregister(Dn)},beforeInit(n){On.set(n,{annotations:[],elements:[],visibleElements:[],listeners:{},listened:!1,moveListened:!1,hooks:{},hooked:!1,hovered:[]})},beforeUpdate(n,t,e){let i=On.get(n),s=i.annotations=[],r=e.annotations;at(r)?Object.keys(r).forEach(o=>{let a=r[o];at(a)&&(a.id=o,s.push(a))}):gt(r)&&s.push(...r),jy(s,n.scales)},afterDataLimits(n,t){let e=On.get(n);$y(n,t.scale,e.annotations.filter(i=>i.display&&i.adjustScaleRange))},afterUpdate(n,t,e){let i=On.get(n);Ay(n,i,e),bx(n,i,e,t.mode),i.visibleElements=i.elements.filter(s=>!s.skip&&s.options.display),Iy(n,i,e)},beforeDatasetsDraw(n,t,e){qr(n,"beforeDatasetsDraw",e.clip)},afterDatasetsDraw(n,t,e){qr(n,"afterDatasetsDraw",e.clip)},beforeDraw(n,t,e){qr(n,"beforeDraw",e.clip)},afterDraw(n,t,e){qr(n,"afterDraw",e.clip)},beforeEvent(n,t,e){let i=On.get(n);Ry(i,t.event,e)&&(t.changed=!0)},afterDestroy(n){On.delete(n)},_getState(n){return On.get(n)},defaults:{animations:{numbers:{properties:["x","y","x2","y2","width","height","centerX","centerY","pointX","pointY","radius"],type:"number"}},clip:!0,interaction:{mode:void 0,axis:void 0,intersect:void 0},common:{drawTime:"afterDatasetsDraw",init:!1,label:{}}},descriptors:{_indexable:!1,_scriptable:n=>!Sx.includes(n)&&n!=="init",annotations:{_allKeys:!1,_fallback:(n,t)=>`elements.${Dn[sl(t.type)].id}`},interaction:{_fallback:!0},common:{label:{_indexable:il,_fallback:!0},_indexable:il}},additionalOptionScopes:[""]};function qr(n,t,e){let{ctx:i,chartArea:s}=n,r=On.get(n);e&&yn(i,s);let o=Mx(r.visibleElements,t).sort((a,l)=>a.element.options.z-l.element.options.z);for(let a of o)Cx(i,s,r,a);e&&xn(i)}function Mx(n,t){let e=[];for(let i of n)if(i.options.drawTime===t&&e.push({element:i,main:!0}),i.elements&&i.elements.length)for(let s of i.elements)s.options.display&&s.options.drawTime===t&&e.push({element:s});return e}function Cx(n,t,e,i){let s=i.element;i.main?(hf(e,s,"beforeDraw"),s.draw(n,t),hf(e,s,"afterDraw")):s.draw(n,t)}se.register(...Iu,Df);var Ur=class{constructor(t){this.plugin=t}async datasetPrep(t,e,i=!1){var c,h,f,g;let s=[];if(!t.id){let p=[];if(this.plugin.settings.themeable||i){let b=1;for(;;){let x=getComputedStyle(e).getPropertyValue(`--chart-color-${b}`);if(x)p.push(x),b++;else break}}for(let b=0;t.series.length>b;b++)s.push({label:(c=t.series[b].title)!=null?c:"",data:t.series[b].data,backgroundColor:t.labelColors?p.length?ln(p,t.transparency):ln(this.plugin.settings.colors,t.transparency):p.length?ln(p,t.transparency)[b]:ln(this.plugin.settings.colors,t.transparency)[b],borderColor:t.labelColors?p.length?p:this.plugin.settings.colors:p.length?p[b]:this.plugin.settings.colors[b],borderWidth:1,fill:t.fill?t.stacked?b==0?"origin":"-1":!0:!1,tension:(h=t.tension)!=null?h:0})}let r=t.time?{type:"time",time:{unit:t.time}}:null,o=t.labels,a=getComputedStyle(e).getPropertyValue("--background-modifier-border"),l;return se.defaults.color=getComputedStyle(e).getPropertyValue("--text-muted"),se.defaults.font.family=getComputedStyle(e).getPropertyValue("--mermaid-font"),se.defaults.plugins=zi(ji({},se.defaults.plugins),{legend:zi(ji({},se.defaults.plugins.legend),{display:(f=t.legend)!=null?f:!0,position:(g=t.legendPosition)!=null?g:"top"})}),se.defaults.layout.padding=t.padding,t.type=="radar"||t.type=="polarArea"?l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},scales:{r:zi(ji({},r),{grid:{color:a},beginAtZero:t.beginAtZero,max:t.rMax,min:t.rMin,ticks:{backdropColor:a}})}}}:t.type=="bar"||t.type=="line"?l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},indexAxis:t.indexAxis,spanGaps:t.spanGaps,scales:{y:{min:t.yMin,max:t.yMax,reverse:t.yReverse,ticks:{display:t.yTickDisplay,padding:t.yTickPadding},display:t.yDisplay,stacked:t.stacked,beginAtZero:t.beginAtZero,grid:{color:a},title:{display:t.yTitle,text:t.yTitle}},x:zi(ji({},r),{min:t.xMin,max:t.xMax,reverse:t.xReverse,ticks:{display:t.xTickDisplay,padding:t.xTickPadding},display:t.xDisplay,stacked:t.stacked,grid:{color:a},title:{display:t.xTitle,text:t.xTitle}})}}}:l={type:t.type,data:{labels:o,datasets:s},options:{animation:{duration:0},spanGaps:t.spanGaps}},{chartOptions:l,width:t.width}}async imageRenderer(t,e){let i=l=>new Promise(c=>setTimeout(c,l)),s=document.createElement("canvas"),r=s.getContext("2d"),o=await this.datasetPrep(await(0,Gr.parseYaml)(t.replace("```chart","").replace("```","").replace(/\t/g,"    ")),document.body);new se(r,o.chartOptions),document.body.append(s),await i(250);let a=s.toDataURL(e.format,e.quality);return document.body.removeChild(s),a.substring(a.indexOf(",")+1)}renderRaw(t,e){var s;let i=e.createEl("canvas");if(t.chartOptions)try{let r=new se(i.getContext("2d"),t.chartOptions);return i.parentElement.style.width=(s=t.width)!=null?s:"100%",i.parentElement.style.margin="auto",r}catch(r){return cn(r,e),null}else try{return new se(i.getContext("2d"),t)}catch(r){return cn(r,e),null}}async renderFromYaml(t,e,i){this.plugin.app.workspace.onLayoutReady(()=>i.addChild(new Of(t,e,this,i.sourcePath)))}},Of=class extends Gr.MarkdownRenderChild{constructor(t,e,i,s){super(e);this.el=e,this.data=t,this.renderer=i,this.ownPath=s,this.changeHandler=this.changeHandler.bind(this),this.reload=this.reload.bind(this)}async onload(){var t,e,i,s;try{let r=await this.renderer.datasetPrep(this.data,this.el),o={};if(this.data.id){let a=[];if(this.renderer.plugin.settings.themeable){let g=1;for(;;){let p=getComputedStyle(this.el).getPropertyValue(`--chart-color-${g}`);if(p)a.push(p),g++;else break}}o.datasets=[];let l;this.data.file&&(l=this.renderer.plugin.app.metadataCache.getFirstLinkpathDest(this.data.file,this.renderer.plugin.app.workspace.getActiveFile().path));let c=(t=this.renderer.plugin.app.metadataCache.getFileCache(l!=null?l:this.renderer.plugin.app.vault.getAbstractFileByPath(this.ownPath)).sections.find(g=>g.id===this.data.id))==null?void 0:t.position;if(!c)throw"Invalid id and/or file";let h=(await this.renderer.plugin.app.vault.cachedRead(this.data.file?l:this.renderer.plugin.app.vault.getAbstractFileByPath(this.ownPath))).substring(c.start.offset,c.end.offset),f;try{f=Ha(h,(e=this.data.layout)!=null?e:"columns",this.data.select)}catch(g){throw"There is no table at that id and/or file"}o.labels=f.labels;for(let g=0;f.dataFields.length>g;g++)o.datasets.push({label:(i=f.dataFields[g].dataTitle)!=null?i:"",data:f.dataFields[g].data,backgroundColor:this.data.labelColors?a.length?ln(a,this.data.transparency):ln(this.renderer.plugin.settings.colors,this.data.transparency):a.length?ln(a,this.data.transparency)[g]:ln(this.renderer.plugin.settings.colors,this.data.transparency)[g],borderColor:this.data.labelColors?a.length?a:this.renderer.plugin.settings.colors:a.length?a[g]:this.renderer.plugin.settings.colors[g],borderWidth:1,fill:this.data.fill?this.data.stacked?g==0?"origin":"-1":!0:!1,tension:(s=this.data.tension)!=null?s:0});r.chartOptions.data.labels=o.labels,r.chartOptions.data.datasets=o.datasets}this.chart=this.renderer.renderRaw(r,this.containerEl)}catch(r){cn(r,this.el)}this.data.id&&this.renderer.plugin.app.metadataCache.on("changed",this.changeHandler),this.renderer.plugin.app.workspace.on("css-change",this.reload)}changeHandler(t){(this.data.file?t.basename===this.data.file:t.path===this.ownPath)&&this.reload()}reload(){this.onunload(),this.onload()}onunload(){this.renderer.plugin.app.metadataCache.off("changed",this.changeHandler),this.renderer.plugin.app.workspace.off("css-change",this.reload),this.el.empty(),this.chart&&this.chart.destroy(),this.chart=null}};var Kr={colors:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(255, 159, 64, 1)"],contextMenu:!0,imageSettings:{format:"image/png",quality:.92},themeable:!1};var Ye=ze(require("obsidian"));var ol=function(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")},al=function(){function n(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}}(),Zr=function(){function n(t,e){var i=[],s=!0,r=!1,o=void 0;try{for(var a=t[Symbol.iterator](),l;!(s=(l=a.next()).done)&&(i.push(l.value),!(e&&i.length===e));s=!0);}catch(c){r=!0,o=c}finally{try{!s&&a.return&&a.return()}finally{if(r)throw o}}return i}return function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return n(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();String.prototype.startsWith=String.prototype.startsWith||function(n){return this.indexOf(n)===0};String.prototype.padStart=String.prototype.padStart||function(n,t){for(var e=this;e.length<n;)e=t+e;return e};var Px={cb:"0f8ff",tqw:"aebd7",q:"-ffff",qmrn:"7fffd4",zr:"0ffff",bg:"5f5dc",bsq:"e4c4",bck:"---",nch:"ebcd",b:"--ff",bvt:"8a2be2",brwn:"a52a2a",brw:"deb887",ctb:"5f9ea0",hrt:"7fff-",chcT:"d2691e",cr:"7f50",rnw:"6495ed",crns:"8dc",crms:"dc143c",cn:"-ffff",Db:"--8b",Dcn:"-8b8b",Dgnr:"b8860b",Dgr:"a9a9a9",Dgrn:"-64-",Dkhk:"bdb76b",Dmgn:"8b-8b",Dvgr:"556b2f",Drng:"8c-",Drch:"9932cc",Dr:"8b--",Dsmn:"e9967a",Dsgr:"8fbc8f",DsTb:"483d8b",DsTg:"2f4f4f",Dtrq:"-ced1",Dvt:"94-d3",ppnk:"1493",pskb:"-bfff",mgr:"696969",grb:"1e90ff",rbrc:"b22222",rwht:"af0",stg:"228b22",chs:"-ff",gnsb:"dcdcdc",st:"8f8ff",g:"d7-",gnr:"daa520",gr:"808080",grn:"-8-0",grnw:"adff2f",hnw:"0fff0",htpn:"69b4",nnr:"cd5c5c",ng:"4b-82",vr:"0",khk:"0e68c",vnr:"e6e6fa",nrb:"0f5",wngr:"7cfc-",mnch:"acd",Lb:"add8e6",Lcr:"08080",Lcn:"e0ffff",Lgnr:"afad2",Lgr:"d3d3d3",Lgrn:"90ee90",Lpnk:"b6c1",Lsmn:"a07a",Lsgr:"20b2aa",Lskb:"87cefa",LsTg:"778899",Lstb:"b0c4de",Lw:"e0",m:"-ff-",mgrn:"32cd32",nn:"af0e6",mgnt:"-ff",mrn:"8--0",mqm:"66cdaa",mmb:"--cd",mmrc:"ba55d3",mmpr:"9370db",msg:"3cb371",mmsT:"7b68ee","":"-fa9a",mtr:"48d1cc",mmvt:"c71585",mnLb:"191970",ntc:"5fffa",mstr:"e4e1",mccs:"e4b5",vjw:"dead",nv:"--80",c:"df5e6",v:"808-0",vrb:"6b8e23",rng:"a5-",rngr:"45-",rch:"da70d6",pgnr:"eee8aa",pgrn:"98fb98",ptrq:"afeeee",pvtr:"db7093",ppwh:"efd5",pchp:"dab9",pr:"cd853f",pnk:"c0cb",pm:"dda0dd",pwrb:"b0e0e6",prp:"8-080",cc:"663399",r:"--",sbr:"bc8f8f",rb:"4169e1",sbrw:"8b4513",smn:"a8072",nbr:"4a460",sgrn:"2e8b57",ssh:"5ee",snn:"a0522d",svr:"c0c0c0",skb:"87ceeb",sTb:"6a5acd",sTgr:"708090",snw:"afa",n:"-ff7f",stb:"4682b4",tn:"d2b48c",t:"-8080",thst:"d8bfd8",tmT:"6347",trqs:"40e0d0",vt:"ee82ee",whT:"5deb3",wht:"",hts:"5f5f5",w:"-",wgrn:"9acd32"};function Ef(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,e=t>0?n.toFixed(t).replace(/0+$/,"").replace(/\.$/,""):n.toString();return e||"0"}var Tx=function(){function n(t,e,i,s){ol(this,n);var r=this;function o(l){if(l.startsWith("hsl")){var c=l.match(/([\-\d\.e]+)/g).map(Number),h=Zr(c,4),f=h[0],g=h[1],p=h[2],b=h[3];b===void 0&&(b=1),f/=360,g/=100,p/=100,r.hsla=[f,g,p,b]}else if(l.startsWith("rgb")){var x=l.match(/([\-\d\.e]+)/g).map(Number),S=Zr(x,4),C=S[0],T=S[1],I=S[2],A=S[3];A===void 0&&(A=1),r.rgba=[C,T,I,A]}else l.startsWith("#")?r.rgba=n.hexToRgb(l):r.rgba=n.nameToRgb(l)||n.hexToRgb(l)}if(t!==void 0)if(Array.isArray(t))this.rgba=t;else if(i===void 0){var a=t&&""+t;a&&o(a.toLowerCase())}else this.rgba=[t,e,i,s===void 0?1:s]}return al(n,[{key:"printRGB",value:function(e){var i=e?this.rgba:this.rgba.slice(0,3),s=i.map(function(r,o){return Ef(r,o===3?3:0)});return e?"rgba("+s+")":"rgb("+s+")"}},{key:"printHSL",value:function(e){var i=[360,100,100,1],s=["","%","%",""],r=e?this.hsla:this.hsla.slice(0,3),o=r.map(function(a,l){return Ef(a*i[l],l===3?3:1)+s[l]});return e?"hsla("+o+")":"hsl("+o+")"}},{key:"printHex",value:function(e){var i=this.hex;return e?i:i.substring(0,7)}},{key:"rgba",get:function(){if(this._rgba)return this._rgba;if(!this._hsla)throw new Error("No color is set");return this._rgba=n.hslToRgb(this._hsla)},set:function(e){e.length===3&&(e[3]=1),this._rgba=e,this._hsla=null}},{key:"rgbString",get:function(){return this.printRGB()}},{key:"rgbaString",get:function(){return this.printRGB(!0)}},{key:"hsla",get:function(){if(this._hsla)return this._hsla;if(!this._rgba)throw new Error("No color is set");return this._hsla=n.rgbToHsl(this._rgba)},set:function(e){e.length===3&&(e[3]=1),this._hsla=e,this._rgba=null}},{key:"hslString",get:function(){return this.printHSL()}},{key:"hslaString",get:function(){return this.printHSL(!0)}},{key:"hex",get:function(){var e=this.rgba,i=e.map(function(s,r){return r<3?s.toString(16):Math.round(s*255).toString(16)});return"#"+i.map(function(s){return s.padStart(2,"0")}).join("")},set:function(e){this.rgba=n.hexToRgb(e)}}],[{key:"hexToRgb",value:function(e){var i=(e.startsWith("#")?e.slice(1):e).replace(/^(\w{3})$/,"$1F").replace(/^(\w)(\w)(\w)(\w)$/,"$1$1$2$2$3$3$4$4").replace(/^(\w{6})$/,"$1FF");if(!i.match(/^([0-9a-fA-F]{8})$/))throw new Error("Unknown hex color; "+e);var s=i.match(/^(\w\w)(\w\w)(\w\w)(\w\w)$/).slice(1).map(function(r){return parseInt(r,16)});return s[3]=s[3]/255,s}},{key:"nameToRgb",value:function(e){var i=e.toLowerCase().replace("at","T").replace(/[aeiouyldf]/g,"").replace("ght","L").replace("rk","D").slice(-5,4),s=Px[i];return s===void 0?s:n.hexToRgb(s.replace(/\-/g,"00").padStart(6,"f"))}},{key:"rgbToHsl",value:function(e){var i=Zr(e,4),s=i[0],r=i[1],o=i[2],a=i[3];s/=255,r/=255,o/=255;var l=Math.max(s,r,o),c=Math.min(s,r,o),h=void 0,f=void 0,g=(l+c)/2;if(l===c)h=f=0;else{var p=l-c;switch(f=g>.5?p/(2-l-c):p/(l+c),l){case s:h=(r-o)/p+(r<o?6:0);break;case r:h=(o-s)/p+2;break;case o:h=(s-r)/p+4;break}h/=6}return[h,f,g,a]}},{key:"hslToRgb",value:function(e){var i=Zr(e,4),s=i[0],r=i[1],o=i[2],a=i[3],l=void 0,c=void 0,h=void 0;if(r===0)l=c=h=o;else{var f=function(S,C,T){return T<0&&(T+=1),T>1&&(T-=1),T<1/6?S+(C-S)*6*T:T<1/2?C:T<2/3?S+(C-S)*(2/3-T)*6:S},g=o<.5?o*(1+r):o+r-o*r,p=2*o-g;l=f(p,g,s+1/3),c=f(p,g,s),h=f(p,g,s-1/3)}var b=[l*255,c*255,h*255].map(Math.round);return b[3]=a,b}}]),n}(),Dx=function(){function n(){ol(this,n),this._events=[]}return al(n,[{key:"add",value:function(e,i,s){e.addEventListener(i,s,!1),this._events.push({target:e,type:i,handler:s})}},{key:"remove",value:function(e,i,s){this._events=this._events.filter(function(r){var o=!0;return e&&e!==r.target&&(o=!1),i&&i!==r.type&&(o=!1),s&&s!==r.handler&&(o=!1),o&&n._doRemove(r.target,r.type,r.handler),!o})}},{key:"destroy",value:function(){this._events.forEach(function(e){return n._doRemove(e.target,e.type,e.handler)}),this._events=[]}}],[{key:"_doRemove",value:function(e,i,s){e.removeEventListener(i,s,!1)}}]),n}();function Ox(n){var t=document.createElement("div");return t.innerHTML=n,t.firstElementChild}function ll(n,t,e){var i=!1;function s(l,c,h){return Math.max(c,Math.min(l,h))}function r(l,c,h){if(h&&(i=!0),!!i){l.preventDefault();var f=t.getBoundingClientRect(),g=f.width,p=f.height,b=c.clientX,x=c.clientY,S=s(b-f.left,0,g),C=s(x-f.top,0,p);e(S/g,C/p)}}function o(l,c){var h=l.buttons===void 0?l.which:l.buttons;h===1?r(l,l,c):i=!1}function a(l,c){l.touches.length===1?r(l,l.touches[0],c):i=!1}n.add(t,"mousedown",function(l){o(l,!0)}),n.add(t,"touchstart",function(l){a(l,!0)}),n.add(window,"mousemove",o),n.add(t,"touchmove",a),n.add(window,"mouseup",function(l){i=!1}),n.add(t,"touchend",function(l){i=!1}),n.add(t,"touchcancel",function(l){i=!1})}var Ex=`linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0 / 2em 2em,
                   linear-gradient(45deg, lightgrey 25%,       white 25%,       white 75%, lightgrey 75%) 1em 1em / 2em 2em`,Ax=360,Af="keydown",Jr="mousedown",cl="focusin";function Ve(n,t){return(t||document).querySelector(n)}function Rf(n){n.preventDefault(),n.stopPropagation()}function hl(n,t,e,i,s){n.add(t,Af,function(r){e.indexOf(r.key)>=0&&(s&&Rf(r),i(r))})}var ul=function(){function n(t){ol(this,n),this.settings={popup:"right",layout:"default",alpha:!0,editor:!0,editorFormat:"hex",cancelButton:!1,defaultColor:"#0cf"},this._events=new Dx,this.onChange=null,this.onDone=null,this.onOpen=null,this.onClose=null,this.setOptions(t)}return al(n,[{key:"setOptions",value:function(e){var i=this;if(!e)return;var s=this.settings;function r(c,h,f){for(var g in c)f&&f.indexOf(g)>=0||(h[g]=c[g])}if(e instanceof HTMLElement)s.parent=e;else{s.parent&&e.parent&&s.parent!==e.parent&&(this._events.remove(s.parent),this._popupInited=!1),r(e,s),e.onChange&&(this.onChange=e.onChange),e.onDone&&(this.onDone=e.onDone),e.onOpen&&(this.onOpen=e.onOpen),e.onClose&&(this.onClose=e.onClose);var o=e.color||e.colour;o&&this._setColor(o)}var a=s.parent;if(a&&s.popup&&!this._popupInited){var l=function(h){return i.openHandler(h)};this._events.add(a,"click",l),hl(this._events,a,[" ","Spacebar","Enter"],l),this._popupInited=!0}else e.parent&&!s.popup&&this.show()}},{key:"openHandler",value:function(e){if(this.show()){e&&e.preventDefault(),this.settings.parent.style.pointerEvents="none";var i=e&&e.type===Af?this._domEdit:this.domElement;setTimeout(function(){return i.focus()},100),this.onOpen&&this.onOpen(this.colour)}}},{key:"closeHandler",value:function(e){var i=e&&e.type,s=!1;if(!e)s=!0;else if(i===Jr||i===cl){var r=(this.__containedEvent||0)+100;e.timeStamp>r&&(s=!0)}else Rf(e),s=!0;s&&this.hide()&&(this.settings.parent.style.pointerEvents="",i!==Jr&&this.settings.parent.focus(),this.onClose&&this.onClose(this.colour))}},{key:"movePopup",value:function(e,i){this.closeHandler(),this.setOptions(e),i&&this.openHandler()}},{key:"setColor",value:function(e,i){this._setColor(e,{silent:i})}},{key:"_setColor",value:function(e,i){if(typeof e=="string"&&(e=e.trim()),!!e){i=i||{};var s=void 0;try{s=new Tx(e)}catch(o){if(i.failSilently)return;throw o}if(!this.settings.alpha){var r=s.hsla;r[3]=1,s.hsla=r}this.colour=this.color=s,this._setHSLA(null,null,null,null,i)}}},{key:"setColour",value:function(e,i){this.setColor(e,i)}},{key:"show",value:function(){var e=this.settings.parent;if(!e)return!1;if(this.domElement){var i=this._toggleDOM(!0);return this._setPosition(),i}var s=this.settings.template||'<div class="picker_wrapper" tabindex="-1"><div class="picker_arrow"></div><div class="picker_hue picker_slider"><div class="picker_selector"></div></div><div class="picker_sl"><div class="picker_selector"></div></div><div class="picker_alpha picker_slider"><div class="picker_selector"></div></div><div class="picker_editor"><input aria-label="Type a color name or hex value"/></div><div class="picker_sample"></div><div class="picker_done"><button>Ok</button></div><div class="picker_cancel"><button>Cancel</button></div></div>',r=Ox(s);return this.domElement=r,this._domH=Ve(".picker_hue",r),this._domSL=Ve(".picker_sl",r),this._domA=Ve(".picker_alpha",r),this._domEdit=Ve(".picker_editor input",r),this._domSample=Ve(".picker_sample",r),this._domOkay=Ve(".picker_done button",r),this._domCancel=Ve(".picker_cancel button",r),r.classList.add("layout_"+this.settings.layout),this.settings.alpha||r.classList.add("no_alpha"),this.settings.editor||r.classList.add("no_editor"),this.settings.cancelButton||r.classList.add("no_cancel"),this._ifPopup(function(){return r.classList.add("popup")}),this._setPosition(),this.colour?this._updateUI():this._setColor(this.settings.defaultColor),this._bindEvents(),!0}},{key:"hide",value:function(){return this._toggleDOM(!1)}},{key:"destroy",value:function(){this._events.destroy(),this.domElement&&this.settings.parent.removeChild(this.domElement)}},{key:"_bindEvents",value:function(){var e=this,i=this,s=this.domElement,r=this._events;function o(c,h,f){r.add(c,h,f)}o(s,"click",function(c){return c.preventDefault()}),ll(r,this._domH,function(c,h){return i._setHSLA(c)}),ll(r,this._domSL,function(c,h){return i._setHSLA(null,c,1-h)}),this.settings.alpha&&ll(r,this._domA,function(c,h){return i._setHSLA(null,null,null,1-h)});var a=this._domEdit;o(a,"input",function(c){i._setColor(this.value,{fromEditor:!0,failSilently:!0})}),o(a,"focus",function(c){var h=this;h.selectionStart===h.selectionEnd&&h.select()}),this._ifPopup(function(){var c=function(g){return e.closeHandler(g)};o(window,Jr,c),o(window,cl,c),hl(r,s,["Esc","Escape"],c);var h=function(g){e.__containedEvent=g.timeStamp};o(s,Jr,h),o(s,cl,h),o(e._domCancel,"click",c)});var l=function(h){e._ifPopup(function(){return e.closeHandler(h)}),e.onDone&&e.onDone(e.colour)};o(this._domOkay,"click",l),hl(r,s,["Enter"],l)}},{key:"_setPosition",value:function(){var e=this.settings.parent,i=this.domElement;e!==i.parentNode&&e.appendChild(i),this._ifPopup(function(s){getComputedStyle(e).position==="static"&&(e.style.position="relative");var r=s===!0?"popup_right":"popup_"+s;["popup_top","popup_bottom","popup_left","popup_right"].forEach(function(o){o===r?i.classList.add(o):i.classList.remove(o)}),i.classList.add(r)})}},{key:"_setHSLA",value:function(e,i,s,r,o){o=o||{};var a=this.colour,l=a.hsla;[e,i,s,r].forEach(function(c,h){(c||c===0)&&(l[h]=c)}),a.hsla=l,this._updateUI(o),this.onChange&&!o.silent&&this.onChange(a)}},{key:"_updateUI",value:function(e){if(!this.domElement)return;e=e||{};var i=this.colour,s=i.hsla,r="hsl("+s[0]*Ax+", 100%, 50%)",o=i.hslString,a=i.hslaString,l=this._domH,c=this._domSL,h=this._domA,f=Ve(".picker_selector",l),g=Ve(".picker_selector",c),p=Ve(".picker_selector",h);function b(j,N,G){N.style.left=G*100+"%"}function x(j,N,G){N.style.top=G*100+"%"}b(l,f,s[0]),this._domSL.style.backgroundColor=this._domH.style.color=r,b(c,g,s[1]),x(c,g,1-s[2]),c.style.color=o,x(h,p,1-s[3]);var S=o,C=S.replace("hsl","hsla").replace(")",", 0)"),T="linear-gradient("+[S,C]+")";if(this._domA.style.background=T+", "+Ex,!e.fromEditor){var I=this.settings.editorFormat,A=this.settings.alpha,F=void 0;switch(I){case"rgb":F=i.printRGB(A);break;case"hsl":F=i.printHSL(A);break;default:F=i.printHex(A)}this._domEdit.value=F}this._domSample.style.color=a}},{key:"_ifPopup",value:function(e,i){this.settings.parent&&this.settings.popup?e&&e(this.settings.popup):i&&i()}},{key:"_toggleDOM",value:function(e){var i=this.domElement;if(!i)return!1;var s=e?"":"none",r=i.style.display!==s;return r&&(i.style.display=s),r}}]),n}();Qr=document.createElement("style"),Qr.textContent='.picker_wrapper.no_alpha .picker_alpha{display:none}.picker_wrapper.no_editor .picker_editor{position:absolute;z-index:-1;opacity:0}.picker_wrapper.no_cancel .picker_cancel{display:none}.layout_default.picker_wrapper{display:flex;flex-flow:row wrap;justify-content:space-between;align-items:stretch;font-size:10px;width:25em;padding:.5em}.layout_default.picker_wrapper input,.layout_default.picker_wrapper button{font-size:1rem}.layout_default.picker_wrapper>*{margin:.5em}.layout_default.picker_wrapper::before{content:"";display:block;width:100%;height:0;order:1}.layout_default .picker_slider,.layout_default .picker_selector{padding:1em}.layout_default .picker_hue{width:100%}.layout_default .picker_sl{flex:1 1 auto}.layout_default .picker_sl::before{content:"";display:block;padding-bottom:100%}.layout_default .picker_editor{order:1;width:6.5rem}.layout_default .picker_editor input{width:100%;height:100%}.layout_default .picker_sample{order:1;flex:1 1 auto}.layout_default .picker_done,.layout_default .picker_cancel{order:1}.picker_wrapper{box-sizing:border-box;background:#f2f2f2;box-shadow:0 0 0 1px silver;cursor:default;font-family:sans-serif;color:#444;pointer-events:auto}.picker_wrapper:focus{outline:none}.picker_wrapper button,.picker_wrapper input{box-sizing:border-box;border:none;box-shadow:0 0 0 1px silver;outline:none}.picker_wrapper button:focus,.picker_wrapper button:active,.picker_wrapper input:focus,.picker_wrapper input:active{box-shadow:0 0 2px 1px #1e90ff}.picker_wrapper button{padding:.4em .6em;cursor:pointer;background-color:#f5f5f5;background-image:linear-gradient(0deg, gainsboro, transparent)}.picker_wrapper button:active{background-image:linear-gradient(0deg, transparent, gainsboro)}.picker_wrapper button:hover{background-color:#fff}.picker_selector{position:absolute;z-index:1;display:block;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);border:2px solid #fff;border-radius:100%;box-shadow:0 0 3px 1px #67b9ff;background:currentColor;cursor:pointer}.picker_slider .picker_selector{border-radius:2px}.picker_hue{position:relative;background-image:linear-gradient(90deg, red, yellow, lime, cyan, blue, magenta, red);box-shadow:0 0 0 1px silver}.picker_sl{position:relative;box-shadow:0 0 0 1px silver;background-image:linear-gradient(180deg, white, rgba(255, 255, 255, 0) 50%),linear-gradient(0deg, black, rgba(0, 0, 0, 0) 50%),linear-gradient(90deg, #808080, rgba(128, 128, 128, 0))}.picker_alpha,.picker_sample{position:relative;background:linear-gradient(45deg, lightgrey 25%, transparent 25%, transparent 75%, lightgrey 75%) 0 0/2em 2em,linear-gradient(45deg, lightgrey 25%, white 25%, white 75%, lightgrey 75%) 1em 1em/2em 2em;box-shadow:0 0 0 1px silver}.picker_alpha .picker_selector,.picker_sample .picker_selector{background:none}.picker_editor input{font-family:monospace;padding:.2em .4em}.picker_sample::before{content:"";position:absolute;display:block;width:100%;height:100%;background:currentColor}.picker_arrow{position:absolute;z-index:-1}.picker_wrapper.popup{position:absolute;z-index:2;margin:1.5em}.picker_wrapper.popup,.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{background:#f2f2f2;box-shadow:0 0 10px 1px rgba(0,0,0,.4)}.picker_wrapper.popup .picker_arrow{width:3em;height:3em;margin:0}.picker_wrapper.popup .picker_arrow::before,.picker_wrapper.popup .picker_arrow::after{content:"";display:block;position:absolute;top:0;left:0;z-index:-99}.picker_wrapper.popup .picker_arrow::before{width:100%;height:100%;-webkit-transform:skew(45deg);transform:skew(45deg);-webkit-transform-origin:0 100%;transform-origin:0 100%}.picker_wrapper.popup .picker_arrow::after{width:150%;height:150%;box-shadow:none}.popup.popup_top{bottom:100%;left:0}.popup.popup_top .picker_arrow{bottom:0;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.popup.popup_bottom{top:100%;left:0}.popup.popup_bottom .picker_arrow{top:0;left:0;-webkit-transform:rotate(90deg) scale(1, -1);transform:rotate(90deg) scale(1, -1)}.popup.popup_left{top:0;right:100%}.popup.popup_left .picker_arrow{top:0;right:0;-webkit-transform:scale(-1, 1);transform:scale(-1, 1)}.popup.popup_right{top:0;left:100%}.popup.popup_right .picker_arrow{top:0;left:0}',document.documentElement.firstElementChild.appendChild(Qr),ul.StyleElement=Qr;var Qr;var fl=class extends Ye.PluginSettingTab{constructor(t,e){super(t,e);this.plugin=e}isColor(t){var e=new Option().style;return e.color=t,e.color==t}display(){let{containerEl:t,plugin:e}=this;t.empty(),t.createEl("h2",{text:"Settings - Obsidian Charts"}),t.createEl("h3",{text:"General"}),new Ye.Setting(t).setName("Show Button in Context Menu").setDesc("If enabled, you will se a Button in your Editor Context Menu to open the Chart Creator.").addToggle(r=>{r.setValue(this.plugin.settings.contextMenu).onChange(async o=>{e.settings.contextMenu=o,await e.saveSettings()})}),new Ye.Setting(t).setName("Donate").setDesc("If you like this Plugin, consider donating to support continued development:").addButton(r=>{r.buttonEl.outerHTML='<a href="https://ko-fi.com/phibr0"><img src="https://uploads-ssl.webflow.com/5c14e387dab576fe667689cf/61e11e22d8ff4a5b4a1b3346_Supportbutton-1.png"></a>'}),t.createEl("h3",{text:"Colors",attr:{style:"margin-bottom: 0"}});let i=t.createEl("p",{cls:"setting-item-description"});i.append("Set the Colors for your Charts. This will set the border Color and the inner Color will be the same, but with less opacity. This ensures better compatibility with Dark and Light Mode. ","You can use any ",i.createEl("a",{href:"https://www.w3schools.com/cssref/css_colors.asp",text:"valid CSS Color."})),new Ye.Setting(t).setName("Enable Theme Colors").setDesc("If your Obsidian Theme (or snippet) provides Colors you can use them instead.").addToggle(r=>{r.setValue(e.settings.themeable).onChange(async o=>{e.settings.themeable=o,await e.saveSettings(),this.display()})}),e.settings.themeable||(e.settings.colors.forEach((r,o)=>{let a=document.createDocumentFragment();a.createSpan({text:"\u25CF",attr:{style:`color: ${r}`}}),a.appendText(` Color #${o+1}`),new Ye.Setting(t).setName(a).setDesc("This will be the border Color used in the Charts you create.").addButton(l=>{l.setButtonText("Change Color"),new ul({parent:l.buttonEl,onDone:async c=>{this.plugin.settings.colors[o]=c.hex,await this.plugin.saveSettings(),this.display()},popup:"left",color:r,alpha:!1})}).addExtraButton(l=>{l.setIcon("trash").setTooltip("Remove").onClick(async()=>{this.plugin.settings.colors.remove(r),await this.plugin.saveSettings(),this.display()}),this.plugin.settings.colors.length===1&&l.setDisabled(!0)}).addExtraButton(l=>{l.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{var c;this.plugin.settings.colors[o]=(c=Kr.colors[o])!=null?c:"#ffffff",await this.plugin.saveSettings(),this.display()})})}),new Ye.Setting(t).addButton(r=>{r.setButtonText("Add Color").onClick(async()=>{this.plugin.settings.colors.push("#ffffff"),await this.plugin.saveSettings(),this.display()})})),t.createEl("h3",{text:"Chart to Image Converter"});let s=t.createEl("details");s.createEl("summary",{text:"How to use"}),s.createEl("img",{attr:{src:"https://media.discordapp.net/attachments/855181471643861002/897811615037136966/charttoimage.gif"}}),new Ye.Setting(t).setName("Image Format").setDesc("The Format to be used, when generating a Image from a Chart.").addDropdown(r=>{r.addOptions({"image/jpeg":"jpeg","image/png":"png","image/webp":"webp"}),r.setValue(e.settings.imageSettings.format),r.onChange(async o=>{e.settings.imageSettings.format=o,await e.saveSettings()})}),new Ye.Setting(t).setName("Image Quality").setDesc("If using a lossy format, set the Image Quality.").addSlider(r=>{r.setDynamicTooltip().setLimits(.01,1,.01).setValue(e.settings.imageSettings.quality).onChange(async o=>{e.settings.imageSettings.quality=o,await e.saveSettings()})})}};var ed=ze(require("obsidian"));function Cs(){}function Rx(n,t){for(let e in t)n[e]=t[e];return n}function dl(n){return n()}function Lf(){return Object.create(null)}function hn(n){n.forEach(dl)}function to(n){return typeof n=="function"}function eo(n,t){return n!=n?t==t:n!==t||n&&typeof n=="object"||typeof n=="function"}function Ff(n){return Object.keys(n).length===0}function If(n,t,e,i){if(n){let s=$f(n,t,e,i);return n[0](s)}}function $f(n,t,e,i){return n[1]&&i?Rx(e.ctx.slice(),n[1](i(t))):e.ctx}function jf(n,t,e,i){if(n[2]&&i){let s=n[2](i(e));if(t.dirty===void 0)return s;if(typeof s=="object"){let r=[],o=Math.max(t.dirty.length,s.length);for(let a=0;a<o;a+=1)r[a]=t.dirty[a]|s[a];return r}return t.dirty|s}return t.dirty}function zf(n,t,e,i,s,r){if(s){let o=$f(t,e,i,r);n.p(o,s)}}function Bf(n){if(n.ctx.length>32){let t=[],e=n.ctx.length/32;for(let i=0;i<e;i++)t[i]=-1;return t}return-1}var W1=new Set;var Nf=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global,no=class{constructor(t){this.options=t,this._listeners="WeakMap"in Nf?new WeakMap:void 0}observe(t,e){return this._listeners.set(t,e),this._getObserver().observe(t,this.options),()=>{this._listeners.delete(t),this._observer.unobserve(t)}}_getObserver(){var t;return(t=this._observer)!==null&&t!==void 0?t:this._observer=new ResizeObserver(e=>{var i;for(let s of e)no.entries.set(s.target,s),(i=this._listeners.get(s.target))===null||i===void 0||i(s)})}};no.entries="WeakMap"in Nf?new WeakMap:void 0;var Hf=!1;function Lx(){Hf=!0}function Fx(){Hf=!1}function z(n,t){n.appendChild(t)}function io(n,t,e){let i=Ix(n);if(!i.getElementById(t)){let s=Y("style");s.id=t,s.textContent=e,$x(i,s)}}function Ix(n){if(!n)return document;let t=n.getRootNode?n.getRootNode():n.ownerDocument;return t&&t.host?t:n.ownerDocument}function $x(n,t){return z(n.head||n,t),t.sheet}function Xe(n,t,e){n.insertBefore(t,e||null)}function Ie(n){n.parentNode&&n.parentNode.removeChild(n)}function Wf(n,t){for(let e=0;e<n.length;e+=1)n[e]&&n[e].d(t)}function Y(n){return document.createElement(n)}function so(n){return document.createElementNS("http://www.w3.org/2000/svg",n)}function pl(n){return document.createTextNode(n)}function Ft(){return pl(" ")}function Jt(n,t,e,i){return n.addEventListener(t,e,i),()=>n.removeEventListener(t,e,i)}function X(n,t,e){e==null?n.removeAttribute(t):n.getAttribute(t)!==e&&n.setAttribute(t,e)}function gl(n){return n===""?null:+n}function jx(n){return Array.from(n.childNodes)}function Vf(n,t){t=""+t,n.data!==t&&(n.data=t)}function de(n,t){n.value=t??""}function qt(n,t,e,i){e==null?n.style.removeProperty(t):n.style.setProperty(t,e,i?"important":"")}function ml(n,t,e){for(let i=0;i<n.options.length;i+=1){let s=n.options[i];if(s.__value===t){s.selected=!0;return}}(!e||t!==void 0)&&(n.selectedIndex=-1)}function Yf(n){let t=n.querySelector(":checked");return t&&t.__value}function zx(n,t,{bubbles:e=!1,cancelable:i=!1}={}){let s=document.createEvent("CustomEvent");return s.initCustomEvent(n,e,i,t),s}var V1=new Map;var Ps;function Ts(n){Ps=n}function Bx(){if(!Ps)throw new Error("Function called outside component initialization");return Ps}function bl(){let n=Bx();return(t,e,{cancelable:i=!1}={})=>{let s=n.$$.callbacks[t];if(s){let r=zx(t,e,{cancelable:i});return s.slice().forEach(o=>{o.call(n,r)}),!r.defaultPrevented}return!0}}var Di=[];var ro=[],Oi=[],Xf=[],Nx=Promise.resolve(),vl=!1;function Hx(){vl||(vl=!0,Nx.then(qf))}function Ds(n){Oi.push(n)}var _l=new Set,Ei=0;function qf(){if(Ei!==0)return;let n=Ps;do{try{for(;Ei<Di.length;){let t=Di[Ei];Ei++,Ts(t),Wx(t.$$)}}catch(t){throw Di.length=0,Ei=0,t}for(Ts(null),Di.length=0,Ei=0;ro.length;)ro.pop()();for(let t=0;t<Oi.length;t+=1){let e=Oi[t];_l.has(e)||(_l.add(e),e())}Oi.length=0}while(Di.length);for(;Xf.length;)Xf.pop()();vl=!1,_l.clear(),Ts(n)}function Wx(n){if(n.fragment!==null){n.update(),hn(n.before_update);let t=n.dirty;n.dirty=[-1],n.fragment&&n.fragment.p(n.ctx,t),n.after_update.forEach(Ds)}}function Vx(n){let t=[],e=[];Oi.forEach(i=>n.indexOf(i)===-1?t.push(i):e.push(i)),e.forEach(i=>i()),Oi=t}var oo=new Set,Yx;function Os(n,t){n&&n.i&&(oo.delete(n),n.i(t))}function ao(n,t,e,i){if(n&&n.o){if(oo.has(n))return;oo.add(n),Yx.c.push(()=>{oo.delete(n),i&&(e&&n.d(1),i())}),n.o(t)}else i&&i()}var Xx=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"],Y1=new Set([...Xx]);function Gf(n){n&&n.c()}function yl(n,t,e,i){let{fragment:s,after_update:r}=n.$$;s&&s.m(t,e),i||Ds(()=>{let o=n.$$.on_mount.map(dl).filter(to);n.$$.on_destroy?n.$$.on_destroy.push(...o):hn(o),n.$$.on_mount=[]}),r.forEach(Ds)}function lo(n,t){let e=n.$$;e.fragment!==null&&(Vx(e.after_update),hn(e.on_destroy),e.fragment&&e.fragment.d(t),e.on_destroy=e.fragment=null,e.ctx=[])}function qx(n,t){n.$$.dirty[0]===-1&&(Di.push(n),Hx(),n.$$.dirty.fill(0)),n.$$.dirty[t/31|0]|=1<<t%31}function co(n,t,e,i,s,r,o,a=[-1]){let l=Ps;Ts(n);let c=n.$$={fragment:null,ctx:[],props:r,update:Cs,not_equal:s,bound:Lf(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(l?l.$$.context:[])),callbacks:Lf(),dirty:a,skip_bound:!1,root:t.target||l.$$.root};o&&o(c.root);let h=!1;if(c.ctx=e?e(n,t.props||{},(f,g,...p)=>{let b=p.length?p[0]:g;return c.ctx&&s(c.ctx[f],c.ctx[f]=b)&&(!c.skip_bound&&c.bound[f]&&c.bound[f](b),h&&qx(n,f)),g}):[],c.update(),h=!0,hn(c.before_update),c.fragment=i?i(c.ctx):!1,t.target){if(t.hydrate){Lx();let f=jx(t.target);c.fragment&&c.fragment.l(f),f.forEach(Ie)}else c.fragment&&c.fragment.c();t.intro&&Os(n.$$.fragment),yl(n,t.target,t.anchor,t.customElement),Fx(),qf()}Ts(l)}var Gx;typeof HTMLElement=="function"&&(Gx=class extends HTMLElement{constructor(){super();this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:n}=this.$$;this.$$.on_disconnect=n.map(dl).filter(to);for(let t in this.$$.slotted)this.appendChild(this.$$.slotted[t])}attributeChangedCallback(n,t,e){this[n]=e}disconnectedCallback(){hn(this.$$.on_disconnect)}$destroy(){lo(this,1),this.$destroy=Cs}$on(n,t){if(!to(t))return Cs;let e=this.$$.callbacks[n]||(this.$$.callbacks[n]=[]);return e.push(t),()=>{let i=e.indexOf(t);i!==-1&&e.splice(i,1)}}$set(n){this.$$set&&!Ff(n)&&(this.$$.skip_bound=!0,this.$$set(n),this.$$.skip_bound=!1)}});var Es=class{$destroy(){lo(this,1),this.$destroy=Cs}$on(t,e){if(!to(e))return Cs;let i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(e),()=>{let s=i.indexOf(e);s!==-1&&i.splice(s,1)}}$set(t){this.$$set&&!Ff(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}};var ho=ze(require("obsidian"));function Ux(n){io(n,"svelte-1lboqqp","h3.svelte-1lboqqp{margin:0}button.svelte-1lboqqp{display:flex;justify-content:space-between;width:100%;border:none;margin:0;padding:1em 0.5em}button.svelte-1lboqqp{fill:currentColor}svg.svelte-1lboqqp{height:0.7em;width:0.7em}")}function Kx(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,b,x=n[3].default,S=If(x,n,n[2],null);return{c(){t=Y("div"),e=Y("h3"),i=Y("button"),s=pl(n[0]),r=Ft(),o=so("svg"),a=so("path"),l=so("path"),c=Ft(),h=Y("div"),S&&S.c(),X(a,"class","vert"),X(a,"d","M10 1V19"),X(a,"stroke","black"),X(a,"stroke-width","2"),X(l,"d","M1 10L19 10"),X(l,"stroke","black"),X(l,"stroke-width","2"),X(o,"viewBox","0 0 20 20"),X(o,"fill","none"),X(o,"class","svelte-1lboqqp"),X(i,"aria-expanded",n[1]),X(i,"class","svelte-1lboqqp"),X(e,"class","svelte-1lboqqp"),X(h,"class","contents"),h.hidden=f=!n[1],X(t,"class","collapsible")},m(C,T){Xe(C,t,T),z(t,e),z(e,i),z(i,s),z(i,r),z(i,o),z(o,a),z(o,l),z(t,c),z(t,h),S&&S.m(h,null),g=!0,p||(b=Jt(i,"click",n[4]),p=!0)},p(C,[T]){(!g||T&1)&&Vf(s,C[0]),(!g||T&2)&&X(i,"aria-expanded",C[1]),S&&S.p&&(!g||T&4)&&zf(S,x,C,C[2],g?jf(x,C[2],T,null):Bf(C[2]),null),(!g||T&2&&f!==(f=!C[1]))&&(h.hidden=f)},i(C){g||(Os(S,C),g=!0)},o(C){ao(S,C),g=!1},d(C){C&&Ie(t),S&&S.d(C),p=!1,b()}}}function Zx(n,t,e){let{$$slots:i={},$$scope:s}=t,{headerText:r}=t,o=!1,a=()=>e(1,o=!o);return n.$$set=l=>{"headerText"in l&&e(0,r=l.headerText),"$$scope"in l&&e(2,s=l.$$scope)},[r,o,s,i,a]}var Uf=class extends Es{constructor(t){super();co(this,t,Zx,Kx,eo,{headerText:0},Ux)}},Kf=Uf;function Jx(n){io(n,"svelte-1tlkntj",".addMoreButtonContainer.svelte-1tlkntj{display:flex;justify-content:flex-end;margin-top:0.4rem}.subDesc.svelte-1tlkntj{font-size:smaller;opacity:0.5;margin:0}.desc.svelte-1tlkntj{padding-right:1em}.mainDesc.svelte-1tlkntj{margin:0}table.svelte-1tlkntj{margin:auto}.controlElement.svelte-1tlkntj{text-align:center}.chart-modal.svelte-1tlkntj{overflow-y:auto}.modalColumn.svelte-1tlkntj{display:flex;gap:2em}.chartPreview.svelte-1tlkntj{width:30vw;display:flex;justify-content:center;align-items:center}")}function Zf(n,t,e){let i=n.slice();return i[33]=t[e],i[34]=t,i[35]=e,i}function Jf(n){let t,e,i,s,r,o,a,l,c,h,f;function g(){n[23].call(r,n[34],n[35])}function p(){n[24].call(c,n[34],n[35])}return{c(){t=Y("tr"),e=Y("td"),e.innerHTML=`<p class="mainDesc svelte-1tlkntj">Y Axis</p> 
              <p class="subDesc svelte-1tlkntj">Set Data Fields (Comma seperated)</p>`,i=Ft(),s=Y("td"),r=Y("input"),o=Ft(),a=Y("br"),l=Ft(),c=Y("input"),X(e,"class","desc svelte-1tlkntj"),X(r,"type","text"),X(r,"placeholder","Name"),X(c,"type","text"),X(c,"placeholder","1, -2, 11, 5"),qt(c,"margin-top","3px"),X(s,"class","controlElement svelte-1tlkntj")},m(b,x){Xe(b,t,x),z(t,e),z(t,i),z(t,s),z(s,r),de(r,n[33].dataTitle),z(s,o),z(s,a),z(s,l),z(s,c),de(c,n[33].data),h||(f=[Jt(r,"input",g),Jt(c,"input",p)],h=!0)},p(b,x){n=b,x[0]&1024&&r.value!==n[33].dataTitle&&de(r,n[33].dataTitle),x[0]&1024&&c.value!==n[33].data&&de(c,n[33].data)},d(b){b&&Ie(t),h=!1,hn(f)}}}function Qx(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,b,x,S,C,T,I,A,F;return{c(){t=Y("hr"),e=Ft(),i=Y("table"),s=Y("tr"),r=Y("td"),r.innerHTML=`<p class="mainDesc svelte-1tlkntj">Line of Best Fit</p> 
            <p class="subDesc svelte-1tlkntj">Create a line of best fit</p>`,o=Y("td"),a=Y("input"),l=Ft(),c=Y("tr"),h=Y("td"),h.innerHTML=`<p class="mainDesc svelte-1tlkntj">Best Fit Line ID</p> 
            <p class="subDesc svelte-1tlkntj">The line ID used to create the line of best fit</p>`,f=Y("td"),g=Y("input"),p=Y("br"),b=Ft(),x=Y("tr"),S=Y("td"),S.innerHTML=`<p class="mainDesc svelte-1tlkntj">Line of Best Fit Title</p> 
            <p class="subDesc svelte-1tlkntj">The title for the line of best fit</p>`,C=Y("td"),T=Y("input"),I=Y("br"),X(r,"class","desc svelte-1tlkntj"),X(a,"type","checkbox"),X(a,"class","task-list-item-checkbox"),qt(a,"width","16px"),qt(a,"height","16px"),X(o,"class","controlElement svelte-1tlkntj"),X(h,"class","desc svelte-1tlkntj"),X(g,"type","text"),X(g,"placeholder","0"),qt(g,"width","26px"),qt(g,"height","32px"),X(f,"class","controlElement svelte-1tlkntj"),X(S,"class","desc svelte-1tlkntj"),X(T,"type","text"),X(T,"placeholder","Line of Best Fit"),qt(T,"width","96px"),qt(T,"height","32px"),X(C,"class","controlElement svelte-1tlkntj"),qt(i,"width","100%"),X(i,"class","svelte-1tlkntj")},m(j,N){Xe(j,t,N),Xe(j,e,N),Xe(j,i,N),z(i,s),z(s,r),z(s,o),z(o,a),a.checked=n[6],z(i,l),z(i,c),z(c,h),z(c,f),z(f,g),de(g,n[8]),z(f,p),z(i,b),z(i,x),z(x,S),z(x,C),z(C,T),de(T,n[7]),z(C,I),A||(F=[Jt(a,"change",n[26]),Jt(g,"input",n[27]),Jt(T,"input",n[28])],A=!0)},p(j,N){N[0]&64&&(a.checked=j[6]),N[0]&256&&g.value!==j[8]&&de(g,j[8]),N[0]&128&&T.value!==j[7]&&de(T,j[7])},d(j){j&&Ie(t),j&&Ie(e),j&&Ie(i),A=!1,hn(F)}}}function t1(n){let t,e,i,s,r,o,a,l,c,h,f,g,p,b,x,S,C,T,I,A,F,j,N,G,U,nt,lt,rt,Pt,zt,et,Ot,St,re,_e,ot,Lt,Bt,Gt,Qt,k,v,y,R,D,O,Z,V,J,tt,xt,Ht,Et,Yt,Xt,ue,we,ye,En,un,An,je,Ai,Rn,ei,As,Ri,ni,qe,Ln,Fn,ii,Rs,fn=n[10],oe=[];for(let mt=0;mt<fn.length;mt+=1)oe[mt]=Jf(Zf(n,fn,mt));return je=new Kf({props:{headerText:"Line of Best Fit (Line chart only)",$$slots:{default:[Qx]},$$scope:{ctx:n}}}),{c(){t=Y("div"),e=Y("h3"),e.textContent="Create a new Chart",i=Ft(),s=Y("div"),r=Y("div"),o=Y("table"),a=Y("tr"),l=Y("td"),l.innerHTML=`<p class="mainDesc svelte-1tlkntj">Chart Type</p> 
            <p class="subDesc svelte-1tlkntj">Choose a Chart Type</p>`,c=Y("td"),h=Y("select"),f=Y("option"),f.textContent="Bar",g=Y("option"),g.textContent="Line",p=Y("option"),p.textContent="Pie",b=Y("option"),b.textContent="Doughnut",x=Y("option"),x.textContent="Radar",S=Y("option"),S.textContent="Polar Area",C=Ft(),T=Y("tr"),I=Y("td"),I.innerHTML=`<p class="mainDesc svelte-1tlkntj">Smoothness</p> 
            <p class="subDesc svelte-1tlkntj">Changes the smoothness of the Chart</p>`,A=Y("td"),F=Y("input"),j=Ft(),N=Y("tr"),G=Y("td"),G.innerHTML=`<p class="mainDesc svelte-1tlkntj">Width</p> 
            <p class="subDesc svelte-1tlkntj">Changes the horizontal width</p>`,U=Y("td"),nt=Y("input"),lt=Ft(),rt=Y("tr"),Pt=Y("td"),Pt.innerHTML=`<p class="mainDesc svelte-1tlkntj">Fill</p> 
            <p class="subDesc svelte-1tlkntj">Fill the underside of the Chart</p>`,zt=Y("td"),et=Y("input"),Ot=Ft(),St=Y("tr"),re=Y("td"),re.innerHTML=`<p class="mainDesc svelte-1tlkntj">Distinct Colors</p> 
            <p class="subDesc svelte-1tlkntj">Use distinct Colors for each Label</p>`,_e=Y("td"),ot=Y("input"),Lt=Ft(),Bt=Y("tr"),Gt=Y("td"),Gt.innerHTML=`<p class="mainDesc svelte-1tlkntj">Start at Zero</p> 
            <p class="subDesc svelte-1tlkntj">Don&#39;t cut the graph at the bottom</p>`,Qt=Y("td"),k=Y("input"),v=Ft(),y=Y("hr"),R=Ft(),D=Y("table"),O=Y("tr"),Z=Y("td"),Z.innerHTML=`<p class="mainDesc svelte-1tlkntj">X Axis</p> 
            <p class="subDesc svelte-1tlkntj">Set Labels (Comma seperated)</p>`,V=Ft(),J=Y("td"),tt=Y("input"),xt=Y("br"),Ht=Ft(),Et=Y("hr"),Yt=Ft(),Xt=Y("table");for(let mt=0;mt<oe.length;mt+=1)oe[mt].c();ue=Ft(),we=Y("div"),ye=Y("button"),ye.textContent="Add more",En=Ft(),un=Y("hr"),An=Ft(),Gf(je.$$.fragment),Ai=Ft(),Rn=Y("div"),ei=Y("div"),As=Ft(),Ri=Y("hr"),ni=Ft(),qe=Y("div"),Ln=Y("button"),Ln.textContent="Insert Chart",X(l,"class","desc svelte-1tlkntj"),f.__value="bar",f.value=f.__value,g.__value="line",g.value=g.__value,p.__value="pie",p.value=p.__value,b.__value="doughnut",b.value=b.__value,x.__value="radar",x.value=x.__value,S.__value="polarArea",S.value=S.__value,X(h,"name","Chart Types"),X(h,"id","chart-types"),X(h,"class","dropdown"),n[0]===void 0&&Ds(()=>n[16].call(h)),X(c,"class","controlElement svelte-1tlkntj"),X(I,"class","desc svelte-1tlkntj"),X(F,"type","range"),X(F,"min","0"),X(F,"max","100"),X(F,"class","slider"),X(A,"class","controlElement svelte-1tlkntj"),X(G,"class","desc svelte-1tlkntj"),X(nt,"type","range"),X(nt,"min","20"),X(nt,"max","100"),X(nt,"class","slider"),X(U,"class","controlElement svelte-1tlkntj"),X(Pt,"class","desc svelte-1tlkntj"),X(et,"type","checkbox"),X(et,"class","task-list-item-checkbox"),qt(et,"width","16px"),qt(et,"height","16px"),X(zt,"class","controlElement svelte-1tlkntj"),X(re,"class","desc svelte-1tlkntj"),X(ot,"type","checkbox"),X(ot,"class","task-list-item-checkbox"),qt(ot,"width","16px"),qt(ot,"height","16px"),X(_e,"class","controlElement svelte-1tlkntj"),X(Gt,"class","desc svelte-1tlkntj"),X(k,"type","checkbox"),X(k,"class","task-list-item-checkbox"),qt(k,"width","16px"),qt(k,"height","16px"),X(Qt,"class","controlElement svelte-1tlkntj"),qt(o,"width","100%"),X(o,"class","svelte-1tlkntj"),X(Z,"class","desc svelte-1tlkntj"),X(tt,"type","text"),X(tt,"placeholder","Monday, Tuesday, ..."),X(J,"class","controlElement svelte-1tlkntj"),qt(D,"width","100%"),X(D,"class","svelte-1tlkntj"),X(we,"class","addMoreButtonContainer svelte-1tlkntj"),qt(Xt,"width","100%"),X(Xt,"class","svelte-1tlkntj"),X(ei,"id","preview"),X(Rn,"class","chartPreview svelte-1tlkntj"),X(s,"class","modalColumn svelte-1tlkntj"),X(t,"class","chart-modal svelte-1tlkntj"),X(Ln,"class","mod-cta"),qt(qe,"display","flex"),qt(qe,"justify-content","center"),qt(qe,"align-items","center")},m(mt,te){Xe(mt,t,te),z(t,e),z(t,i),z(t,s),z(s,r),z(r,o),z(o,a),z(a,l),z(a,c),z(c,h),z(h,f),z(h,g),z(h,p),z(h,b),z(h,x),z(h,S),ml(h,n[0],!0),z(o,C),z(o,T),z(T,I),z(T,A),z(A,F),de(F,n[1]),z(o,j),z(o,N),z(N,G),z(N,U),z(U,nt),de(nt,n[2]),z(o,lt),z(o,rt),z(rt,Pt),z(rt,zt),z(zt,et),et.checked=n[3],z(o,Ot),z(o,St),z(St,re),z(St,_e),z(_e,ot),ot.checked=n[4],z(o,Lt),z(o,Bt),z(Bt,Gt),z(Bt,Qt),z(Qt,k),k.checked=n[5],z(r,v),z(r,y),z(r,R),z(r,D),z(D,O),z(O,Z),z(O,V),z(O,J),z(J,tt),de(tt,n[9]),z(J,xt),z(r,Ht),z(r,Et),z(r,Yt),z(r,Xt);for(let Ge=0;Ge<oe.length;Ge+=1)oe[Ge]&&oe[Ge].m(Xt,null);z(Xt,ue),z(Xt,we),z(we,ye),z(r,En),z(r,un),z(r,An),yl(je,r,null),z(s,Ai),z(s,Rn),z(Rn,ei),n[29](ei),z(t,As),z(t,Ri),Xe(mt,ni,te),Xe(mt,qe,te),z(qe,Ln),Fn=!0,ii||(Rs=[Jt(h,"change",n[16]),Jt(F,"change",n[17]),Jt(F,"input",n[17]),Jt(nt,"change",n[18]),Jt(nt,"input",n[18]),Jt(et,"change",n[19]),Jt(ot,"change",n[20]),Jt(k,"change",n[21]),Jt(tt,"input",n[22]),Jt(ye,"click",n[25]),Jt(Ln,"click",n[12])],ii=!0)},p(mt,te){if(te[0]&1&&ml(h,mt[0]),te[0]&2&&de(F,mt[1]),te[0]&4&&de(nt,mt[2]),te[0]&8&&(et.checked=mt[3]),te[0]&16&&(ot.checked=mt[4]),te[0]&32&&(k.checked=mt[5]),te[0]&512&&tt.value!==mt[9]&&de(tt,mt[9]),te[0]&1024){fn=mt[10];let pe;for(pe=0;pe<fn.length;pe+=1){let Ls=Zf(mt,fn,pe);oe[pe]?oe[pe].p(Ls,te):(oe[pe]=Jf(Ls),oe[pe].c(),oe[pe].m(Xt,ue))}for(;pe<oe.length;pe+=1)oe[pe].d(1);oe.length=fn.length}let Ge={};te[0]&448|te[1]&32&&(Ge.$$scope={dirty:te,ctx:mt}),je.$set(Ge)},i(mt){Fn||(Os(je.$$.fragment,mt),Fn=!0)},o(mt){ao(je.$$.fragment,mt),Fn=!1},d(mt){mt&&Ie(t),Wf(oe,mt),lo(je),n[29](null),mt&&Ie(ni),mt&&Ie(qe),ii=!1,hn(Rs)}}}function e1(n,t,e){let{editor:i}=t,{renderer:s}=t,r=bl(),o="bar",a=null,l=20,c=80,h=!1,f=!1,g=!1,p=!1,b,x="0",S="",C=[{dataTitle:"",data:""}],T,I=null,A=(0,ho.debounce)(async(ot,Lt)=>{var Bt;a&&a.destroy(),(Bt=I.lastElementChild)===null||Bt===void 0||Bt.remove(),a=s.renderRaw(await s.datasetPrep((0,ho.parseYaml)(ot),Lt),Lt)},500,!0);function F(){let ot=i.getDoc(),Lt=ot.getCursor();a.destroy(),ot.replaceRange("```chart\n"+T+"\n```",Lt),r("close")}function j(){o=Yf(this),e(0,o)}function N(){l=gl(this.value),e(1,l)}function G(){c=gl(this.value),e(2,c)}function U(){h=this.checked,e(3,h)}function nt(){f=this.checked,e(4,f)}function lt(){g=this.checked,e(5,g)}function rt(){S=this.value,e(9,S)}function Pt(ot,Lt){ot[Lt].dataTitle=this.value,e(10,C)}function zt(ot,Lt){ot[Lt].data=this.value,e(10,C)}let et=()=>e(10,C=[...C,{data:"",dataTitle:""}]);function Ot(){p=this.checked,e(6,p)}function St(){x=this.value,e(8,x)}function re(){b=this.value,e(7,b)}function _e(ot){ro[ot?"unshift":"push"](()=>{I=ot,e(11,I)})}return n.$$set=ot=>{"editor"in ot&&e(13,i=ot.editor),"renderer"in ot&&e(14,s=ot.renderer)},n.$$.update=()=>{if(n.$$.dirty[0]&2047){t:e(15,T=`type: ${o}
labels: [${S}]
series:
${C.map(ot=>`  - title: ${ot.dataTitle}
    data: [${ot.data}]`).join(`
`)}
tension: ${l/100}
width: ${c}%
labelColors: ${f}
fill: ${h}
beginAtZero: ${g}
bestFit: ${p}
bestFitTitle: ${b}
bestFitNumber: ${x}`)}if(n.$$.dirty[0]&34816){t:if(I)try{A(T,I)}catch(ot){cn(ot,I)}}},[o,l,c,h,f,g,p,b,x,S,C,I,F,i,s,T,j,N,G,U,nt,lt,rt,Pt,zt,et,Ot,St,re,_e]}var Qf=class extends Es{constructor(t){super();co(this,t,e1,t1,eo,{editor:13,renderer:14},Jx,[-1,-1])}},td=Qf;var uo=class extends ed.Modal{constructor(t,e,i,s){super(t);this.settings=i,this.view=e,this.renderer=s}onOpen(){let{contentEl:t,view:e,settings:i,renderer:s}=this;t.empty(),new td({target:t,props:{editor:e.editor,renderer:s}}).$on("close",()=>this.close())}onClose(){let{contentEl:t}=this;t.empty()}};var nd=ze(require("obsidian")),id={chart:'<svg xmlns="http://www.w3.org/2000/svg" fill-opacity="0.0" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pie-chart"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path></svg>'},sd=()=>{Object.keys(id).forEach(n=>{(0,nd.addIcon)(n,id[n])})};var xl=class extends $e.Plugin{constructor(){super(...arguments);this.postprocessor=async(t,e,i)=>{let s;try{s=await(0,$e.parseYaml)(t.replace(/	/g,"    "))}catch(a){cn(a,e);return}if(!s.id&&(!s||!s.type||!s.labels||!s.series)){cn("Missing type, labels or series",e);return}if(s.bestFit===!0&&s.type==="line"){if(s.bestFitNumber!=null)var r=s.series[Number(s.bestFitNumber)].data;else var r=s.series[0].data;let a=s.labels,l=0,c=0,h=0,f=0;for(let x=0;x<r.length;++x)l=l+r[x],c=c+a[x],h=h+r[x]*r[x],f=f+r[x]*a[x];let g=(r.length*f-c*l)/(r.length*h-l*l),p=(c-g*l)/r.length,b=[];for(let x=0;x<a.length;++x)b.push((a[x]-p)/g);if(s.bestFitTitle!=null&&s.bestFitTitle!="undefined")var o=String(s.bestFitTitle);else var o="Line of Best Fit";s.series.push({title:o,data:b})}await this.renderer.renderFromYaml(s,e,i)}}async loadSettings(){this.settings=Object.assign({},Kr,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async onload(){console.log("loading plugin: Obsidian Charts"),await this.loadSettings(),sd(),this.renderer=new Ur(this),window.renderChart=this.renderer.renderRaw,this.addSettingTab(new fl(this.app,this)),this.addCommand({id:"creation-helper",name:"Insert new Chart",checkCallback:t=>{let e=this.app.workspace.activeLeaf;return e.view instanceof $e.MarkdownView?(t||new uo(this.app,e.view,this.settings,this.renderer).open(),!0):!1}}),this.addCommand({id:"chart-from-table-column",name:"Create Chart from Table (Column oriented Layout)",editorCheckCallback:(t,e,i)=>{let s=e.getSelection();return i instanceof $e.MarkdownView&&s.split(`
`).length>=3&&s.split("|").length>=2?(t||Na(e,"columns"),!0):!1}}),this.addCommand({id:"chart-from-table-row",name:"Create Chart from Table (Row oriented Layout)",editorCheckCallback:(t,e,i)=>i instanceof $e.MarkdownView&&e.getSelection().split(`
`).length>=3&&e.getSelection().split("|").length>=2?(t||Na(e,"rows"),!0):!1}),this.addCommand({id:"chart-to-svg",name:"Create Image from Chart",editorCheckCallback:(t,e,i)=>i instanceof $e.MarkdownView&&e.getSelection().startsWith("```chart")&&e.getSelection().endsWith("```")?(t||(new $e.Notice("Rendering Chart..."),zu(e,this.app,this.renderer,i.file,this.settings)),!0):!1}),this.registerMarkdownCodeBlockProcessor("chart",this.postprocessor),this.registerMarkdownCodeBlockProcessor("advanced-chart",async(t,e)=>this.renderer.renderRaw(await JSON.parse(t),e)),this.registerEvent(this.app.workspace.on("editor-menu",(t,e,i)=>{i&&this.settings.contextMenu&&t.addItem(s=>{s.setTitle("Insert Chart").setIcon("chart").onClick(r=>{new uo(this.app,i,this.settings,this.renderer).open()})})}))}onunload(){console.log("unloading plugin: Obsidian Charts")}};
/*
 * @license
 *
 * Copyright (c) 2011-2014, Christopher Jeffrey. (MIT Licensed)
 * https://github.com/chjj/marked
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/*!
  * chartjs-adapter-moment v1.0.0
  * https://www.chartjs.org
  * (c) 2021 chartjs-adapter-moment Contributors
  * Released under the MIT license
  */
/*!
 * @kurkle/color v0.2.1
 * https://github.com/kurkle/color#readme
 * (c) 2022 Jukka Kurkela
 * Released under the MIT License
 */
/*!
 * Chart.js v3.9.1
 * https://www.chartjs.org
 * (c) 2022 Chart.js Contributors
 * Released under the MIT License
 */
/*!
 * vanilla-picker v2.12.1
 * https://vanilla-picker.js.org
 *
 * Copyright 2017-2021 Andreas Borgen (https://github.com/Sphinxxxx), Adam Brooks (https://github.com/dissimulate)
 * Released under the ISC license.
 */
/*!
* chartjs-plugin-annotation v2.2.1
* https://www.chartjs.org/chartjs-plugin-annotation/index
 * (c) 2023 chartjs-plugin-annotation Contributors
 * Released under the MIT License
 */
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */
/**
 * @license
 *
 * Copyright (c) 2011-2014, Christopher Jeffrey. (MIT Licensed)
 * https://github.com/chjj/marked
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/**
 * @license
 *
 * Copyright (c) 2018-2021, Костя Третяк. (MIT Licensed)
 * https://github.com/ts-stack/markdown
 */
/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2019, Gregor Aisch
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name Gregor Aisch may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */
