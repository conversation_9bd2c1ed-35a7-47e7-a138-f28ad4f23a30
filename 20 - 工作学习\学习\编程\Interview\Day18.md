---
title: Day18
date: 2025-01-15 15:51:30
categories:
  - - 笔记
    - 编程
    - 面试训练营
tags:
  - 计算机网络
---
**2025-01-15**🌱上海: ☀️   🌡️+6°C 🌬️↓18km/h
## 常见的HTTP状态码有哪些？

### HTTP状态码都有什么？

![image.png](https://cdn.easymuzi.cn/img/20250115155220715.png)


### 趣图解译

![image.png](https://cdn.easymuzi.cn/img/20250115155232940.png)


![image.png](https://cdn.easymuzi.cn/img/20250115155244843.png)


![image.png](https://cdn.easymuzi.cn/img/20250115155257345.png)


#### HTTP概述

HTTP 是获取 HTML 文档等资源的协议，是 Web 数据交换基础，属于客户端 — 服务器协议，由接受方（常为 Web 浏览器）发起请求，完整网页文档包含文本、布局描述、图片等多种资源 。

![image.png](https://cdn.easymuzi.cn/img/20250115155308580.png)


客户端与服务端之间通过交换一个个独立的消息（而非数据流）进行通信。由客户端发出的消息被称作请求（request），由服务端发出的应答消息被称作响应（response）。

![image.png](https://cdn.easymuzi.cn/img/20250115155325926.png)


20 世纪 90 年代设计出可扩展的 HTTP 协议，且随时间演进。它是应用层协议，通过 TCP 或 TLS 发送，理论上可借助任何可靠传输协议。因其可扩展性，如今不仅能获取超文本文档、图片、视频，还能向服务端发送信息，也可获取文档部分内容用于按需更新网页 。

#### 基于HTTP的系统的组成

HTTP 是客户端 - 服务器协议，请求由用户代理或代理方发出（常见用户代理为 Web 浏览器，也有其他形式），发往服务器并获响应，客户端和服务器间存在起不同作用（如网关、缓存）的代理实体 。

![image.png](https://cdn.easymuzi.cn/img/20250115155338466.png)


- **通信层级**：浏览器与服务器间有路由器等众多计算机，因 Web 分层设计，它们处于网络层和传输层，HTTP 则在应用层，下层对 HTTP 设计大多无关。
- **客户端**：

- 用户代理代表用户行为，主要是浏览器，也可是调试程序。
- 浏览器先请求获取 HTML 文档，解析后再请求脚本、CSS 等子资源，整合展现网页。后续脚本还能获取资源更新网页。
- 网页是超文本文档，含链接，浏览器将用户点击链接的指示转为 HTTP 请求，解析响应后呈现给用户。

- **服务器**：负责提供客户端请求的文档，可能是单台机器，也可是服务器集群或多种软件；同一机器可托管多个服务器软件实例，借助 HTTP/1.1 和 Host 标头还能共用 IP 地址。
- **代理**：在浏览器和服务器间，部分参与消息传递的应用层实体是代理。代理可透明转发请求，也可修改请求，能发挥缓存、过滤、负载均衡、认证、日志等作用 。

#### HTTP的基本性质

1. **简约性**：整体设计简单易读，虽 HTTP/2 中消息封装进帧增加复杂度，但 HTTP 报文便于人理解，利于开发者测试和初学者学习。
2. **可扩展性**：HTTP/1.0 引入的标头使协议易扩展和实验，服务器与客户端协商新标头语义后就能添加新功能。
3. **无状态与有会话**：HTTP 无状态，同一连接中成功请求间无关联，影响用户连贯交互（如购物车功能）。借助 HTTP Cookie 可实现有状态会话，利用标头扩展性将其加入协议流程，让请求共享上下文或状态。
4. **与连接的关系**：

- 连接由传输层控制，非 HTTP 范畴。HTTP 只需底层传输协议可靠（不丢消息或能告知错误），因 TCP 可靠，HTTP 依赖面向连接的 TCP 标准。
- 客户端和服务器传递请求、响应前需建立 TCP 连接，过程需多次往返交互。HTTP/1.0 为每对请求 / 响应单独打开 TCP 连接，连续请求时效率低。
- HTTP/1.1 引入流水线（实现困难）和持久化连接，通过 Connection 标头部分控制 TCP 连接。HTTP/2 进一步在一个连接中复合多个消息，提高连接效率。
- 为适配 HTTP，各方开展实验，如 Google 测试基于 UDP 构建的更可靠高效的传输协议 QUIC。

#### HTTP报文

HTTP/1.1 及更早协议报文语义可读，HTTP/2 中报文被嵌入新的二进制结构 “帧”。帧能实现诸多优化，如标头压缩与多路复用。即便部分原始 HTTP 报文以 HTTP/2 发送，报文语义不变，客户端会重组原始 HTTP/1.1 请求，所以以 HTTP/1.1 格式理解 HTTP/2 报文依然有效 。

**HTTP请求的例子**

![image.png](https://cdn.easymuzi.cn/img/20250115155430806.png)


请求由以下元素组成：

- HTTP 方法，通常是由一个动词，像 GET、POST 等，或者一个名词，像 OPTIONS、HEAD 等，来定义客户端执行的动作。典型场景有：客户端意图获取某个资源（使用 `GET`）；发送 HTML 表单的参数值（使用 `POST`）；以及其他情况下需要的那些其他操作。
- 要获取的那个资源的路径——去除了当前上下文中显而易见的信息之后的 URL，比如说，它不包括协议（`http://`）、域名（这里是 `developer.mozilla.org`），或是 TCP 的端口（这里是 `80`）。
- HTTP 协议版本号。
- 为服务端表达其他信息的可选标头。
- 请求体（body），类似于响应中的请求体，一些像 `POST` 这样的方法，请求体内包含需要了发送的资源。

**HTTP响应的例子**

![image.png](https://cdn.easymuzi.cn/img/20250115155503052.png)


响应报文包含了下面的元素：

- HTTP 协议版本号。
- 状态码，来指明对应请求已成功执行与否，以及不成功时相应的原因。
- 状态信息，这个信息是一个不权威、简短的状态码描述。
- HTTP 标头，与请求标头类似。
- 可选项，一个包含了被获取资源的主体。

**参考链接**

[HTTP 概述 - HTTP | MDN](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Overview)

### HTTP/1.0和HTTP/2的区别（ai的总结，具体细节可以自己搜索学习）

HTTP/2 是 HTTP/1 的重大升级，它们在性能、协议结构、功能特性等方面存在诸多区别：

#### 性能表现

- **HTTP/1**：

- **连接方式**：HTTP/1.0 默认每个请求 / 响应都需要建立一个新的 TCP 连接，完成交互后关闭连接。这在连续发送多个请求时，会造成大量的连接建立和关闭开销，降低效率。HTTP/1.1 虽引入了持久连接（Connection: keep-alive），允许在同一个 TCP 连接上发送多个请求，但多个请求仍需按顺序排队发送，前一个请求的响应接收完成后，才能发送下一个请求，存在队头阻塞问题。
- **资源加载**：在加载包含多个资源（如图片、脚本、样式表等）的网页时，由于队头阻塞，会导致页面加载时间较长。

- **HTTP/2**：

- **多路复用**：这是 HTTP/2 的核心特性之一。它允许在同一个 TCP 连接上同时发送和接收多个请求和响应，各个请求和响应之间不会相互阻塞。比如，浏览器可以同时向服务器发送获取 HTML、CSS、JavaScript 和图片等资源的请求，服务器也能交错地返回这些响应，大大提高了资源的加载速度。
- **头部压缩**：HTTP/2 使用 HPACK 算法对请求和响应的头部进行压缩。在 HTTP/1 中，每个请求都需要携带完整的头部信息，随着请求数量增加，头部信息的冗余会占用大量带宽。而 HTTP/2 通过压缩头部，减少了头部数据的大小，降低了传输开销，提高了性能。

#### 协议结构

- **HTTP/1**：报文采用文本格式，由起始行（请求行或状态行）、头部字段和消息体组成，各个部分之间通过回车换行符分隔。这种文本格式的优点是可读性强，便于开发人员调试和理解，但解析和处理效率相对较低。
- **HTTP/2**：采用了二进制分帧层，将 HTTP 报文分解为独立的帧，通过帧来传输数据。这种二进制结构更加紧凑和高效，能够让计算机更快速地解析和处理，提高了协议的处理速度和性能。

#### 功能特性

- **HTTP/1**：支持基本的请求方法（如 GET、POST、PUT、DELETE 等）和头部字段，功能相对较为基础。在安全性方面，需要借助 SSL/TLS 协议来实现加密传输，通常需要额外配置。
- **HTTP/2**：

- **服务器推送**：服务器可以主动向客户端推送资源，而无需客户端明确请求。例如，服务器在接收到客户端对 HTML 页面的请求时，能够预测客户端可能需要的其他资源（如 CSS、JavaScript 文件），并主动将这些资源推送给客户端，进一步减少了客户端的等待时间，提升了用户体验。
- **增强的安全性**：HTTP/2 要求在传输层使用 TLS 协议进行加密，提供了更安全的通信环境，减少了数据被窃取或篡改的风险。

## HTTP请求包含哪些内容，请求头和请求体有哪些类型？

### 思维导图总结

![image.png](https://cdn.easymuzi.cn/img/20250115155516136.png)


可以通过浏览器F12进行查看一个网络的请求信息

![image.png](https://cdn.easymuzi.cn/img/20250115155523547.png)


## HTTP中get和post的区别是什么？

### 思维导图总结

![image.png](https://cdn.easymuzi.cn/img/20250115155542877.png)


### 扩展分析

| **比较维度**       | **GET**                                      | **POST**                                                                                            |
| -------------- | -------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| 数据传输方式与限制      | 参数通过 URL 传递，受 URL 长度限制，一般约 2048 字节，不适合大数据传输  | 数据放在请求体中，理论无长度限制，实际服务器有配置限制，如 Nginx 默认 1MB，可调整                                                      |
| 数据安全性          | 参数出现在 URL 中，可能被浏览器缓存、日志记录等，易泄露信息，不适合传敏感信息    | 数据位于请求体，比 GET 隐蔽，配合 HTTPS 可增强安全                                                                     |
| 缓存机制           | 可被浏览器和 CDN 缓存，适合不频繁变动资源                      | 默认不被浏览器和缓存服务器缓存，因常影响服务器数据                                                                           |
| 幂等性与安全性        | 幂等，重复请求不影响服务器资源状态；是安全的，只获取数据                 | 非幂等，重复请求可能导致重复操作；不安全，可能更改服务器数据                                                                      |
| RESTful API 角色 | 用于查询或检索资源数据                                  | 用于创建资源或执行某些动作                                                                                       |
| 网络层差异          | 默认无状态、响应快，常复用已有 TCP 连接；请求行和头（含 URL 参数）可一次性发送 | 数据量大时可能用新 TCP 连接；数据放请求体，量大需分段传输，HTTPS 下拆包多，开销稍高                                                     |
| 浏览器行为与编码       | 重定向后自动保留 GET 方法；URL 编码为百分比编码                 | 重定向会转换为 GET；支持多种内容类型，如 application/x-www-form-urlencoded、multipart/form-data、application/json 等编码格式 |
| 书签             | 可收藏为书签                                       | 不可收藏为书签                                                                                             |

**通过网络上搜索的相关知识学习，还了解到一些不同的点**

GET产生**一个TCP数据包**；POST产生**两个TCP数据包**。对于GET方式的请求，浏览器会把http header和data一并发送出去，服务器响应200（返回数据）；而对于POST，浏览器先发送header，服务器响应100 continue，浏览器再发送data，服务器响应200 ok（返回数据）。

**GET和POST本质上就是TCP链接，并无差别。但是由于HTTP的规定和浏览器/服务器的限制，导致他们在应用过程中体现出一些不同。**
