```dataviewjs

let folderChoicePath = "00 - 每日日记"
const files = app.vault.getMarkdownFiles().filter(file => file.path.includes(folderChoicePath))

let arr = files.map(async(file) => { 
    const content = await app.vault.cachedRead(file) 
    let lines = await content.split("\n").filter(line => line.includes("#记录")) 
    //console.log(lines) 
    return [dv.fileLink(file.name.split(".")[0], false, moment(file.name.split(".")[0], "YYYY-MM-DD").format("M月D日")), lines] 
}) 

Promise.all(arr).then(values => { 
    const beautify = values.map(value => { 
        const temp = value[1].map(line => { return line.slice(4,) }) //美化要重写
        return [value[0],temp] 
    }) 
    const exists = beautify.filter(value => value[1][0] && value[0] != "[[未命名 10]]").slice(0,7).sort().reverse()
    dv.table(["Date", "Bird Log"], exists)
})
```