/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository
*/

/*
License obsidian-tasks:
MIT License

Copyright (c) 2021 <PERSON> and <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/

/*
License rrule (included library):
rrule.js: Library for working with recurrence rules for calendar dates.
=======================================================================

Copyright 2010, Jakub Roztocil <<EMAIL>> and Lars Schöning

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.

    3. Neither the name of The author nor the names of its contributors may
       be used to endorse or promote products derived from this software
       without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE AUTHOR AND CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.



./rrule.js and ./test/tests.js is based on python-dateutil. LICENCE:

python-dateutil - Extensions to the standard Python datetime module.
====================================================================

Copyright (c) 2003-2011 - Gustavo Niemeyer <<EMAIL>>
Copyright (c) 2012 - Tomi Pieviläinen <<EMAIL>>

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright notice,
      this list of conditions and the following disclaimer in the documentation
      and/or other materials provided with the distribution.
    * Neither the name of the copyright holder nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/*
License chrono-node (included library):
The MIT License

Copyright (c) 2014, Wanasit Tanakitrungruang

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

"use strict";var hb=Object.create;var Ui=Object.defineProperty,gb=Object.defineProperties,yb=Object.getOwnPropertyDescriptor,_b=Object.getOwnPropertyDescriptors,Tb=Object.getOwnPropertyNames,yp=Object.getOwnPropertySymbols,bb=Object.getPrototypeOf,Tp=Object.prototype.hasOwnProperty,vb=Object.prototype.propertyIsEnumerable;var _p=(r,e,t)=>e in r?Ui(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,he=(r,e)=>{for(var t in e||(e={}))Tp.call(e,t)&&_p(r,t,e[t]);if(yp)for(var t of yp(e))vb.call(e,t)&&_p(r,t,e[t]);return r},Dt=(r,e)=>gb(r,_b(e));var b=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),Eb=(r,e)=>{for(var t in e)Ui(r,t,{get:e[t],enumerable:!0})},bp=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Tb(e))!Tp.call(r,i)&&i!==t&&Ui(r,i,{get:()=>e[i],enumerable:!(n=yb(e,i))||n.enumerable});return r};var Wi=(r,e,t)=>(t=r!=null?hb(bb(r)):{},bp(e||!r||!r.__esModule?Ui(t,"default",{value:r,enumerable:!0}):t,r)),wb=r=>bp(Ui({},"__esModule",{value:!0}),r);var F=(r,e,t)=>new Promise((n,i)=>{var s=u=>{try{o(t.next(u))}catch(l){i(l)}},a=u=>{try{o(t.throw(u))}catch(l){i(l)}},o=u=>u.done?n(u.value):Promise.resolve(u.value).then(s,a);o((t=t.apply(r,e)).next())});var Ee=b(jr=>{"use strict";Object.defineProperty(jr,"__esModule",{value:!0});jr.matchAnyPattern=jr.extractTerms=jr.repeatedTimeunitPattern=void 0;function Sb(r,e){let t=e.replace(/\((?!\?)/g,"(?:");return`${r}${t}\\s{0,5}(?:,?\\s{0,5}${t}){0,10}`}jr.repeatedTimeunitPattern=Sb;function vp(r){let e;return r instanceof Array?e=[...r]:r instanceof Map?e=Array.from(r.keys()):e=Object.keys(r),e}jr.extractTerms=vp;function Rb(r){return`(?:${vp(r).sort((t,n)=>n.length-t.length).join("|").replace(/\./g,"\\.")})`}jr.matchAnyPattern=Rb});var ge=b((_u,Tu)=>{(function(r,e){typeof _u=="object"&&typeof Tu!="undefined"?Tu.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs=e()})(_u,function(){"use strict";var r=1e3,e=6e4,t=36e5,n="millisecond",i="second",s="minute",a="hour",o="day",u="week",l="month",c="quarter",p="year",m="date",T="Invalid Date",y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,E=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,M={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},U=function(j,A,w){var B=String(j);return!B||B.length>=A?j:""+Array(A+1-B.length).join(w)+j},q={s:U,z:function(j){var A=-j.utcOffset(),w=Math.abs(A),B=Math.floor(w/60),C=w%60;return(A<=0?"+":"-")+U(B,2,"0")+":"+U(C,2,"0")},m:function j(A,w){if(A.date()<w.date())return-j(w,A);var B=12*(w.year()-A.year())+(w.month()-A.month()),C=A.clone().add(B,l),X=w-C<0,d=A.clone().add(B+(X?-1:1),l);return+(-(B+(w-C)/(X?C-d:d-C))||0)},a:function(j){return j<0?Math.ceil(j)||0:Math.floor(j)},p:function(j){return{M:l,y:p,w:u,d:o,D:m,h:a,m:s,s:i,ms:n,Q:c}[j]||String(j||"").toLowerCase().replace(/s$/,"")},u:function(j){return j===void 0}},W="en",ie={};ie[W]=M;var re=function(j){return j instanceof Pe},ve=function(j,A,w){var B;if(!j)return W;if(typeof j=="string")ie[j]&&(B=j),A&&(ie[j]=A,B=j);else{var C=j.name;ie[C]=j,B=C}return!w&&B&&(W=B),B||!w&&W},G=function(j,A){if(re(j))return j.clone();var w=typeof A=="object"?A:{};return w.date=j,w.args=arguments,new Pe(w)},K=q;K.l=ve,K.i=re,K.w=function(j,A){return G(j,{locale:A.$L,utc:A.$u,x:A.$x,$offset:A.$offset})};var Pe=function(){function j(w){this.$L=ve(w.locale,null,!0),this.parse(w)}var A=j.prototype;return A.parse=function(w){this.$d=function(B){var C=B.date,X=B.utc;if(C===null)return new Date(NaN);if(K.u(C))return new Date;if(C instanceof Date)return new Date(C);if(typeof C=="string"&&!/Z$/i.test(C)){var d=C.match(y);if(d){var f=d[2]-1||0,h=(d[7]||"0").substring(0,3);return X?new Date(Date.UTC(d[1],f,d[3]||1,d[4]||0,d[5]||0,d[6]||0,h)):new Date(d[1],f,d[3]||1,d[4]||0,d[5]||0,d[6]||0,h)}}return new Date(C)}(w),this.$x=w.x||{},this.init()},A.init=function(){var w=this.$d;this.$y=w.getFullYear(),this.$M=w.getMonth(),this.$D=w.getDate(),this.$W=w.getDay(),this.$H=w.getHours(),this.$m=w.getMinutes(),this.$s=w.getSeconds(),this.$ms=w.getMilliseconds()},A.$utils=function(){return K},A.isValid=function(){return this.$d.toString()!==T},A.isSame=function(w,B){var C=G(w);return this.startOf(B)<=C&&C<=this.endOf(B)},A.isAfter=function(w,B){return G(w)<this.startOf(B)},A.isBefore=function(w,B){return this.endOf(B)<G(w)},A.$g=function(w,B,C){return K.u(w)?this[B]:this.set(C,w)},A.unix=function(){return Math.floor(this.valueOf()/1e3)},A.valueOf=function(){return this.$d.getTime()},A.startOf=function(w,B){var C=this,X=!!K.u(B)||B,d=K.p(w),f=function(D,P){var me=K.w(C.$u?Date.UTC(C.$y,P,D):new Date(C.$y,P,D),C);return X?me:me.endOf(o)},h=function(D,P){return K.w(C.toDate()[D].apply(C.toDate("s"),(X?[0,0,0,0]:[23,59,59,999]).slice(P)),C)},g=this.$W,_=this.$M,v=this.$D,k="set"+(this.$u?"UTC":"");switch(d){case p:return X?f(1,0):f(31,11);case l:return X?f(1,_):f(0,_+1);case u:var S=this.$locale().weekStart||0,O=(g<S?g+7:g)-S;return f(X?v-O:v+(6-O),_);case o:case m:return h(k+"Hours",0);case a:return h(k+"Minutes",1);case s:return h(k+"Seconds",2);case i:return h(k+"Milliseconds",3);default:return this.clone()}},A.endOf=function(w){return this.startOf(w,!1)},A.$set=function(w,B){var C,X=K.p(w),d="set"+(this.$u?"UTC":""),f=(C={},C[o]=d+"Date",C[m]=d+"Date",C[l]=d+"Month",C[p]=d+"FullYear",C[a]=d+"Hours",C[s]=d+"Minutes",C[i]=d+"Seconds",C[n]=d+"Milliseconds",C)[X],h=X===o?this.$D+(B-this.$W):B;if(X===l||X===p){var g=this.clone().set(m,1);g.$d[f](h),g.init(),this.$d=g.set(m,Math.min(this.$D,g.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},A.set=function(w,B){return this.clone().$set(w,B)},A.get=function(w){return this[K.p(w)]()},A.add=function(w,B){var C,X=this;w=Number(w);var d=K.p(B),f=function(_){var v=G(X);return K.w(v.date(v.date()+Math.round(_*w)),X)};if(d===l)return this.set(l,this.$M+w);if(d===p)return this.set(p,this.$y+w);if(d===o)return f(1);if(d===u)return f(7);var h=(C={},C[s]=e,C[a]=t,C[i]=r,C)[d]||1,g=this.$d.getTime()+w*h;return K.w(g,this)},A.subtract=function(w,B){return this.add(-1*w,B)},A.format=function(w){var B=this,C=this.$locale();if(!this.isValid())return C.invalidDate||T;var X=w||"YYYY-MM-DDTHH:mm:ssZ",d=K.z(this),f=this.$H,h=this.$m,g=this.$M,_=C.weekdays,v=C.months,k=function(P,me,ae,te){return P&&(P[me]||P(B,X))||ae[me].substr(0,te)},S=function(P){return K.s(f%12||12,P,"0")},O=C.meridiem||function(P,me,ae){var te=P<12?"AM":"PM";return ae?te.toLowerCase():te},D={YY:String(this.$y).slice(-2),YYYY:this.$y,M:g+1,MM:K.s(g+1,2,"0"),MMM:k(C.monthsShort,g,v,3),MMMM:k(v,g),D:this.$D,DD:K.s(this.$D,2,"0"),d:String(this.$W),dd:k(C.weekdaysMin,this.$W,_,2),ddd:k(C.weekdaysShort,this.$W,_,3),dddd:_[this.$W],H:String(f),HH:K.s(f,2,"0"),h:S(1),hh:S(2),a:O(f,h,!0),A:O(f,h,!1),m:String(h),mm:K.s(h,2,"0"),s:String(this.$s),ss:K.s(this.$s,2,"0"),SSS:K.s(this.$ms,3,"0"),Z:d};return X.replace(E,function(P,me){return me||D[P]||d.replace(":","")})},A.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},A.diff=function(w,B,C){var X,d=K.p(B),f=G(w),h=(f.utcOffset()-this.utcOffset())*e,g=this-f,_=K.m(this,f);return _=(X={},X[p]=_/12,X[l]=_,X[c]=_/3,X[u]=(g-h)/6048e5,X[o]=(g-h)/864e5,X[a]=g/t,X[s]=g/e,X[i]=g/r,X)[d]||g,C?_:K.a(_)},A.daysInMonth=function(){return this.endOf(l).$D},A.$locale=function(){return ie[this.$L]},A.locale=function(w,B){if(!w)return this.$L;var C=this.clone(),X=ve(w,B,!0);return X&&(C.$L=X),C},A.clone=function(){return K.w(this.$d,this)},A.toDate=function(){return new Date(this.valueOf())},A.toJSON=function(){return this.isValid()?this.toISOString():null},A.toISOString=function(){return this.$d.toISOString()},A.toString=function(){return this.$d.toUTCString()},j}(),Fe=Pe.prototype;return G.prototype=Fe,[["$ms",n],["$s",i],["$m",s],["$H",a],["$W",o],["$M",l],["$y",p],["$D",m]].forEach(function(j){Fe[j[1]]=function(A){return this.$g(A,j[0],j[1])}}),G.extend=function(j,A){return j.$i||(j(A,Pe,G),j.$i=!0),G},G.locale=ve,G.isDayjs=re,G.unix=function(j){return G(1e3*j)},G.en=ie[W],G.Ls=ie,G.p={},G})});var nt=b(Gr=>{"use strict";var Mb=Gr&&Gr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Gr,"__esModule",{value:!0});Gr.findYearClosestToRef=Gr.findMostLikelyADYear=void 0;var xb=Mb(ge());function Ab(r){return r<100&&(r>50?r=r+1900:r=r+2e3),r}Gr.findMostLikelyADYear=Ab;function Cb(r,e,t){let n=xb.default(r),i=n;i=i.month(t-1),i=i.date(e),i=i.year(n.year());let s=i.add(1,"y"),a=i.add(-1,"y");return Math.abs(s.diff(n))<Math.abs(i.diff(n))?i=s:Math.abs(a.diff(n))<Math.abs(i.diff(n))&&(i=a),i.year()}Gr.findYearClosestToRef=Cb});var it=b(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.parseTimeUnits=ne.TIME_UNITS_PATTERN=ne.parseYear=ne.YEAR_PATTERN=ne.parseOrdinalNumberPattern=ne.ORDINAL_NUMBER_PATTERN=ne.parseNumberPattern=ne.NUMBER_PATTERN=ne.TIME_UNIT_DICTIONARY=ne.ORDINAL_WORD_DICTIONARY=ne.INTEGER_WORD_DICTIONARY=ne.MONTH_DICTIONARY=ne.FULL_MONTH_NAME_DICTIONARY=ne.WEEKDAY_DICTIONARY=void 0;var ba=Ee(),Pb=nt();ne.WEEKDAY_DICTIONARY={sunday:0,sun:0,"sun.":0,monday:1,mon:1,"mon.":1,tuesday:2,tue:2,"tue.":2,wednesday:3,wed:3,"wed.":3,thursday:4,thurs:4,"thurs.":4,thur:4,"thur.":4,thu:4,"thu.":4,friday:5,fri:5,"fri.":5,saturday:6,sat:6,"sat.":6};ne.FULL_MONTH_NAME_DICTIONARY={january:1,february:2,march:3,april:4,may:5,june:6,july:7,august:8,september:9,october:10,november:11,december:12};ne.MONTH_DICTIONARY=Object.assign(Object.assign({},ne.FULL_MONTH_NAME_DICTIONARY),{jan:1,"jan.":1,feb:2,"feb.":2,mar:3,"mar.":3,apr:4,"apr.":4,jun:6,"jun.":6,jul:7,"jul.":7,aug:8,"aug.":8,sep:9,"sep.":9,sept:9,"sept.":9,oct:10,"oct.":10,nov:11,"nov.":11,dec:12,"dec.":12});ne.INTEGER_WORD_DICTIONARY={one:1,two:2,three:3,four:4,five:5,six:6,seven:7,eight:8,nine:9,ten:10,eleven:11,twelve:12};ne.ORDINAL_WORD_DICTIONARY={first:1,second:2,third:3,fourth:4,fifth:5,sixth:6,seventh:7,eighth:8,ninth:9,tenth:10,eleventh:11,twelfth:12,thirteenth:13,fourteenth:14,fifteenth:15,sixteenth:16,seventeenth:17,eighteenth:18,nineteenth:19,twentieth:20,"twenty first":21,"twenty-first":21,"twenty second":22,"twenty-second":22,"twenty third":23,"twenty-third":23,"twenty fourth":24,"twenty-fourth":24,"twenty fifth":25,"twenty-fifth":25,"twenty sixth":26,"twenty-sixth":26,"twenty seventh":27,"twenty-seventh":27,"twenty eighth":28,"twenty-eighth":28,"twenty ninth":29,"twenty-ninth":29,thirtieth:30,"thirty first":31,"thirty-first":31};ne.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconds:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",hour:"hour",hours:"hour",day:"d",days:"d",week:"week",weeks:"week",month:"month",months:"month",qtr:"quarter",quarter:"quarter",quarters:"quarter",y:"year",yr:"year",year:"year",years:"year"};ne.NUMBER_PATTERN=`(?:${ba.matchAnyPattern(ne.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s{0,2}an?)?|an?\\b(?:\\s{0,2}few)?|few|several|a?\\s{0,2}couple\\s{0,2}(?:of)?)`;function wp(r){let e=r.toLowerCase();return ne.INTEGER_WORD_DICTIONARY[e]!==void 0?ne.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}ne.parseNumberPattern=wp;ne.ORDINAL_NUMBER_PATTERN=`(?:${ba.matchAnyPattern(ne.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:st|nd|rd|th)?)`;function Nb(r){let e=r.toLowerCase();return ne.ORDINAL_WORD_DICTIONARY[e]!==void 0?ne.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}ne.parseOrdinalNumberPattern=Nb;ne.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s{0,2}(?:BE|AD|BC|BCE|CE)|[1-2][0-9]{3}|[5-9][0-9])";function Ib(r){if(/BE/i.test(r))return r=r.replace(/BE/i,""),parseInt(r)-543;if(/BCE?/i.test(r))return r=r.replace(/BCE?/i,""),-parseInt(r);if(/(AD|CE)/i.test(r))return r=r.replace(/(AD|CE)/i,""),parseInt(r);let e=parseInt(r);return Pb.findMostLikelyADYear(e)}ne.parseYear=Ib;var kp=`(${ne.NUMBER_PATTERN})\\s{0,3}(${ba.matchAnyPattern(ne.TIME_UNIT_DICTIONARY)})`,Ep=new RegExp(kp,"i");ne.TIME_UNITS_PATTERN=ba.repeatedTimeunitPattern("(?:(?:about|around)\\s{0,3})?",kp);function Fb(r){let e={},t=r,n=Ep.exec(t);for(;n;)Lb(e,n),t=t.substring(n[0].length).trim(),n=Ep.exec(t);return e}ne.parseTimeUnits=Fb;function Lb(r,e){let t=wp(e[1]),n=ne.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Op=b((bu,vu)=>{(function(r,e){typeof bu=="object"&&typeof vu!="undefined"?vu.exports=e():typeof define=="function"&&define.amd?define(e):(r=typeof globalThis!="undefined"?globalThis:r||self).dayjs_plugin_quarterOfYear=e()})(bu,function(){"use strict";var r="month",e="quarter";return function(t,n){var i=n.prototype;i.quarter=function(o){return this.$utils().u(o)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(o-1))};var s=i.add;i.add=function(o,u){return o=Number(o),this.$utils().p(u)===e?this.add(3*o,r):s.bind(this)(o,u)};var a=i.startOf;i.startOf=function(o,u){var l=this.$utils(),c=!!l.u(u)||u;if(l.p(o)===e){var p=this.quarter()-1;return c?this.month(3*p).startOf(r).startOf("day"):this.month(3*p+2).endOf(r).endOf("day")}return a.bind(this)(o,u)}}})});var Ut=b(or=>{"use strict";Object.defineProperty(or,"__esModule",{value:!0});or.implySimilarTime=or.assignSimilarTime=or.assignSimilarDate=or.assignTheNextDay=void 0;var Dp=Be();function Ub(r,e){e=e.add(1,"day"),Sp(r,e),Rp(r,e)}or.assignTheNextDay=Ub;function Sp(r,e){r.assign("day",e.date()),r.assign("month",e.month()+1),r.assign("year",e.year())}or.assignSimilarDate=Sp;function Wb(r,e){r.assign("hour",e.hour()),r.assign("minute",e.minute()),r.assign("second",e.second()),r.assign("millisecond",e.millisecond()),r.get("hour")<12?r.assign("meridiem",Dp.Meridiem.AM):r.assign("meridiem",Dp.Meridiem.PM)}or.assignSimilarTime=Wb;function Rp(r,e){r.imply("hour",e.hour()),r.imply("minute",e.minute()),r.imply("second",e.second()),r.imply("millisecond",e.millisecond())}or.implySimilarTime=Rp});var Mp=b(dn=>{"use strict";Object.defineProperty(dn,"__esModule",{value:!0});dn.toTimezoneOffset=dn.TIMEZONE_ABBR_MAP=void 0;dn.TIMEZONE_ABBR_MAP={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:180,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360};function qb(r){var e;return r==null?null:typeof r=="number"?r:(e=dn.TIMEZONE_ABBR_MAP[r])!==null&&e!==void 0?e:null}dn.toTimezoneOffset=qb});var Ne=b(ur=>{"use strict";var xp=ur&&ur.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ur,"__esModule",{value:!0});ur.ParsingResult=ur.ParsingComponents=ur.ReferenceWithTimezone=void 0;var Yb=xp(Op()),va=xp(ge()),Eu=Ut(),$b=Mp();va.default.extend(Yb.default);var wu=class{constructor(e){var t;e=e!=null?e:new Date,e instanceof Date?this.instant=e:(this.instant=(t=e.instant)!==null&&t!==void 0?t:new Date,this.timezoneOffset=$b.toTimezoneOffset(e.timezone))}};ur.ReferenceWithTimezone=wu;var fn=class{constructor(e,t){if(this.reference=e,this.knownValues={},this.impliedValues={},t)for(let i in t)this.knownValues[i]=t[i];let n=va.default(e.instant);this.imply("day",n.date()),this.imply("month",n.month()+1),this.imply("year",n.year()),this.imply("hour",12),this.imply("minute",0),this.imply("second",0),this.imply("millisecond",0)}get(e){return e in this.knownValues?this.knownValues[e]:e in this.impliedValues?this.impliedValues[e]:null}isCertain(e){return e in this.knownValues}getCertainComponents(){return Object.keys(this.knownValues)}imply(e,t){return e in this.knownValues?this:(this.impliedValues[e]=t,this)}assign(e,t){return this.knownValues[e]=t,delete this.impliedValues[e],this}delete(e){delete this.knownValues[e],delete this.impliedValues[e]}clone(){let e=new fn(this.reference);e.knownValues={},e.impliedValues={};for(let t in this.knownValues)e.knownValues[t]=this.knownValues[t];for(let t in this.impliedValues)e.impliedValues[t]=this.impliedValues[t];return e}isOnlyDate(){return!this.isCertain("hour")&&!this.isCertain("minute")&&!this.isCertain("second")}isOnlyTime(){return!this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyWeekdayComponent(){return this.isCertain("weekday")&&!this.isCertain("day")&&!this.isCertain("month")}isOnlyDayMonthComponent(){return this.isCertain("day")&&this.isCertain("month")&&!this.isCertain("year")}isValidDate(){let e=this.dateWithoutTimezoneAdjustment();return!(e.getFullYear()!==this.get("year")||e.getMonth()!==this.get("month")-1||e.getDate()!==this.get("day")||this.get("hour")!=null&&e.getHours()!=this.get("hour")||this.get("minute")!=null&&e.getMinutes()!=this.get("minute"))}toString(){return`[ParsingComponents {knownValues: ${JSON.stringify(this.knownValues)}, impliedValues: ${JSON.stringify(this.impliedValues)}}, reference: ${JSON.stringify(this.reference)}]`}dayjs(){return va.default(this.date())}date(){let e=this.dateWithoutTimezoneAdjustment();return new Date(e.getTime()+this.getSystemTimezoneAdjustmentMinute(e)*6e4)}dateWithoutTimezoneAdjustment(){let e=new Date(this.get("year"),this.get("month")-1,this.get("day"),this.get("hour"),this.get("minute"),this.get("second"),this.get("millisecond"));return e.setFullYear(this.get("year")),e}getSystemTimezoneAdjustmentMinute(e){var t,n;(!e||e.getTime()<0)&&(e=new Date);let i=-e.getTimezoneOffset(),s=(n=(t=this.get("timezoneOffset"))!==null&&t!==void 0?t:this.reference.timezoneOffset)!==null&&n!==void 0?n:i;return i-s}static createRelativeFromReference(e,t){let n=va.default(e.instant);for(let s in t)n=n.add(t[s],s);let i=new fn(e);return t.hour||t.minute||t.second?(Eu.assignSimilarTime(i,n),Eu.assignSimilarDate(i,n),e.timezoneOffset!==null&&i.assign("timezoneOffset",-e.instant.getTimezoneOffset())):(Eu.implySimilarTime(i,n),e.timezoneOffset!==null&&i.imply("timezoneOffset",-e.instant.getTimezoneOffset()),t.d?(i.assign("day",n.date()),i.assign("month",n.month()+1),i.assign("year",n.year())):(t.week&&i.imply("weekday",n.day()),i.imply("day",n.date()),t.month?(i.assign("month",n.month()+1),i.assign("year",n.year())):(i.imply("month",n.month()+1),t.year?i.assign("year",n.year()):i.imply("year",n.year())))),i}};ur.ParsingComponents=fn;var qi=class{constructor(e,t,n,i,s){this.reference=e,this.refDate=e.instant,this.index=t,this.text=n,this.start=i||new fn(e),this.end=s}clone(){let e=new qi(this.reference,this.index,this.text);return e.start=this.start?this.start.clone():null,e.end=this.end?this.end.clone():null,e}date(){return this.start.date()}toString(){return`[ParsingResult {index: ${this.index}, text: '${this.text}', ...}]`}};ur.ParsingResult=qi});var L=b(Ea=>{"use strict";Object.defineProperty(Ea,"__esModule",{value:!0});Ea.AbstractParserWithWordBoundaryChecking=void 0;var ku=class{constructor(){this.cachedInnerPattern=null,this.cachedPattern=null}patternLeftBoundary(){return"(\\W|^)"}pattern(e){let t=this.innerPattern(e);return t==this.cachedInnerPattern?this.cachedPattern:(this.cachedPattern=new RegExp(`${this.patternLeftBoundary()}${t.source}`,t.flags),this.cachedInnerPattern=t,this.cachedPattern)}extract(e,t){var n;let i=(n=t[1])!==null&&n!==void 0?n:"";t.index=t.index+i.length,t[0]=t[0].substring(i.length);for(let s=2;s<t.length;s++)t[s-1]=t[s];return this.innerExtract(e,t)}};Ea.AbstractParserWithWordBoundaryChecking=ku});var Ap=b(Su=>{"use strict";Object.defineProperty(Su,"__esModule",{value:!0});var Du=it(),jb=Ne(),Gb=L(),Bb=new RegExp(`(?:within|in|for)\\s*(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Du.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Hb=new RegExp(`(?:(?:about|around|roughly|approximately|just)\\s*(?:~\\s*)?)?(${Du.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Ou=class extends Gb.AbstractParserWithWordBoundaryChecking{innerPattern(e){return e.option.forwardDate?Hb:Bb}innerExtract(e,t){let n=Du.parseTimeUnits(t[1]);return jb.ParsingComponents.createRelativeFromReference(e.reference,n)}};Su.default=Ou});var Lp=b(Mu=>{"use strict";Object.defineProperty(Mu,"__esModule",{value:!0});var zb=nt(),Ip=it(),Fp=it(),wa=it(),Vb=Ee(),Kb=L(),Qb=new RegExp(`(?:on\\s{0,3})?(${wa.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:to|\\-|\\\u2013|until|through|till)?\\s{0,3}(${wa.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s{0,3}(?:of)?\\s{0,3})(${Vb.matchAnyPattern(Ip.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s{0,3})(${Fp.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),Cp=1,Pp=2,Xb=3,Np=4,Ru=class extends Kb.AbstractParserWithWordBoundaryChecking{innerPattern(){return Qb}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=Ip.MONTH_DICTIONARY[t[Xb].toLowerCase()],s=wa.parseOrdinalNumberPattern(t[Cp]);if(s>31)return t.index=t.index+t[Cp].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Np]){let a=Fp.parseYear(t[Np]);n.start.assign("year",a)}else{let a=zb.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Pp]){let a=wa.parseOrdinalNumberPattern(t[Pp]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Mu.default=Ru});var $p=b(Au=>{"use strict";Object.defineProperty(Au,"__esModule",{value:!0});var Zb=nt(),qp=it(),ka=it(),Yp=it(),Jb=Ee(),ev=L(),tv=new RegExp(`(${Jb.matchAnyPattern(qp.MONTH_DICTIONARY)})(?:-|/|\\s*,?\\s*)(${ka.ORDINAL_NUMBER_PATTERN})(?!\\s*(?:am|pm))\\s*(?:(?:to|\\-)\\s*(${ka.ORDINAL_NUMBER_PATTERN})\\s*)?(?:(?:-|/|\\s*,?\\s*)(${Yp.YEAR_PATTERN}))?(?=\\W|$)(?!\\:\\d)`,"i"),rv=1,nv=2,Up=3,Wp=4,xu=class extends ev.AbstractParserWithWordBoundaryChecking{innerPattern(){return tv}innerExtract(e,t){let n=qp.MONTH_DICTIONARY[t[rv].toLowerCase()],i=ka.parseOrdinalNumberPattern(t[nv]);if(i>31)return null;let s=e.createParsingComponents({day:i,month:n});if(t[Wp]){let u=Yp.parseYear(t[Wp]);s.assign("year",u)}else{let u=Zb.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[Up])return s;let a=ka.parseOrdinalNumberPattern(t[Up]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};Au.default=xu});var Bp=b(Nu=>{"use strict";Object.defineProperty(Nu,"__esModule",{value:!0});var Cu=it(),iv=nt(),sv=Ee(),Gp=it(),av=L(),ov=new RegExp(`((?:in)\\s*)?(${sv.matchAnyPattern(Cu.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${Gp.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),uv=1,lv=2,jp=3,Pu=class extends av.AbstractParserWithWordBoundaryChecking{innerPattern(){return ov}innerExtract(e,t){let n=t[lv].toLowerCase();if(t[0].length<=3&&!Cu.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index+(t[uv]||"").length,t.index+t[0].length);i.start.imply("day",1);let s=Cu.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[jp]){let a=Gp.parseYear(t[jp]);i.start.assign("year",a)}else{let a=iv.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};Nu.default=Pu});var Vp=b(Fu=>{"use strict";Object.defineProperty(Fu,"__esModule",{value:!0});var zp=it(),cv=Ee(),dv=L(),fv=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${cv.matchAnyPattern(zp.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),pv=1,mv=2,Hp=3,hv=4,Iu=class extends dv.AbstractParserWithWordBoundaryChecking{innerPattern(){return fv}innerExtract(e,t){let n=t[Hp]?parseInt(t[Hp]):zp.MONTH_DICTIONARY[t[mv].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[pv]);return{day:parseInt(t[hv]),month:n,year:i}}};Fu.default=Iu});var Kp=b(Uu=>{"use strict";Object.defineProperty(Uu,"__esModule",{value:!0});var gv=L(),yv=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),_v=1,Tv=2,Lu=class extends gv.AbstractParserWithWordBoundaryChecking{innerPattern(){return yv}innerExtract(e,t){let n=parseInt(t[Tv]),i=parseInt(t[_v]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};Uu.default=Lu});var pn=b(Sa=>{"use strict";Object.defineProperty(Sa,"__esModule",{value:!0});Sa.AbstractTimeExpressionParser=void 0;var gt=Be();function bv(r,e,t,n){return new RegExp(`${r}${e}(\\d{1,4})(?:(?:\\.|:|\uFF1A)(\\d{1,2})(?:(?::|\uFF1A)(\\d{2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${t}`,n)}function vv(r,e){return new RegExp(`^(${r})(\\d{1,4})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:(?:\\.|\\:|\\\uFF1A)(\\d{1,2})(?:\\.(\\d{1,6}))?)?)?(?:\\s*(a\\.m\\.|p\\.m\\.|am?|pm?))?${e}`,"i")}var Qp=2,Hn=3,Oa=4,Da=5,Yi=6,Wu=class{constructor(e=!1){this.cachedPrimaryPrefix=null,this.cachedPrimarySuffix=null,this.cachedPrimaryTimePattern=null,this.cachedFollowingPhase=null,this.cachedFollowingSuffix=null,this.cachedFollowingTimePatten=null,this.strictMode=e}patternFlags(){return"i"}primaryPatternLeftBoundary(){return"(^|\\s|T|\\b)"}primarySuffix(){return"(?=\\W|$)"}followingSuffix(){return"(?=\\W|$)"}pattern(e){return this.getPrimaryTimePatternThroughCache()}extract(e,t){let n=this.extractPrimaryTimeComponents(e,t);if(!n)return t.index+=t[0].length,null;let i=t.index+t[1].length,s=t[0].substring(t[1].length),a=e.createParsingResult(i,s,n);t.index+=t[0].length;let o=e.text.substring(t.index),l=this.getFollowingTimePatternThroughCache().exec(o);return s.match(/^\d{3,4}/)&&l&&l[0].match(/^\s*([+-])\s*\d{2,4}$/)?null:!l||l[0].match(/^\s*([+-])\s*\d{3,4}$/)?this.checkAndReturnWithoutFollowingPattern(a):(a.end=this.extractFollowingTimeComponents(e,l,a),a.end&&(a.text+=l[0]),this.checkAndReturnWithFollowingPattern(a))}extractPrimaryTimeComponents(e,t,n=!1){let i=e.createParsingComponents(),s=0,a=null,o=parseInt(t[Qp]);if(o>100){if(this.strictMode||t[Hn]!=null)return null;s=o%100,o=Math.floor(o/100)}if(o>24)return null;if(t[Hn]!=null){if(t[Hn].length==1&&!t[Yi])return null;s=parseInt(t[Hn])}if(s>=60)return null;if(o>12&&(a=gt.Meridiem.PM),t[Yi]!=null){if(o>12)return null;let u=t[Yi][0].toLowerCase();u=="a"&&(a=gt.Meridiem.AM,o==12&&(o=0)),u=="p"&&(a=gt.Meridiem.PM,o!=12&&(o+=12))}if(i.assign("hour",o),i.assign("minute",s),a!==null?i.assign("meridiem",a):o<12?i.imply("meridiem",gt.Meridiem.AM):i.imply("meridiem",gt.Meridiem.PM),t[Da]!=null){let u=parseInt(t[Da].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[Oa]!=null){let u=parseInt(t[Oa]);if(u>=60)return null;i.assign("second",u)}return i}extractFollowingTimeComponents(e,t,n){let i=e.createParsingComponents();if(t[Da]!=null){let u=parseInt(t[Da].substring(0,3));if(u>=1e3)return null;i.assign("millisecond",u)}if(t[Oa]!=null){let u=parseInt(t[Oa]);if(u>=60)return null;i.assign("second",u)}let s=parseInt(t[Qp]),a=0,o=-1;if(t[Hn]!=null?a=parseInt(t[Hn]):s>100&&(a=s%100,s=Math.floor(s/100)),a>=60||s>24)return null;if(s>=12&&(o=gt.Meridiem.PM),t[Yi]!=null){if(s>12)return null;let u=t[Yi][0].toLowerCase();u=="a"&&(o=gt.Meridiem.AM,s==12&&(s=0,i.isCertain("day")||i.imply("day",i.get("day")+1))),u=="p"&&(o=gt.Meridiem.PM,s!=12&&(s+=12)),n.start.isCertain("meridiem")||(o==gt.Meridiem.AM?(n.start.imply("meridiem",gt.Meridiem.AM),n.start.get("hour")==12&&n.start.assign("hour",0)):(n.start.imply("meridiem",gt.Meridiem.PM),n.start.get("hour")!=12&&n.start.assign("hour",n.start.get("hour")+12)))}return i.assign("hour",s),i.assign("minute",a),o>=0?i.assign("meridiem",o):n.start.isCertain("meridiem")&&n.start.get("hour")>12?n.start.get("hour")-12>s?i.imply("meridiem",gt.Meridiem.AM):s<=12&&(i.assign("hour",s+12),i.assign("meridiem",gt.Meridiem.PM)):s>12?i.imply("meridiem",gt.Meridiem.PM):s<=12&&i.imply("meridiem",gt.Meridiem.AM),i.date().getTime()<n.start.date().getTime()&&i.imply("day",i.get("day")+1),i}checkAndReturnWithoutFollowingPattern(e){if(e.text.match(/^\d$/)||e.text.match(/^\d\d\d+$/)||e.text.match(/\d[apAP]$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)$/);if(t){let n=t[1];if(this.strictMode||n.includes(".")&&!n.match(/\d(\.\d{2})+$/)||parseInt(n)>24)return null}return e}checkAndReturnWithFollowingPattern(e){if(e.text.match(/^\d+-\d+$/))return null;let t=e.text.match(/[^\d:.](\d[\d.]+)\s*-\s*(\d[\d.]+)$/);if(t){if(this.strictMode)return null;let n=t[1],i=t[2];if(i.includes(".")&&!i.match(/\d(\.\d{2})+$/))return null;let s=parseInt(i),a=parseInt(n);if(s>24||a>24)return null}return e}getPrimaryTimePatternThroughCache(){let e=this.primaryPrefix(),t=this.primarySuffix();return this.cachedPrimaryPrefix===e&&this.cachedPrimarySuffix===t?this.cachedPrimaryTimePattern:(this.cachedPrimaryTimePattern=bv(this.primaryPatternLeftBoundary(),e,t,this.patternFlags()),this.cachedPrimaryPrefix=e,this.cachedPrimarySuffix=t,this.cachedPrimaryTimePattern)}getFollowingTimePatternThroughCache(){let e=this.followingPhase(),t=this.followingSuffix();return this.cachedFollowingPhase===e&&this.cachedFollowingSuffix===t?this.cachedFollowingTimePatten:(this.cachedFollowingTimePatten=vv(e,t),this.cachedFollowingPhase=e,this.cachedFollowingSuffix=t,this.cachedFollowingTimePatten)}};Sa.AbstractTimeExpressionParser=Wu});var Xp=b(Yu=>{"use strict";Object.defineProperty(Yu,"__esModule",{value:!0});var Ra=Be(),Ev=pn(),qu=class extends Ev.AbstractTimeExpressionParser{constructor(e){super(e)}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|to|\\?)\\s*"}primaryPrefix(){return"(?:(?:at|from)\\s*)??"}primarySuffix(){return"(?:\\s*(?:o\\W*clock|at\\s*night|in\\s*the\\s*(?:morning|afternoon)))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("night")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",Ra.Meridiem.PM)):i<6&&n.assign("meridiem",Ra.Meridiem.AM)}if(t[0].endsWith("afternoon")){n.assign("meridiem",Ra.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("morning")&&(n.assign("meridiem",Ra.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};Yu.default=qu});var Wt=b(zn=>{"use strict";Object.defineProperty(zn,"__esModule",{value:!0});zn.addImpliedTimeUnits=zn.reverseTimeUnits=void 0;function wv(r){let e={};for(let t in r)e[t]=-r[t];return e}zn.reverseTimeUnits=wv;function kv(r,e){let t=r.clone(),n=r.dayjs();for(let i in e)n=n.add(e[i],i);return("day"in e||"d"in e||"week"in e||"month"in e||"year"in e)&&(t.imply("day",n.date()),t.imply("month",n.month()+1),t.imply("year",n.year())),("second"in e||"minute"in e||"hour"in e)&&(t.imply("second",n.second()),t.imply("minute",n.minute()),t.imply("hour",n.hour())),t}zn.addImpliedTimeUnits=kv});var Zp=b(Gu=>{"use strict";Object.defineProperty(Gu,"__esModule",{value:!0});var ju=it(),Ov=Ne(),Dv=L(),Sv=Wt(),Rv=new RegExp(`(${ju.TIME_UNITS_PATTERN})\\s{0,5}(?:ago|before|earlier)(?=(?:\\W|$))`,"i"),Mv=new RegExp(`(${ju.TIME_UNITS_PATTERN})\\s{0,5}ago(?=(?:\\W|$))`,"i"),$u=class extends Dv.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?Mv:Rv}innerExtract(e,t){let n=ju.parseTimeUnits(t[1]),i=Sv.reverseTimeUnits(n);return Ov.ParsingComponents.createRelativeFromReference(e.reference,i)}};Gu.default=$u});var Jp=b(zu=>{"use strict";Object.defineProperty(zu,"__esModule",{value:!0});var Hu=it(),xv=Ne(),Av=L(),Cv=new RegExp(`(${Hu.TIME_UNITS_PATTERN})\\s{0,5}(?:later|after|from now|henceforth|forward|out)(?=(?:\\W|$))`,"i"),Pv=new RegExp("("+Hu.TIME_UNITS_PATTERN+")(later|from now)(?=(?:\\W|$))","i"),Nv=1,Bu=class extends Av.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?Pv:Cv}innerExtract(e,t){let n=Hu.parseTimeUnits(t[Nv]);return xv.ParsingComponents.createRelativeFromReference(e.reference,n)}};zu.default=Bu});var Kn=b(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});Vn.MergingRefiner=Vn.Filter=void 0;var Vu=class{refine(e,t){return t.filter(n=>this.isValid(e,n))}};Vn.Filter=Vu;var Ku=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0],s=null;for(let a=1;a<t.length;a++){s=t[a];let o=e.text.substring(i.index+i.text.length,s.index);if(!this.shouldMergeResults(o,i,s,e))n.push(i),i=s;else{let u=i,l=s,c=this.mergeResults(o,u,l,e);e.debug(()=>{console.log(`${this.constructor.name} merged ${u} and ${l} into ${c}`)}),i=c}}return i!=null&&n.push(i),n}};Vn.MergingRefiner=Ku});var lr=b(Xu=>{"use strict";Object.defineProperty(Xu,"__esModule",{value:!0});var Iv=Kn(),Qu=class extends Iv.MergingRefiner{shouldMergeResults(e,t,n){return!t.end&&!n.end&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){if(!t.start.isOnlyWeekdayComponent()&&!n.start.isOnlyWeekdayComponent()&&(n.start.getCertainComponents().forEach(s=>{t.start.isCertain(s)||t.start.assign(s,n.start.get(s))}),t.start.getCertainComponents().forEach(s=>{n.start.isCertain(s)||n.start.assign(s,t.start.get(s))})),t.start.date().getTime()>n.start.date().getTime()){let s=t.start.dayjs(),a=n.start.dayjs();t.start.isOnlyWeekdayComponent()&&s.add(-7,"days").isBefore(a)?(s=s.add(-7,"days"),t.start.imply("day",s.date()),t.start.imply("month",s.month()+1),t.start.imply("year",s.year())):n.start.isOnlyWeekdayComponent()&&a.add(7,"days").isAfter(s)?(a=a.add(7,"days"),n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())):[n,t]=[t,n]}let i=t.clone();return i.start=t.start,i.end=n.start,i.index=Math.min(t.index,n.index),t.index<n.index?i.text=t.text+e+n.text:i.text=n.text+e+t.text,i}};Xu.default=Qu});var em=b($i=>{"use strict";var Fv=$i&&$i.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty($i,"__esModule",{value:!0});var Lv=Fv(lr()),Zu=class extends Lv.default{patternBetween(){return/^\s*(to|-)\s*$/i}};$i.default=Zu});var tm=b(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0});Qn.mergeDateTimeComponent=Qn.mergeDateTimeResult=void 0;var Uv=Be();function Wv(r,e){let t=r.clone(),n=r.start,i=e.start;if(t.start=Ju(n,i),r.end!=null||e.end!=null){let s=r.end==null?r.start:r.end,a=e.end==null?e.start:e.end,o=Ju(s,a);r.end==null&&o.date().getTime()<t.start.date().getTime()&&(o.isCertain("day")?o.assign("day",o.get("day")+1):o.imply("day",o.get("day")+1)),t.end=o}return t}Qn.mergeDateTimeResult=Wv;function Ju(r,e){let t=r.clone();return e.isCertain("hour")?(t.assign("hour",e.get("hour")),t.assign("minute",e.get("minute")),e.isCertain("second")?(t.assign("second",e.get("second")),e.isCertain("millisecond")?t.assign("millisecond",e.get("millisecond")):t.imply("millisecond",e.get("millisecond"))):(t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond")))):(t.imply("hour",e.get("hour")),t.imply("minute",e.get("minute")),t.imply("second",e.get("second")),t.imply("millisecond",e.get("millisecond"))),e.isCertain("timezoneOffset")&&t.assign("timezoneOffset",e.get("timezoneOffset")),e.isCertain("meridiem")?t.assign("meridiem",e.get("meridiem")):e.get("meridiem")!=null&&t.get("meridiem")==null&&t.imply("meridiem",e.get("meridiem")),t.get("meridiem")==Uv.Meridiem.PM&&t.get("hour")<12&&(e.isCertain("hour")?t.assign("hour",t.get("hour")+12):t.imply("hour",t.get("hour")+12)),t}Qn.mergeDateTimeComponent=Ju});var br=b(tl=>{"use strict";Object.defineProperty(tl,"__esModule",{value:!0});var qv=Kn(),rm=tm(),el=class extends qv.MergingRefiner{shouldMergeResults(e,t,n){return(t.start.isOnlyDate()&&n.start.isOnlyTime()||n.start.isOnlyDate()&&t.start.isOnlyTime())&&e.match(this.patternBetween())!=null}mergeResults(e,t,n){let i=t.start.isOnlyDate()?rm.mergeDateTimeResult(t,n):rm.mergeDateTimeResult(n,t);return i.index=t.index,i.text=t.text+e+n.text,i}};tl.default=el});var nm=b(ji=>{"use strict";var Yv=ji&&ji.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ji,"__esModule",{value:!0});var $v=Yv(br()),rl=class extends $v.default{patternBetween(){return new RegExp("^\\s*(T|at|after|before|on|of|,|-)?\\s*$")}};ji.default=rl});var im=b(il=>{"use strict";Object.defineProperty(il,"__esModule",{value:!0});var jv=new RegExp("^\\s*,?\\s*\\(?([A-Z]{2,4})\\)?(?=\\W|$)","i"),Gv={ACDT:630,ACST:570,ADT:-180,AEDT:660,AEST:600,AFT:270,AKDT:-480,AKST:-540,ALMT:360,AMST:-180,AMT:-240,ANAST:720,ANAT:720,AQTT:300,ART:-180,AST:-240,AWDT:540,AWST:480,AZOST:0,AZOT:-60,AZST:300,AZT:240,BNT:480,BOT:-240,BRST:-120,BRT:-180,BST:60,BTT:360,CAST:480,CAT:120,CCT:390,CDT:-300,CEST:120,CET:60,CHADT:825,CHAST:765,CKT:-600,CLST:-180,CLT:-240,COT:-300,CST:-360,CVT:-60,CXT:420,ChST:600,DAVT:420,EASST:-300,EAST:-360,EAT:180,ECT:-300,EDT:-240,EEST:180,EET:120,EGST:0,EGT:-60,EST:-300,ET:-300,FJST:780,FJT:720,FKST:-180,FKT:-240,FNT:-120,GALT:-360,GAMT:-540,GET:240,GFT:-180,GILT:720,GMT:0,GST:240,GYT:-240,HAA:-180,HAC:-300,HADT:-540,HAE:-240,HAP:-420,HAR:-360,HAST:-600,HAT:-90,HAY:-480,HKT:480,HLV:-210,HNA:-240,HNC:-360,HNE:-300,HNP:-480,HNR:-420,HNT:-150,HNY:-540,HOVT:420,ICT:420,IDT:180,IOT:360,IRDT:270,IRKST:540,IRKT:540,IRST:210,IST:330,JST:540,KGT:360,KRAST:480,KRAT:480,KST:540,KUYT:240,LHDT:660,LHST:630,LINT:840,MAGST:720,MAGT:720,MART:-510,MAWT:300,MDT:-360,MESZ:120,MEZ:60,MHT:720,MMT:390,MSD:240,MSK:240,MST:-420,MUT:240,MVT:300,MYT:480,NCT:660,NDT:-90,NFT:690,NOVST:420,NOVT:360,NPT:345,NST:-150,NUT:-660,NZDT:780,NZST:720,OMSST:420,OMST:420,PDT:-420,PET:-300,PETST:720,PETT:720,PGT:600,PHOT:780,PHT:480,PKT:300,PMDT:-120,PMST:-180,PONT:660,PST:-480,PT:-480,PWT:540,PYST:-180,PYT:-240,RET:240,SAMT:240,SAST:120,SBT:660,SCT:240,SGT:480,SRT:-180,SST:-660,TAHT:-600,TFT:300,TJT:300,TKT:780,TLT:540,TMT:300,TVT:720,ULAT:480,UTC:0,UYST:-120,UYT:-180,UZT:300,VET:-210,VLAST:660,VLAT:660,VUT:660,WAST:120,WAT:60,WEST:60,WESZ:60,WET:0,WEZ:0,WFT:720,WGST:-120,WGT:-180,WIB:420,WIT:540,WITA:480,WST:780,WT:0,YAKST:600,YAKT:600,YAPT:600,YEKST:360,YEKT:360},nl=class{constructor(e){this.timezone=Object.assign(Object.assign({},Gv),e)}refine(e,t){var n;let i=(n=e.option.timezones)!==null&&n!==void 0?n:{};return t.forEach(s=>{var a,o;let u=e.text.substring(s.index+s.text.length),l=jv.exec(u);if(!l)return;let c=l[1].toUpperCase(),p=(o=(a=i[c])!==null&&a!==void 0?a:this.timezone[c])!==null&&o!==void 0?o:null;if(p===null)return;e.debug(()=>{console.log(`Extracting timezone: '${c}' into: ${p} for: ${s.start}`)});let m=s.start.get("timezoneOffset");m!==null&&p!=m&&(s.start.isCertain("timezoneOffset")||c!=l[1])||s.start.isOnlyDate()&&c!=l[1]||(s.text+=l[0],s.start.isCertain("timezoneOffset")||s.start.assign("timezoneOffset",p),s.end!=null&&!s.end.isCertain("timezoneOffset")&&s.end.assign("timezoneOffset",p))}),t}};il.default=nl});var Ma=b(al=>{"use strict";Object.defineProperty(al,"__esModule",{value:!0});var Bv=new RegExp("^\\s*(?:\\(?(?:GMT|UTC)\\s?)?([+-])(\\d{1,2})(?::?(\\d{2}))?\\)?","i"),Hv=1,zv=2,Vv=3,sl=class{refine(e,t){return t.forEach(function(n){if(n.start.isCertain("timezoneOffset"))return;let i=e.text.substring(n.index+n.text.length),s=Bv.exec(i);if(!s)return;e.debug(()=>{console.log(`Extracting timezone: '${s[0]}' into : ${n}`)});let a=parseInt(s[zv]),o=parseInt(s[Vv]||"0"),u=a*60+o;u>14*60||(s[Hv]==="-"&&(u=-u),n.end!=null&&n.end.assign("timezoneOffset",u),n.start.assign("timezoneOffset",u),n.text+=s[0])}),t}};al.default=sl});var sm=b(ul=>{"use strict";Object.defineProperty(ul,"__esModule",{value:!0});var ol=class{refine(e,t){if(t.length<2)return t;let n=[],i=t[0];for(let s=1;s<t.length;s++){let a=t[s];a.index<i.index+i.text.length?a.text.length>i.text.length&&(i=a):(n.push(i),i=a)}return i!=null&&n.push(i),n}};ul.default=ol});var am=b(Gi=>{"use strict";var Kv=Gi&&Gi.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Gi,"__esModule",{value:!0});var Qv=Kv(ge()),ll=class{refine(e,t){return e.option.forwardDate&&t.forEach(function(n){let i=Qv.default(e.refDate);if(n.start.isOnlyDayMonthComponent()&&i.isAfter(n.start.dayjs()))for(let s=0;s<3&&i.isAfter(n.start.dayjs());s++)n.start.imply("year",n.start.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.start})`)}),n.end&&!n.end.isCertain("year")&&(n.end.imply("year",n.end.get("year")+1),e.debug(()=>{console.log(`Forward yearly adjusted for ${n} (${n.end})`)}));n.start.isOnlyWeekdayComponent()&&i.isAfter(n.start.dayjs())&&(i.day()>=n.start.get("weekday")?i=i.day(n.start.get("weekday")+7):i=i.day(n.start.get("weekday")),n.start.imply("day",i.date()),n.start.imply("month",i.month()+1),n.start.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.start})`)}),n.end&&n.end.isOnlyWeekdayComponent()&&(i.day()>n.end.get("weekday")?i=i.day(n.end.get("weekday")+7):i=i.day(n.end.get("weekday")),n.end.imply("day",i.date()),n.end.imply("month",i.month()+1),n.end.imply("year",i.year()),e.debug(()=>{console.log(`Forward weekly adjusted for ${n} (${n.end})`)})))}),t}};Gi.default=ll});var om=b(dl=>{"use strict";Object.defineProperty(dl,"__esModule",{value:!0});var Xv=Kn(),cl=class extends Xv.Filter{constructor(e){super(),this.strictMode=e}isValid(e,t){return t.text.replace(" ","").match(/^\d*(\.\d*)?$/)?(e.debug(()=>{console.log(`Removing unlikely result '${t.text}'`)}),!1):t.start.isValidDate()?t.end&&!t.end.isValidDate()?(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.end})`)}),!1):this.strictMode?this.isStrictModeValid(e,t):!0:(e.debug(()=>{console.log(`Removing invalid result: ${t} (${t.start})`)}),!1)}isStrictModeValid(e,t){return t.start.isOnlyWeekdayComponent()?(e.debug(()=>{console.log(`(Strict) Removing weekday only component: ${t} (${t.end})`)}),!1):t.start.isOnlyTime()&&(!t.start.isCertain("hour")||!t.start.isCertain("minute"))?(e.debug(()=>{console.log(`(Strict) Removing uncertain time component: ${t} (${t.end})`)}),!1):!0}};dl.default=cl});var ml=b(pl=>{"use strict";Object.defineProperty(pl,"__esModule",{value:!0});var Zv=L(),Jv=new RegExp("([0-9]{4})\\-([0-9]{1,2})\\-([0-9]{1,2})(?:T([0-9]{1,2}):([0-9]{1,2})(?::([0-9]{1,2})(?:\\.(\\d{1,4}))?)?(?:Z|([+-]\\d{2}):?(\\d{2})?)?)?(?=\\W|$)","i"),eE=1,tE=2,rE=3,um=4,nE=5,lm=6,cm=7,dm=8,fm=9,fl=class extends Zv.AbstractParserWithWordBoundaryChecking{innerPattern(){return Jv}innerExtract(e,t){let n={};if(n.year=parseInt(t[eE]),n.month=parseInt(t[tE]),n.day=parseInt(t[rE]),t[um]!=null)if(n.hour=parseInt(t[um]),n.minute=parseInt(t[nE]),t[lm]!=null&&(n.second=parseInt(t[lm])),t[cm]!=null&&(n.millisecond=parseInt(t[cm])),t[dm]==null)n.timezoneOffset=0;else{let i=parseInt(t[dm]),s=0;t[fm]!=null&&(s=parseInt(t[fm]));let a=i*60;a<0?a-=s:a+=s,n.timezoneOffset=a}return n}};pl.default=fl});var pm=b(gl=>{"use strict";Object.defineProperty(gl,"__esModule",{value:!0});var iE=Kn(),hl=class extends iE.MergingRefiner{mergeResults(e,t,n){let i=n.clone();return i.index=t.index,i.text=t.text+e+i.text,i.start.assign("weekday",t.start.get("weekday")),i.end&&i.end.assign("weekday",t.start.get("weekday")),i}shouldMergeResults(e,t,n){return t.start.isOnlyWeekdayComponent()&&!t.start.isCertain("hour")&&n.start.isCertain("day")&&e.match(/^,?\s*$/)!=null}};gl.default=hl});var vr=b(Xn=>{"use strict";var mn=Xn&&Xn.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xn,"__esModule",{value:!0});Xn.includeCommonConfiguration=void 0;var sE=mn(im()),aE=mn(Ma()),mm=mn(sm()),oE=mn(am()),uE=mn(om()),lE=mn(ml()),cE=mn(pm());function dE(r,e=!1){return r.parsers.unshift(new lE.default),r.refiners.unshift(new cE.default),r.refiners.unshift(new sE.default),r.refiners.unshift(new aE.default),r.refiners.unshift(new mm.default),r.refiners.push(new mm.default),r.refiners.push(new oE.default),r.refiners.push(new uE.default(e)),r}Xn.includeCommonConfiguration=dE});var wr=b(_e=>{"use strict";var fE=_e&&_e.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(_e,"__esModule",{value:!0});_e.noon=_e.morning=_e.midnight=_e.yesterdayEvening=_e.evening=_e.lastNight=_e.tonight=_e.theDayAfter=_e.tomorrow=_e.theDayBefore=_e.yesterday=_e.today=_e.now=void 0;var cr=Ne(),Zn=fE(ge()),Er=Ut(),Bi=Be();function pE(r){let e=Zn.default(r.instant),t=new cr.ParsingComponents(r,{});return Er.assignSimilarDate(t,e),Er.assignSimilarTime(t,e),r.timezoneOffset!==null&&t.assign("timezoneOffset",e.utcOffset()),t}_e.now=pE;function mE(r){let e=Zn.default(r.instant),t=new cr.ParsingComponents(r,{});return Er.assignSimilarDate(t,e),Er.implySimilarTime(t,e),t}_e.today=mE;function hE(r){return hm(r,1)}_e.yesterday=hE;function hm(r,e){return yl(r,-e)}_e.theDayBefore=hm;function gE(r){return yl(r,1)}_e.tomorrow=gE;function yl(r,e){let t=Zn.default(r.instant),n=new cr.ParsingComponents(r,{});return t=t.add(e,"day"),Er.assignSimilarDate(n,t),Er.implySimilarTime(n,t),n}_e.theDayAfter=yl;function yE(r,e=22){let t=Zn.default(r.instant),n=new cr.ParsingComponents(r,{});return n.imply("hour",e),n.imply("meridiem",Bi.Meridiem.PM),Er.assignSimilarDate(n,t),n}_e.tonight=yE;function _E(r,e=0){let t=Zn.default(r.instant),n=new cr.ParsingComponents(r,{});return t.hour()<6&&(t=t.add(-1,"day")),Er.assignSimilarDate(n,t),n.imply("hour",e),n}_e.lastNight=_E;function TE(r,e=20){let t=new cr.ParsingComponents(r,{});return t.imply("meridiem",Bi.Meridiem.PM),t.imply("hour",e),t}_e.evening=TE;function bE(r,e=20){let t=Zn.default(r.instant),n=new cr.ParsingComponents(r,{});return t=t.add(-1,"day"),Er.assignSimilarDate(n,t),n.imply("hour",e),n.imply("meridiem",Bi.Meridiem.PM),n}_e.yesterdayEvening=bE;function vE(r){let e=new cr.ParsingComponents(r,{});return e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e}_e.midnight=vE;function EE(r,e=6){let t=new cr.ParsingComponents(r,{});return t.imply("meridiem",Bi.Meridiem.AM),t.imply("hour",e),t}_e.morning=EE;function wE(r){let e=new cr.ParsingComponents(r,{});return e.imply("meridiem",Bi.Meridiem.AM),e.imply("hour",12),e}_e.noon=wE});var gm=b(Kt=>{"use strict";var kE=Kt&&Kt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),OE=Kt&&Kt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),DE=Kt&&Kt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&kE(e,r,t);return OE(e,r),e},SE=Kt&&Kt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Kt,"__esModule",{value:!0});var RE=SE(ge()),ME=L(),xE=Ut(),Hi=DE(wr()),AE=/(now|today|tonight|tomorrow|tmr|tmrw|yesterday|last\s*night)(?=\W|$)/i,_l=class extends ME.AbstractParserWithWordBoundaryChecking{innerPattern(e){return AE}innerExtract(e,t){let n=RE.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"now":return Hi.now(e.reference);case"today":return Hi.today(e.reference);case"yesterday":return Hi.yesterday(e.reference);case"tomorrow":case"tmr":case"tmrw":return Hi.tomorrow(e.reference);case"tonight":return Hi.tonight(e.reference);default:i.match(/last\s*night/)&&(n.hour()>6&&(n=n.add(-1,"day")),xE.assignSimilarDate(s,n),s.imply("hour",0));break}return s}};Kt.default=_l});var ym=b(zi=>{"use strict";var CE=zi&&zi.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(zi,"__esModule",{value:!0});var xa=Be(),PE=L(),NE=CE(ge()),IE=Ut(),FE=/(?:this)?\s{0,3}(morning|afternoon|evening|night|midnight|noon)(?=\W|$)/i,Tl=class extends PE.AbstractParserWithWordBoundaryChecking{innerPattern(){return FE}innerExtract(e,t){let n=NE.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"afternoon":i.imply("meridiem",xa.Meridiem.PM),i.imply("hour",15);break;case"evening":case"night":i.imply("meridiem",xa.Meridiem.PM),i.imply("hour",20);break;case"midnight":IE.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"morning":i.imply("meridiem",xa.Meridiem.AM),i.imply("hour",6);break;case"noon":i.imply("meridiem",xa.Meridiem.AM),i.imply("hour",12);break}return i}};zi.default=Tl});var hn=b(Br=>{"use strict";var LE=Br&&Br.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Br,"__esModule",{value:!0});Br.toDayJSClosestWeekday=Br.toDayJSWeekday=void 0;var _m=LE(ge());function UE(r,e,t){if(!t)return Tm(r,e);let n=_m.default(r);switch(t){case"this":n=n.day(e);break;case"next":n=n.day(e+7);break;case"last":n=n.day(e-7);break}return n}Br.toDayJSWeekday=UE;function Tm(r,e){let t=_m.default(r),n=t.day();return Math.abs(e-7-n)<Math.abs(e-n)?t=t.day(e-7):Math.abs(e+7-n)<Math.abs(e-n)?t=t.day(e+7):t=t.day(e),t}Br.toDayJSClosestWeekday=Tm});var vm=b(vl=>{"use strict";Object.defineProperty(vl,"__esModule",{value:!0});var bm=it(),WE=Ee(),qE=L(),YE=hn(),$E=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:on\\s*?)?(?:(this|last|past|next)\\s*)?(${WE.matchAnyPattern(bm.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(this|last|past|next)\\s*week)?(?=\\W|$)`,"i"),jE=1,GE=2,BE=3,bl=class extends qE.AbstractParserWithWordBoundaryChecking{innerPattern(){return $E}innerExtract(e,t){let n=t[GE].toLowerCase(),i=bm.WEEKDAY_DICTIONARY[n],s=t[jE],a=t[BE],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="last"||o=="past"?u="last":o=="next"?u="next":o=="this"&&(u="this");let l=YE.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};vl.default=bl});var km=b(Vi=>{"use strict";var HE=Vi&&Vi.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Vi,"__esModule",{value:!0});var wm=it(),Em=Ne(),zE=HE(ge()),VE=L(),KE=Ee(),QE=new RegExp(`(this|last|past|next|after\\s*this)\\s*(${KE.matchAnyPattern(wm.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),XE=1,ZE=2,El=class extends VE.AbstractParserWithWordBoundaryChecking{innerPattern(){return QE}innerExtract(e,t){let n=t[XE].toLowerCase(),i=t[ZE].toLowerCase(),s=wm.TIME_UNIT_DICTIONARY[i];if(n=="next"||n.startsWith("after")){let u={};return u[s]=1,Em.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="last"||n=="past"){let u={};return u[s]=-1,Em.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=zE.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};Vi.default=El});var Qt=b(ti=>{"use strict";Object.defineProperty(ti,"__esModule",{value:!0});ti.ParsingContext=ti.Chrono=void 0;var Jn=Ne(),JE=wl(),ei=class{constructor(e){e=e||JE.createCasualConfiguration(),this.parsers=[...e.parsers],this.refiners=[...e.refiners]}clone(){return new ei({parsers:[...this.parsers],refiners:[...this.refiners]})}parseDate(e,t,n){let i=this.parse(e,t,n);return i.length>0?i[0].start.date():null}parse(e,t,n){let i=new Aa(e,t,n),s=[];return this.parsers.forEach(a=>{let o=ei.executeParser(i,a);s=s.concat(o)}),s.sort((a,o)=>a.index-o.index),this.refiners.forEach(function(a){s=a.refine(i,s)}),s}static executeParser(e,t){let n=[],i=t.pattern(e),s=e.text,a=e.text,o=i.exec(a);for(;o;){let u=o.index+s.length-a.length;o.index=u;let l=t.extract(e,o);if(!l){a=s.substring(o.index+1),o=i.exec(a);continue}let c=null;l instanceof Jn.ParsingResult?c=l:l instanceof Jn.ParsingComponents?(c=e.createParsingResult(o.index,o[0]),c.start=l):c=e.createParsingResult(o.index,o[0],l),e.debug(()=>console.log(`${t.constructor.name} extracted result ${c}`)),n.push(c),a=s.substring(u+c.text.length),o=i.exec(a)}return n}};ti.Chrono=ei;var Aa=class{constructor(e,t,n){this.text=e,this.reference=new Jn.ReferenceWithTimezone(t),this.option=n!=null?n:{},this.refDate=this.reference.instant}createParsingComponents(e){return e instanceof Jn.ParsingComponents?e:new Jn.ParsingComponents(this.reference,e)}createParsingResult(e,t,n,i){let s=typeof t=="string"?t:this.text.substring(e,t),a=n?this.createParsingComponents(n):null,o=i?this.createParsingComponents(i):null;return new Jn.ParsingResult(this.reference,e,s,a,o)}debug(e){this.option.debug&&(this.option.debug instanceof Function?this.option.debug(e):this.option.debug.debug(e))}};ti.ParsingContext=Aa});var gn=b(Dl=>{"use strict";Object.defineProperty(Dl,"__esModule",{value:!0});var Om=nt(),ew=new RegExp("([^\\d]|^)([0-3]{0,1}[0-9]{1})[\\/\\.\\-]([0-3]{0,1}[0-9]{1})(?:[\\/\\.\\-]([0-9]{4}|[0-9]{2}))?(\\W|$)","i"),Ca=1,Dm=5,Sm=2,Rm=3,kl=4,Ol=class{constructor(e){this.groupNumberMonth=e?Rm:Sm,this.groupNumberDay=e?Sm:Rm}pattern(){return ew}extract(e,t){if(t[Ca]=="/"||t[Dm]=="/"){t.index+=t[0].length;return}let n=t.index+t[Ca].length,i=t[0].substr(t[Ca].length,t[0].length-t[Ca].length-t[Dm].length);if(i.match(/^\d\.\d$/)||i.match(/^\d\.\d{1,2}\.\d{1,2}\s*$/)||!t[kl]&&t[0].indexOf("/")<0)return;let s=e.createParsingResult(n,i),a=parseInt(t[this.groupNumberMonth]),o=parseInt(t[this.groupNumberDay]);if((a<1||a>12)&&a>12)if(o>=1&&o<=12&&a<=31)[o,a]=[a,o];else return null;if(o<1||o>31)return null;if(s.start.assign("day",o),s.start.assign("month",a),t[kl]){let u=parseInt(t[kl]),l=Om.findMostLikelyADYear(u);s.start.assign("year",l)}else{let u=Om.findYearClosestToRef(e.refDate,o,a);s.start.imply("year",u)}return s}};Dl.default=Ol});var xm=b(Rl=>{"use strict";Object.defineProperty(Rl,"__esModule",{value:!0});var Mm=it(),tw=Ne(),rw=L(),nw=Wt(),iw=new RegExp(`(this|last|past|next|after|\\+|-)\\s*(${Mm.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Sl=class extends rw.AbstractParserWithWordBoundaryChecking{innerPattern(){return iw}innerExtract(e,t){let n=t[1].toLowerCase(),i=Mm.parseTimeUnits(t[2]);switch(n){case"last":case"past":case"-":i=nw.reverseTimeUnits(i);break}return tw.ParsingComponents.createRelativeFromReference(e.reference,i)}};Rl.default=Sl});var Cm=b(Al=>{"use strict";Object.defineProperty(Al,"__esModule",{value:!0});var sw=Kn(),Ml=Ne(),aw=it(),ow=Wt();function Am(r){return r.text.match(/\s+(before|from)$/i)!=null}function uw(r){return r.text.match(/\s+(after|since)$/i)!=null}var xl=class extends sw.MergingRefiner{patternBetween(){return/^\s*$/i}shouldMergeResults(e,t,n){return!e.match(this.patternBetween())||!Am(t)&&!uw(t)?!1:!!n.start.get("day")&&!!n.start.get("month")&&!!n.start.get("year")}mergeResults(e,t,n){let i=aw.parseTimeUnits(t.text);Am(t)&&(i=ow.reverseTimeUnits(i));let s=Ml.ParsingComponents.createRelativeFromReference(new Ml.ReferenceWithTimezone(n.start.date()),i);return new Ml.ParsingResult(n.reference,t.index,`${t.text}${e}${n.text}`,s)}};Al.default=xl});var wl=b(qe=>{"use strict";var ze=qe&&qe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(qe,"__esModule",{value:!0});qe.createConfiguration=qe.createCasualConfiguration=qe.parseDate=qe.parse=qe.GB=qe.strict=qe.casual=void 0;var lw=ze(Ap()),cw=ze(Lp()),dw=ze($p()),fw=ze(Bp()),pw=ze(Vp()),mw=ze(Kp()),hw=ze(Xp()),gw=ze(Zp()),yw=ze(Jp()),_w=ze(em()),Tw=ze(nm()),bw=vr(),vw=ze(gm()),Ew=ze(ym()),ww=ze(vm()),kw=ze(km()),Cl=Qt(),Ow=ze(gn()),Dw=ze(xm()),Sw=ze(Cm());qe.casual=new Cl.Chrono(Pm(!1));qe.strict=new Cl.Chrono(Pa(!0,!1));qe.GB=new Cl.Chrono(Pa(!1,!0));function Rw(r,e,t){return qe.casual.parse(r,e,t)}qe.parse=Rw;function Mw(r,e,t){return qe.casual.parseDate(r,e,t)}qe.parseDate=Mw;function Pm(r=!1){let e=Pa(!1,r);return e.parsers.unshift(new vw.default),e.parsers.unshift(new Ew.default),e.parsers.unshift(new fw.default),e.parsers.unshift(new kw.default),e.parsers.unshift(new Dw.default),e}qe.createCasualConfiguration=Pm;function Pa(r=!0,e=!1){return bw.includeCommonConfiguration({parsers:[new Ow.default(e),new lw.default,new cw.default,new dw.default,new ww.default,new pw.default,new mw.default,new hw.default(r),new gw.default(r),new yw.default(r)],refiners:[new Sw.default,new Tw.default,new _w.default]},r)}qe.createConfiguration=Pa});var Nm=b(Nl=>{"use strict";Object.defineProperty(Nl,"__esModule",{value:!0});var xw=pn(),Pl=class extends xw.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:um|von)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|bis)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};Nl.default=Pl});var Ki=b(De=>{"use strict";Object.defineProperty(De,"__esModule",{value:!0});De.parseTimeUnits=De.TIME_UNITS_PATTERN=De.parseYear=De.YEAR_PATTERN=De.parseNumberPattern=De.NUMBER_PATTERN=De.TIME_UNIT_DICTIONARY=De.INTEGER_WORD_DICTIONARY=De.MONTH_DICTIONARY=De.WEEKDAY_DICTIONARY=void 0;var Il=Ee(),Aw=nt();De.WEEKDAY_DICTIONARY={sonntag:0,so:0,montag:1,mo:1,dienstag:2,di:2,mittwoch:3,mi:3,donnerstag:4,do:4,freitag:5,fr:5,samstag:6,sa:6};De.MONTH_DICTIONARY={januar:1,j\u00E4nner:1,janner:1,jan:1,"jan.":1,februar:2,feber:2,feb:2,"feb.":2,m\u00E4rz:3,maerz:3,m\u00E4r:3,"m\xE4r.":3,mrz:3,"mrz.":3,april:4,apr:4,"apr.":4,mai:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,august:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,dezember:12,dez:12,"dez.":12};De.INTEGER_WORD_DICTIONARY={eins:1,eine:1,einem:1,einen:1,einer:1,zwei:2,drei:3,vier:4,f\u00FCnf:5,fuenf:5,sechs:6,sieben:7,acht:8,neun:9,zehn:10,elf:11,zw\u00F6lf:12,zwoelf:12};De.TIME_UNIT_DICTIONARY={sek:"second",sekunde:"second",sekunden:"second",min:"minute",minute:"minute",minuten:"minute",h:"hour",std:"hour",stunde:"hour",stunden:"hour",tag:"d",tage:"d",tagen:"d",woche:"week",wochen:"week",monat:"month",monate:"month",monaten:"month",monats:"month",quartal:"quarter",quartals:"quarter",quartale:"quarter",quartalen:"quarter",a:"year",j:"year",jr:"year",jahr:"year",jahre:"year",jahren:"year",jahres:"year"};De.NUMBER_PATTERN=`(?:${Il.matchAnyPattern(De.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|half(?:\\s*an?)?|an?\\b(?:\\s*few)?|few|several|a?\\s*couple\\s*(?:of)?)`;function Fm(r){let e=r.toLowerCase();return De.INTEGER_WORD_DICTIONARY[e]!==void 0?De.INTEGER_WORD_DICTIONARY[e]:e==="a"||e==="an"?1:e.match(/few/)?3:e.match(/half/)?.5:e.match(/couple/)?2:e.match(/several/)?7:parseFloat(e)}De.parseNumberPattern=Fm;De.YEAR_PATTERN="(?:[0-9]{1,4}(?:\\s*[vn]\\.?\\s*(?:C(?:hr)?|(?:u\\.?|d\\.?(?:\\s*g\\.?)?)?\\s*Z)\\.?|\\s*(?:u\\.?|d\\.?(?:\\s*g\\.)?)\\s*Z\\.?)?)";function Cw(r){if(/v/i.test(r))return-parseInt(r.replace(/[^0-9]+/gi,""));if(/n/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));if(/z/i.test(r))return parseInt(r.replace(/[^0-9]+/gi,""));let e=parseInt(r);return Aw.findMostLikelyADYear(e)}De.parseYear=Cw;var Lm=`(${De.NUMBER_PATTERN})\\s{0,5}(${Il.matchAnyPattern(De.TIME_UNIT_DICTIONARY)})\\s{0,5}`,Im=new RegExp(Lm,"i");De.TIME_UNITS_PATTERN=Il.repeatedTimeunitPattern("",Lm);function Pw(r){let e={},t=r,n=Im.exec(t);for(;n;)Nw(e,n),t=t.substring(n[0].length),n=Im.exec(t);return e}De.parseTimeUnits=Pw;function Nw(r,e){let t=Fm(e[1]),n=De.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Wm=b(Ll=>{"use strict";Object.defineProperty(Ll,"__esModule",{value:!0});var Um=Ki(),Iw=Ee(),Fw=L(),Lw=hn(),Uw=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:a[mn]\\s*?)?(?:(diese[mn]|letzte[mn]|n(?:\xE4|ae)chste[mn])\\s*)?(${Iw.matchAnyPattern(Um.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(diese|letzte|n(?:\xE4|ae)chste)\\s*woche)?(?=\\W|$)`,"i"),Ww=1,qw=3,Yw=2,Fl=class extends Fw.AbstractParserWithWordBoundaryChecking{innerPattern(){return Uw}innerExtract(e,t){let n=t[Yw].toLowerCase(),i=Um.WEEKDAY_DICTIONARY[n],s=t[Ww],a=t[qw],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o.match(/letzte/)?u="last":o.match(/chste/)?u="next":o.match(/diese/)&&(u="this");let l=Lw.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};Ll.default=Fl});var jm=b(Ul=>{"use strict";Object.defineProperty(Ul,"__esModule",{value:!0});var Hr=Be(),$w=new RegExp("(^|\\s|T)(?:(?:um|von)\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),jw=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|bis(?:\\s+um)?|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s)?)?(?:\\s*Uhr)?(?:\\s*(morgens|vormittags|nachmittags|abends|nachts|am\\s+(?:Morgen|Vormittag|Nachmittag|Abend)|in\\s+der\\s+Nacht))?(?=\\W|$)","i"),Gw=2,qm=3,Ym=4,$m=5,ri=class{pattern(e){return $w}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=ri.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=jw.exec(i);return s&&(n.end=ri.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[Gw]),t[qm]!=null&&(i=parseInt(t[qm])),i>=60||n>24)return null;if(n>=12&&(s=Hr.Meridiem.PM),t[$m]!=null){if(n>12)return null;let a=t[$m].toLowerCase();a.match(/morgen|vormittag/)&&(s=Hr.Meridiem.AM,n==12&&(n=0)),a.match(/nachmittag|abend/)&&(s=Hr.Meridiem.PM,n!=12&&(n+=12)),a.match(/nacht/)&&(n==12?(s=Hr.Meridiem.AM,n=0):n<6?s=Hr.Meridiem.AM:(s=Hr.Meridiem.PM,n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",Hr.Meridiem.AM):e.imply("meridiem",Hr.Meridiem.PM),t[Ym]!=null){let a=parseInt(t[Ym]);if(a>=60)return null;e.assign("second",a)}return e}};Ul.default=ri});var Gm=b(Qi=>{"use strict";var Bw=Qi&&Qi.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Qi,"__esModule",{value:!0});var Hw=Bw(lr()),Wl=class extends Hw.default{patternBetween(){return/^\s*(bis(?:\s*(?:am|zum))?|-)\s*$/i}};Qi.default=Wl});var Bm=b(Xi=>{"use strict";var zw=Xi&&Xi.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xi,"__esModule",{value:!0});var Vw=zw(br()),ql=class extends Vw.default{patternBetween(){return new RegExp("^\\s*(T|um|am|,|-)?\\s*$")}};Xi.default=ql});var Yl=b(Ji=>{"use strict";var Kw=Ji&&Ji.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ji,"__esModule",{value:!0});var Qw=Kw(ge()),yn=Be(),Xw=L(),Zw=Ut(),Jw=Wt(),Zi=class extends Xw.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(diesen)?\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht)(?=\W|$)/i}innerExtract(e,t){let n=Qw.default(e.refDate),i=t[2].toLowerCase(),s=e.createParsingComponents();return Zw.implySimilarTime(s,n),Zi.extractTimeComponents(s,i)}static extractTimeComponents(e,t){switch(t){case"morgen":e.imply("hour",6),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.AM);break;case"vormittag":e.imply("hour",9),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.AM);break;case"mittag":case"mittags":e.imply("hour",12),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.AM);break;case"nachmittag":e.imply("hour",15),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.PM);break;case"abend":e.imply("hour",18),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.PM);break;case"nacht":e.imply("hour",22),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.PM);break;case"mitternacht":e.get("hour")>1&&(e=Jw.addImpliedTimeUnits(e,{day:1})),e.imply("hour",0),e.imply("minute",0),e.imply("second",0),e.imply("meridiem",yn.Meridiem.AM);break}return e}};Ji.default=Zi});var Vm=b(Xt=>{"use strict";var e1=Xt&&Xt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),t1=Xt&&Xt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),r1=Xt&&Xt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&e1(e,r,t);return t1(e,r),e},zm=Xt&&Xt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xt,"__esModule",{value:!0});var n1=zm(ge()),i1=L(),_n=Ut(),s1=zm(Yl()),Hm=r1(wr()),a1=new RegExp("(jetzt|heute|morgen|\xFCbermorgen|uebermorgen|gestern|vorgestern|letzte\\s*nacht)(?:\\s*(morgen|vormittag|mittags?|nachmittag|abend|nacht|mitternacht))?(?=\\W|$)","i"),o1=1,u1=2,$l=class extends i1.AbstractParserWithWordBoundaryChecking{innerPattern(e){return a1}innerExtract(e,t){let n=n1.default(e.refDate),i=(t[o1]||"").toLowerCase(),s=(t[u1]||"").toLowerCase(),a=e.createParsingComponents();switch(i){case"jetzt":a=Hm.now(e.reference);break;case"heute":a=Hm.today(e.reference);break;case"morgen":_n.assignTheNextDay(a,n);break;case"\xFCbermorgen":case"uebermorgen":n=n.add(1,"day"),_n.assignTheNextDay(a,n);break;case"gestern":n=n.add(-1,"day"),_n.assignSimilarDate(a,n),_n.implySimilarTime(a,n);break;case"vorgestern":n=n.add(-2,"day"),_n.assignSimilarDate(a,n),_n.implySimilarTime(a,n);break;default:i.match(/letzte\s*nacht/)&&(n.hour()>6&&(n=n.add(-1,"day")),_n.assignSimilarDate(a,n),a.imply("hour",0));break}return s&&(a=s1.default.extractTimeComponents(a,s)),a}};Xt.default=$l});var eh=b(Gl=>{"use strict";Object.defineProperty(Gl,"__esModule",{value:!0});var l1=nt(),Zm=Ki(),Jm=Ki(),c1=Ee(),d1=L(),f1=new RegExp(`(?:am\\s*?)?(?:den\\s*?)?([0-9]{1,2})\\.(?:\\s*(?:bis(?:\\s*(?:am|zum))?|\\-|\\\u2013|\\s)\\s*([0-9]{1,2})\\.?)?\\s*(${c1.matchAnyPattern(Zm.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${Jm.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),Km=1,Qm=2,p1=3,Xm=4,jl=class extends d1.AbstractParserWithWordBoundaryChecking{innerPattern(){return f1}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=Zm.MONTH_DICTIONARY[t[p1].toLowerCase()],s=parseInt(t[Km]);if(s>31)return t.index=t.index+t[Km].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Xm]){let a=Jm.parseYear(t[Xm]);n.start.assign("year",a)}else{let a=l1.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Qm]){let a=parseInt(t[Qm]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Gl.default=jl});var th=b(Hl=>{"use strict";Object.defineProperty(Hl,"__esModule",{value:!0});var Na=Ki(),m1=Ne(),h1=L(),g1=Wt(),y1=Ee(),Bl=class extends h1.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:\\s*((?:n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?|vor|in)\\s*)?(${Na.NUMBER_PATTERN})?(?:\\s*(n\xE4chste|kommende|folgende|letzte|vergangene|vorige|vor(?:her|an)gegangene)(?:s|n|m|r)?)?\\s*(${y1.matchAnyPattern(Na.TIME_UNIT_DICTIONARY)})`,"i")}innerExtract(e,t){let n=t[2]?Na.parseNumberPattern(t[2]):1,i=Na.TIME_UNIT_DICTIONARY[t[4].toLowerCase()],s={};s[i]=n;let a=t[1]||t[3]||"";if(a=a.toLowerCase(),!!a)return(/vor/.test(a)||/letzte/.test(a)||/vergangen/.test(a))&&(s=g1.reverseTimeUnits(s)),m1.ParsingComponents.createRelativeFromReference(e.reference,s)}};Hl.default=Bl});var ih=b(Ve=>{"use strict";var Zt=Ve&&Ve.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ve,"__esModule",{value:!0});Ve.createConfiguration=Ve.createCasualConfiguration=Ve.parseDate=Ve.parse=Ve.strict=Ve.casual=void 0;var _1=vr(),rh=Qt(),T1=Zt(gn()),b1=Zt(ml()),v1=Zt(Nm()),E1=Zt(Wm()),w1=Zt(jm()),k1=Zt(Gm()),O1=Zt(Bm()),D1=Zt(Vm()),S1=Zt(Yl()),R1=Zt(eh()),M1=Zt(th());Ve.casual=new rh.Chrono(nh());Ve.strict=new rh.Chrono(zl(!0));function x1(r,e,t){return Ve.casual.parse(r,e,t)}Ve.parse=x1;function A1(r,e,t){return Ve.casual.parseDate(r,e,t)}Ve.parseDate=A1;function nh(r=!0){let e=zl(!1,r);return e.parsers.unshift(new S1.default),e.parsers.unshift(new D1.default),e.parsers.unshift(new M1.default),e}Ve.createCasualConfiguration=nh;function zl(r=!0,e=!0){return _1.includeCommonConfiguration({parsers:[new b1.default,new T1.default(e),new v1.default,new w1.default,new R1.default,new E1.default],refiners:[new k1.default,new O1.default]},r)}Ve.createConfiguration=zl});var ah=b(Jt=>{"use strict";var C1=Jt&&Jt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),P1=Jt&&Jt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),N1=Jt&&Jt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&C1(e,r,t);return P1(e,r),e},I1=Jt&&Jt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Jt,"__esModule",{value:!0});var F1=I1(ge()),L1=Be(),U1=L(),sh=Ut(),Ia=N1(wr()),Vl=class extends U1.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(maintenant|aujourd'hui|demain|hier|cette\s*nuit|la\s*veille)(?=\W|$)/i}innerExtract(e,t){let n=F1.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();switch(i){case"maintenant":return Ia.now(e.reference);case"aujourd'hui":return Ia.today(e.reference);case"hier":return Ia.yesterday(e.reference);case"demain":return Ia.tomorrow(e.reference);default:i.match(/cette\s*nuit/)?(sh.assignSimilarDate(s,n),s.imply("hour",22),s.imply("meridiem",L1.Meridiem.PM)):i.match(/la\s*veille/)&&(n=n.add(-1,"day"),sh.assignSimilarDate(s,n),s.imply("hour",0))}return s}};Jt.default=Vl});var oh=b(Ql=>{"use strict";Object.defineProperty(Ql,"__esModule",{value:!0});var es=Be(),W1=L(),Kl=class extends W1.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(cet?)?\s*(matin|soir|après-midi|aprem|a midi|à minuit)(?=\W|$)/i}innerExtract(e,t){let n=t[2].toLowerCase(),i=e.createParsingComponents();switch(n){case"apr\xE8s-midi":case"aprem":i.imply("hour",14),i.imply("minute",0),i.imply("meridiem",es.Meridiem.PM);break;case"soir":i.imply("hour",18),i.imply("minute",0),i.imply("meridiem",es.Meridiem.PM);break;case"matin":i.imply("hour",8),i.imply("minute",0),i.imply("meridiem",es.Meridiem.AM);break;case"a midi":i.imply("hour",12),i.imply("minute",0),i.imply("meridiem",es.Meridiem.AM);break;case"\xE0 minuit":i.imply("hour",0),i.imply("meridiem",es.Meridiem.AM);break}return i}};Ql.default=Kl});var uh=b(Zl=>{"use strict";Object.defineProperty(Zl,"__esModule",{value:!0});var q1=pn(),Xl=class extends q1.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:[\xE0a])\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};Zl.default=Xl});var lh=b(ts=>{"use strict";var Y1=ts&&ts.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ts,"__esModule",{value:!0});var $1=Y1(br()),Jl=class extends $1.default{patternBetween(){return new RegExp("^\\s*(T|\xE0|a|vers|de|,|-)?\\s*$")}};ts.default=Jl});var ch=b(rs=>{"use strict";var j1=rs&&rs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(rs,"__esModule",{value:!0});var G1=j1(lr()),ec=class extends G1.default{patternBetween(){return/^\s*(à|a|-)\s*$/i}};rs.default=ec});var zr=b(ye=>{"use strict";Object.defineProperty(ye,"__esModule",{value:!0});ye.parseTimeUnits=ye.TIME_UNITS_PATTERN=ye.parseYear=ye.YEAR_PATTERN=ye.parseOrdinalNumberPattern=ye.ORDINAL_NUMBER_PATTERN=ye.parseNumberPattern=ye.NUMBER_PATTERN=ye.TIME_UNIT_DICTIONARY=ye.INTEGER_WORD_DICTIONARY=ye.MONTH_DICTIONARY=ye.WEEKDAY_DICTIONARY=void 0;var tc=Ee();ye.WEEKDAY_DICTIONARY={dimanche:0,dim:0,lundi:1,lun:1,mardi:2,mar:2,mercredi:3,mer:3,jeudi:4,jeu:4,vendredi:5,ven:5,samedi:6,sam:6};ye.MONTH_DICTIONARY={janvier:1,jan:1,"jan.":1,f\u00E9vrier:2,f\u00E9v:2,"f\xE9v.":2,fevrier:2,fev:2,"fev.":2,mars:3,mar:3,"mar.":3,avril:4,avr:4,"avr.":4,mai:5,juin:6,jun:6,juillet:7,juil:7,jul:7,"jul.":7,ao\u00FBt:8,aout:8,septembre:9,sep:9,"sep.":9,sept:9,"sept.":9,octobre:10,oct:10,"oct.":10,novembre:11,nov:11,"nov.":11,d\u00E9cembre:12,decembre:12,dec:12,"dec.":12};ye.INTEGER_WORD_DICTIONARY={un:1,deux:2,trois:3,quatre:4,cinq:5,six:6,sept:7,huit:8,neuf:9,dix:10,onze:11,douze:12,treize:13};ye.TIME_UNIT_DICTIONARY={sec:"second",seconde:"second",secondes:"second",min:"minute",mins:"minute",minute:"minute",minutes:"minute",h:"hour",hr:"hour",hrs:"hour",heure:"hour",heures:"hour",jour:"d",jours:"d",semaine:"week",semaines:"week",mois:"month",trimestre:"quarter",trimestres:"quarter",ans:"year",ann\u00E9e:"year",ann\u00E9es:"year"};ye.NUMBER_PATTERN=`(?:${tc.matchAnyPattern(ye.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|une?\\b|quelques?|demi-?)`;function fh(r){let e=r.toLowerCase();return ye.INTEGER_WORD_DICTIONARY[e]!==void 0?ye.INTEGER_WORD_DICTIONARY[e]:e==="une"||e==="un"?1:e.match(/quelques?/)?3:e.match(/demi-?/)?.5:parseFloat(e)}ye.parseNumberPattern=fh;ye.ORDINAL_NUMBER_PATTERN="(?:[0-9]{1,2}(?:er)?)";function B1(r){let e=r.toLowerCase();return e=e.replace(/(?:er)$/i,""),parseInt(e)}ye.parseOrdinalNumberPattern=B1;ye.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:AC|AD|p\\.\\s*C(?:hr?)?\\.\\s*n\\.)|[1-2][0-9]{3}|[5-9][0-9])";function H1(r){if(/AC/i.test(r))return r=r.replace(/BC/i,""),-parseInt(r);if(/AD/i.test(r)||/C/i.test(r))return r=r.replace(/[^\d]+/i,""),parseInt(r);let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}ye.parseYear=H1;var ph=`(${ye.NUMBER_PATTERN})\\s{0,5}(${tc.matchAnyPattern(ye.TIME_UNIT_DICTIONARY)})\\s{0,5}`,dh=new RegExp(ph,"i");ye.TIME_UNITS_PATTERN=tc.repeatedTimeunitPattern("",ph);function z1(r){let e={},t=r,n=dh.exec(t);for(;n;)V1(e,n),t=t.substring(n[0].length),n=dh.exec(t);return e}ye.parseTimeUnits=z1;function V1(r,e){let t=fh(e[1]),n=ye.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var hh=b(nc=>{"use strict";Object.defineProperty(nc,"__esModule",{value:!0});var mh=zr(),K1=Ee(),Q1=L(),X1=hn(),Z1=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(?:ce)\\s*)?(${K1.matchAnyPattern(mh.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(dernier|prochain)\\s*)?(?=\\W|\\d|$)`,"i"),J1=1,ek=2,rc=class extends Q1.AbstractParserWithWordBoundaryChecking{innerPattern(){return Z1}innerExtract(e,t){let n=t[J1].toLowerCase(),i=mh.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[ek];s=s||"",s=s.toLowerCase();let a=null;s=="dernier"?a="last":s=="prochain"&&(a="next");let o=X1.toDayJSWeekday(e.refDate,i,a);return e.createParsingComponents().assign("weekday",i).imply("day",o.date()).imply("month",o.month()+1).imply("year",o.year())}};nc.default=rc});var Th=b(ic=>{"use strict";Object.defineProperty(ic,"__esModule",{value:!0});var ns=Be(),tk=new RegExp("(^|\\s|T)(?:(?:[\xE0a])\\s*)?(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),rk=new RegExp("^\\s*(\\-|\\\u2013|\\~|\\\u301C|[\xE0a]|\\?)\\s*(\\d{1,2})(?:h|:)?(?:(\\d{1,2})(?:m|:)?)?(?:(\\d{1,2})(?:s|:)?)?(?:\\s*(A\\.M\\.|P\\.M\\.|AM?|PM?))?(?=\\W|$)","i"),nk=2,gh=3,yh=4,_h=5,ni=class{pattern(e){return tk}extract(e,t){let n=e.createParsingResult(t.index+t[1].length,t[0].substring(t[1].length));if(n.text.match(/^\d{4}$/)||(n.start=ni.extractTimeComponent(n.start.clone(),t),!n.start))return t.index+=t[0].length,null;let i=e.text.substring(t.index+t[0].length),s=rk.exec(i);return s&&(n.end=ni.extractTimeComponent(n.start.clone(),s),n.end&&(n.text+=s[0])),n}static extractTimeComponent(e,t){let n=0,i=0,s=null;if(n=parseInt(t[nk]),t[gh]!=null&&(i=parseInt(t[gh])),i>=60||n>24)return null;if(n>=12&&(s=ns.Meridiem.PM),t[_h]!=null){if(n>12)return null;let a=t[_h][0].toLowerCase();a=="a"&&(s=ns.Meridiem.AM,n==12&&(n=0)),a=="p"&&(s=ns.Meridiem.PM,n!=12&&(n+=12))}if(e.assign("hour",n),e.assign("minute",i),s!==null?e.assign("meridiem",s):n<12?e.imply("meridiem",ns.Meridiem.AM):e.imply("meridiem",ns.Meridiem.PM),t[yh]!=null){let a=parseInt(t[yh]);if(a>=60)return null;e.assign("second",a)}return e}};ic.default=ni});var Oh=b(ac=>{"use strict";Object.defineProperty(ac,"__esModule",{value:!0});var ik=nt(),wh=zr(),kh=zr(),Fa=zr(),sk=Ee(),ak=L(),ok=new RegExp(`(?:on\\s*?)?(${Fa.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:au|\\-|\\\u2013|jusqu'au?|\\s)\\s*(${Fa.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:de)?\\s*)(${sk.matchAnyPattern(wh.MONTH_DICTIONARY)})(?:(?:-|/|,?\\s*)(${kh.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),bh=1,vh=2,uk=3,Eh=4,sc=class extends ak.AbstractParserWithWordBoundaryChecking{innerPattern(){return ok}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=wh.MONTH_DICTIONARY[t[uk].toLowerCase()],s=Fa.parseOrdinalNumberPattern(t[bh]);if(s>31)return t.index=t.index+t[bh].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Eh]){let a=kh.parseYear(t[Eh]);n.start.assign("year",a)}else{let a=ik.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[vh]){let a=Fa.parseOrdinalNumberPattern(t[vh]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};ac.default=sc});var Sh=b(uc=>{"use strict";Object.defineProperty(uc,"__esModule",{value:!0});var Dh=zr(),lk=Ne(),ck=L(),dk=Wt(),oc=class extends ck.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`il y a\\s*(${Dh.TIME_UNITS_PATTERN})(?=(?:\\W|$))`,"i")}innerExtract(e,t){let n=Dh.parseTimeUnits(t[1]),i=dk.reverseTimeUnits(n);return lk.ParsingComponents.createRelativeFromReference(e.reference,i)}};uc.default=oc});var Mh=b(cc=>{"use strict";Object.defineProperty(cc,"__esModule",{value:!0});var Rh=zr(),fk=Ne(),pk=L(),lc=class extends pk.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp(`(?:dans|en|pour|pendant|de)\\s*(${Rh.TIME_UNITS_PATTERN})(?=\\W|$)`,"i")}innerExtract(e,t){let n=Rh.parseTimeUnits(t[1]);return fk.ParsingComponents.createRelativeFromReference(e.reference,n)}};cc.default=lc});var xh=b(fc=>{"use strict";Object.defineProperty(fc,"__esModule",{value:!0});var La=zr(),mk=Ne(),hk=L(),gk=Wt(),yk=Ee(),dc=class extends hk.AbstractParserWithWordBoundaryChecking{constructor(){super()}innerPattern(){return new RegExp(`(?:les?|la|l'|du|des?)\\s*(${La.NUMBER_PATTERN})?(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?\\s*(${yk.matchAnyPattern(La.TIME_UNIT_DICTIONARY)})(?:\\s*(prochaine?s?|derni[e\xE8]re?s?|pass[\xE9e]e?s?|pr[\xE9e]c[\xE9e]dents?|suivante?s?))?`,"i")}innerExtract(e,t){let n=t[1]?La.parseNumberPattern(t[1]):1,i=La.TIME_UNIT_DICTIONARY[t[3].toLowerCase()],s={};s[i]=n;let a=t[2]||t[4]||"";if(a=a.toLowerCase(),!!a)return(/derni[eè]re?s?/.test(a)||/pass[ée]e?s?/.test(a)||/pr[ée]c[ée]dents?/.test(a))&&(s=gk.reverseTimeUnits(s)),mk.ParsingComponents.createRelativeFromReference(e.reference,s)}};fc.default=dc});var Ph=b(Ke=>{"use strict";var qt=Ke&&Ke.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ke,"__esModule",{value:!0});Ke.createConfiguration=Ke.createCasualConfiguration=Ke.parseDate=Ke.parse=Ke.strict=Ke.casual=void 0;var _k=vr(),Ah=Qt(),Tk=qt(ah()),bk=qt(oh()),vk=qt(gn()),Ek=qt(uh()),wk=qt(lh()),kk=qt(ch()),Ok=qt(hh()),Dk=qt(Th()),Sk=qt(Oh()),Rk=qt(Sh()),Mk=qt(Mh()),xk=qt(xh());Ke.casual=new Ah.Chrono(Ch());Ke.strict=new Ah.Chrono(pc(!0));function Ak(r,e,t){return Ke.casual.parse(r,e,t)}Ke.parse=Ak;function Ck(r,e,t){return Ke.casual.parseDate(r,e,t)}Ke.parseDate=Ck;function Ch(r=!0){let e=pc(!1,r);return e.parsers.unshift(new Tk.default),e.parsers.unshift(new bk.default),e.parsers.unshift(new xk.default),e}Ke.createCasualConfiguration=Ch;function pc(r=!0,e=!0){return _k.includeCommonConfiguration({parsers:[new vk.default(e),new Sk.default,new Ek.default,new Dk.default,new Rk.default,new Mk.default,new Ok.default],refiners:[new wk.default,new kk.default]},r)}Ke.createConfiguration=pc});var Nh=b(Ua=>{"use strict";Object.defineProperty(Ua,"__esModule",{value:!0});Ua.toHankaku=void 0;function Pk(r){return String(r).replace(/\u2019/g,"'").replace(/\u201D/g,'"').replace(/\u3000/g," ").replace(/\uFFE5/g,"\xA5").replace(/[\uFF01\uFF03-\uFF06\uFF08\uFF09\uFF0C-\uFF19\uFF1C-\uFF1F\uFF21-\uFF3B\uFF3D\uFF3F\uFF41-\uFF5B\uFF5D\uFF5E]/g,Nk)}Ua.toHankaku=Pk;function Nk(r){return String.fromCharCode(r.charCodeAt(0)-65248)}});var Fh=b(is=>{"use strict";var Ik=is&&is.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(is,"__esModule",{value:!0});var mc=Nh(),Fk=nt(),Lk=Ik(ge()),Uk=/(?:(?:([同今本])|((昭和|平成|令和)?([0-9０-９]{1,4}|元)))年\s*)?([0-9０-９]{1,2})月\s*([0-9０-９]{1,2})日/i,Ih=1,Wk=2,hc=3,qk=4,Yk=5,$k=6,gc=class{pattern(){return Uk}extract(e,t){let n=parseInt(mc.toHankaku(t[Yk])),i=parseInt(mc.toHankaku(t[$k])),s=e.createParsingComponents({day:i,month:n});if(t[Ih]&&t[Ih].match("\u540C|\u4ECA|\u672C")){let a=Lk.default(e.refDate);s.assign("year",a.year())}if(t[Wk]){let a=t[qk],o=a=="\u5143"?1:parseInt(mc.toHankaku(a));t[hc]=="\u4EE4\u548C"?o+=2018:t[hc]=="\u5E73\u6210"?o+=1988:t[hc]=="\u662D\u548C"&&(o+=1925),s.assign("year",o)}else{let a=Fk.findYearClosestToRef(e.refDate,i,n);s.imply("year",a)}return s}};is.default=gc});var Lh=b(ss=>{"use strict";var jk=ss&&ss.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ss,"__esModule",{value:!0});var Gk=jk(lr()),yc=class extends Gk.default{patternBetween(){return/^\s*(から|ー|-)\s*$/i}};ss.default=yc});var Wh=b(er=>{"use strict";var Bk=er&&er.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),Hk=er&&er.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),zk=er&&er.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&Bk(e,r,t);return Hk(e,r),e},Vk=er&&er.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(er,"__esModule",{value:!0});var Kk=Vk(ge()),Uh=Be(),_c=zk(wr()),Qk=/今日|当日|昨日|明日|今夜|今夕|今晩|今朝/i,Tc=class{pattern(){return Qk}extract(e,t){let n=t[0],i=Kk.default(e.refDate),s=e.createParsingComponents();switch(n){case"\u6628\u65E5":return _c.yesterday(e.reference);case"\u660E\u65E5":return _c.tomorrow(e.reference);case"\u4ECA\u65E5":case"\u5F53\u65E5":return _c.today(e.reference)}return n=="\u4ECA\u591C"||n=="\u4ECA\u5915"||n=="\u4ECA\u6669"?(s.imply("hour",22),s.assign("meridiem",Uh.Meridiem.PM)):n.match("\u4ECA\u671D")&&(s.imply("hour",6),s.assign("meridiem",Uh.Meridiem.AM)),s.assign("day",i.date()),s.assign("month",i.month()+1),s.assign("year",i.year()),s}};er.default=Tc});var $h=b(Qe=>{"use strict";var bc=Qe&&Qe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Qe,"__esModule",{value:!0});Qe.createConfiguration=Qe.createCasualConfiguration=Qe.parseDate=Qe.parse=Qe.strict=Qe.casual=void 0;var Xk=bc(Fh()),Zk=bc(Lh()),Jk=bc(Wh()),qh=Qt();Qe.casual=new qh.Chrono(Yh());Qe.strict=new qh.Chrono(vc());function eO(r,e,t){return Qe.casual.parse(r,e,t)}Qe.parse=eO;function tO(r,e,t){return Qe.casual.parseDate(r,e,t)}Qe.parseDate=tO;function Yh(){let r=vc();return r.parsers.unshift(new Jk.default),r}Qe.createCasualConfiguration=Yh;function vc(){return{parsers:[new Xk.default],refiners:[new Zk.default]}}Qe.createConfiguration=vc});var Wa=b(dr=>{"use strict";Object.defineProperty(dr,"__esModule",{value:!0});dr.parseYear=dr.YEAR_PATTERN=dr.MONTH_DICTIONARY=dr.WEEKDAY_DICTIONARY=void 0;dr.WEEKDAY_DICTIONARY={domingo:0,dom:0,segunda:1,"segunda-feira":1,seg:1,ter\u00E7a:2,"ter\xE7a-feira":2,ter:2,quarta:3,"quarta-feira":3,qua:3,quinta:4,"quinta-feira":4,qui:4,sexta:5,"sexta-feira":5,sex:5,s\u00E1bado:6,sabado:6,sab:6};dr.MONTH_DICTIONARY={janeiro:1,jan:1,"jan.":1,fevereiro:2,fev:2,"fev.":2,mar\u00E7o:3,mar:3,"mar.":3,abril:4,abr:4,"abr.":4,maio:5,mai:5,"mai.":5,junho:6,jun:6,"jun.":6,julho:7,jul:7,"jul.":7,agosto:8,ago:8,"ago.":8,setembro:9,set:9,"set.":9,outubro:10,out:10,"out.":10,novembro:11,nov:11,"nov.":11,dezembro:12,dez:12,"dez.":12};dr.YEAR_PATTERN="[0-9]{1,4}(?![^\\s]\\d)(?:\\s*[a|d]\\.?\\s*c\\.?|\\s*a\\.?\\s*d\\.?)?";function rO(r){if(r.match(/^[0-9]{1,4}$/)){let e=parseInt(r);return e<100&&(e>50?e=e+1900:e=e+2e3),e}return r.match(/a\.?\s*c\.?/i)?(r=r.replace(/a\.?\s*c\.?/i,""),-parseInt(r)):parseInt(r)}dr.parseYear=rO});var Gh=b(wc=>{"use strict";Object.defineProperty(wc,"__esModule",{value:!0});var jh=Wa(),nO=Ee(),iO=L(),sO=hn(),aO=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:(este|esta|passado|pr[o\xF3]ximo)\\s*)?(${nO.matchAnyPattern(jh.WEEKDAY_DICTIONARY)})(?:\\s*(?:\\,|\\)|\\\uFF09))?(?:\\s*(este|esta|passado|pr[\xF3o]ximo)\\s*semana)?(?=\\W|\\d|$)`,"i"),oO=1,uO=2,lO=3,Ec=class extends iO.AbstractParserWithWordBoundaryChecking{innerPattern(){return aO}innerExtract(e,t){let n=t[uO].toLowerCase(),i=jh.WEEKDAY_DICTIONARY[n];if(i===void 0)return null;let s=t[oO],a=t[lO],o=s||a||"";o=o.toLowerCase();let u=null;o=="passado"?u="this":o=="pr\xF3ximo"||o=="proximo"?u="next":o=="este"&&(u="this");let l=sO.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};wc.default=Ec});var Bh=b(Oc=>{"use strict";Object.defineProperty(Oc,"__esModule",{value:!0});var cO=pn(),kc=class extends cO.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:ao?|\xE0s?|das|da|de|do)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|a(?:o)?|\\?)\\s*"}};Oc.default=kc});var Hh=b(as=>{"use strict";var dO=as&&as.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(as,"__esModule",{value:!0});var fO=dO(br()),Dc=class extends fO.default{patternBetween(){return new RegExp("^\\s*(?:,|\xE0)?\\s*$")}};as.default=Dc});var zh=b(os=>{"use strict";var pO=os&&os.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(os,"__esModule",{value:!0});var mO=pO(lr()),Sc=class extends mO.default{patternBetween(){return/^\s*(?:-)\s*$/i}};os.default=Sc});var Jh=b(Mc=>{"use strict";Object.defineProperty(Mc,"__esModule",{value:!0});var hO=nt(),Xh=Wa(),Zh=Wa(),gO=Ee(),yO=L(),_O=new RegExp(`([0-9]{1,2})(?:\xBA|\xAA|\xB0)?(?:\\s*(?:desde|de|\\-|\\\u2013|ao?|\\s)\\s*([0-9]{1,2})(?:\xBA|\xAA|\xB0)?)?\\s*(?:de)?\\s*(?:-|/|\\s*(?:de|,)?\\s*)(${gO.matchAnyPattern(Xh.MONTH_DICTIONARY)})(?:\\s*(?:de|,)?\\s*(${Zh.YEAR_PATTERN}))?(?=\\W|$)`,"i"),Vh=1,Kh=2,TO=3,Qh=4,Rc=class extends yO.AbstractParserWithWordBoundaryChecking{innerPattern(){return _O}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=Xh.MONTH_DICTIONARY[t[TO].toLowerCase()],s=parseInt(t[Vh]);if(s>31)return t.index=t.index+t[Vh].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[Qh]){let a=Zh.parseYear(t[Qh]);n.start.assign("year",a)}else{let a=hO.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[Kh]){let a=parseInt(t[Kh]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Mc.default=Rc});var eg=b(kr=>{"use strict";var bO=kr&&kr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),vO=kr&&kr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),EO=kr&&kr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&bO(e,r,t);return vO(e,r),e};Object.defineProperty(kr,"__esModule",{value:!0});var wO=L(),qa=EO(wr()),xc=class extends wO.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(agora|hoje|amanha|amanhã|ontem)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"agora":return qa.now(e.reference);case"hoje":return qa.today(e.reference);case"amanha":case"amanh\xE3":return qa.tomorrow(e.reference);case"ontem":return qa.yesterday(e.reference)}return i}};kr.default=xc});var tg=b(us=>{"use strict";var kO=us&&us.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(us,"__esModule",{value:!0});var Ya=Be(),OO=L(),DO=Ut(),SO=kO(ge()),Ac=class extends OO.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(?:esta\s*)?(manha|manhã|tarde|meia-noite|meio-dia|noite)(?=\W|$)/i}innerExtract(e,t){let n=SO.default(e.refDate),i=e.createParsingComponents();switch(t[1].toLowerCase()){case"tarde":i.imply("meridiem",Ya.Meridiem.PM),i.imply("hour",15);break;case"noite":i.imply("meridiem",Ya.Meridiem.PM),i.imply("hour",22);break;case"manha":case"manh\xE3":i.imply("meridiem",Ya.Meridiem.AM),i.imply("hour",6);break;case"meia-noite":DO.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"meio-dia":i.imply("meridiem",Ya.Meridiem.AM),i.imply("hour",12);break}return i}};us.default=Ac});var ig=b(Xe=>{"use strict";var Vr=Xe&&Xe.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Xe,"__esModule",{value:!0});Xe.createConfiguration=Xe.createCasualConfiguration=Xe.parseDate=Xe.parse=Xe.strict=Xe.casual=void 0;var RO=vr(),rg=Qt(),MO=Vr(gn()),xO=Vr(Gh()),AO=Vr(Bh()),CO=Vr(Hh()),PO=Vr(zh()),NO=Vr(Jh()),IO=Vr(eg()),FO=Vr(tg());Xe.casual=new rg.Chrono(ng());Xe.strict=new rg.Chrono(Cc(!0));function LO(r,e,t){return Xe.casual.parse(r,e,t)}Xe.parse=LO;function UO(r,e,t){return Xe.casual.parseDate(r,e,t)}Xe.parseDate=UO;function ng(r=!0){let e=Cc(!1,r);return e.parsers.push(new IO.default),e.parsers.push(new FO.default),e}Xe.createCasualConfiguration=ng;function Cc(r=!0,e=!0){return RO.includeCommonConfiguration({parsers:[new MO.default(e),new xO.default,new AO.default,new NO.default],refiners:[new CO.default,new PO.default]},r)}Xe.createConfiguration=Cc});var sg=b(ls=>{"use strict";var WO=ls&&ls.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ls,"__esModule",{value:!0});var qO=WO(lr()),Pc=class extends qO.default{patternBetween(){return/^\s*(tot|-)\s*$/i}};ls.default=Pc});var ag=b(cs=>{"use strict";var YO=cs&&cs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(cs,"__esModule",{value:!0});var $O=YO(br()),Nc=class extends $O.default{patternBetween(){return new RegExp("^\\s*(om|na|voor|in de|,|-)?\\s*$")}};cs.default=Nc});var og=b(Or=>{"use strict";var jO=Or&&Or.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),GO=Or&&Or.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),BO=Or&&Or.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&jO(e,r,t);return GO(e,r),e};Object.defineProperty(Or,"__esModule",{value:!0});var HO=L(),$a=BO(wr()),Ic=class extends HO.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(nu|vandaag|morgen|morgend|gisteren)(?=\W|$)/i}innerExtract(e,t){let n=t[0].toLowerCase(),i=e.createParsingComponents();switch(n){case"nu":return $a.now(e.reference);case"vandaag":return $a.today(e.reference);case"morgen":case"morgend":return $a.tomorrow(e.reference);case"gisteren":return $a.yesterday(e.reference)}return i}};Or.default=Ic});var ug=b(ds=>{"use strict";var zO=ds&&ds.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ds,"__esModule",{value:!0});var ja=Be(),VO=L(),KO=zO(ge()),QO=Ut(),XO=1,ZO=2,Fc=class extends VO.AbstractParserWithWordBoundaryChecking{innerPattern(){return/(deze)?\s*(namiddag|avond|middernacht|ochtend|middag|'s middags|'s avonds|'s ochtends)(?=\W|$)/i}innerExtract(e,t){let n=KO.default(e.refDate),i=e.createParsingComponents();switch(t[XO]==="deze"&&(i.assign("day",e.refDate.getDate()),i.assign("month",e.refDate.getMonth()+1),i.assign("year",e.refDate.getFullYear())),t[ZO].toLowerCase()){case"namiddag":case"'s namiddags":i.imply("meridiem",ja.Meridiem.PM),i.imply("hour",15);break;case"avond":case"'s avonds'":i.imply("meridiem",ja.Meridiem.PM),i.imply("hour",20);break;case"middernacht":QO.assignTheNextDay(i,n),i.imply("hour",0),i.imply("minute",0),i.imply("second",0);break;case"ochtend":case"'s ochtends":i.imply("meridiem",ja.Meridiem.AM),i.imply("hour",6);break;case"middag":case"'s middags":i.imply("meridiem",ja.Meridiem.AM),i.imply("hour",12);break}return i}};ds.default=Fc});var St=b(ue=>{"use strict";Object.defineProperty(ue,"__esModule",{value:!0});ue.parseTimeUnits=ue.TIME_UNITS_PATTERN=ue.parseYear=ue.YEAR_PATTERN=ue.parseOrdinalNumberPattern=ue.ORDINAL_NUMBER_PATTERN=ue.parseNumberPattern=ue.NUMBER_PATTERN=ue.TIME_UNIT_DICTIONARY=ue.ORDINAL_WORD_DICTIONARY=ue.INTEGER_WORD_DICTIONARY=ue.MONTH_DICTIONARY=ue.WEEKDAY_DICTIONARY=void 0;var Ga=Ee(),JO=nt();ue.WEEKDAY_DICTIONARY={zondag:0,zon:0,"zon.":0,zo:0,"zo.":0,maandag:1,ma:1,"ma.":1,dinsdag:2,din:2,"din.":2,di:2,"di.":2,woensdag:3,woe:3,"woe.":3,wo:3,"wo.":3,donderdag:4,dond:4,"dond.":4,do:4,"do.":4,vrijdag:5,vrij:5,"vrij.":5,vr:5,"vr.":5,zaterdag:6,zat:6,"zat.":6,za:6,"za.":6};ue.MONTH_DICTIONARY={januari:1,jan:1,"jan.":1,februari:2,feb:2,"feb.":2,maart:3,mar:3,"mar.":3,april:4,apr:4,"apr.":4,mei:5,juni:6,jun:6,"jun.":6,juli:7,jul:7,"jul.":7,augustus:8,aug:8,"aug.":8,september:9,sep:9,"sep.":9,sept:9,"sept.":9,oktober:10,okt:10,"okt.":10,november:11,nov:11,"nov.":11,december:12,dec:12,"dec.":12};ue.INTEGER_WORD_DICTIONARY={een:1,twee:2,drie:3,vier:4,vijf:5,zes:6,zeven:7,acht:8,negen:9,tien:10,elf:11,twaalf:12};ue.ORDINAL_WORD_DICTIONARY={eerste:1,tweede:2,derde:3,vierde:4,vijfde:5,zesde:6,zevende:7,achtste:8,negende:9,tiende:10,elfde:11,twaalfde:12,dertiende:13,veertiende:14,vijftiende:15,zestiende:16,zeventiende:17,achttiende:18,negentiende:19,twintigste:20,eenentwintigste:21,twee\u00EBntwintigste:22,drieentwintigste:23,vierentwintigste:24,vijfentwintigste:25,zesentwintigste:26,zevenentwintigste:27,achtentwintig:28,negenentwintig:29,dertigste:30,eenendertigste:31};ue.TIME_UNIT_DICTIONARY={sec:"second",second:"second",seconden:"second",min:"minute",mins:"minute",minute:"minute",minuut:"minute",minuten:"minute",minuutje:"minute",h:"hour",hr:"hour",hrs:"hour",uur:"hour",u:"hour",uren:"hour",dag:"d",dagen:"d",week:"week",weken:"week",maand:"month",maanden:"month",jaar:"year",jr:"year",jaren:"year"};ue.NUMBER_PATTERN=`(?:${Ga.matchAnyPattern(ue.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+[\\.,][0-9]+|halve?|half|paar)`;function cg(r){let e=r.toLowerCase();return ue.INTEGER_WORD_DICTIONARY[e]!==void 0?ue.INTEGER_WORD_DICTIONARY[e]:e==="paar"?2:e==="half"||e.match(/halve?/)?.5:parseFloat(e.replace(",","."))}ue.parseNumberPattern=cg;ue.ORDINAL_NUMBER_PATTERN=`(?:${Ga.matchAnyPattern(ue.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:ste|de)?)`;function eD(r){let e=r.toLowerCase();return ue.ORDINAL_WORD_DICTIONARY[e]!==void 0?ue.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:ste|de)$/i,""),parseInt(e))}ue.parseOrdinalNumberPattern=eD;ue.YEAR_PATTERN="(?:[1-9][0-9]{0,3}\\s*(?:voor Christus|na Christus)|[1-2][0-9]{3}|[5-9][0-9])";function tD(r){if(/voor Christus/i.test(r))return r=r.replace(/voor Christus/i,""),-parseInt(r);if(/na Christus/i.test(r))return r=r.replace(/na Christus/i,""),parseInt(r);let e=parseInt(r);return JO.findMostLikelyADYear(e)}ue.parseYear=tD;var dg=`(${ue.NUMBER_PATTERN})\\s{0,5}(${Ga.matchAnyPattern(ue.TIME_UNIT_DICTIONARY)})\\s{0,5}`,lg=new RegExp(dg,"i");ue.TIME_UNITS_PATTERN=Ga.repeatedTimeunitPattern("(?:(?:binnen|in)\\s*)?",dg);function rD(r){let e={},t=r,n=lg.exec(t);for(;n;)nD(e,n),t=t.substring(n[0].length),n=lg.exec(t);return e}ue.parseTimeUnits=rD;function nD(r,e){let t=cg(e[1]),n=ue.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var pg=b(Uc=>{"use strict";Object.defineProperty(Uc,"__esModule",{value:!0});var fg=St(),iD=Ne(),sD=L(),Lc=class extends sD.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(?:binnen|in|binnen de|voor)\\s*("+fg.TIME_UNITS_PATTERN+")(?=\\W|$)","i")}innerExtract(e,t){let n=fg.parseTimeUnits(t[1]);return iD.ParsingComponents.createRelativeFromReference(e.reference,n)}};Uc.default=Lc});var hg=b(qc=>{"use strict";Object.defineProperty(qc,"__esModule",{value:!0});var mg=St(),aD=Ee(),oD=L(),uD=hn(),lD=new RegExp(`(?:(?:\\,|\\(|\\\uFF08)\\s*)?(?:op\\s*?)?(?:(deze|vorige|volgende)\\s*(?:week\\s*)?)?(${aD.matchAnyPattern(mg.WEEKDAY_DICTIONARY)})(?=\\W|$)`,"i"),cD=1,dD=2,fD=3,Wc=class extends oD.AbstractParserWithWordBoundaryChecking{innerPattern(){return lD}innerExtract(e,t){let n=t[dD].toLowerCase(),i=mg.WEEKDAY_DICTIONARY[n],s=t[cD],a=t[fD],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="vorige"?u="last":o=="volgende"?u="next":o=="deze"&&(u="this");let l=uD.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};qc.default=Wc});var vg=b($c=>{"use strict";Object.defineProperty($c,"__esModule",{value:!0});var pD=nt(),Tg=St(),Ba=St(),bg=St(),mD=Ee(),hD=L(),gD=new RegExp(`(?:on\\s*?)?(${Ba.ORDINAL_NUMBER_PATTERN})(?:\\s*(?:tot|\\-|\\\u2013|until|through|till|\\s)\\s*(${Ba.ORDINAL_NUMBER_PATTERN}))?(?:-|/|\\s*(?:of)?\\s*)(`+mD.matchAnyPattern(Tg.MONTH_DICTIONARY)+`)(?:(?:-|/|,?\\s*)(${bg.YEAR_PATTERN}(?![^\\s]\\d)))?(?=\\W|$)`,"i"),yD=3,gg=1,yg=2,_g=4,Yc=class extends hD.AbstractParserWithWordBoundaryChecking{innerPattern(){return gD}innerExtract(e,t){let n=Tg.MONTH_DICTIONARY[t[yD].toLowerCase()],i=Ba.parseOrdinalNumberPattern(t[gg]);if(i>31)return t.index=t.index+t[gg].length,null;let s=e.createParsingComponents({day:i,month:n});if(t[_g]){let u=bg.parseYear(t[_g]);s.assign("year",u)}else{let u=pD.findYearClosestToRef(e.refDate,i,n);s.imply("year",u)}if(!t[yg])return s;let a=Ba.parseOrdinalNumberPattern(t[yg]),o=e.createParsingResult(t.index,t[0]);return o.start=s,o.end=s.clone(),o.end.assign("day",a),o}};$c.default=Yc});var Og=b(Gc=>{"use strict";Object.defineProperty(Gc,"__esModule",{value:!0});var wg=St(),_D=nt(),TD=Ee(),kg=St(),bD=L(),vD=new RegExp(`(${TD.matchAnyPattern(wg.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${kg.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,"i"),ED=1,Eg=2,jc=class extends bD.AbstractParserWithWordBoundaryChecking{innerPattern(){return vD}innerExtract(e,t){let n=e.createParsingComponents();n.imply("day",1);let i=t[ED],s=wg.MONTH_DICTIONARY[i.toLowerCase()];if(n.assign("month",s),t[Eg]){let a=kg.parseYear(t[Eg]);n.assign("year",a)}else{let a=_D.findYearClosestToRef(e.refDate,1,s);n.imply("year",a)}return n}};Gc.default=jc});var Dg=b(Hc=>{"use strict";Object.defineProperty(Hc,"__esModule",{value:!0});var wD=L(),kD=new RegExp("([0-9]|0[1-9]|1[012])/([0-9]{4})","i"),OD=1,DD=2,Bc=class extends wD.AbstractParserWithWordBoundaryChecking{innerPattern(){return kD}innerExtract(e,t){let n=parseInt(t[DD]),i=parseInt(t[OD]);return e.createParsingComponents().imply("day",1).assign("month",i).assign("year",n)}};Hc.default=Bc});var Sg=b(Vc=>{"use strict";Object.defineProperty(Vc,"__esModule",{value:!0});var SD=pn(),zc=class extends SD.AbstractTimeExpressionParser{primaryPrefix(){return"(?:(?:om)\\s*)?"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|om|\\?)\\s*"}primarySuffix(){return"(?:\\s*(?:uur))?(?!/)(?=\\W|$)"}extractPrimaryTimeComponents(e,t){return t[0].match(/^\s*\d{4}\s*$/)?null:super.extractPrimaryTimeComponents(e,t)}};Vc.default=zc});var xg=b(Qc=>{"use strict";Object.defineProperty(Qc,"__esModule",{value:!0});var Mg=St(),RD=Ee(),MD=L(),xD=new RegExp(`([0-9]{4})[\\.\\/\\s](?:(${RD.matchAnyPattern(Mg.MONTH_DICTIONARY)})|([0-9]{1,2}))[\\.\\/\\s]([0-9]{1,2})(?=\\W|$)`,"i"),AD=1,CD=2,Rg=3,PD=4,Kc=class extends MD.AbstractParserWithWordBoundaryChecking{innerPattern(){return xD}innerExtract(e,t){let n=t[Rg]?parseInt(t[Rg]):Mg.MONTH_DICTIONARY[t[CD].toLowerCase()];if(n<1||n>12)return null;let i=parseInt(t[AD]);return{day:parseInt(t[PD]),month:n,year:i}}};Qc.default=Kc});var Ag=b(fs=>{"use strict";var ND=fs&&fs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(fs,"__esModule",{value:!0});var ID=L(),Ha=Be(),Xc=Ut(),FD=ND(ge()),LD=1,UD=2,Zc=class extends ID.AbstractParserWithWordBoundaryChecking{innerPattern(e){return/(gisteren|morgen|van)(ochtend|middag|namiddag|avond|nacht)(?=\W|$)/i}innerExtract(e,t){let n=t[LD].toLowerCase(),i=t[UD].toLowerCase(),s=e.createParsingComponents(),a=FD.default(e.refDate);switch(n){case"gisteren":Xc.assignSimilarDate(s,a.add(-1,"day"));break;case"van":Xc.assignSimilarDate(s,a);break;case"morgen":Xc.assignTheNextDay(s,a);break}switch(i){case"ochtend":s.imply("meridiem",Ha.Meridiem.AM),s.imply("hour",6);break;case"middag":s.imply("meridiem",Ha.Meridiem.AM),s.imply("hour",12);break;case"namiddag":s.imply("meridiem",Ha.Meridiem.PM),s.imply("hour",15);break;case"avond":s.imply("meridiem",Ha.Meridiem.PM),s.imply("hour",20);break}return s}};fs.default=Zc});var Pg=b(ed=>{"use strict";Object.defineProperty(ed,"__esModule",{value:!0});var Cg=St(),WD=Ne(),qD=L(),YD=Wt(),$D=new RegExp(`(deze|vorige|afgelopen|komende|over|\\+|-)\\s*(${Cg.TIME_UNITS_PATTERN})(?=\\W|$)`,"i"),Jc=class extends qD.AbstractParserWithWordBoundaryChecking{innerPattern(){return $D}innerExtract(e,t){let n=t[1].toLowerCase(),i=Cg.parseTimeUnits(t[2]);switch(n){case"vorige":case"afgelopen":case"-":i=YD.reverseTimeUnits(i);break}return WD.ParsingComponents.createRelativeFromReference(e.reference,i)}};ed.default=Jc});var Fg=b(ps=>{"use strict";var jD=ps&&ps.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ps,"__esModule",{value:!0});var Ig=St(),Ng=Ne(),GD=jD(ge()),BD=L(),HD=Ee(),zD=new RegExp(`(dit|deze|komende|volgend|volgende|afgelopen|vorige)\\s*(${HD.matchAnyPattern(Ig.TIME_UNIT_DICTIONARY)})(?=\\s*)(?=\\W|$)`,"i"),VD=1,KD=2,td=class extends BD.AbstractParserWithWordBoundaryChecking{innerPattern(){return zD}innerExtract(e,t){let n=t[VD].toLowerCase(),i=t[KD].toLowerCase(),s=Ig.TIME_UNIT_DICTIONARY[i];if(n=="volgend"||n=="volgende"||n=="komende"){let u={};return u[s]=1,Ng.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="afgelopen"||n=="vorige"){let u={};return u[s]=-1,Ng.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=GD.default(e.reference.instant);return i.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):i.match(/maand/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):i.match(/jaar/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};ps.default=td});var Lg=b(id=>{"use strict";Object.defineProperty(id,"__esModule",{value:!0});var nd=St(),QD=Ne(),XD=L(),ZD=Wt(),JD=new RegExp("("+nd.TIME_UNITS_PATTERN+")(?:geleden|voor|eerder)(?=(?:\\W|$))","i"),eS=new RegExp("("+nd.TIME_UNITS_PATTERN+")geleden(?=(?:\\W|$))","i"),rd=class extends XD.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?eS:JD}innerExtract(e,t){let n=nd.parseTimeUnits(t[1]),i=ZD.reverseTimeUnits(n);return QD.ParsingComponents.createRelativeFromReference(e.reference,i)}};id.default=rd});var Ug=b(od=>{"use strict";Object.defineProperty(od,"__esModule",{value:!0});var ad=St(),tS=Ne(),rS=L(),nS=new RegExp("("+ad.TIME_UNITS_PATTERN+")(later|na|vanaf nu|voortaan|vooruit|uit)(?=(?:\\W|$))","i"),iS=new RegExp("("+ad.TIME_UNITS_PATTERN+")(later|vanaf nu)(?=(?:\\W|$))","i"),sS=1,sd=class extends rS.AbstractParserWithWordBoundaryChecking{constructor(e){super(),this.strictMode=e}innerPattern(){return this.strictMode?iS:nS}innerExtract(e,t){let n=ad.parseTimeUnits(t[sS]);return tS.ParsingComponents.createRelativeFromReference(e.reference,n)}};od.default=sd});var $g=b(Ze=>{"use strict";var st=Ze&&Ze.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ze,"__esModule",{value:!0});Ze.createConfiguration=Ze.createCasualConfiguration=Ze.parseDate=Ze.parse=Ze.strict=Ze.casual=void 0;var aS=vr(),Wg=Qt(),oS=st(sg()),uS=st(ag()),lS=st(og()),cS=st(ug()),dS=st(gn()),fS=st(pg()),pS=st(hg()),mS=st(vg()),qg=st(Og()),hS=st(Dg()),gS=st(Sg()),yS=st(xg()),_S=st(Ag()),TS=st(Pg()),bS=st(Fg()),vS=st(Lg()),ES=st(Ug());Ze.casual=new Wg.Chrono(Yg());Ze.strict=new Wg.Chrono(ud(!0));function wS(r,e,t){return Ze.casual.parse(r,e,t)}Ze.parse=wS;function kS(r,e,t){return Ze.casual.parseDate(r,e,t)}Ze.parseDate=kS;function Yg(r=!0){let e=ud(!1,r);return e.parsers.unshift(new lS.default),e.parsers.unshift(new cS.default),e.parsers.unshift(new _S.default),e.parsers.unshift(new qg.default),e.parsers.unshift(new bS.default),e.parsers.unshift(new TS.default),e}Ze.createCasualConfiguration=Yg;function ud(r=!0,e=!0){return aS.includeCommonConfiguration({parsers:[new dS.default(e),new fS.default,new mS.default,new qg.default,new pS.default,new yS.default,new hS.default,new gS.default(r),new vS.default(r),new ES.default(r)],refiners:[new uS.default,new oS.default]},r)}Ze.createConfiguration=ud});var Hg=b(ms=>{"use strict";var OS=ms&&ms.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ms,"__esModule",{value:!0});var DS=OS(ge()),SS=L(),RS=1,jg=2,MS=3,Gg=4,Bg=5,xS=6,ld=class extends SS.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u800C\u5BB6|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=DS.default(e.refDate),a=s;if(t[RS])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[jg]){let o=t[jg],u=t[MS];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day")),u=="\u65E9"||u=="\u671D"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[Gg]){let u=t[Gg][0];u=="\u65E9"||u=="\u671D"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"||u=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[Bg]){let o=t[Bg];o=="\u660E"||o=="\u807D"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u5F8C"?a=a.add(2,"day"):o=="\u5927\u5F8C"&&(a=a.add(3,"day"));let u=t[xS];if(u){let l=u[0];l=="\u65E9"||l=="\u671D"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"||l=="\u664F"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};ms.default=ld});var ii=b(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.zhStringToYear=vt.zhStringToNumber=vt.WEEKDAY_OFFSET=vt.NUMBER=void 0;vt.NUMBER={\u96F6:0,\u4E00:1,\u4E8C:2,\u5169:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10,\u5EFF:20,\u5345:30};vt.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function AS(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?vt.NUMBER[n]:e*vt.NUMBER[n]:e+=vt.NUMBER[n]}return e}vt.zhStringToNumber=AS;function CS(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+vt.NUMBER[n]}return parseInt(e)}vt.zhStringToYear=CS});var Vg=b(hs=>{"use strict";var PS=hs&&hs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(hs,"__esModule",{value:!0});var NS=PS(ge()),IS=L(),Tn=ii(),cd=1,zg=2,dd=3,fd=class extends IS.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(Tn.NUMBER).join("")+"]{4}|["+Object.keys(Tn.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(Tn.NUMBER).join("")+"]{1,2})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(Tn.NUMBER).join("")+"]{1,2})?(?:\\s*)(?:\u65E5|\u865F)?")}innerExtract(e,t){let n=NS.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[zg]);if(isNaN(s)&&(s=Tn.zhStringToNumber(t[zg])),i.start.assign("month",s),t[dd]){let a=parseInt(t[dd]);isNaN(a)&&(a=Tn.zhStringToNumber(t[dd])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[cd]){let a=parseInt(t[cd]);isNaN(a)&&(a=Tn.zhStringToYear(t[cd])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};hs.default=fd});var Qg=b(gs=>{"use strict";var FS=gs&&gs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(gs,"__esModule",{value:!0});var LS=FS(ge()),US=L(),Kg=ii(),WS=new RegExp("(\\d+|["+Object.keys(Kg.NUMBER).join("")+"]+|\u534A|\u5E7E)(?:\\s*)(?:\u500B)?(\u79D2(?:\u9418)?|\u5206\u9418|\u5C0F\u6642|\u9418|\u65E5|\u5929|\u661F\u671F|\u79AE\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u904E)?\u5F8C|(?:\u4E4B)?\u5167)","i"),pd=1,qS=2,md=class extends US.AbstractParserWithWordBoundaryChecking{innerPattern(){return WS}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[pd]);if(isNaN(i)&&(i=Kg.zhStringToNumber(t[pd])),isNaN(i)){let u=t[pd];if(u==="\u5E7E")i=3;else if(u==="\u534A")i=.5;else return null}let s=LS.default(e.refDate),o=t[qS][0];return o.match(/[日天星禮月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u79AE"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u9418")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};gs.default=md});var Zg=b(ys=>{"use strict";var YS=ys&&ys.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ys,"__esModule",{value:!0});var $S=YS(ge()),jS=L(),Xg=ii(),GS=new RegExp("(?<prefix>\u4E0A|\u4ECA|\u4E0B|\u9019|\u5462)(?:\u500B)?(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(Xg.WEEKDAY_OFFSET).join("|")+")"),hd=class extends jS.AbstractParserWithWordBoundaryChecking{innerPattern(){return GS}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=Xg.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":(o=="\u4ECA"||o=="\u9019"||o=="\u5462")&&(a="this");let u=$S.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};ys.default=hd});var Jg=b(_s=>{"use strict";var BS=_s&&_s.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(_s,"__esModule",{value:!0});var HS=BS(ge()),zS=L(),$t=ii(),VS=new RegExp("(?:\u7531|\u5F9E|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys($t.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys($t.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys($t.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),KS=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u5F8C|\u5927\u5F8C|\u807D|\u6628|\u5C0B|\u7434)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348|\u665D)|\u671D(?:\u65E9)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348|\u665D)|\u664F(?:\u665D)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys($t.NUMBER).join("")+"]+)(?:\\s*)(?:\u9EDE|\u6642|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys($t.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys($t.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),za=1,Va=2,Ka=3,Qa=4,Xa=5,Za=6,Yt=7,si=8,Ja=9,gd=class extends zS.AbstractParserWithWordBoundaryChecking{innerPattern(){return VS}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=HS.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[za]){var a=t[za];a=="\u660E"||a=="\u807D"?n.hour()>1&&s.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?s.add(-1,"day"):a=="\u524D"?s.add(-2,"day"):a=="\u5927\u524D"?s.add(-3,"day"):a=="\u5F8C"?s.add(2,"day"):a=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[Qa]){var o=t[Qa];o=="\u660E"||o=="\u807D"?s.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?s.add(-1,"day"):o=="\u524D"?s.add(-2,"day"):o=="\u5927\u524D"?s.add(-3,"day"):o=="\u5F8C"?s.add(2,"day"):o=="\u5927\u5F8C"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let u=0,l=0,c=-1;if(t[si]){var p=parseInt(t[si]);if(isNaN(p)&&(p=$t.zhStringToNumber(t[si])),p>=60)return null;i.start.assign("second",p)}if(u=parseInt(t[Za]),isNaN(u)&&(u=$t.zhStringToNumber(t[Za])),t[Yt]?t[Yt]=="\u534A"?l=30:t[Yt]=="\u6B63"||t[Yt]=="\u6574"?l=0:(l=parseInt(t[Yt]),isNaN(l)&&(l=$t.zhStringToNumber(t[Yt]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[Ja]){if(u>12)return null;var m=t[Ja][0].toLowerCase();m=="a"&&(c=0,u==12&&(u=0)),m=="p"&&(c=1,u!=12&&(u+=12))}else if(t[Va]){var T=t[Va],y=T[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Ka]){var E=t[Ka],M=E[0];M=="\u4E0A"||M=="\u671D"||M=="\u65E9"||M=="\u51CC"?(c=0,u==12&&(u=0)):(M=="\u4E0B"||M=="\u664F"||M=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Xa]){var U=t[Xa],q=U[0];q=="\u4E0A"||q=="\u671D"||q=="\u65E9"||q=="\u51CC"?(c=0,u==12&&(u=0)):(q=="\u4E0B"||q=="\u664F"||q=="\u665A")&&(c=1,u!=12&&(u+=12))}if(i.start.assign("hour",u),i.start.assign("minute",l),c>=0?i.start.assign("meridiem",c):u<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=KS.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let W=s.clone();if(i.end=e.createParsingComponents(),t[za]){var a=t[za];a=="\u660E"||a=="\u807D"?n.hour()>1&&W.add(1,"day"):a=="\u6628"||a=="\u5C0B"||a=="\u7434"?W.add(-1,"day"):a=="\u524D"?W.add(-2,"day"):a=="\u5927\u524D"?W.add(-3,"day"):a=="\u5F8C"?W.add(2,"day"):a=="\u5927\u5F8C"&&W.add(3,"day"),i.end.assign("day",W.date()),i.end.assign("month",W.month()+1),i.end.assign("year",W.year())}else if(t[Qa]){var o=t[Qa];o=="\u660E"||o=="\u807D"?W.add(1,"day"):o=="\u6628"||o=="\u5C0B"||o=="\u7434"?W.add(-1,"day"):o=="\u524D"?W.add(-2,"day"):o=="\u5927\u524D"?W.add(-3,"day"):o=="\u5F8C"?W.add(2,"day"):o=="\u5927\u5F8C"&&W.add(3,"day"),i.end.assign("day",W.date()),i.end.assign("month",W.month()+1),i.end.assign("year",W.year())}else i.end.imply("day",W.date()),i.end.imply("month",W.month()+1),i.end.imply("year",W.year());if(u=0,l=0,c=-1,t[si]){var p=parseInt(t[si]);if(isNaN(p)&&(p=$t.zhStringToNumber(t[si])),p>=60)return null;i.end.assign("second",p)}if(u=parseInt(t[Za]),isNaN(u)&&(u=$t.zhStringToNumber(t[Za])),t[Yt]?t[Yt]=="\u534A"?l=30:t[Yt]=="\u6B63"||t[Yt]=="\u6574"?l=0:(l=parseInt(t[Yt]),isNaN(l)&&(l=$t.zhStringToNumber(t[Yt]))):u>100&&(l=u%100,u=Math.floor(u/100)),l>=60||u>24)return null;if(u>=12&&(c=1),t[Ja]){if(u>12)return null;var m=t[Ja][0].toLowerCase();m=="a"&&(c=0,u==12&&(u=0)),m=="p"&&(c=1,u!=12&&(u+=12)),i.start.isCertain("meridiem")||(c==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[Va]){var T=t[Va],y=T[0];y=="\u671D"||y=="\u65E9"?(c=0,u==12&&(u=0)):y=="\u665A"&&(c=1,u!=12&&(u+=12))}else if(t[Ka]){var E=t[Ka],M=E[0];M=="\u4E0A"||M=="\u671D"||M=="\u65E9"||M=="\u51CC"?(c=0,u==12&&(u=0)):(M=="\u4E0B"||M=="\u664F"||M=="\u665A")&&(c=1,u!=12&&(u+=12))}else if(t[Xa]){var U=t[Xa],q=U[0];q=="\u4E0A"||q=="\u671D"||q=="\u65E9"||q=="\u51CC"?(c=0,u==12&&(u=0)):(q=="\u4E0B"||q=="\u664F"||q=="\u665A")&&(c=1,u!=12&&(u+=12))}return i.text=i.text+t[0],i.end.assign("hour",u),i.end.assign("minute",l),c>=0?i.end.assign("meridiem",c):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>u?i.end.imply("meridiem",0):u>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};_s.default=gd});var ty=b(Ts=>{"use strict";var QS=Ts&&Ts.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ts,"__esModule",{value:!0});var XS=QS(ge()),ZS=L(),ey=ii(),JS=new RegExp("(?:\u661F\u671F|\u79AE\u62DC|\u9031)(?<weekday>"+Object.keys(ey.WEEKDAY_OFFSET).join("|")+")"),yd=class extends ZS.AbstractParserWithWordBoundaryChecking{innerPattern(){return JS}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=ey.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=XS.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};Ts.default=yd});var ry=b(bs=>{"use strict";var e0=bs&&bs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(bs,"__esModule",{value:!0});var t0=e0(lr()),_d=class extends t0.default{patternBetween(){return/^\s*(至|到|\-|\~|～|－|ー)\s*$/i}};bs.default=_d});var ny=b(vs=>{"use strict";var r0=vs&&vs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(vs,"__esModule",{value:!0});var n0=r0(br()),Td=class extends n0.default{patternBetween(){return/^\s*$/i}};vs.default=Td});var iy=b(Ye=>{"use strict";var Dr=Ye&&Ye.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ye,"__esModule",{value:!0});Ye.createConfiguration=Ye.createCasualConfiguration=Ye.parseDate=Ye.parse=Ye.strict=Ye.casual=Ye.hant=void 0;var bd=Qt(),i0=Dr(Ma()),s0=vr(),a0=Dr(Hg()),o0=Dr(Vg()),u0=Dr(Qg()),l0=Dr(Zg()),c0=Dr(Jg()),d0=Dr(ty()),f0=Dr(ry()),p0=Dr(ny());Ye.hant=new bd.Chrono(vd());Ye.casual=new bd.Chrono(vd());Ye.strict=new bd.Chrono(Ed());function m0(r,e,t){return Ye.casual.parse(r,e,t)}Ye.parse=m0;function h0(r,e,t){return Ye.casual.parseDate(r,e,t)}Ye.parseDate=h0;function vd(){let r=Ed();return r.parsers.unshift(new a0.default),r}Ye.createCasualConfiguration=vd;function Ed(){let r=s0.includeCommonConfiguration({parsers:[new o0.default,new l0.default,new d0.default,new c0.default,new u0.default],refiners:[new f0.default,new p0.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof i0.default)),r}Ye.createConfiguration=Ed});var uy=b(Es=>{"use strict";var g0=Es&&Es.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Es,"__esModule",{value:!0});var y0=g0(ge()),_0=L(),T0=1,sy=2,b0=3,ay=4,oy=5,v0=6,wd=class extends _0.AbstractParserWithWordBoundaryChecking{innerPattern(e){return new RegExp("(\u73B0\u5728|\u7ACB(?:\u523B|\u5373)|\u5373\u523B)|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s|,|\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?","i")}innerExtract(e,t){let n=t.index,i=e.createParsingResult(n,t[0]),s=y0.default(e.refDate),a=s;if(t[T0])i.start.imply("hour",s.hour()),i.start.imply("minute",s.minute()),i.start.imply("second",s.second()),i.start.imply("millisecond",s.millisecond());else if(t[sy]){let o=t[sy],u=t[b0];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day")),u=="\u65E9"?i.start.imply("hour",6):u=="\u665A"&&(i.start.imply("hour",22),i.start.imply("meridiem",1))}else if(t[ay]){let u=t[ay][0];u=="\u65E9"||u=="\u4E0A"?i.start.imply("hour",6):u=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):u=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):u=="\u591C"||u=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):u=="\u51CC"&&i.start.imply("hour",0)}else if(t[oy]){let o=t[oy];o=="\u660E"?s.hour()>1&&(a=a.add(1,"day")):o=="\u6628"?a=a.add(-1,"day"):o=="\u524D"?a=a.add(-2,"day"):o=="\u5927\u524D"?a=a.add(-3,"day"):o=="\u540E"?a=a.add(2,"day"):o=="\u5927\u540E"&&(a=a.add(3,"day"));let u=t[v0];if(u){let l=u[0];l=="\u65E9"||l=="\u4E0A"?i.start.imply("hour",6):l=="\u4E0B"?(i.start.imply("hour",15),i.start.imply("meridiem",1)):l=="\u4E2D"?(i.start.imply("hour",12),i.start.imply("meridiem",1)):l=="\u591C"||l=="\u665A"?(i.start.imply("hour",22),i.start.imply("meridiem",1)):l=="\u51CC"&&i.start.imply("hour",0)}}return i.start.assign("day",a.date()),i.start.assign("month",a.month()+1),i.start.assign("year",a.year()),i}};Es.default=wd});var ai=b(Et=>{"use strict";Object.defineProperty(Et,"__esModule",{value:!0});Et.zhStringToYear=Et.zhStringToNumber=Et.WEEKDAY_OFFSET=Et.NUMBER=void 0;Et.NUMBER={\u96F6:0,"\u3007":0,\u4E00:1,\u4E8C:2,\u4E24:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6,\u4E03:7,\u516B:8,\u4E5D:9,\u5341:10};Et.WEEKDAY_OFFSET={\u5929:0,\u65E5:0,\u4E00:1,\u4E8C:2,\u4E09:3,\u56DB:4,\u4E94:5,\u516D:6};function E0(r){let e=0;for(let t=0;t<r.length;t++){let n=r[t];n==="\u5341"?e=e===0?Et.NUMBER[n]:e*Et.NUMBER[n]:e+=Et.NUMBER[n]}return e}Et.zhStringToNumber=E0;function w0(r){let e="";for(let t=0;t<r.length;t++){let n=r[t];e=e+Et.NUMBER[n]}return parseInt(e)}Et.zhStringToYear=w0});var cy=b(ws=>{"use strict";var k0=ws&&ws.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ws,"__esModule",{value:!0});var O0=k0(ge()),D0=L(),bn=ai(),kd=1,ly=2,Od=3,Dd=class extends D0.AbstractParserWithWordBoundaryChecking{innerPattern(){return new RegExp("(\\d{2,4}|["+Object.keys(bn.NUMBER).join("")+"]{4}|["+Object.keys(bn.NUMBER).join("")+"]{2})?(?:\\s*)(?:\u5E74)?(?:[\\s|,|\uFF0C]*)(\\d{1,2}|["+Object.keys(bn.NUMBER).join("")+"]{1,3})(?:\\s*)(?:\u6708)(?:\\s*)(\\d{1,2}|["+Object.keys(bn.NUMBER).join("")+"]{1,3})?(?:\\s*)(?:\u65E5|\u53F7)?")}innerExtract(e,t){let n=O0.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=parseInt(t[ly]);if(isNaN(s)&&(s=bn.zhStringToNumber(t[ly])),i.start.assign("month",s),t[Od]){let a=parseInt(t[Od]);isNaN(a)&&(a=bn.zhStringToNumber(t[Od])),i.start.assign("day",a)}else i.start.imply("day",n.date());if(t[kd]){let a=parseInt(t[kd]);isNaN(a)&&(a=bn.zhStringToYear(t[kd])),i.start.assign("year",a)}else i.start.imply("year",n.year());return i}};ws.default=Dd});var fy=b(ks=>{"use strict";var S0=ks&&ks.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ks,"__esModule",{value:!0});var R0=S0(ge()),M0=L(),dy=ai(),x0=new RegExp("(\\d+|["+Object.keys(dy.NUMBER).join("")+"]+|\u534A|\u51E0)(?:\\s*)(?:\u4E2A)?(\u79D2(?:\u949F)?|\u5206\u949F|\u5C0F\u65F6|\u949F|\u65E5|\u5929|\u661F\u671F|\u793C\u62DC|\u6708|\u5E74)(?:(?:\u4E4B|\u8FC7)?\u540E|(?:\u4E4B)?\u5185)","i"),Sd=1,A0=2,Rd=class extends M0.AbstractParserWithWordBoundaryChecking{innerPattern(){return x0}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=parseInt(t[Sd]);if(isNaN(i)&&(i=dy.zhStringToNumber(t[Sd])),isNaN(i)){let u=t[Sd];if(u==="\u51E0")i=3;else if(u==="\u534A")i=.5;else return null}let s=R0.default(e.refDate),o=t[A0][0];return o.match(/[日天星礼月年]/)?(o=="\u65E5"||o=="\u5929"?s=s.add(i,"d"):o=="\u661F"||o=="\u793C"?s=s.add(i*7,"d"):o=="\u6708"?s=s.add(i,"month"):o=="\u5E74"&&(s=s.add(i,"year")),n.start.assign("year",s.year()),n.start.assign("month",s.month()+1),n.start.assign("day",s.date()),n):(o=="\u79D2"?s=s.add(i,"second"):o=="\u5206"?s=s.add(i,"minute"):(o=="\u5C0F"||o=="\u949F")&&(s=s.add(i,"hour")),n.start.imply("year",s.year()),n.start.imply("month",s.month()+1),n.start.imply("day",s.date()),n.start.assign("hour",s.hour()),n.start.assign("minute",s.minute()),n.start.assign("second",s.second()),n)}};ks.default=Rd});var my=b(Os=>{"use strict";var C0=Os&&Os.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Os,"__esModule",{value:!0});var P0=C0(ge()),N0=L(),py=ai(),I0=new RegExp("(?<prefix>\u4E0A|\u4E0B|\u8FD9)(?:\u4E2A)?(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(py.WEEKDAY_OFFSET).join("|")+")"),Md=class extends N0.AbstractParserWithWordBoundaryChecking{innerPattern(){return I0}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=py.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=null,o=t.groups.prefix;o=="\u4E0A"?a="last":o=="\u4E0B"?a="next":o=="\u8FD9"&&(a="this");let u=P0.default(e.refDate),l=!1,c=u.day();return a=="last"||a=="past"?(u=u.day(s-7),l=!0):a=="next"?(u=u.day(s+7),l=!0):a=="this"?u=u.day(s):Math.abs(s-7-c)<Math.abs(s-c)?u=u.day(s-7):Math.abs(s+7-c)<Math.abs(s-c)?u=u.day(s+7):u=u.day(s),n.start.assign("weekday",s),l?(n.start.assign("day",u.date()),n.start.assign("month",u.month()+1),n.start.assign("year",u.year())):(n.start.imply("day",u.date()),n.start.imply("month",u.month()+1),n.start.imply("year",u.year())),n}};Os.default=Md});var hy=b(Ds=>{"use strict";var F0=Ds&&Ds.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ds,"__esModule",{value:!0});var L0=F0(ge()),U0=L(),Gt=ai(),W0=new RegExp("(?:\u4ECE|\u81EA)?(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(Gt.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(Gt.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(Gt.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),q0=new RegExp("(?:^\\s*(?:\u5230|\u81F3|\\-|\\\u2013|\\~|\\\u301C)\\s*)(?:(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(\u65E9|\u671D|\u665A)|(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668))|(\u4ECA|\u660E|\u524D|\u5927\u524D|\u540E|\u5927\u540E|\u6628)(?:\u65E5|\u5929)(?:[\\s,\uFF0C]*)(?:(\u4E0A(?:\u5348)|\u65E9(?:\u4E0A)|\u4E0B(?:\u5348)|\u665A(?:\u4E0A)|\u591C(?:\u665A)?|\u4E2D(?:\u5348)|\u51CC(?:\u6668)))?)?(?:[\\s,\uFF0C]*)(?:(\\d+|["+Object.keys(Gt.NUMBER).join("")+"]+)(?:\\s*)(?:\u70B9|\u65F6|:|\uFF1A)(?:\\s*)(\\d+|\u534A|\u6B63|\u6574|["+Object.keys(Gt.NUMBER).join("")+"]+)?(?:\\s*)(?:\u5206|:|\uFF1A)?(?:\\s*)(\\d+|["+Object.keys(Gt.NUMBER).join("")+"]+)?(?:\\s*)(?:\u79D2)?)(?:\\s*(A.M.|P.M.|AM?|PM?))?","i"),eo=1,to=2,ro=3,no=4,io=5,so=6,jt=7,oi=8,ao=9,xd=class extends U0.AbstractParserWithWordBoundaryChecking{innerPattern(){return W0}innerExtract(e,t){if(t.index>0&&e.text[t.index-1].match(/\w/))return null;let n=L0.default(e.refDate),i=e.createParsingResult(t.index,t[0]),s=n.clone();if(t[eo]){let c=t[eo];c=="\u660E"?n.hour()>1&&s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else if(t[no]){let c=t[no];c=="\u660E"?s.add(1,"day"):c=="\u6628"?s.add(-1,"day"):c=="\u524D"?s.add(-2,"day"):c=="\u5927\u524D"?s.add(-3,"day"):c=="\u540E"?s.add(2,"day"):c=="\u5927\u540E"&&s.add(3,"day"),i.start.assign("day",s.date()),i.start.assign("month",s.month()+1),i.start.assign("year",s.year())}else i.start.imply("day",s.date()),i.start.imply("month",s.month()+1),i.start.imply("year",s.year());let a=0,o=0,u=-1;if(t[oi]){let c=parseInt(t[oi]);if(isNaN(c)&&(c=Gt.zhStringToNumber(t[oi])),c>=60)return null;i.start.assign("second",c)}if(a=parseInt(t[so]),isNaN(a)&&(a=Gt.zhStringToNumber(t[so])),t[jt]?t[jt]=="\u534A"?o=30:t[jt]=="\u6B63"||t[jt]=="\u6574"?o=0:(o=parseInt(t[jt]),isNaN(o)&&(o=Gt.zhStringToNumber(t[jt]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[ao]){if(a>12)return null;let c=t[ao][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12))}else if(t[to]){let p=t[to][0];p=="\u65E9"?(u=0,a==12&&(a=0)):p=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[ro]){let p=t[ro][0];p=="\u4E0A"||p=="\u65E9"||p=="\u51CC"?(u=0,a==12&&(a=0)):(p=="\u4E0B"||p=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[io]){let p=t[io][0];p=="\u4E0A"||p=="\u65E9"||p=="\u51CC"?(u=0,a==12&&(a=0)):(p=="\u4E0B"||p=="\u665A")&&(u=1,a!=12&&(a+=12))}if(i.start.assign("hour",a),i.start.assign("minute",o),u>=0?i.start.assign("meridiem",u):a<12?i.start.imply("meridiem",0):i.start.imply("meridiem",1),t=q0.exec(e.text.substring(i.index+i.text.length)),!t)return i.text.match(/^\d+$/)?null:i;let l=s.clone();if(i.end=e.createParsingComponents(),t[eo]){let c=t[eo];c=="\u660E"?n.hour()>1&&l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else if(t[no]){let c=t[no];c=="\u660E"?l.add(1,"day"):c=="\u6628"?l.add(-1,"day"):c=="\u524D"?l.add(-2,"day"):c=="\u5927\u524D"?l.add(-3,"day"):c=="\u540E"?l.add(2,"day"):c=="\u5927\u540E"&&l.add(3,"day"),i.end.assign("day",l.date()),i.end.assign("month",l.month()+1),i.end.assign("year",l.year())}else i.end.imply("day",l.date()),i.end.imply("month",l.month()+1),i.end.imply("year",l.year());if(a=0,o=0,u=-1,t[oi]){let c=parseInt(t[oi]);if(isNaN(c)&&(c=Gt.zhStringToNumber(t[oi])),c>=60)return null;i.end.assign("second",c)}if(a=parseInt(t[so]),isNaN(a)&&(a=Gt.zhStringToNumber(t[so])),t[jt]?t[jt]=="\u534A"?o=30:t[jt]=="\u6B63"||t[jt]=="\u6574"?o=0:(o=parseInt(t[jt]),isNaN(o)&&(o=Gt.zhStringToNumber(t[jt]))):a>100&&(o=a%100,a=Math.floor(a/100)),o>=60||a>24)return null;if(a>=12&&(u=1),t[ao]){if(a>12)return null;let c=t[ao][0].toLowerCase();c=="a"&&(u=0,a==12&&(a=0)),c=="p"&&(u=1,a!=12&&(a+=12)),i.start.isCertain("meridiem")||(u==0?(i.start.imply("meridiem",0),i.start.get("hour")==12&&i.start.assign("hour",0)):(i.start.imply("meridiem",1),i.start.get("hour")!=12&&i.start.assign("hour",i.start.get("hour")+12)))}else if(t[to]){let p=t[to][0];p=="\u65E9"?(u=0,a==12&&(a=0)):p=="\u665A"&&(u=1,a!=12&&(a+=12))}else if(t[ro]){let p=t[ro][0];p=="\u4E0A"||p=="\u65E9"||p=="\u51CC"?(u=0,a==12&&(a=0)):(p=="\u4E0B"||p=="\u665A")&&(u=1,a!=12&&(a+=12))}else if(t[io]){let p=t[io][0];p=="\u4E0A"||p=="\u65E9"||p=="\u51CC"?(u=0,a==12&&(a=0)):(p=="\u4E0B"||p=="\u665A")&&(u=1,a!=12&&(a+=12))}return i.text=i.text+t[0],i.end.assign("hour",a),i.end.assign("minute",o),u>=0?i.end.assign("meridiem",u):i.start.isCertain("meridiem")&&i.start.get("meridiem")==1&&i.start.get("hour")>a?i.end.imply("meridiem",0):a>12&&i.end.imply("meridiem",1),i.end.date().getTime()<i.start.date().getTime()&&i.end.imply("day",i.end.get("day")+1),i}};Ds.default=xd});var yy=b(Ss=>{"use strict";var Y0=Ss&&Ss.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ss,"__esModule",{value:!0});var $0=Y0(ge()),j0=L(),gy=ai(),G0=new RegExp("(?:\u661F\u671F|\u793C\u62DC|\u5468)(?<weekday>"+Object.keys(gy.WEEKDAY_OFFSET).join("|")+")"),Ad=class extends j0.AbstractParserWithWordBoundaryChecking{innerPattern(){return G0}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=t.groups.weekday,s=gy.WEEKDAY_OFFSET[i];if(s===void 0)return null;let a=$0.default(e.refDate),o=!1,u=a.day();return Math.abs(s-7-u)<Math.abs(s-u)?a=a.day(s-7):Math.abs(s+7-u)<Math.abs(s-u)?a=a.day(s+7):a=a.day(s),n.start.assign("weekday",s),o?(n.start.assign("day",a.date()),n.start.assign("month",a.month()+1),n.start.assign("year",a.year())):(n.start.imply("day",a.date()),n.start.imply("month",a.month()+1),n.start.imply("year",a.year())),n}};Ss.default=Ad});var _y=b(Rs=>{"use strict";var B0=Rs&&Rs.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Rs,"__esModule",{value:!0});var H0=B0(lr()),Cd=class extends H0.default{patternBetween(){return/^\s*(至|到|-|~|～|－|ー)\s*$/i}};Rs.default=Cd});var Ty=b(Ms=>{"use strict";var z0=Ms&&Ms.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ms,"__esModule",{value:!0});var V0=z0(br()),Pd=class extends V0.default{patternBetween(){return/^\s*$/i}};Ms.default=Pd});var by=b($e=>{"use strict";var Sr=$e&&$e.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty($e,"__esModule",{value:!0});$e.createConfiguration=$e.createCasualConfiguration=$e.parseDate=$e.parse=$e.strict=$e.casual=$e.hans=void 0;var Nd=Qt(),K0=Sr(Ma()),Q0=vr(),X0=Sr(uy()),Z0=Sr(cy()),J0=Sr(fy()),eR=Sr(my()),tR=Sr(hy()),rR=Sr(yy()),nR=Sr(_y()),iR=Sr(Ty());$e.hans=new Nd.Chrono(Id());$e.casual=new Nd.Chrono(Id());$e.strict=new Nd.Chrono(Fd());function sR(r,e,t){return $e.casual.parse(r,e,t)}$e.parse=sR;function aR(r,e,t){return $e.casual.parseDate(r,e,t)}$e.parseDate=aR;function Id(){let r=Fd();return r.parsers.unshift(new X0.default),r}$e.createCasualConfiguration=Id;function Fd(){let r=Q0.includeCommonConfiguration({parsers:[new Z0.default,new eR.default,new rR.default,new tR.default,new J0.default],refiners:[new nR.default,new iR.default]});return r.refiners=r.refiners.filter(e=>!(e instanceof K0.default)),r}$e.createConfiguration=Fd});var Ey=b(Rt=>{"use strict";var vy=Rt&&Rt.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),oR=Rt&&Rt.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),uR=Rt&&Rt.__exportStar||function(r,e){for(var t in r)t!=="default"&&!Object.prototype.hasOwnProperty.call(e,t)&&vy(e,r,t)},lR=Rt&&Rt.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&vy(e,r,t);return oR(e,r),e};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.hans=void 0;uR(iy(),Rt);Rt.hans=lR(by())});var wt=b(J=>{"use strict";Object.defineProperty(J,"__esModule",{value:!0});J.parseTimeUnits=J.TIME_UNITS_PATTERN=J.parseYear=J.YEAR_PATTERN=J.parseOrdinalNumberPattern=J.ORDINAL_NUMBER_PATTERN=J.parseNumberPattern=J.NUMBER_PATTERN=J.TIME_UNIT_DICTIONARY=J.ORDINAL_WORD_DICTIONARY=J.INTEGER_WORD_DICTIONARY=J.MONTH_DICTIONARY=J.FULL_MONTH_NAME_DICTIONARY=J.WEEKDAY_DICTIONARY=J.REGEX_PARTS=void 0;var oo=Ee(),cR=nt();J.REGEX_PARTS={leftBoundary:"([^\\p{L}\\p{N}_]|^)",rightBoundary:"(?=[^\\p{L}\\p{N}_]|$)",flags:"iu"};J.WEEKDAY_DICTIONARY={\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u0435:0,\u0432\u043E\u0441\u043A\u0440\u0435\u0441\u0435\u043D\u044C\u044F:0,\u0432\u0441\u043A:0,"\u0432\u0441\u043A.":0,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A:1,\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u044C\u043D\u0438\u043A\u0430:1,\u043F\u043D:1,"\u043F\u043D.":1,\u0432\u0442\u043E\u0440\u043D\u0438\u043A:2,\u0432\u0442\u043E\u0440\u043D\u0438\u043A\u0430:2,\u0432\u0442:2,"\u0432\u0442.":2,\u0441\u0440\u0435\u0434\u0430:3,\u0441\u0440\u0435\u0434\u044B:3,\u0441\u0440\u0435\u0434\u0443:3,\u0441\u0440:3,"\u0441\u0440.":3,\u0447\u0435\u0442\u0432\u0435\u0440\u0433:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0433\u0430:4,\u0447\u0442:4,"\u0447\u0442.":4,\u043F\u044F\u0442\u043D\u0438\u0446\u0430:5,\u043F\u044F\u0442\u043D\u0438\u0446\u0443:5,\u043F\u044F\u0442\u043D\u0438\u0446\u044B:5,\u043F\u0442:5,"\u043F\u0442.":5,\u0441\u0443\u0431\u0431\u043E\u0442\u0430:6,\u0441\u0443\u0431\u0431\u043E\u0442\u0443:6,\u0441\u0443\u0431\u0431\u043E\u0442\u044B:6,\u0441\u0431:6,"\u0441\u0431.":6};J.FULL_MONTH_NAME_DICTIONARY={\u044F\u043D\u0432\u0430\u0440\u044C:1,\u044F\u043D\u0432\u0430\u0440\u044F:1,\u044F\u043D\u0432\u0430\u0440\u0435:1,\u0444\u0435\u0432\u0440\u044F\u043B\u044C:2,\u0444\u0435\u0432\u0440\u044F\u043B\u044F:2,\u0444\u0435\u0432\u0440\u044F\u043B\u0435:2,\u043C\u0430\u0440\u0442:3,\u043C\u0430\u0440\u0442\u0430:3,\u043C\u0430\u0440\u0442\u0435:3,\u0430\u043F\u0440\u0435\u043B\u044C:4,\u0430\u043F\u0440\u0435\u043B\u044F:4,\u0430\u043F\u0440\u0435\u043B\u0435:4,\u043C\u0430\u0439:5,\u043C\u0430\u044F:5,\u043C\u0430\u0435:5,\u0438\u044E\u043D\u044C:6,\u0438\u044E\u043D\u044F:6,\u0438\u044E\u043D\u0435:6,\u0438\u044E\u043B\u044C:7,\u0438\u044E\u043B\u044F:7,\u0438\u044E\u043B\u0435:7,\u0430\u0432\u0433\u0443\u0441\u0442:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0430:8,\u0430\u0432\u0433\u0443\u0441\u0442\u0435:8,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044C:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u044F:9,\u0441\u0435\u043D\u0442\u044F\u0431\u0440\u0435:9,\u043E\u043A\u0442\u044F\u0431\u0440\u044C:10,\u043E\u043A\u0442\u044F\u0431\u0440\u044F:10,\u043E\u043A\u0442\u044F\u0431\u0440\u0435:10,\u043D\u043E\u044F\u0431\u0440\u044C:11,\u043D\u043E\u044F\u0431\u0440\u044F:11,\u043D\u043E\u044F\u0431\u0440\u0435:11,\u0434\u0435\u043A\u0430\u0431\u0440\u044C:12,\u0434\u0435\u043A\u0430\u0431\u0440\u044F:12,\u0434\u0435\u043A\u0430\u0431\u0440\u0435:12};J.MONTH_DICTIONARY=Object.assign(Object.assign({},J.FULL_MONTH_NAME_DICTIONARY),{\u044F\u043D\u0432:1,"\u044F\u043D\u0432.":1,\u0444\u0435\u0432:2,"\u0444\u0435\u0432.":2,\u043C\u0430\u0440:3,"\u043C\u0430\u0440.":3,\u0430\u043F\u0440:4,"\u0430\u043F\u0440.":4,\u0430\u0432\u0433:8,"\u0430\u0432\u0433.":8,\u0441\u0435\u043D:9,"\u0441\u0435\u043D.":9,\u043E\u043A\u0442:10,"\u043E\u043A\u0442.":10,\u043D\u043E\u044F:11,"\u043D\u043E\u044F.":11,\u0434\u0435\u043A:12,"\u0434\u0435\u043A.":12});J.INTEGER_WORD_DICTIONARY={\u043E\u0434\u0438\u043D:1,\u043E\u0434\u043D\u0430:1,\u043E\u0434\u043D\u043E\u0439:1,\u043E\u0434\u043D\u0443:1,\u0434\u0432\u0435:2,\u0434\u0432\u0430:2,\u0434\u0432\u0443\u0445:2,\u0442\u0440\u0438:3,\u0442\u0440\u0435\u0445:3,\u0442\u0440\u0451\u0445:3,\u0447\u0435\u0442\u044B\u0440\u0435:4,\u0447\u0435\u0442\u044B\u0440\u0435\u0445:4,\u0447\u0435\u0442\u044B\u0440\u0451\u0445:4,\u043F\u044F\u0442\u044C:5,\u043F\u044F\u0442\u0438:5,\u0448\u0435\u0441\u0442\u044C:6,\u0448\u0435\u0441\u0442\u0438:6,\u0441\u0435\u043C\u044C:7,\u0441\u0435\u043C\u0438:7,\u0432\u043E\u0441\u0435\u043C\u044C:8,\u0432\u043E\u0441\u0435\u043C\u044C\u043C\u0438:8,\u0434\u0435\u0432\u044F\u0442\u044C:9,\u0434\u0435\u0432\u044F\u0442\u0438:9,\u0434\u0435\u0441\u044F\u0442\u044C:10,\u0434\u0435\u0441\u044F\u0442\u0438:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u044C:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u0438:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u044C:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u0438:12};J.ORDINAL_WORD_DICTIONARY={\u043F\u0435\u0440\u0432\u043E\u0435:1,\u043F\u0435\u0440\u0432\u043E\u0433\u043E:1,\u0432\u0442\u043E\u0440\u043E\u0435:2,\u0432\u0442\u043E\u0440\u043E\u0433\u043E:2,\u0442\u0440\u0435\u0442\u044C\u0435:3,\u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E:3,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435:4,\u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E:4,\u043F\u044F\u0442\u043E\u0435:5,\u043F\u044F\u0442\u043E\u0433\u043E:5,\u0448\u0435\u0441\u0442\u043E\u0435:6,\u0448\u0435\u0441\u0442\u043E\u0433\u043E:6,\u0441\u0435\u0434\u044C\u043C\u043E\u0435:7,\u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E:7,\u0432\u043E\u0441\u044C\u043C\u043E\u0435:8,\u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E:8,\u0434\u0435\u0432\u044F\u0442\u043E\u0435:9,\u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E:9,\u0434\u0435\u0441\u044F\u0442\u043E\u0435:10,\u0434\u0435\u0441\u044F\u0442\u043E\u0433\u043E:10,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:11,\u043E\u0434\u0438\u043D\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:11,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:12,\u0434\u0432\u0435\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:12,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:13,\u0442\u0440\u0438\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:13,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:14,\u0447\u0435\u0442\u044B\u0440\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:14,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:15,\u043F\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:15,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:16,\u0448\u0435\u0441\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:16,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:17,\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:17,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:18,\u0432\u043E\u0441\u0435\u043C\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:18,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0435:19,\u0434\u0435\u0432\u044F\u0442\u043D\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:19,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0435:20,\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u043E\u0433\u043E:20,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":21,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0435":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u0442\u043E\u0440\u043E\u0433\u043E":22,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0442\u0440\u0435\u0442\u044C\u0435\u0433\u043E":23,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0435":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0447\u0435\u0442\u0432\u0435\u0440\u0442\u043E\u0433\u043E":24,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0435":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u043F\u044F\u0442\u043E\u0433\u043E":25,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0435":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0448\u0435\u0441\u0442\u043E\u0433\u043E":26,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0435":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0441\u0435\u0434\u044C\u043C\u043E\u0433\u043E":27,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0435":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0432\u043E\u0441\u044C\u043C\u043E\u0433\u043E":28,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0435":29,"\u0434\u0432\u0430\u0434\u0446\u0430\u0442\u044C \u0434\u0435\u0432\u044F\u0442\u043E\u0433\u043E":29,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0435:30,\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u043E\u0433\u043E:30,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0435":31,"\u0442\u0440\u0438\u0434\u0446\u0430\u0442\u044C \u043F\u0435\u0440\u0432\u043E\u0433\u043E":31};J.TIME_UNIT_DICTIONARY={\u0441\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u044B:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u0443:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0430:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0438:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u0435\u043A:"second",\u0441\u0435\u043A\u0443\u043D\u0434\u043E\u0447\u043A\u0443:"second",\u043C\u0438\u043D:"minute",\u043C\u0438\u043D\u0443\u0442\u0430:"minute",\u043C\u0438\u043D\u0443\u0442:"minute",\u043C\u0438\u043D\u0443\u0442\u044B:"minute",\u043C\u0438\u043D\u0443\u0442\u0443:"minute",\u043C\u0438\u043D\u0443\u0442\u043E\u043A:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0438:"minute",\u043C\u0438\u043D\u0443\u0442\u043A\u0443:"minute",\u0447\u0430\u0441:"hour",\u0447\u0430\u0441\u043E\u0432:"hour",\u0447\u0430\u0441\u0430:"hour",\u0447\u0430\u0441\u0443:"hour",\u0447\u0430\u0441\u0438\u043A\u043E\u0432:"hour",\u0447\u0430\u0441\u0438\u043A\u0430:"hour",\u0447\u0430\u0441\u0438\u043A\u0435:"hour",\u0447\u0430\u0441\u0438\u043A:"hour",\u0434\u0435\u043D\u044C:"d",\u0434\u043D\u044F:"d",\u0434\u043D\u0435\u0439:"d",\u0441\u0443\u0442\u043E\u043A:"d",\u0441\u0443\u0442\u043A\u0438:"d",\u043D\u0435\u0434\u0435\u043B\u044F:"week",\u043D\u0435\u0434\u0435\u043B\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u044E:"week",\u043D\u0435\u0434\u0435\u043B\u044C:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0435:"week",\u043D\u0435\u0434\u0435\u043B\u044C\u043A\u0438:"week",\u043D\u0435\u0434\u0435\u043B\u0435\u043A:"week",\u043C\u0435\u0441\u044F\u0446:"month",\u043C\u0435\u0441\u044F\u0446\u0435:"month",\u043C\u0435\u0441\u044F\u0446\u0435\u0432:"month",\u043C\u0435\u0441\u044F\u0446\u0430:"month",\u043A\u0432\u0430\u0440\u0442\u0430\u043B:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u0435:"quarter",\u043A\u0432\u0430\u0440\u0442\u0430\u043B\u043E\u0432:"quarter",\u0433\u043E\u0434:"year",\u0433\u043E\u0434\u0430:"year",\u0433\u043E\u0434\u0443:"year",\u0433\u043E\u0434\u043E\u0432:"year",\u043B\u0435\u0442:"year",\u0433\u043E\u0434\u0438\u043A:"year",\u0433\u043E\u0434\u0438\u043A\u0430:"year",\u0433\u043E\u0434\u0438\u043A\u043E\u0432:"year"};J.NUMBER_PATTERN=`(?:${oo.matchAnyPattern(J.INTEGER_WORD_DICTIONARY)}|[0-9]+|[0-9]+\\.[0-9]+|\u043F\u043E\u043B|\u043D\u0435\u0441\u043A\u043E\u043B\u044C\u043A\u043E|\u043F\u0430\u0440(?:\u044B|\u0443)|\\s{0,3})`;function ky(r){let e=r.toLowerCase();return J.INTEGER_WORD_DICTIONARY[e]!==void 0?J.INTEGER_WORD_DICTIONARY[e]:e.match(/несколько/)?3:e.match(/пол/)?.5:e.match(/пар/)?2:e===""?1:parseFloat(e)}J.parseNumberPattern=ky;J.ORDINAL_NUMBER_PATTERN=`(?:${oo.matchAnyPattern(J.ORDINAL_WORD_DICTIONARY)}|[0-9]{1,2}(?:\u0433\u043E|\u043E\u0433\u043E|\u0435|\u043E\u0435)?)`;function dR(r){let e=r.toLowerCase();return J.ORDINAL_WORD_DICTIONARY[e]!==void 0?J.ORDINAL_WORD_DICTIONARY[e]:(e=e.replace(/(?:st|nd|rd|th)$/i,""),parseInt(e))}J.parseOrdinalNumberPattern=dR;var Ld="(?:\\s+(?:\u0433\u043E\u0434\u0443|\u0433\u043E\u0434\u0430|\u0433\u043E\u0434|\u0433|\u0433.))?";J.YEAR_PATTERN=`(?:[1-9][0-9]{0,3}${Ld}\\s*(?:\u043D.\u044D.|\u0434\u043E \u043D.\u044D.|\u043D. \u044D.|\u0434\u043E \u043D. \u044D.)|[1-2][0-9]{3}${Ld}|[5-9][0-9]${Ld})`;function fR(r){if(/(год|года|г|г.)/i.test(r)&&(r=r.replace(/(год|года|г|г.)/i,"")),/(до н.э.|до н. э.)/i.test(r))return r=r.replace(/(до н.э.|до н. э.)/i,""),-parseInt(r);if(/(н. э.|н.э.)/i.test(r))return r=r.replace(/(н. э.|н.э.)/i,""),parseInt(r);let e=parseInt(r);return cR.findMostLikelyADYear(e)}J.parseYear=fR;var Oy=`(${J.NUMBER_PATTERN})\\s{0,3}(${oo.matchAnyPattern(J.TIME_UNIT_DICTIONARY)})`,wy=new RegExp(Oy,"i");J.TIME_UNITS_PATTERN=oo.repeatedTimeunitPattern("(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s{0,3})?",Oy);function pR(r){let e={},t=r,n=wy.exec(t);for(;n;)mR(e,n),t=t.substring(n[0].length).trim(),n=wy.exec(t);return e}J.parseTimeUnits=pR;function mR(r,e){let t=ky(e[1]),n=J.TIME_UNIT_DICTIONARY[e[2].toLowerCase()];r[n]=t}});var Sy=b(Wd=>{"use strict";Object.defineProperty(Wd,"__esModule",{value:!0});var xs=wt(),hR=Ne(),gR=L(),Dy=`(?:(?:\u043E\u043A\u043E\u043B\u043E|\u043F\u0440\u0438\u043C\u0435\u0440\u043D\u043E)\\s*(?:~\\s*)?)?(${xs.TIME_UNITS_PATTERN})${xs.REGEX_PARTS.rightBoundary}`,yR=new RegExp(`(?:\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0435|\u0432 \u0442\u0435\u0447\u0435\u043D\u0438\u0438)\\s*${Dy}`,xs.REGEX_PARTS.flags),_R=new RegExp(Dy,"i"),Ud=class extends gR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return xs.REGEX_PARTS.leftBoundary}innerPattern(e){return e.option.forwardDate?_R:yR}innerExtract(e,t){let n=xs.parseTimeUnits(t[1]);return hR.ParsingComponents.createRelativeFromReference(e.reference,n)}};Wd.default=Ud});var Cy=b(Yd=>{"use strict";Object.defineProperty(Yd,"__esModule",{value:!0});var TR=nt(),As=wt(),Ay=wt(),uo=wt(),bR=Ee(),vR=L(),ER=new RegExp(`(?:\u0441)?\\s*(${uo.ORDINAL_NUMBER_PATTERN})(?:\\s{0,3}(?:\u043F\u043E|-|\u2013|\u0434\u043E)?\\s{0,3}(${uo.ORDINAL_NUMBER_PATTERN}))?(?:-|\\/|\\s{0,3}(?:of)?\\s{0,3})(${bR.matchAnyPattern(As.MONTH_DICTIONARY)})(?:(?:-|\\/|,?\\s{0,3})(${Ay.YEAR_PATTERN}(?![^\\s]\\d)))?${As.REGEX_PARTS.rightBoundary}`,As.REGEX_PARTS.flags),Ry=1,My=2,wR=3,xy=4,qd=class extends vR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return As.REGEX_PARTS.leftBoundary}innerPattern(){return ER}innerExtract(e,t){let n=e.createParsingResult(t.index,t[0]),i=As.MONTH_DICTIONARY[t[wR].toLowerCase()],s=uo.parseOrdinalNumberPattern(t[Ry]);if(s>31)return t.index=t.index+t[Ry].length,null;if(n.start.assign("month",i),n.start.assign("day",s),t[xy]){let a=Ay.parseYear(t[xy]);n.start.assign("year",a)}else{let a=TR.findYearClosestToRef(e.refDate,s,i);n.start.imply("year",a)}if(t[My]){let a=uo.parseOrdinalNumberPattern(t[My]);n.end=n.start.clone(),n.end.assign("day",a)}return n}};Yd.default=qd});var Iy=b(jd=>{"use strict";Object.defineProperty(jd,"__esModule",{value:!0});var Cs=wt(),kR=nt(),OR=Ee(),Ny=wt(),DR=L(),SR=new RegExp(`((?:\u0432)\\s*)?(${OR.matchAnyPattern(Cs.MONTH_DICTIONARY)})\\s*(?:[,-]?\\s*(${Ny.YEAR_PATTERN})?)?(?=[^\\s\\w]|\\s+[^0-9]|\\s+$|$)`,Cs.REGEX_PARTS.flags),RR=2,Py=3,$d=class extends DR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Cs.REGEX_PARTS.leftBoundary}innerPattern(){return SR}innerExtract(e,t){let n=t[RR].toLowerCase();if(t[0].length<=3&&!Cs.FULL_MONTH_NAME_DICTIONARY[n])return null;let i=e.createParsingResult(t.index,t.index+t[0].length);i.start.imply("day",1);let s=Cs.MONTH_DICTIONARY[n];if(i.start.assign("month",s),t[Py]){let a=Ny.parseYear(t[Py]);i.start.assign("year",a)}else{let a=kR.findYearClosestToRef(e.refDate,1,s);i.start.imply("year",a)}return i}};jd.default=$d});var Ly=b(Bd=>{"use strict";Object.defineProperty(Bd,"__esModule",{value:!0});var lo=Be(),MR=pn(),Fy=wt(),Gd=class extends MR.AbstractTimeExpressionParser{constructor(e){super(e)}patternFlags(){return Fy.REGEX_PARTS.flags}primaryPatternLeftBoundary(){return"(^|\\s|T|(?:[^\\p{L}\\p{N}_]))"}followingPhase(){return"\\s*(?:\\-|\\\u2013|\\~|\\\u301C|\u0434\u043E|\u0438|\u043F\u043E|\\?)\\s*"}primaryPrefix(){return"(?:(?:\u0432|\u0441)\\s*)??"}primarySuffix(){return`(?:\\s*(?:\u0443\u0442\u0440\u0430|\u0432\u0435\u0447\u0435\u0440\u0430|\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F))?(?!\\/)${Fy.REGEX_PARTS.rightBoundary}`}extractPrimaryTimeComponents(e,t){let n=super.extractPrimaryTimeComponents(e,t);if(n){if(t[0].endsWith("\u0432\u0435\u0447\u0435\u0440\u0430")){let i=n.get("hour");i>=6&&i<12?(n.assign("hour",n.get("hour")+12),n.assign("meridiem",lo.Meridiem.PM)):i<6&&n.assign("meridiem",lo.Meridiem.AM)}if(t[0].endsWith("\u043F\u043E\u0441\u043B\u0435 \u043F\u043E\u043B\u0443\u0434\u043D\u044F")){n.assign("meridiem",lo.Meridiem.PM);let i=n.get("hour");i>=0&&i<=6&&n.assign("hour",n.get("hour")+12)}t[0].endsWith("\u0443\u0442\u0440\u0430")&&(n.assign("meridiem",lo.Meridiem.AM),n.get("hour")<12&&n.assign("hour",n.get("hour")))}return n}};Bd.default=Gd});var Uy=b(zd=>{"use strict";Object.defineProperty(zd,"__esModule",{value:!0});var co=wt(),xR=Ne(),AR=L(),CR=Wt(),PR=new RegExp(`(${co.TIME_UNITS_PATTERN})\\s{0,5}\u043D\u0430\u0437\u0430\u0434(?=(?:\\W|$))`,co.REGEX_PARTS.flags),Hd=class extends AR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return co.REGEX_PARTS.leftBoundary}innerPattern(){return PR}innerExtract(e,t){let n=co.parseTimeUnits(t[1]),i=CR.reverseTimeUnits(n);return xR.ParsingComponents.createRelativeFromReference(e.reference,i)}};zd.default=Hd});var Wy=b(Ps=>{"use strict";var NR=Ps&&Ps.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ps,"__esModule",{value:!0});var IR=NR(lr()),Vd=class extends IR.default{patternBetween(){return/^\s*(и до|и по|до|по|-)\s*$/i}};Ps.default=Vd});var qy=b(Ns=>{"use strict";var FR=Ns&&Ns.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ns,"__esModule",{value:!0});var LR=FR(br()),Kd=class extends LR.default{patternBetween(){return new RegExp("^\\s*(T|\u0432|,|-)?\\s*$")}};Ns.default=Kd});var Yy=b(Rr=>{"use strict";var UR=Rr&&Rr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),WR=Rr&&Rr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),qR=Rr&&Rr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&UR(e,r,t);return WR(e,r),e};Object.defineProperty(Rr,"__esModule",{value:!0});var YR=L(),Is=qR(wr()),Qd=wt(),$R=new RegExp(`(?:\u0441|\u0441\u043E)?\\s*(\u0441\u0435\u0433\u043E\u0434\u043D\u044F|\u0432\u0447\u0435\u0440\u0430|\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430|\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430)${Qd.REGEX_PARTS.rightBoundary}`,Qd.REGEX_PARTS.flags),Xd=class extends YR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Qd.REGEX_PARTS.leftBoundary}innerPattern(e){return $R}innerExtract(e,t){let n=t[1].toLowerCase(),i=e.createParsingComponents();switch(n){case"\u0441\u0435\u0433\u043E\u0434\u043D\u044F":return Is.today(e.reference);case"\u0432\u0447\u0435\u0440\u0430":return Is.yesterday(e.reference);case"\u0437\u0430\u0432\u0442\u0440\u0430":return Is.tomorrow(e.reference);case"\u043F\u043E\u0441\u043B\u0435\u0437\u0430\u0432\u0442\u0440\u0430":return Is.theDayAfter(e.reference,2);case"\u043F\u043E\u0437\u0430\u0432\u0447\u0435\u0440\u0430":return Is.theDayBefore(e.reference,2)}return i}};Rr.default=Xd});var $y=b(tr=>{"use strict";var jR=tr&&tr.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),GR=tr&&tr.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),BR=tr&&tr.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&jR(e,r,t);return GR(e,r),e},HR=tr&&tr.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(tr,"__esModule",{value:!0});var zR=L(),vn=BR(wr()),VR=Ut(),KR=HR(ge()),Zd=wt(),QR=new RegExp(`(\u0441\u0435\u0439\u0447\u0430\u0441|\u043F\u0440\u043E\u0448\u043B\u044B\u043C\\s*\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u043F\u0440\u043E\u0448\u043B\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u0441\u0435\u0433\u043E\u0434\u043D\u044F\\s*\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u043E\u0439\\s*\u043D\u043E\u0447\u044C\u044E|\u043D\u043E\u0447\u044C\u044E|\u044D\u0442\u0438\u043C \u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u043E\u043C|\u0443\u0442\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u0434\u0435\u043D\u044C|\u0432\u0435\u0447\u0435\u0440\u043E\u043C|\u0432\u0435\u0447\u0435\u0440\u0430|\u0432\\s*\u043F\u043E\u043B\u043D\u043E\u0447\u044C)${Zd.REGEX_PARTS.rightBoundary}`,Zd.REGEX_PARTS.flags),Jd=class extends zR.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Zd.REGEX_PARTS.leftBoundary}innerPattern(){return QR}innerExtract(e,t){let n=KR.default(e.refDate),i=t[0].toLowerCase(),s=e.createParsingComponents();if(i==="\u0441\u0435\u0439\u0447\u0430\u0441")return vn.now(e.reference);if(i==="\u0432\u0435\u0447\u0435\u0440\u043E\u043C"||i==="\u0432\u0435\u0447\u0435\u0440\u0430")return vn.evening(e.reference);if(i.endsWith("\u0443\u0442\u0440\u043E\u043C")||i.endsWith("\u0443\u0442\u0440\u0430"))return vn.morning(e.reference);if(i.match(/в\s*полдень/))return vn.noon(e.reference);if(i.match(/прошлой\s*ночью/))return vn.lastNight(e.reference);if(i.match(/прошлым\s*вечером/))return vn.yesterdayEvening(e.reference);if(i.match(/следующей\s*ночью/)){let a=n.hour()<22?1:2;n=n.add(a,"day"),VR.assignSimilarDate(s,n),s.imply("hour",0)}return i.match(/в\s*полночь/)||i.endsWith("\u043D\u043E\u0447\u044C\u044E")?vn.midnight(e.reference):s}};tr.default=Jd});var jy=b(tf=>{"use strict";Object.defineProperty(tf,"__esModule",{value:!0});var Fs=wt(),XR=Ee(),ZR=L(),JR=hn(),eM=new RegExp(`(?:(?:,|\\(|\uFF08)\\s*)?(?:\u0432\\s*?)?(?:(\u044D\u0442\u0443|\u044D\u0442\u043E\u0442|\u043F\u0440\u043E\u0448\u043B\u044B\u0439|\u043F\u0440\u043E\u0448\u043B\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E)\\s*)?(${XR.matchAnyPattern(Fs.WEEKDAY_DICTIONARY)})(?:\\s*(?:,|\\)|\uFF09))?(?:\\s*\u043D\u0430\\s*(\u044D\u0442\u043E\u0439|\u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439)\\s*\u043D\u0435\u0434\u0435\u043B\u0435)?${Fs.REGEX_PARTS.rightBoundary}`,Fs.REGEX_PARTS.flags),tM=1,rM=2,nM=3,ef=class extends ZR.AbstractParserWithWordBoundaryChecking{innerPattern(){return eM}patternLeftBoundary(){return Fs.REGEX_PARTS.leftBoundary}innerExtract(e,t){let n=t[rM].toLowerCase(),i=Fs.WEEKDAY_DICTIONARY[n],s=t[tM],a=t[nM],o=s||a;o=o||"",o=o.toLowerCase();let u=null;o=="\u043F\u0440\u043E\u0448\u043B\u044B\u0439"||o=="\u043F\u0440\u043E\u0448\u043B\u0443\u044E"||o=="\u043F\u0440\u043E\u0448\u043B\u043E\u0439"?u="last":o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0443\u044E"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||o=="\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0433\u043E"?u="next":(o=="\u044D\u0442\u043E\u0442"||o=="\u044D\u0442\u0443"||o=="\u044D\u0442\u043E\u0439")&&(u="this");let l=JR.toDayJSWeekday(e.refDate,i,u);return e.createParsingComponents().assign("weekday",i).imply("day",l.date()).imply("month",l.month()+1).imply("year",l.year())}};tf.default=ef});var By=b(Us=>{"use strict";var iM=Us&&Us.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Us,"__esModule",{value:!0});var Ls=wt(),Gy=Ne(),sM=iM(ge()),aM=L(),oM=Ee(),uM=new RegExp(`(\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C|\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439|\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439|\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C|\u043D\u0430 \u044D\u0442\u043E\u0439|\u0432 \u044D\u0442\u043E\u043C)\\s*(${oM.matchAnyPattern(Ls.TIME_UNIT_DICTIONARY)})(?=\\s*)${Ls.REGEX_PARTS.rightBoundary}`,Ls.REGEX_PARTS.flags),lM=1,cM=2,rf=class extends aM.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Ls.REGEX_PARTS.leftBoundary}innerPattern(){return uM}innerExtract(e,t){let n=t[lM].toLowerCase(),i=t[cM].toLowerCase(),s=Ls.TIME_UNIT_DICTIONARY[i];if(n=="\u043D\u0430 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u0439"||n=="\u0432 \u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0435\u043C"){let u={};return u[s]=1,Gy.ParsingComponents.createRelativeFromReference(e.reference,u)}if(n=="\u0432 \u043F\u0440\u043E\u0448\u043B\u043E\u043C"||n=="\u043D\u0430 \u043F\u0440\u043E\u0448\u043B\u043E\u0439"){let u={};return u[s]=-1,Gy.ParsingComponents.createRelativeFromReference(e.reference,u)}let a=e.createParsingComponents(),o=sM.default(e.reference.instant);return s.match(/week/i)?(o=o.add(-o.get("d"),"d"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.imply("year",o.year())):s.match(/month/i)?(o=o.add(-o.date()+1,"d"),a.imply("day",o.date()),a.assign("year",o.year()),a.assign("month",o.month()+1)):s.match(/year/i)&&(o=o.add(-o.date()+1,"d"),o=o.add(-o.month(),"month"),a.imply("day",o.date()),a.imply("month",o.month()+1),a.assign("year",o.year())),a}};Us.default=rf});var Hy=b(sf=>{"use strict";Object.defineProperty(sf,"__esModule",{value:!0});var Ws=wt(),dM=Ne(),fM=L(),pM=Wt(),mM=new RegExp(`(\u044D\u0442\u0438|\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435|\u043F\u0440\u043E\u0448\u043B\u044B\u0435|\u0441\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0435|\u043F\u043E\u0441\u043B\u0435|\u0447\u0435\u0440\u0435\u0437|\\+|-)\\s*(${Ws.TIME_UNITS_PATTERN})${Ws.REGEX_PARTS.rightBoundary}`,Ws.REGEX_PARTS.flags),nf=class extends fM.AbstractParserWithWordBoundaryChecking{patternLeftBoundary(){return Ws.REGEX_PARTS.leftBoundary}innerPattern(){return mM}innerExtract(e,t){let n=t[1].toLowerCase(),i=Ws.parseTimeUnits(t[2]);switch(n){case"\u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0438\u0435":case"\u043F\u0440\u043E\u0448\u043B\u044B\u0435":case"-":i=pM.reverseTimeUnits(i);break}return dM.ParsingComponents.createRelativeFromReference(e.reference,i)}};sf.default=nf});var Ky=b(Je=>{"use strict";var Mt=Je&&Je.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Je,"__esModule",{value:!0});Je.createConfiguration=Je.createCasualConfiguration=Je.parseDate=Je.parse=Je.strict=Je.casual=void 0;var hM=Mt(Sy()),gM=Mt(Cy()),yM=Mt(Iy()),_M=Mt(Ly()),TM=Mt(Uy()),bM=Mt(Wy()),vM=Mt(qy()),EM=vr(),wM=Mt(Yy()),kM=Mt($y()),OM=Mt(jy()),DM=Mt(By()),zy=Qt(),SM=Mt(gn()),RM=Mt(Hy());Je.casual=new zy.Chrono(Vy());Je.strict=new zy.Chrono(af(!0));function MM(r,e,t){return Je.casual.parse(r,e,t)}Je.parse=MM;function xM(r,e,t){return Je.casual.parseDate(r,e,t)}Je.parseDate=xM;function Vy(){let r=af(!1);return r.parsers.unshift(new wM.default),r.parsers.unshift(new kM.default),r.parsers.unshift(new yM.default),r.parsers.unshift(new DM.default),r.parsers.unshift(new RM.default),r}Je.createCasualConfiguration=Vy;function af(r=!0){return EM.includeCommonConfiguration({parsers:[new SM.default(!0),new hM.default,new gM.default,new OM.default,new _M.default(r),new TM.default],refiners:[new vM.default,new bM.default]},r)}Je.createConfiguration=af});var Be=b(se=>{"use strict";var AM=se&&se.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),CM=se&&se.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),Kr=se&&se.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.prototype.hasOwnProperty.call(r,t)&&AM(e,r,t);return CM(e,r),e};Object.defineProperty(se,"__esModule",{value:!0});se.parseDate=se.parse=se.casual=se.strict=se.ru=se.zh=se.nl=se.pt=se.ja=se.fr=se.de=se.Meridiem=se.Chrono=se.en=void 0;var of=Kr(wl());se.en=of;var PM=Qt();Object.defineProperty(se,"Chrono",{enumerable:!0,get:function(){return PM.Chrono}});var NM;(function(r){r[r.AM=0]="AM",r[r.PM=1]="PM"})(NM=se.Meridiem||(se.Meridiem={}));var IM=Kr(ih());se.de=IM;var FM=Kr(Ph());se.fr=FM;var LM=Kr($h());se.ja=LM;var UM=Kr(ig());se.pt=UM;var WM=Kr($g());se.nl=WM;var qM=Kr(Ey());se.zh=qM;var YM=Kr(Ky());se.ru=YM;se.strict=of.strict;se.casual=of.casual;function $M(r,e,t){return se.casual.parse(r,e,t)}se.parse=$M;function jM(r,e,t){return se.casual.parseDate(r,e,t)}se.parseDate=jM});var T_=b((pN,yo)=>{var Zy,Jy,e_,t_,r_,n_,i_,s_,a_,ho,lf,o_,u_,l_,li,c_,d_,f_,p_,m_,h_,g_,y_,__,go;(function(r){var e=typeof global=="object"?global:typeof self=="object"?self:typeof this=="object"?this:{};typeof define=="function"&&define.amd?define("tslib",["exports"],function(n){r(t(e,t(n)))}):typeof yo=="object"&&typeof yo.exports=="object"?r(t(e,t(yo.exports))):r(t(e));function t(n,i){return n!==e&&(typeof Object.create=="function"?Object.defineProperty(n,"__esModule",{value:!0}):n.__esModule=!0),function(s,a){return n[s]=i?i(s,a):a}}})(function(r){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(n[s]=i[s])};Zy=function(n,i){if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");e(n,i);function s(){this.constructor=n}n.prototype=i===null?Object.create(i):(s.prototype=i.prototype,new s)},Jy=Object.assign||function(n){for(var i,s=1,a=arguments.length;s<a;s++){i=arguments[s];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(n[o]=i[o])}return n},e_=function(n,i){var s={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&i.indexOf(a)<0&&(s[a]=n[a]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(n);o<a.length;o++)i.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(n,a[o])&&(s[a[o]]=n[a[o]]);return s},t_=function(n,i,s,a){var o=arguments.length,u=o<3?i:a===null?a=Object.getOwnPropertyDescriptor(i,s):a,l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(n,i,s,a);else for(var c=n.length-1;c>=0;c--)(l=n[c])&&(u=(o<3?l(u):o>3?l(i,s,u):l(i,s))||u);return o>3&&u&&Object.defineProperty(i,s,u),u},r_=function(n,i){return function(s,a){i(s,a,n)}},n_=function(n,i){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(n,i)},i_=function(n,i,s,a){function o(u){return u instanceof s?u:new s(function(l){l(u)})}return new(s||(s=Promise))(function(u,l){function c(T){try{m(a.next(T))}catch(y){l(y)}}function p(T){try{m(a.throw(T))}catch(y){l(y)}}function m(T){T.done?u(T.value):o(T.value).then(c,p)}m((a=a.apply(n,i||[])).next())})},s_=function(n,i){var s={label:0,sent:function(){if(u[0]&1)throw u[1];return u[1]},trys:[],ops:[]},a,o,u,l;return l={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function c(m){return function(T){return p([m,T])}}function p(m){if(a)throw new TypeError("Generator is already executing.");for(;s;)try{if(a=1,o&&(u=m[0]&2?o.return:m[0]?o.throw||((u=o.return)&&u.call(o),0):o.next)&&!(u=u.call(o,m[1])).done)return u;switch(o=0,u&&(m=[m[0]&2,u.value]),m[0]){case 0:case 1:u=m;break;case 4:return s.label++,{value:m[1],done:!1};case 5:s.label++,o=m[1],m=[0];continue;case 7:m=s.ops.pop(),s.trys.pop();continue;default:if(u=s.trys,!(u=u.length>0&&u[u.length-1])&&(m[0]===6||m[0]===2)){s=0;continue}if(m[0]===3&&(!u||m[1]>u[0]&&m[1]<u[3])){s.label=m[1];break}if(m[0]===6&&s.label<u[1]){s.label=u[1],u=m;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(m);break}u[2]&&s.ops.pop(),s.trys.pop();continue}m=i.call(n,s)}catch(T){m=[6,T],o=0}finally{a=u=0}if(m[0]&5)throw m[1];return{value:m[0]?m[1]:void 0,done:!0}}},a_=function(n,i){for(var s in n)s!=="default"&&!Object.prototype.hasOwnProperty.call(i,s)&&go(i,n,s)},go=Object.create?function(n,i,s,a){a===void 0&&(a=s);var o=Object.getOwnPropertyDescriptor(i,s);(!o||("get"in o?!i.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return i[s]}}),Object.defineProperty(n,a,o)}:function(n,i,s,a){a===void 0&&(a=s),n[a]=i[s]},ho=function(n){var i=typeof Symbol=="function"&&Symbol.iterator,s=i&&n[i],a=0;if(s)return s.call(n);if(n&&typeof n.length=="number")return{next:function(){return n&&a>=n.length&&(n=void 0),{value:n&&n[a++],done:!n}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")},lf=function(n,i){var s=typeof Symbol=="function"&&n[Symbol.iterator];if(!s)return n;var a=s.call(n),o,u=[],l;try{for(;(i===void 0||i-- >0)&&!(o=a.next()).done;)u.push(o.value)}catch(c){l={error:c}}finally{try{o&&!o.done&&(s=a.return)&&s.call(a)}finally{if(l)throw l.error}}return u},o_=function(){for(var n=[],i=0;i<arguments.length;i++)n=n.concat(lf(arguments[i]));return n},u_=function(){for(var n=0,i=0,s=arguments.length;i<s;i++)n+=arguments[i].length;for(var a=Array(n),o=0,i=0;i<s;i++)for(var u=arguments[i],l=0,c=u.length;l<c;l++,o++)a[o]=u[l];return a},l_=function(n,i,s){if(s||arguments.length===2)for(var a=0,o=i.length,u;a<o;a++)(u||!(a in i))&&(u||(u=Array.prototype.slice.call(i,0,a)),u[a]=i[a]);return n.concat(u||Array.prototype.slice.call(i))},li=function(n){return this instanceof li?(this.v=n,this):new li(n)},c_=function(n,i,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var a=s.apply(n,i||[]),o,u=[];return o={},l("next"),l("throw"),l("return"),o[Symbol.asyncIterator]=function(){return this},o;function l(E){a[E]&&(o[E]=function(M){return new Promise(function(U,q){u.push([E,M,U,q])>1||c(E,M)})})}function c(E,M){try{p(a[E](M))}catch(U){y(u[0][3],U)}}function p(E){E.value instanceof li?Promise.resolve(E.value.v).then(m,T):y(u[0][2],E)}function m(E){c("next",E)}function T(E){c("throw",E)}function y(E,M){E(M),u.shift(),u.length&&c(u[0][0],u[0][1])}},d_=function(n){var i,s;return i={},a("next"),a("throw",function(o){throw o}),a("return"),i[Symbol.iterator]=function(){return this},i;function a(o,u){i[o]=n[o]?function(l){return(s=!s)?{value:li(n[o](l)),done:o==="return"}:u?u(l):l}:u}},f_=function(n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=n[Symbol.asyncIterator],s;return i?i.call(n):(n=typeof ho=="function"?ho(n):n[Symbol.iterator](),s={},a("next"),a("throw"),a("return"),s[Symbol.asyncIterator]=function(){return this},s);function a(u){s[u]=n[u]&&function(l){return new Promise(function(c,p){l=n[u](l),o(c,p,l.done,l.value)})}}function o(u,l,c,p){Promise.resolve(p).then(function(m){u({value:m,done:c})},l)}},p_=function(n,i){return Object.defineProperty?Object.defineProperty(n,"raw",{value:i}):n.raw=i,n};var t=Object.create?function(n,i){Object.defineProperty(n,"default",{enumerable:!0,value:i})}:function(n,i){n.default=i};m_=function(n){if(n&&n.__esModule)return n;var i={};if(n!=null)for(var s in n)s!=="default"&&Object.prototype.hasOwnProperty.call(n,s)&&go(i,n,s);return t(i,n),i},h_=function(n){return n&&n.__esModule?n:{default:n}},g_=function(n,i,s,a){if(s==="a"&&!a)throw new TypeError("Private accessor was defined without a getter");if(typeof i=="function"?n!==i||!a:!i.has(n))throw new TypeError("Cannot read private member from an object whose class did not declare it");return s==="m"?a:s==="a"?a.call(n):a?a.value:i.get(n)},y_=function(n,i,s,a,o){if(a==="m")throw new TypeError("Private method is not writable");if(a==="a"&&!o)throw new TypeError("Private accessor was defined without a setter");if(typeof i=="function"?n!==i||!o:!i.has(n))throw new TypeError("Cannot write private member to an object whose class did not declare it");return a==="a"?o.call(n,s):o?o.value=s:i.set(n,s),s},__=function(n,i){if(i===null||typeof i!="object"&&typeof i!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof n=="function"?i===n:n.has(i)},r("__extends",Zy),r("__assign",Jy),r("__rest",e_),r("__decorate",t_),r("__param",r_),r("__metadata",n_),r("__awaiter",i_),r("__generator",s_),r("__exportStar",a_),r("__createBinding",go),r("__values",ho),r("__read",lf),r("__spread",o_),r("__spreadArrays",u_),r("__spreadArray",l_),r("__await",li),r("__asyncGenerator",c_),r("__asyncDelegator",d_),r("__asyncValues",f_),r("__makeTemplateObject",p_),r("__importStar",m_),r("__importDefault",h_),r("__classPrivateFieldGet",g_),r("__classPrivateFieldSet",y_),r("__classPrivateFieldIn",__)})});var iT=b((rT,nT)=>{(function(r){var e=Object.hasOwnProperty,t=Array.isArray?Array.isArray:function(f){return Object.prototype.toString.call(f)==="[object Array]"},n=10,i=typeof process=="object"&&typeof process.nextTick=="function",s=typeof Symbol=="function",a=typeof Reflect=="object",o=typeof setImmediate=="function",u=o?setImmediate:setTimeout,l=s?a&&typeof Reflect.ownKeys=="function"?Reflect.ownKeys:function(d){var f=Object.getOwnPropertyNames(d);return f.push.apply(f,Object.getOwnPropertySymbols(d)),f}:Object.keys;function c(){this._events={},this._conf&&p.call(this,this._conf)}function p(d){d&&(this._conf=d,d.delimiter&&(this.delimiter=d.delimiter),d.maxListeners!==r&&(this._maxListeners=d.maxListeners),d.wildcard&&(this.wildcard=d.wildcard),d.newListener&&(this._newListener=d.newListener),d.removeListener&&(this._removeListener=d.removeListener),d.verboseMemoryLeak&&(this.verboseMemoryLeak=d.verboseMemoryLeak),d.ignoreErrors&&(this.ignoreErrors=d.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function m(d,f){var h="(node) warning: possible EventEmitter memory leak detected. "+d+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(h+=" Event name: "+f+"."),typeof process!="undefined"&&process.emitWarning){var g=new Error(h);g.name="MaxListenersExceededWarning",g.emitter=this,g.count=d,process.emitWarning(g)}else console.error(h),console.trace&&console.trace()}var T=function(d,f,h){var g=arguments.length;switch(g){case 0:return[];case 1:return[d];case 2:return[d,f];case 3:return[d,f,h];default:for(var _=new Array(g);g--;)_[g]=arguments[g];return _}};function y(d,f){for(var h={},g,_=d.length,v=f?f.length:0,k=0;k<_;k++)g=d[k],h[g]=k<v?f[k]:r;return h}function E(d,f,h){this._emitter=d,this._target=f,this._listeners={},this._listenersCount=0;var g,_;if((h.on||h.off)&&(g=h.on,_=h.off),f.addEventListener?(g=f.addEventListener,_=f.removeEventListener):f.addListener?(g=f.addListener,_=f.removeListener):f.on&&(g=f.on,_=f.off),!g&&!_)throw Error("target does not implement any known event API");if(typeof g!="function")throw TypeError("on method must be a function");if(typeof _!="function")throw TypeError("off method must be a function");this._on=g,this._off=_;var v=d._observers;v?v.push(this):d._observers=[this]}Object.assign(E.prototype,{subscribe:function(d,f,h){var g=this,_=this._target,v=this._emitter,k=this._listeners,S=function(){var O=T.apply(null,arguments),D={data:O,name:f,original:d};if(h){var P=h.call(_,D);P!==!1&&v.emit.apply(v,[D.name].concat(O));return}v.emit.apply(v,[f].concat(O))};if(k[d])throw Error("Event '"+d+"' is already listening");this._listenersCount++,v._newListener&&v._removeListener&&!g._onNewListener?(this._onNewListener=function(O){O===f&&k[d]===null&&(k[d]=S,g._on.call(_,d,S))},v.on("newListener",this._onNewListener),this._onRemoveListener=function(O){O===f&&!v.hasListeners(O)&&k[d]&&(k[d]=null,g._off.call(_,d,S))},k[d]=null,v.on("removeListener",this._onRemoveListener)):(k[d]=S,g._on.call(_,d,S))},unsubscribe:function(d){var f=this,h=this._listeners,g=this._emitter,_,v,k=this._off,S=this._target,O;if(d&&typeof d!="string")throw TypeError("event must be a string");function D(){f._onNewListener&&(g.off("newListener",f._onNewListener),g.off("removeListener",f._onRemoveListener),f._onNewListener=null,f._onRemoveListener=null);var P=ve.call(g,f);g._observers.splice(P,1)}if(d){if(_=h[d],!_)return;k.call(S,d,_),delete h[d],--this._listenersCount||D()}else{for(v=l(h),O=v.length;O-- >0;)d=v[O],k.call(S,d,h[d]);this._listeners={},this._listenersCount=0,D()}}});function M(d,f,h,g){var _=Object.assign({},f);if(!d)return _;if(typeof d!="object")throw TypeError("options must be an object");var v=Object.keys(d),k=v.length,S,O,D;function P(ae){throw Error('Invalid "'+S+'" option value'+(ae?". Reason: "+ae:""))}for(var me=0;me<k;me++){if(S=v[me],!g&&!e.call(f,S))throw Error('Unknown "'+S+'" option');O=d[S],O!==r&&(D=h[S],_[S]=D?D(O,P):O)}return _}function U(d,f){return(typeof d!="function"||!d.hasOwnProperty("prototype"))&&f("value must be a constructor"),d}function q(d){var f="value must be type of "+d.join("|"),h=d.length,g=d[0],_=d[1];return h===1?function(v,k){if(typeof v===g)return v;k(f)}:h===2?function(v,k){var S=typeof v;if(S===g||S===_)return v;k(f)}:function(v,k){for(var S=typeof v,O=h;O-- >0;)if(S===d[O])return v;k(f)}}var W=q(["function"]),ie=q(["object","function"]);function re(d,f,h){var g,_,v=0,k,S=new d(function(O,D,P){h=M(h,{timeout:0,overload:!1},{timeout:function(Se,I){return Se*=1,(typeof Se!="number"||Se<0||!Number.isFinite(Se))&&I("timeout must be a positive number"),Se}}),g=!h.overload&&typeof d.prototype.cancel=="function"&&typeof P=="function";function me(){_&&(_=null),v&&(clearTimeout(v),v=0)}var ae=function(Se){me(),O(Se)},te=function(Se){me(),D(Se)};g?f(ae,te,P):(_=[function(Se){te(Se||Error("canceled"))}],f(ae,te,function(Se){if(k)throw Error("Unable to subscribe on cancel event asynchronously");if(typeof Se!="function")throw TypeError("onCancel callback must be a function");_.push(Se)}),k=!0),h.timeout>0&&(v=setTimeout(function(){var Se=Error("timeout");Se.code="ETIMEDOUT",v=0,S.cancel(Se),D(Se)},h.timeout))});return g||(S.cancel=function(O){if(!!_){for(var D=_.length,P=1;P<D;P++)_[P](O);_[0](O),_=null}}),S}function ve(d){var f=this._observers;if(!f)return-1;for(var h=f.length,g=0;g<h;g++)if(f[g]._target===d)return g;return-1}function G(d,f,h,g,_){if(!h)return null;if(g===0){var v=typeof f;if(v==="string"){var k,S,O=0,D=0,P=this.delimiter,me=P.length;if((S=f.indexOf(P))!==-1){k=new Array(5);do k[O++]=f.slice(D,S),D=S+me;while((S=f.indexOf(P,D))!==-1);k[O++]=f.slice(D),f=k,_=O}else f=[f],_=1}else v==="object"?_=f.length:(f=[f],_=1)}var ae=null,te,Se,I,Ae,Ce,rt=f[g],Tr=f[g+1],We,Le;if(g===_)h._listeners&&(typeof h._listeners=="function"?(d&&d.push(h._listeners),ae=[h]):(d&&d.push.apply(d,h._listeners),ae=[h]));else if(rt==="*"){for(We=l(h),S=We.length;S-- >0;)te=We[S],te!=="_listeners"&&(Le=G(d,f,h[te],g+1,_),Le&&(ae?ae.push.apply(ae,Le):ae=Le));return ae}else if(rt==="**"){for(Ce=g+1===_||g+2===_&&Tr==="*",Ce&&h._listeners&&(ae=G(d,f,h,_,_)),We=l(h),S=We.length;S-- >0;)te=We[S],te!=="_listeners"&&(te==="*"||te==="**"?(h[te]._listeners&&!Ce&&(Le=G(d,f,h[te],_,_),Le&&(ae?ae.push.apply(ae,Le):ae=Le)),Le=G(d,f,h[te],g,_)):te===Tr?Le=G(d,f,h[te],g+2,_):Le=G(d,f,h[te],g,_),Le&&(ae?ae.push.apply(ae,Le):ae=Le));return ae}else h[rt]&&(ae=G(d,f,h[rt],g+1,_));if(Se=h["*"],Se&&G(d,f,Se,g+1,_),I=h["**"],I)if(g<_)for(I._listeners&&G(d,f,I,_,_),We=l(I),S=We.length;S-- >0;)te=We[S],te!=="_listeners"&&(te===Tr?G(d,f,I[te],g+2,_):te===rt?G(d,f,I[te],g+1,_):(Ae={},Ae[te]=I[te],G(d,f,{"**":Ae},g+1,_)));else I._listeners?G(d,f,I,_,_):I["*"]&&I["*"]._listeners&&G(d,f,I["*"],_,_);return ae}function K(d,f,h){var g=0,_=0,v,k=this.delimiter,S=k.length,O;if(typeof d=="string")if((v=d.indexOf(k))!==-1){O=new Array(5);do O[g++]=d.slice(_,v),_=v+S;while((v=d.indexOf(k,_))!==-1);O[g++]=d.slice(_)}else O=[d],g=1;else O=d,g=d.length;if(g>1){for(v=0;v+1<g;v++)if(O[v]==="**"&&O[v+1]==="**")return}var D=this.listenerTree,P;for(v=0;v<g;v++)if(P=O[v],D=D[P]||(D[P]={}),v===g-1)return D._listeners?(typeof D._listeners=="function"&&(D._listeners=[D._listeners]),h?D._listeners.unshift(f):D._listeners.push(f),!D._listeners.warned&&this._maxListeners>0&&D._listeners.length>this._maxListeners&&(D._listeners.warned=!0,m.call(this,D._listeners.length,P))):D._listeners=f,!0;return!0}function Pe(d,f,h,g){for(var _=l(d),v=_.length,k,S,O,D=d._listeners,P;v-- >0;)S=_[v],k=d[S],S==="_listeners"?O=h:O=h?h.concat(S):[S],P=g||typeof S=="symbol",D&&f.push(P?O:O.join(this.delimiter)),typeof k=="object"&&Pe.call(this,k,f,O,P);return f}function Fe(d){for(var f=l(d),h=f.length,g,_,v;h-- >0;)_=f[h],g=d[_],g&&(v=!0,_!=="_listeners"&&!Fe(g)&&delete d[_]);return v}function j(d,f,h){this.emitter=d,this.event=f,this.listener=h}j.prototype.off=function(){return this.emitter.off(this.event,this.listener),this};function A(d,f,h){if(h===!0)_=!0;else if(h===!1)g=!0;else{if(!h||typeof h!="object")throw TypeError("options should be an object or true");var g=h.async,_=h.promisify,v=h.nextTick,k=h.objectify}if(g||v||_){var S=f,O=f._origin||f;if(v&&!i)throw Error("process.nextTick is not supported");_===r&&(_=f.constructor.name==="AsyncFunction"),f=function(){var D=arguments,P=this,me=this.event;return _?v?Promise.resolve():new Promise(function(ae){u(ae)}).then(function(){return P.event=me,S.apply(P,D)}):(v?process.nextTick:u)(function(){P.event=me,S.apply(P,D)})},f._async=!0,f._origin=O}return[f,k?new j(this,d,f):this]}function w(d){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,p.call(this,d)}w.EventEmitter2=w,w.prototype.listenTo=function(d,f,h){if(typeof d!="object")throw TypeError("target musts be an object");var g=this;h=M(h,{on:r,off:r,reducers:r},{on:W,off:W,reducers:ie});function _(v){if(typeof v!="object")throw TypeError("events must be an object");var k=h.reducers,S=ve.call(g,d),O;S===-1?O=new E(g,d,h):O=g._observers[S];for(var D=l(v),P=D.length,me,ae=typeof k=="function",te=0;te<P;te++)me=D[te],O.subscribe(me,v[me]||me,ae?k:k&&k[me])}return t(f)?_(y(f)):_(typeof f=="string"?y(f.split(/\s+/)):f),this},w.prototype.stopListeningTo=function(d,f){var h=this._observers;if(!h)return!1;var g=h.length,_,v=!1;if(d&&typeof d!="object")throw TypeError("target should be an object");for(;g-- >0;)_=h[g],(!d||_._target===d)&&(_.unsubscribe(f),v=!0);return v},w.prototype.delimiter=".",w.prototype.setMaxListeners=function(d){d!==r&&(this._maxListeners=d,this._conf||(this._conf={}),this._conf.maxListeners=d)},w.prototype.getMaxListeners=function(){return this._maxListeners},w.prototype.event="",w.prototype.once=function(d,f,h){return this._once(d,f,!1,h)},w.prototype.prependOnceListener=function(d,f,h){return this._once(d,f,!0,h)},w.prototype._once=function(d,f,h,g){return this._many(d,1,f,h,g)},w.prototype.many=function(d,f,h,g){return this._many(d,f,h,!1,g)},w.prototype.prependMany=function(d,f,h,g){return this._many(d,f,h,!0,g)},w.prototype._many=function(d,f,h,g,_){var v=this;if(typeof h!="function")throw new Error("many only accepts instances of Function");function k(){return--f===0&&v.off(d,k),h.apply(this,arguments)}return k._origin=h,this._on(d,k,g,_)},w.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var d=arguments[0],f,h=this.wildcard,g,_,v,k,S;if(d==="newListener"&&!this._newListener&&!this._events.newListener)return!1;if(h&&(f=d,d!=="newListener"&&d!=="removeListener"&&typeof d=="object")){if(_=d.length,s){for(v=0;v<_;v++)if(typeof d[v]=="symbol"){S=!0;break}}S||(d=d.join(this.delimiter))}var O=arguments.length,D;if(this._all&&this._all.length)for(D=this._all.slice(),v=0,_=D.length;v<_;v++)switch(this.event=d,O){case 1:D[v].call(this,d);break;case 2:D[v].call(this,d,arguments[1]);break;case 3:D[v].call(this,d,arguments[1],arguments[2]);break;default:D[v].apply(this,arguments)}if(h)D=[],G.call(this,D,f,this.listenerTree,0,_);else if(D=this._events[d],typeof D=="function"){switch(this.event=d,O){case 1:D.call(this);break;case 2:D.call(this,arguments[1]);break;case 3:D.call(this,arguments[1],arguments[2]);break;default:for(g=new Array(O-1),k=1;k<O;k++)g[k-1]=arguments[k];D.apply(this,g)}return!0}else D&&(D=D.slice());if(D&&D.length){if(O>3)for(g=new Array(O-1),k=1;k<O;k++)g[k-1]=arguments[k];for(v=0,_=D.length;v<_;v++)switch(this.event=d,O){case 1:D[v].call(this);break;case 2:D[v].call(this,arguments[1]);break;case 3:D[v].call(this,arguments[1],arguments[2]);break;default:D[v].apply(this,g)}return!0}else if(!this.ignoreErrors&&!this._all&&d==="error")throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},w.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||c.call(this);var d=arguments[0],f=this.wildcard,h,g,_,v,k,S;if(d==="newListener"&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(f&&(h=d,d!=="newListener"&&d!=="removeListener"&&typeof d=="object")){if(v=d.length,s){for(k=0;k<v;k++)if(typeof d[k]=="symbol"){g=!0;break}}g||(d=d.join(this.delimiter))}var O=[],D=arguments.length,P;if(this._all)for(k=0,v=this._all.length;k<v;k++)switch(this.event=d,D){case 1:O.push(this._all[k].call(this,d));break;case 2:O.push(this._all[k].call(this,d,arguments[1]));break;case 3:O.push(this._all[k].call(this,d,arguments[1],arguments[2]));break;default:O.push(this._all[k].apply(this,arguments))}if(f?(P=[],G.call(this,P,h,this.listenerTree,0)):P=this._events[d],typeof P=="function")switch(this.event=d,D){case 1:O.push(P.call(this));break;case 2:O.push(P.call(this,arguments[1]));break;case 3:O.push(P.call(this,arguments[1],arguments[2]));break;default:for(_=new Array(D-1),S=1;S<D;S++)_[S-1]=arguments[S];O.push(P.apply(this,_))}else if(P&&P.length){if(P=P.slice(),D>3)for(_=new Array(D-1),S=1;S<D;S++)_[S-1]=arguments[S];for(k=0,v=P.length;k<v;k++)switch(this.event=d,D){case 1:O.push(P[k].call(this));break;case 2:O.push(P[k].call(this,arguments[1]));break;case 3:O.push(P[k].call(this,arguments[1],arguments[2]));break;default:O.push(P[k].apply(this,_))}}else if(!this.ignoreErrors&&!this._all&&d==="error")return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(O)},w.prototype.on=function(d,f,h){return this._on(d,f,!1,h)},w.prototype.prependListener=function(d,f,h){return this._on(d,f,!0,h)},w.prototype.onAny=function(d){return this._onAny(d,!1)},w.prototype.prependAny=function(d){return this._onAny(d,!0)},w.prototype.addListener=w.prototype.on,w.prototype._onAny=function(d,f){if(typeof d!="function")throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),f?this._all.unshift(d):this._all.push(d),this},w.prototype._on=function(d,f,h,g){if(typeof d=="function")return this._onAny(d,f),this;if(typeof f!="function")throw new Error("on only accepts instances of Function");this._events||c.call(this);var _=this,v;return g!==r&&(v=A.call(this,d,f,g),f=v[0],_=v[1]),this._newListener&&this.emit("newListener",d,f),this.wildcard?(K.call(this,d,f,h),_):(this._events[d]?(typeof this._events[d]=="function"&&(this._events[d]=[this._events[d]]),h?this._events[d].unshift(f):this._events[d].push(f),!this._events[d].warned&&this._maxListeners>0&&this._events[d].length>this._maxListeners&&(this._events[d].warned=!0,m.call(this,this._events[d].length,d))):this._events[d]=f,_)},w.prototype.off=function(d,f){if(typeof f!="function")throw new Error("removeListener only takes instances of Function");var h,g=[];if(this.wildcard){var _=typeof d=="string"?d.split(this.delimiter):d.slice();if(g=G.call(this,null,_,this.listenerTree,0),!g)return this}else{if(!this._events[d])return this;h=this._events[d],g.push({_listeners:h})}for(var v=0;v<g.length;v++){var k=g[v];if(h=k._listeners,t(h)){for(var S=-1,O=0,D=h.length;O<D;O++)if(h[O]===f||h[O].listener&&h[O].listener===f||h[O]._origin&&h[O]._origin===f){S=O;break}if(S<0)continue;return this.wildcard?k._listeners.splice(S,1):this._events[d].splice(S,1),h.length===0&&(this.wildcard?delete k._listeners:delete this._events[d]),this._removeListener&&this.emit("removeListener",d,f),this}else(h===f||h.listener&&h.listener===f||h._origin&&h._origin===f)&&(this.wildcard?delete k._listeners:delete this._events[d],this._removeListener&&this.emit("removeListener",d,f))}return this.listenerTree&&Fe(this.listenerTree),this},w.prototype.offAny=function(d){var f=0,h=0,g;if(d&&this._all&&this._all.length>0){for(g=this._all,f=0,h=g.length;f<h;f++)if(d===g[f])return g.splice(f,1),this._removeListener&&this.emit("removeListenerAny",d),this}else{if(g=this._all,this._removeListener)for(f=0,h=g.length;f<h;f++)this.emit("removeListenerAny",g[f]);this._all=[]}return this},w.prototype.removeListener=w.prototype.off,w.prototype.removeAllListeners=function(d){if(d===r)return!this._events||c.call(this),this;if(this.wildcard){var f=G.call(this,null,d,this.listenerTree,0),h,g;if(!f)return this;for(g=0;g<f.length;g++)h=f[g],h._listeners=null;this.listenerTree&&Fe(this.listenerTree)}else this._events&&(this._events[d]=null);return this},w.prototype.listeners=function(d){var f=this._events,h,g,_,v,k;if(d===r){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!f)return[];for(h=l(f),v=h.length,_=[];v-- >0;)g=f[h[v]],typeof g=="function"?_.push(g):_.push.apply(_,g);return _}else{if(this.wildcard){if(k=this.listenerTree,!k)return[];var S=[],O=typeof d=="string"?d.split(this.delimiter):d.slice();return G.call(this,S,O,k,0),S}return f?(g=f[d],g?typeof g=="function"?[g]:g:[]):[]}},w.prototype.eventNames=function(d){var f=this._events;return this.wildcard?Pe.call(this,this.listenerTree,[],null,d):f?l(f):[]},w.prototype.listenerCount=function(d){return this.listeners(d).length},w.prototype.hasListeners=function(d){if(this.wildcard){var f=[],h=typeof d=="string"?d.split(this.delimiter):d.slice();return G.call(this,f,h,this.listenerTree,0),f.length>0}var g=this._events,_=this._all;return!!(_&&_.length||g&&(d===r?l(g).length:g[d]))},w.prototype.listenersAny=function(){return this._all?this._all:[]},w.prototype.waitFor=function(d,f){var h=this,g=typeof f;return g==="number"?f={timeout:f}:g==="function"&&(f={filter:f}),f=M(f,{timeout:0,filter:r,handleError:!1,Promise,overload:!1},{filter:W,Promise:U}),re(f.Promise,function(_,v,k){function S(){var O=f.filter;if(!(O&&!O.apply(h,arguments)))if(h.off(d,S),f.handleError){var D=arguments[0];D?v(D):_(T.apply(null,arguments).slice(1))}else _(T.apply(null,arguments))}k(function(){h.off(d,S)}),h._on(d,S,!1)},{timeout:f.timeout,overload:f.overload})};function B(d,f,h){h=M(h,{Promise,timeout:0,overload:!1},{Promise:U});var g=h.Promise;return re(g,function(_,v,k){var S;if(typeof d.addEventListener=="function"){S=function(){_(T.apply(null,arguments))},k(function(){d.removeEventListener(f,S)}),d.addEventListener(f,S,{once:!0});return}var O=function(){D&&d.removeListener("error",D),_(T.apply(null,arguments))},D;f!=="error"&&(D=function(P){d.removeListener(f,O),v(P)},d.once("error",D)),k(function(){D&&d.removeListener("error",D),d.removeListener(f,O)}),d.once(f,O)},{timeout:h.timeout,overload:h.overload})}var C=w.prototype;if(Object.defineProperties(w,{defaultMaxListeners:{get:function(){return C._maxListeners},set:function(d){if(typeof d!="number"||d<0||Number.isNaN(d))throw TypeError("n must be a non-negative number");C._maxListeners=d},enumerable:!0},once:{value:B,writable:!0,configurable:!0}}),Object.defineProperties(C,{_maxListeners:{value:n,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),typeof define=="function"&&define.amd)define(function(){return w});else if(typeof rT=="object")nT.exports=w;else{var X=new Function("","return this")();X.EventEmitter2=w}})()});var _r=b(un=>{"use strict";un.__esModule=!0;var fA;(function(r){r.AND="AND",r.OR="OR",r.XOR="XOR",r.NOT="NOT"})(fA=un.Operators||(un.Operators={}));var pA;(function(r){r.OPEN_PARENTHESIS="(",r.CLOSE_PARENTHESIS=")"})(pA=un.StructuralCharacters||(un.StructuralCharacters={}));var mA;(function(r){r.IDENTIFIER="IDENTIFIER",r.OPERATOR="OPERATOR",r.STRUCTURAL_CHARACTER="STRUCTURAL_CHARACTER",r.EOF="EOF",r.COMMENT="COMMENT"})(mA=un.Tokens||(un.Tokens={}))});var Gf=b(Jo=>{"use strict";Jo.__esModule=!0;var Ie=_r();Jo.OPERATOR_PRECEDENCE={NOT:0,XOR:1,AND:2,OR:3};Jo.VALID_TOKENS={identifierOnly:[{name:Ie.Tokens.IDENTIFIER},{name:Ie.Tokens.STRUCTURAL_CHARACTER,value:Ie.StructuralCharacters.OPEN_PARENTHESIS}],identifierOrNot:[{name:Ie.Tokens.IDENTIFIER},{name:Ie.Tokens.STRUCTURAL_CHARACTER,value:Ie.StructuralCharacters.OPEN_PARENTHESIS},{name:Ie.Tokens.OPERATOR,value:Ie.Operators.NOT}],binaryOperator:[{name:Ie.Tokens.OPERATOR,value:Ie.Operators.AND},{name:Ie.Tokens.OPERATOR,value:Ie.Operators.OR},{name:Ie.Tokens.OPERATOR,value:Ie.Operators.XOR}],binaryOperatorOrClose:[{name:Ie.Tokens.OPERATOR,value:Ie.Operators.AND},{name:Ie.Tokens.OPERATOR,value:Ie.Operators.OR},{name:Ie.Tokens.OPERATOR,value:Ie.Operators.XOR},{name:Ie.Tokens.STRUCTURAL_CHARACTER,value:Ie.StructuralCharacters.CLOSE_PARENTHESIS}]}});var Bf=b(Fr=>{"use strict";Fr.__esModule=!0;var Oi=_r();Fr.STRUCTURAL_CHARACTERS={"(":Oi.StructuralCharacters.OPEN_PARENTHESIS,")":Oi.StructuralCharacters.CLOSE_PARENTHESIS};Fr.OPERATORS={AND:Oi.Operators.AND,OR:Oi.Operators.OR,XOR:Oi.Operators.XOR,NOT:Oi.Operators.NOT};Fr.SEPARATORS=new Set([32,9,10,13].map(function(r){return String.fromCodePoint(r)}));Fr.QUOTED_IDENTIFIER_DELIMITER=String.fromCodePoint(34);Fr.COMMENT_DELIMITER=String.fromCodePoint(35);Fr.EOL=String.fromCodePoint(10);Fr.ESCAPE_CHARACTER=String.fromCodePoint(92)});var qT=b(Lr=>{"use strict";var Hf=Lr&&Lr.__assign||function(){return Hf=Object.assign||function(r){for(var e,t=1,n=arguments.length;t<n;t++){e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])}return r},Hf.apply(this,arguments)};Lr.__esModule=!0;var WT=_r(),Fn=Bf();Lr.createResult=function(r,e,t){return{token:Hf({name:r},e!==null?{value:e}:{}),remainingString:t}};Lr.getComment=function(r){for(var e=r.length,t=0;t<r.length;t+=1){var n=r[t];if(n===Fn.EOL){e=t;break}}return Lr.createResult(WT.Tokens.COMMENT,r.slice(0,e),r.slice(e+1))};Lr.getQuotedIdentifier=function(r){for(var e=!1,t="",n=null,i=0;i<r.length;i+=1){var s=r[i];if(n===null)s===Fn.QUOTED_IDENTIFIER_DELIMITER?e?t=t.slice(-1)+Fn.QUOTED_IDENTIFIER_DELIMITER:n=i:(s===Fn.ESCAPE_CHARACTER?e=!0:e=!1,t=t+=s);else{if(!Fn.SEPARATORS.has(s)&&!Fn.STRUCTURAL_CHARACTERS[s])throw new Error("Unexpected character: "+s+" Expected ) character or separator");break}}if(n===null)throw new Error("Unexpected end of expression: expected "+Fn.QUOTED_IDENTIFIER_DELIMITER+" character");return Lr.createResult(WT.Tokens.IDENTIFIER,t,r.slice(n+1))}});var YT=b(zf=>{"use strict";zf.__esModule=!0;var Di=_r(),Nt=Bf(),Si=qT();zf.lex=function(r){for(var e=null,t=null,n=null,i=0;i<r.length;i+=1){var s=r[i];if(e===null){if(!Nt.SEPARATORS.has(s)){var a=Nt.STRUCTURAL_CHARACTERS[s];if(a){var o=r[i+1];if(a===Di.StructuralCharacters.CLOSE_PARENTHESIS&&o&&!Nt.SEPARATORS.has(o)&&o!==Di.StructuralCharacters.CLOSE_PARENTHESIS)throw new Error("Unexpected character: "+o+". A closing parenthesis should be followed by another closing parenthesis or whitespace");return Si.createResult(Di.Tokens.STRUCTURAL_CHARACTER,Nt.STRUCTURAL_CHARACTERS[s],r.slice(i+1))}if(s===Nt.QUOTED_IDENTIFIER_DELIMITER)return Si.getQuotedIdentifier(r.slice(i+1));if(s===Nt.COMMENT_DELIMITER)return Si.getComment(r.slice(i+1));e=i}}else if(Nt.SEPARATORS.has(s)||Nt.STRUCTURAL_CHARACTERS[s]){t=i,n=s;break}else if(s===Nt.QUOTED_IDENTIFIER_DELIMITER||s===Nt.COMMENT_DELIMITER)throw new Error("Unexpected character: "+s)}if(e!==null){t=t!=null?t:r.length;var u=r.slice(e,t),l=r.slice(t);if(Nt.OPERATORS[u]){if(n&&!Nt.SEPARATORS.has(n))throw new Error("Unexpected character: "+n+". Operators should be separated using whitespace");return Si.createResult(Di.Tokens.OPERATOR,Nt.OPERATORS[u],l)}else return Si.createResult(Di.Tokens.IDENTIFIER,u,l)}return Si.createResult(Di.Tokens.EOF,null,"")}});var $T=b(Ur=>{"use strict";var hA=Ur&&Ur.__spreadArrays||function(){for(var r=0,e=0,t=arguments.length;e<t;e++)r+=arguments[e].length;for(var n=Array(r),i=0,e=0;e<t;e++)for(var s=arguments[e],a=0,o=s.length;a<o;a++,i++)n[i]=s[a];return n};Ur.__esModule=!0;var gA=YT(),Ri=_r(),eu=Gf();Ur.newTokenGenerator=function(r){var e=r;return function(t,n){for(n===void 0&&(n=!1);;){var i=gA.lex(e),s=i.token,a=i.remainingString;if(e=a,s.name!==Ri.Tokens.COMMENT)return Ur.validateToken(s,t,n),s}}};Ur.getValue=function(r,e){var t=r(eu.VALID_TOKENS.identifierOrNot),n=t.value===Ri.Operators.NOT;n&&(t=r(eu.VALID_TOKENS.identifierOnly));var i=t.name===Ri.Tokens.STRUCTURAL_CHARACTER?e(r,!0):[t];return n?hA(i,[{name:Ri.Tokens.OPERATOR,value:Ri.Operators.NOT}]):i};Ur.previousOperatorTakesPrecedent=function(r,e){return eu.OPERATOR_PRECEDENCE[r]<=eu.OPERATOR_PRECEDENCE[e]};Ur.validateToken=function(r,e,t){if(t===void 0&&(t=!1),r.name===Ri.Tokens.EOF){if(t)return;throw new Error("Unexpected end of expression")}for(var n=0,i=e;n<i.length;n++){var s=i[n];if(s.name===r.name&&(!s.value||s.value===r.value))return}throw new TypeError("Invalid token")}});var Kf=b(aa=>{"use strict";var Mi=aa&&aa.__spreadArrays||function(){for(var r=0,e=0,t=arguments.length;e<t;e++)r+=arguments[e].length;for(var n=Array(r),i=0,e=0;e<t;e++)for(var s=arguments[e],a=0,o=s.length;a<o;a++,i++)n[i]=s[a];return n};aa.__esModule=!0;var jT=_r(),GT=Gf(),tu=$T();aa.parse=function(r){if(typeof r!="string")throw new Error("Expected string but received "+typeof r);var e=tu.newTokenGenerator(r);return Vf(e)};var Vf=function(r,e){e===void 0&&(e=!1);for(var t=Mi(tu.getValue(r,Vf)),n=[];;){var i=e?GT.VALID_TOKENS.binaryOperatorOrClose:GT.VALID_TOKENS.binaryOperator,s=r(i,!e);if(s.name===jT.Tokens.EOF||s.name===jT.Tokens.STRUCTURAL_CHARACTER)return Mi(t,Mi(n).reverse());for(;n.length;){var a=n[n.length-1]||null;if(a&&tu.previousOperatorTakesPrecedent(a.value,s.value))t=Mi(t,[a]),n=n.slice(0,-1);else break}n=Mi(n,[s]),t=Mi(t,tu.getValue(r,Vf))}}});var Qf=b(Wr=>{"use strict";Wr.__esModule=!0;var BT=_r();Wr.andUtil=function(r,e){return r&&e};Wr.orUtil=function(r,e){return r||e};Wr.xorUtil=function(r,e){return r!==e};Wr.notUtil=function(r){return!r};Wr.isIdentifier=function(r){var e=r.name,t=r.value;return e===BT.Tokens.IDENTIFIER&&typeof t=="string"};Wr.isOperator=function(r){var e=r.name,t=r.value;return e===BT.Tokens.OPERATOR&&typeof t=="string"};Wr.throwInvalidExpression=function(r){throw new TypeError("Invalid postfix expression: "+r)}});var HT=b(Jf=>{"use strict";var oa;Jf.__esModule=!0;var Xf=_r(),Zf=Qf();Jf.OPERATOR_MAP=(oa={},oa[Xf.Operators.AND]=Zf.andUtil,oa[Xf.Operators.OR]=Zf.orUtil,oa[Xf.Operators.XOR]=Zf.xorUtil,oa)});var VT=b(Un=>{"use strict";var ep=Un&&Un.__spreadArrays||function(){for(var r=0,e=0,t=arguments.length;e<t;e++)r+=arguments[e].length;for(var n=Array(r),i=0,e=0;e<t;e++)for(var s=arguments[e],a=0,o=s.length;a<o;a++,i++)n[i]=s[a];return n};Un.__esModule=!0;var yA=Kf(),zT=_r(),_A=HT(),Ln=Qf();Un.getEvaluator=function(r){var e=yA.parse(r);return function(t){return Un.evaluate(e,t)}};Un.evaluate=function(r,e){if(!Array.isArray(r))throw new Error(r+" should be an array. evaluate takes in a parsed expression. Use in combination with parse or use getEvaluator");var t=r.reduce(function(n,i,s){if(!(i&&(Ln.isIdentifier(i)||Ln.isOperator(i))))throw new Error("Invalid token: "+i+". Found in parsed expression at index "+s);if(i.name===zT.Tokens.IDENTIFIER)return ep(n,[Boolean(e[i.value])]);var a=n[n.length-2],o=n[n.length-1];if(i.value===zT.Operators.NOT)return o===void 0&&Ln.throwInvalidExpression("missing identifier"),ep(n.slice(0,-1),[Ln.notUtil(o)]);(o===void 0||a===void 0)&&Ln.throwInvalidExpression("missing identifier");var u=_A.OPERATOR_MAP[i.value];return u||Ln.throwInvalidExpression("unknown operator"),ep(n.slice(0,-2),[u(a,o)])},[]);return t.length!==1&&Ln.throwInvalidExpression("too many identifiers after evaluation"),t[0]}});var QT=b(ua=>{"use strict";ua.__esModule=!0;var KT=VT();ua.getEvaluator=KT.getEvaluator;ua.evaluate=KT.evaluate;var TA=Kf();ua.parse=TA.parse});var OA={};Eb(OA,{default:()=>pu});module.exports=wb(OA);var mb=require("obsidian");var pi=require("obsidian");var SA=new Error("timeout while waiting for mutex to become available"),RA=new Error("mutex already locked"),kb=new Error("request for lock canceled"),Ob=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(p){a(p)}}function u(c){try{l(n.throw(c))}catch(p){a(p)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},yu=class{constructor(e,t=kb){this._value=e,this._cancelError=t,this._weightedQueues=[],this._weightedWaiters=[]}acquire(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((t,n)=>{this._weightedQueues[e-1]||(this._weightedQueues[e-1]=[]),this._weightedQueues[e-1].push({resolve:t,reject:n}),this._dispatch()})}runExclusive(e,t=1){return Ob(this,void 0,void 0,function*(){let[n,i]=yield this.acquire(t);try{return yield e(n)}finally{i()}})}waitForUnlock(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise(t=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),this._weightedWaiters[e-1].push(t),this._dispatch()})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatch()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatch()}cancel(){this._weightedQueues.forEach(e=>e.forEach(t=>t.reject(this._cancelError))),this._weightedQueues=[]}_dispatch(){var e;for(let t=this._value;t>0;t--){let n=(e=this._weightedQueues[t-1])===null||e===void 0?void 0:e.shift();if(!n)continue;let i=this._value,s=t;this._value-=t,t=this._value+1,n.resolve([i,this._newReleaser(s)])}this._drainUnlockWaiters()}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){for(let e=this._value;e>0;e--)!this._weightedWaiters[e-1]||(this._weightedWaiters[e-1].forEach(t=>t()),this._weightedWaiters[e-1]=[])}},Db=function(r,e,t,n){function i(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(n.next(c))}catch(p){a(p)}}function u(c){try{l(n.throw(c))}catch(p){a(p)}}function l(c){c.done?s(c.value):i(c.value).then(o,u)}l((n=n.apply(r,e||[])).next())})},Ta=class{constructor(e){this._semaphore=new yu(1,e)}acquire(){return Db(this,void 0,void 0,function*(){let[,e]=yield this._semaphore.acquire();return e})}runExclusive(e){return this._semaphore.runExclusive(()=>e())}isLocked(){return this._semaphore.isLocked()}waitForUnlock(){return this._semaphore.waitForUnlock()}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}};var fo=Wi(Be());var yt=class{constructor(e,t){this.start=e,this.end=t,t.isBefore(e)&&(this.start=t,this.end=e),this.start=this.start.startOf("day"),this.end=this.end.startOf("day")}static buildRelative(e){let t=e==="week"?"isoWeek":e;return new yt(window.moment().startOf(t).startOf("day"),window.moment().endOf(t).startOf("day"))}static buildInvalid(){return new yt(window.moment.invalid(),window.moment.invalid())}isValid(){return this.start.isValid()&&this.end.isValid()}moveToPrevious(e){let t=window.moment.duration(1,e);this.start.subtract(t),this.end.subtract(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}moveToNext(e){let t=window.moment.duration(1,e);this.start.add(t),this.end.add(t),(e==="month"||e==="quarter")&&(this.end=this.end.endOf(e).startOf("day"))}};var xt=class{static parseDate(e,t=!1){return window.moment(fo.parseDate(e,void 0,{forwardDate:t})).startOf("day")}static parseDateRange(e,t=!1){let n=[xt.parseRelativeDateRange,xt.parseNumberedDateRange,xt.parseAbsoluteDateRange];for(let i of n){let s=i(e,t);if(s.isValid())return s}return yt.buildInvalid()}static parseAbsoluteDateRange(e,t){let n=fo.parse(e,void 0,{forwardDate:t});if(n.length===0)return yt.buildInvalid();let i=n[0].start,s=n[1]&&n[1].start?n[1].start:i,a=window.moment(i.date()),o=window.moment(s.date());return new yt(a,o)}static parseRelativeDateRange(e,t){let n=/(last|this|next) (week|month|quarter|year)/,i=e.match(n);if(i&&i.length===3){let s=i[1],a=i[2],o=yt.buildRelative(a);switch(s){case"last":o.moveToPrevious(a);break;case"next":o.moveToNext(a);break}return o}return yt.buildInvalid()}static parseNumberedDateRange(e,t){let n=[[/^\s*[0-9]{4}\s*$/,"YYYY","year"],[/^\s*[0-9]{4}-Q[1-4]\s*$/,"YYYY-Q","quarter"],[/^\s*[0-9]{4}-[0-9]{2}\s*$/,"YYYY-MM","month"],[/^\s*[0-9]{4}-W[0-9]{2}\s*$/,"YYYY-WW","isoWeek"]];for(let[i,s,a]of n){let o=e.match(i);if(o){let u=o[0].trim();return new yt(window.moment(u,s).startOf(a),window.moment(u,s).endOf(a))}}return yt.buildInvalid()}};var GM={td:"today",tm:"tomorrow",yd:"yesterday",tw:"this week",nw:"next week",weekend:"sat",we:"sat"};function ui(r){for(let[e,t]of Object.entries(GM))r=r.replace(RegExp(`\\b${e}\\s`,"i"),t);return r}var po=["MO","TU","WE","TH","FR","SA","SU"],je=function(){function r(e,t){if(t===0)throw new Error("Can't create weekday with n == 0");this.weekday=e,this.n=t}return r.fromStr=function(e){return new r(po.indexOf(e))},r.prototype.nth=function(e){return this.n===e?this:new r(this.weekday,e)},r.prototype.equals=function(e){return this.weekday===e.weekday&&this.n===e.n},r.prototype.toString=function(){var e=po[this.weekday];return this.n&&(e=(this.n>0?"+":"")+String(this.n)+e),e},r.prototype.getJsWeekday=function(){return this.weekday===6?0:this.weekday+1},r}();var Me=function(r){return r!=null},At=function(r){return typeof r=="number"},uf=function(r){return typeof r=="string"&&po.includes(r)},at=Array.isArray,Bt=function(r,e){e===void 0&&(e=r),arguments.length===1&&(e=r,r=0);for(var t=[],n=r;n<e;n++)t.push(n);return t};var le=function(r,e){var t=0,n=[];if(at(r))for(;t<e;t++)n[t]=[].concat(r);else for(;t<e;t++)n[t]=r;return n},Qy=function(r){return at(r)?r:[r]};function En(r,e,t){t===void 0&&(t=" ");var n=String(r);return e=e>>0,n.length>e?String(n):(e=e-n.length,e>t.length&&(t+=le(t,e/t.length)),t.slice(0,e)+String(n))}var Xy=function(r,e,t){var n=r.split(e);return t?n.slice(0,t).concat([n.slice(t).join(e)]):n},ut=function(r,e){var t=r%e;return t*e<0?t+e:t},mo=function(r,e){return{div:Math.floor(r/e),mod:ut(r,e)}},Ct=function(r){return!Me(r)||r.length===0},Ue=function(r){return!Ct(r)},de=function(r,e){return Ue(r)&&r.indexOf(e)!==-1};var Qr;(function(r){r.MONTH_DAYS=[31,28,31,30,31,30,31,31,30,31,30,31],r.ONE_DAY=1e3*60*60*24,r.MAXYEAR=9999,r.ORDINAL_BASE=new Date(Date.UTC(1970,0,1)),r.PY_WEEKDAYS=[6,0,1,2,3,4,5],r.getYearDay=function(e){var t=new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate());return Math.ceil((t.valueOf()-new Date(e.getUTCFullYear(),0,1).valueOf())/r.ONE_DAY)+1},r.isLeapYear=function(e){return e%4===0&&e%100!==0||e%400===0},r.isDate=function(e){return e instanceof Date},r.isValidDate=function(e){return r.isDate(e)&&!isNaN(e.getTime())},r.tzOffset=function(e){return e.getTimezoneOffset()*60*1e3},r.daysBetween=function(e,t){var n=e.getTime()-r.tzOffset(e),i=t.getTime()-r.tzOffset(t),s=n-i;return Math.round(s/r.ONE_DAY)},r.toOrdinal=function(e){return r.daysBetween(e,r.ORDINAL_BASE)},r.fromOrdinal=function(e){return new Date(r.ORDINAL_BASE.getTime()+e*r.ONE_DAY)},r.getMonthDays=function(e){var t=e.getUTCMonth();return t===1&&r.isLeapYear(e.getUTCFullYear())?29:r.MONTH_DAYS[t]},r.getWeekday=function(e){return r.PY_WEEKDAYS[e.getUTCDay()]},r.monthRange=function(e,t){var n=new Date(Date.UTC(e,t,1));return[r.getWeekday(n),r.getMonthDays(n)]},r.combine=function(e,t){return t=t||e,new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()))},r.clone=function(e){var t=new Date(e.getTime());return t},r.cloneDates=function(e){for(var t=[],n=0;n<e.length;n++)t.push(r.clone(e[n]));return t},r.sort=function(e){e.sort(function(t,n){return t.getTime()-n.getTime()})},r.timeToUntilString=function(e,t){t===void 0&&(t=!0);var n=new Date(e);return[En(n.getUTCFullYear().toString(),4,"0"),En(n.getUTCMonth()+1,2,"0"),En(n.getUTCDate(),2,"0"),"T",En(n.getUTCHours(),2,"0"),En(n.getUTCMinutes(),2,"0"),En(n.getUTCSeconds(),2,"0"),t?"Z":""].join("")},r.untilStringToDate=function(e){var t=/^(\d{4})(\d{2})(\d{2})(T(\d{2})(\d{2})(\d{2})Z?)?$/,n=t.exec(e);if(!n)throw new Error("Invalid UNTIL value: ".concat(e));return new Date(Date.UTC(parseInt(n[1],10),parseInt(n[2],10)-1,parseInt(n[3],10),parseInt(n[5],10)||0,parseInt(n[6],10)||0,parseInt(n[7],10)||0))}})(Qr||(Qr={}));var z=Qr;var BM=function(){function r(e,t){this.minDate=null,this.maxDate=null,this._result=[],this.total=0,this.method=e,this.args=t,e==="between"?(this.maxDate=t.inc?t.before:new Date(t.before.getTime()-1),this.minDate=t.inc?t.after:new Date(t.after.getTime()+1)):e==="before"?this.maxDate=t.inc?t.dt:new Date(t.dt.getTime()-1):e==="after"&&(this.minDate=t.inc?t.dt:new Date(t.dt.getTime()+1))}return r.prototype.accept=function(e){++this.total;var t=this.minDate&&e<this.minDate,n=this.maxDate&&e>this.maxDate;if(this.method==="between"){if(t)return!0;if(n)return!1}else if(this.method==="before"){if(n)return!1}else if(this.method==="after")return t?!0:(this.add(e),!1);return this.add(e)},r.prototype.add=function(e){return this._result.push(e),!0},r.prototype.getValue=function(){var e=this._result;switch(this.method){case"all":case"between":return e;case"before":case"after":default:return e.length?e[e.length-1]:null}},r.prototype.clone=function(){return new r(this.method,this.args)},r}(),Mr=BM;var b_=Wi(T_(),1),{__extends:ci,__assign:_t,__rest:mN,__decorate:hN,__param:gN,__metadata:yN,__awaiter:_N,__generator:TN,__exportStar:bN,__createBinding:vN,__values:EN,__read:wN,__spread:kN,__spreadArrays:ON,__spreadArray:x,__await:DN,__asyncGenerator:SN,__asyncDelegator:RN,__asyncValues:MN,__makeTemplateObject:xN,__importStar:AN,__importDefault:CN,__classPrivateFieldGet:PN,__classPrivateFieldSet:NN,__classPrivateFieldIn:IN}=b_.default;var HM=function(r){ci(e,r);function e(t,n,i){var s=r.call(this,t,n)||this;return s.iterator=i,s}return e.prototype.add=function(t){return this.iterator(t,this._result.length)?(this._result.push(t),!0):!1},e}(Mr),cf=HM;var zM={dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],tokens:{SKIP:/^[ \r\n\t]+|^\.$/,number:/^[1-9][0-9]*/,numberAsText:/^(one|two|three)/i,every:/^every/i,"day(s)":/^days?/i,"weekday(s)":/^weekdays?/i,"week(s)":/^weeks?/i,"hour(s)":/^hours?/i,"minute(s)":/^minutes?/i,"month(s)":/^months?/i,"year(s)":/^years?/i,on:/^(on|in)/i,at:/^(at)/i,the:/^the/i,first:/^first/i,second:/^second/i,third:/^third/i,nth:/^([1-9][0-9]*)(\.|th|nd|rd|st)/i,last:/^last/i,for:/^for/i,"time(s)":/^times?/i,until:/^(un)?til/i,monday:/^mo(n(day)?)?/i,tuesday:/^tu(e(s(day)?)?)?/i,wednesday:/^we(d(n(esday)?)?)?/i,thursday:/^th(u(r(sday)?)?)?/i,friday:/^fr(i(day)?)?/i,saturday:/^sa(t(urday)?)?/i,sunday:/^su(n(day)?)?/i,january:/^jan(uary)?/i,february:/^feb(ruary)?/i,march:/^mar(ch)?/i,april:/^apr(il)?/i,may:/^may/i,june:/^june?/i,july:/^july?/i,august:/^aug(ust)?/i,september:/^sep(t(ember)?)?/i,october:/^oct(ober)?/i,november:/^nov(ember)?/i,december:/^dec(ember)?/i,comma:/^(,\s*|(and|or)\s*)+/i}},wn=zM;var v_=function(r,e){return r.indexOf(e)!==-1},VM=function(r){return r.toString()},KM=function(r,e,t){return"".concat(e," ").concat(t,", ").concat(r)},QM=function(){function r(e,t,n,i){if(t===void 0&&(t=VM),n===void 0&&(n=wn),i===void 0&&(i=KM),this.text=[],this.language=n||wn,this.gettext=t,this.dateFormatter=i,this.rrule=e,this.options=e.options,this.origOptions=e.origOptions,this.origOptions.bymonthday){var s=[].concat(this.options.bymonthday),a=[].concat(this.options.bynmonthday);s.sort(function(c,p){return c-p}),a.sort(function(c,p){return p-c}),this.bymonthday=s.concat(a),this.bymonthday.length||(this.bymonthday=null)}if(Me(this.origOptions.byweekday)){var o=at(this.origOptions.byweekday)?this.origOptions.byweekday:[this.origOptions.byweekday],u=String(o);this.byweekday={allWeeks:o.filter(function(c){return!c.n}),someWeeks:o.filter(function(c){return Boolean(c.n)}),isWeekdays:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")===-1&&u.indexOf("SU")===-1,isEveryDay:u.indexOf("MO")!==-1&&u.indexOf("TU")!==-1&&u.indexOf("WE")!==-1&&u.indexOf("TH")!==-1&&u.indexOf("FR")!==-1&&u.indexOf("SA")!==-1&&u.indexOf("SU")!==-1};var l=function(c,p){return c.weekday-p.weekday};this.byweekday.allWeeks.sort(l),this.byweekday.someWeeks.sort(l),this.byweekday.allWeeks.length||(this.byweekday.allWeeks=null),this.byweekday.someWeeks.length||(this.byweekday.someWeeks=null)}else this.byweekday=null}return r.isFullyConvertible=function(e){var t=!0;if(!(e.options.freq in r.IMPLEMENTED)||e.origOptions.until&&e.origOptions.count)return!1;for(var n in e.origOptions){if(v_(["dtstart","wkst","freq"],n))return!0;if(!v_(r.IMPLEMENTED[e.options.freq],n))return!1}return t},r.prototype.isFullyConvertible=function(){return r.isFullyConvertible(this.rrule)},r.prototype.toString=function(){var e=this.gettext;if(!(this.options.freq in r.IMPLEMENTED))return e("RRule error: Unable to fully convert this rrule to text");if(this.text=[e("every")],this[Y.FREQUENCIES[this.options.freq]](),this.options.until){this.add(e("until"));var t=this.options.until;this.add(this.dateFormatter(t.getUTCFullYear(),this.language.monthNames[t.getUTCMonth()],t.getUTCDate()))}else this.options.count&&this.add(e("for")).add(this.options.count.toString()).add(this.plural(this.options.count)?e("times"):e("time"));return this.isFullyConvertible()||this.add(e("(~ approximate)")),this.text.join("")},r.prototype.HOURLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("hours"):e("hour"))},r.prototype.MINUTELY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("minutes"):e("minute"))},r.prototype.DAILY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()),this.byweekday&&this.byweekday.isWeekdays?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(this.plural(this.options.interval)?e("days"):e("day")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday?this._byweekday():this.origOptions.byhour&&this._byhour()},r.prototype.WEEKLY=function(){var e=this.gettext;this.options.interval!==1&&this.add(this.options.interval.toString()).add(this.plural(this.options.interval)?e("weeks"):e("week")),this.byweekday&&this.byweekday.isWeekdays?this.options.interval===1?this.add(this.plural(this.options.interval)?e("weekdays"):e("weekday")):this.add(e("on")).add(e("weekdays")):this.byweekday&&this.byweekday.isEveryDay?this.add(this.plural(this.options.interval)?e("days"):e("day")):(this.options.interval===1&&this.add(e("week")),this.origOptions.bymonth&&(this.add(e("in")),this._bymonth()),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday())},r.prototype.MONTHLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()).add(e("months")),this.plural(this.options.interval)&&this.add(e("in"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("months"):e("month"))),this.bymonthday?this._bymonthday():this.byweekday&&this.byweekday.isWeekdays?this.add(e("on")).add(e("weekdays")):this.byweekday&&this._byweekday()},r.prototype.YEARLY=function(){var e=this.gettext;this.origOptions.bymonth?(this.options.interval!==1&&(this.add(this.options.interval.toString()),this.add(e("years"))),this._bymonth()):(this.options.interval!==1&&this.add(this.options.interval.toString()),this.add(this.plural(this.options.interval)?e("years"):e("year"))),this.bymonthday?this._bymonthday():this.byweekday&&this._byweekday(),this.options.byyearday&&this.add(e("on the")).add(this.list(this.options.byyearday,this.nth,e("and"))).add(e("day")),this.options.byweekno&&this.add(e("in")).add(this.plural(this.options.byweekno.length)?e("weeks"):e("week")).add(this.list(this.options.byweekno,void 0,e("and")))},r.prototype._bymonthday=function(){var e=this.gettext;this.byweekday&&this.byweekday.allWeeks?this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext,e("or"))).add(e("the")).add(this.list(this.bymonthday,this.nth,e("or"))):this.add(e("on the")).add(this.list(this.bymonthday,this.nth,e("and")))},r.prototype._byweekday=function(){var e=this.gettext;this.byweekday.allWeeks&&!this.byweekday.isWeekdays&&this.add(e("on")).add(this.list(this.byweekday.allWeeks,this.weekdaytext)),this.byweekday.someWeeks&&(this.byweekday.allWeeks&&this.add(e("and")),this.add(e("on the")).add(this.list(this.byweekday.someWeeks,this.weekdaytext,e("and"))))},r.prototype._byhour=function(){var e=this.gettext;this.add(e("at")).add(this.list(this.origOptions.byhour,void 0,e("and")))},r.prototype._bymonth=function(){this.add(this.list(this.options.bymonth,this.monthtext,this.gettext("and")))},r.prototype.nth=function(e){e=parseInt(e.toString(),10);var t,n=this.gettext;if(e===-1)return n("last");var i=Math.abs(e);switch(i){case 1:case 21:case 31:t=i+n("st");break;case 2:case 22:t=i+n("nd");break;case 3:case 23:t=i+n("rd");break;default:t=i+n("th")}return e<0?t+" "+n("last"):t},r.prototype.monthtext=function(e){return this.language.monthNames[e-1]},r.prototype.weekdaytext=function(e){var t=At(e)?(e+1)%7:e.getJsWeekday();return(e.n?this.nth(e.n)+" ":"")+this.language.dayNames[t]},r.prototype.plural=function(e){return e%100!==1},r.prototype.add=function(e){return this.text.push(" "),this.text.push(e),this},r.prototype.list=function(e,t,n,i){var s=this;i===void 0&&(i=","),at(e)||(e=[e]);var a=function(u,l,c){for(var p="",m=0;m<u.length;m++)m!==0&&(m===u.length-1?p+=" "+c+" ":p+=l+" "),p+=u[m];return p};t=t||function(u){return u.toString()};var o=function(u){return t&&t.call(s,u)};return n?a(e.map(o),i,n):e.map(o).join(i+" ")},r}(),fr=QM;var XM=function(){function r(e){this.done=!0,this.rules=e}return r.prototype.start=function(e){return this.text=e,this.done=!1,this.nextSymbol()},r.prototype.isDone=function(){return this.done&&this.symbol===null},r.prototype.nextSymbol=function(){var e,t;this.symbol=null,this.value=null;do{if(this.done)return!1;var n=void 0;e=null;for(var i in this.rules){n=this.rules[i];var s=n.exec(this.text);s&&(e===null||s[0].length>e[0].length)&&(e=s,t=i)}if(e!=null&&(this.text=this.text.substr(e[0].length),this.text===""&&(this.done=!0)),e==null){this.done=!0,this.symbol=null,this.value=null;return}}while(t==="SKIP");return this.symbol=t,this.value=e,!0},r.prototype.accept=function(e){if(this.symbol===e){if(this.value){var t=this.value;return this.nextSymbol(),t}return this.nextSymbol(),!0}return!1},r.prototype.acceptNumber=function(){return this.accept("number")},r.prototype.expect=function(e){if(this.accept(e))return!0;throw new Error("expected "+e+" but found "+this.symbol)},r}();function qs(r,e){e===void 0&&(e=wn);var t={},n=new XM(e.tokens);if(!n.start(r))return null;return i(),t;function i(){n.expect("every");var m=n.acceptNumber();if(m&&(t.interval=parseInt(m[0],10)),n.isDone())throw new Error("Unexpected end");switch(n.symbol){case"day(s)":t.freq=Y.DAILY,n.nextSymbol()&&(a(),p());break;case"weekday(s)":t.freq=Y.WEEKLY,t.byweekday=[Y.MO,Y.TU,Y.WE,Y.TH,Y.FR],n.nextSymbol(),p();break;case"week(s)":t.freq=Y.WEEKLY,n.nextSymbol()&&(s(),p());break;case"hour(s)":t.freq=Y.HOURLY,n.nextSymbol()&&(s(),p());break;case"minute(s)":t.freq=Y.MINUTELY,n.nextSymbol()&&(s(),p());break;case"month(s)":t.freq=Y.MONTHLY,n.nextSymbol()&&(s(),p());break;case"year(s)":t.freq=Y.YEARLY,n.nextSymbol()&&(s(),p());break;case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":t.freq=Y.WEEKLY;var T=n.symbol.substr(0,2).toUpperCase();if(t.byweekday=[Y[T]],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var y=u();if(!y)throw new Error("Unexpected symbol "+n.symbol+", expected weekday");t.byweekday.push(Y[y]),n.nextSymbol()}c(),p();break;case"january":case"february":case"march":case"april":case"may":case"june":case"july":case"august":case"september":case"october":case"november":case"december":if(t.freq=Y.YEARLY,t.bymonth=[o()],!n.nextSymbol())return;for(;n.accept("comma");){if(n.isDone())throw new Error("Unexpected end");var E=o();if(!E)throw new Error("Unexpected symbol "+n.symbol+", expected month");t.bymonth.push(E),n.nextSymbol()}s(),p();break;default:throw new Error("Unknown symbol")}}function s(){var m=n.accept("on"),T=n.accept("the");if(!!(m||T))do{var y=l(),E=u(),M=o();if(y)E?(n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(Y[E].nth(y))):(t.bymonthday||(t.bymonthday=[]),t.bymonthday.push(y),n.accept("day(s)"));else if(E)n.nextSymbol(),t.byweekday||(t.byweekday=[]),t.byweekday.push(Y[E]);else if(n.symbol==="weekday(s)")n.nextSymbol(),t.byweekday||(t.byweekday=[Y.MO,Y.TU,Y.WE,Y.TH,Y.FR]);else if(n.symbol==="week(s)"){n.nextSymbol();var U=n.acceptNumber();if(!U)throw new Error("Unexpected symbol "+n.symbol+", expected week number");for(t.byweekno=[parseInt(U[0],10)];n.accept("comma");){if(U=n.acceptNumber(),!U)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.byweekno.push(parseInt(U[0],10))}}else if(M)n.nextSymbol(),t.bymonth||(t.bymonth=[]),t.bymonth.push(M);else return}while(n.accept("comma")||n.accept("the")||n.accept("on"))}function a(){var m=n.accept("at");if(!!m)do{var T=n.acceptNumber();if(!T)throw new Error("Unexpected symbol "+n.symbol+", expected hour");for(t.byhour=[parseInt(T[0],10)];n.accept("comma");){if(T=n.acceptNumber(),!T)throw new Error("Unexpected symbol "+n.symbol+"; expected hour");t.byhour.push(parseInt(T[0],10))}}while(n.accept("comma")||n.accept("at"))}function o(){switch(n.symbol){case"january":return 1;case"february":return 2;case"march":return 3;case"april":return 4;case"may":return 5;case"june":return 6;case"july":return 7;case"august":return 8;case"september":return 9;case"october":return 10;case"november":return 11;case"december":return 12;default:return!1}}function u(){switch(n.symbol){case"monday":case"tuesday":case"wednesday":case"thursday":case"friday":case"saturday":case"sunday":return n.symbol.substr(0,2).toUpperCase();default:return!1}}function l(){switch(n.symbol){case"last":return n.nextSymbol(),-1;case"first":return n.nextSymbol(),1;case"second":return n.nextSymbol(),n.accept("last")?-2:2;case"third":return n.nextSymbol(),n.accept("last")?-3:3;case"nth":var m=parseInt(n.value[1],10);if(m<-366||m>366)throw new Error("Nth out of range: "+m);return n.nextSymbol(),n.accept("last")?-m:m;default:return!1}}function c(){n.accept("on"),n.accept("the");var m=l();if(!!m)for(t.bymonthday=[m],n.nextSymbol();n.accept("comma");){if(m=l(),!m)throw new Error("Unexpected symbol "+n.symbol+"; expected monthday");t.bymonthday.push(m),n.nextSymbol()}}function p(){if(n.symbol==="until"){var m=Date.parse(n.text);if(!m)throw new Error("Cannot parse until date:"+n.text);t.until=new Date(m)}else n.accept("for")&&(t.count=parseInt(n.value[0],10),n.expect("number"))}}var oe;(function(r){r[r.YEARLY=0]="YEARLY",r[r.MONTHLY=1]="MONTHLY",r[r.WEEKLY=2]="WEEKLY",r[r.DAILY=3]="DAILY",r[r.HOURLY=4]="HOURLY",r[r.MINUTELY=5]="MINUTELY",r[r.SECONDLY=6]="SECONDLY"})(oe||(oe={}));function Ys(r){return r<oe.HOURLY}var E_=function(r,e){return e===void 0&&(e=wn),new Y(qs(r,e)||void 0)},di=["count","until","interval","byweekday","bymonthday","bymonth"];fr.IMPLEMENTED=[];fr.IMPLEMENTED[oe.HOURLY]=di;fr.IMPLEMENTED[oe.MINUTELY]=di;fr.IMPLEMENTED[oe.DAILY]=["byhour"].concat(di);fr.IMPLEMENTED[oe.WEEKLY]=di;fr.IMPLEMENTED[oe.MONTHLY]=di;fr.IMPLEMENTED[oe.YEARLY]=["byweekno","byyearday"].concat(di);var w_=function(r,e,t,n){return new fr(r,e,t,n).toString()},k_=fr.isFullyConvertible;var fi=function(){function r(e,t,n,i){this.hour=e,this.minute=t,this.second=n,this.millisecond=i||0}return r.prototype.getHours=function(){return this.hour},r.prototype.getMinutes=function(){return this.minute},r.prototype.getSeconds=function(){return this.second},r.prototype.getMilliseconds=function(){return this.millisecond},r.prototype.getTime=function(){return(this.hour*60*60+this.minute*60+this.second)*1e3+this.millisecond},r}();var O_=function(r){ci(e,r);function e(t,n,i,s,a,o,u){var l=r.call(this,s,a,o,u)||this;return l.year=t,l.month=n,l.day=i,l}return e.fromDate=function(t){return new this(t.getUTCFullYear(),t.getUTCMonth()+1,t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.valueOf()%1e3)},e.prototype.getWeekday=function(){return Qr.getWeekday(new Date(this.getTime()))},e.prototype.getTime=function(){return new Date(Date.UTC(this.year,this.month-1,this.day,this.hour,this.minute,this.second,this.millisecond)).getTime()},e.prototype.getDay=function(){return this.day},e.prototype.getMonth=function(){return this.month},e.prototype.getYear=function(){return this.year},e.prototype.addYears=function(t){this.year+=t},e.prototype.addMonths=function(t){if(this.month+=t,this.month>12){var n=Math.floor(this.month/12),i=ut(this.month,12);this.month=i,this.year+=n,this.month===0&&(this.month=12,--this.year)}},e.prototype.addWeekly=function(t,n){n>this.getWeekday()?this.day+=-(this.getWeekday()+1+(6-n))+t*7:this.day+=-(this.getWeekday()-n)+t*7,this.fixDay()},e.prototype.addDaily=function(t){this.day+=t,this.fixDay()},e.prototype.addHours=function(t,n,i){for(n&&(this.hour+=Math.floor((23-this.hour)/t)*t);;){this.hour+=t;var s=mo(this.hour,24),a=s.div,o=s.mod;if(a&&(this.hour=o,this.addDaily(a)),Ct(i)||de(i,this.hour))break}},e.prototype.addMinutes=function(t,n,i,s){for(n&&(this.minute+=Math.floor((1439-(this.hour*60+this.minute))/t)*t);;){this.minute+=t;var a=mo(this.minute,60),o=a.div,u=a.mod;if(o&&(this.minute=u,this.addHours(o,!1,i)),(Ct(i)||de(i,this.hour))&&(Ct(s)||de(s,this.minute)))break}},e.prototype.addSeconds=function(t,n,i,s,a){for(n&&(this.second+=Math.floor((86399-(this.hour*3600+this.minute*60+this.second))/t)*t);;){this.second+=t;var o=mo(this.second,60),u=o.div,l=o.mod;if(u&&(this.second=l,this.addMinutes(u,!1,i,s)),(Ct(i)||de(i,this.hour))&&(Ct(s)||de(s,this.minute))&&(Ct(a)||de(a,this.second)))break}},e.prototype.fixDay=function(){if(!(this.day<=28)){var t=Qr.monthRange(this.year,this.month-1)[1];if(!(this.day<=t))for(;this.day>t;){if(this.day-=t,++this.month,this.month===13&&(this.month=1,++this.year,this.year>Qr.MAXYEAR))return;t=Qr.monthRange(this.year,this.month-1)[1]}}},e.prototype.add=function(t,n){var i=t.freq,s=t.interval,a=t.wkst,o=t.byhour,u=t.byminute,l=t.bysecond;switch(i){case oe.YEARLY:return this.addYears(s);case oe.MONTHLY:return this.addMonths(s);case oe.WEEKLY:return this.addWeekly(s,a);case oe.DAILY:return this.addDaily(s);case oe.HOURLY:return this.addHours(s,n,o);case oe.MINUTELY:return this.addMinutes(s,n,o,u);case oe.SECONDLY:return this.addSeconds(s,n,o,u,l)}},e}(fi);function df(r){for(var e=[],t=Object.keys(r),n=0,i=t;n<i.length;n++){var s=i[n];de(R_,s)||e.push(s),z.isDate(r[s])&&!z.isValidDate(r[s])&&e.push(s)}if(e.length)throw new Error("Invalid options: "+e.join(", "));return _t({},r)}function D_(r){var e=_t(_t({},$s),df(r));if(Me(e.byeaster)&&(e.freq=Y.YEARLY),!(Me(e.freq)&&Y.FREQUENCIES[e.freq]))throw new Error("Invalid frequency: ".concat(e.freq," ").concat(r.freq));if(e.dtstart||(e.dtstart=new Date(new Date().setMilliseconds(0))),Me(e.wkst)?At(e.wkst)||(e.wkst=e.wkst.weekday):e.wkst=Y.MO.weekday,Me(e.bysetpos)){At(e.bysetpos)&&(e.bysetpos=[e.bysetpos]);for(var t=0;t<e.bysetpos.length;t++){var n=e.bysetpos[t];if(n===0||!(n>=-366&&n<=366))throw new Error("bysetpos must be between 1 and 366, or between -366 and -1")}}if(!(Boolean(e.byweekno)||Ue(e.byweekno)||Ue(e.byyearday)||Boolean(e.bymonthday)||Ue(e.bymonthday)||Me(e.byweekday)||Me(e.byeaster)))switch(e.freq){case Y.YEARLY:e.bymonth||(e.bymonth=e.dtstart.getUTCMonth()+1),e.bymonthday=e.dtstart.getUTCDate();break;case Y.MONTHLY:e.bymonthday=e.dtstart.getUTCDate();break;case Y.WEEKLY:e.byweekday=[z.getWeekday(e.dtstart)];break}if(Me(e.bymonth)&&!at(e.bymonth)&&(e.bymonth=[e.bymonth]),Me(e.byyearday)&&!at(e.byyearday)&&At(e.byyearday)&&(e.byyearday=[e.byyearday]),!Me(e.bymonthday))e.bymonthday=[],e.bynmonthday=[];else if(at(e.bymonthday)){for(var i=[],s=[],t=0;t<e.bymonthday.length;t++){var n=e.bymonthday[t];n>0?i.push(n):n<0&&s.push(n)}e.bymonthday=i,e.bynmonthday=s}else e.bymonthday<0?(e.bynmonthday=[e.bymonthday],e.bymonthday=[]):(e.bynmonthday=[],e.bymonthday=[e.bymonthday]);if(Me(e.byweekno)&&!at(e.byweekno)&&(e.byweekno=[e.byweekno]),!Me(e.byweekday))e.bynweekday=null;else if(At(e.byweekday))e.byweekday=[e.byweekday],e.bynweekday=null;else if(uf(e.byweekday))e.byweekday=[je.fromStr(e.byweekday).weekday],e.bynweekday=null;else if(e.byweekday instanceof je)!e.byweekday.n||e.freq>Y.MONTHLY?(e.byweekday=[e.byweekday.weekday],e.bynweekday=null):(e.bynweekday=[[e.byweekday.weekday,e.byweekday.n]],e.byweekday=null);else{for(var a=[],o=[],t=0;t<e.byweekday.length;t++){var u=e.byweekday[t];if(At(u)){a.push(u);continue}else if(uf(u)){a.push(je.fromStr(u).weekday);continue}!u.n||e.freq>Y.MONTHLY?a.push(u.weekday):o.push([u.weekday,u.n])}e.byweekday=Ue(a)?a:null,e.bynweekday=Ue(o)?o:null}return Me(e.byhour)?At(e.byhour)&&(e.byhour=[e.byhour]):e.byhour=e.freq<Y.HOURLY?[e.dtstart.getUTCHours()]:null,Me(e.byminute)?At(e.byminute)&&(e.byminute=[e.byminute]):e.byminute=e.freq<Y.MINUTELY?[e.dtstart.getUTCMinutes()]:null,Me(e.bysecond)?At(e.bysecond)&&(e.bysecond=[e.bysecond]):e.bysecond=e.freq<Y.SECONDLY?[e.dtstart.getUTCSeconds()]:null,{parsedOptions:e}}function S_(r){var e=r.dtstart.getTime()%1e3;if(!Ys(r.freq))return[];var t=[];return r.byhour.forEach(function(n){r.byminute.forEach(function(i){r.bysecond.forEach(function(s){t.push(new fi(n,i,s,e))})})}),t}function Gs(r){var e=r.split(`
`).map(ZM).filter(function(t){return t!==null});return _t(_t({},e[0]),e[1])}function js(r){var e={},t=/DTSTART(?:;TZID=([^:=]+?))?(?::|=)([^;\s]+)/i.exec(r);if(!t)return e;var n=t[1],i=t[2];return n&&(e.tzid=n),e.dtstart=z.untilStringToDate(i),e}function ZM(r){if(r=r.replace(/^\s+|\s+$/,""),!r.length)return null;var e=/^([A-Z]+?)[:;]/.exec(r.toUpperCase());if(!e)return M_(r);var t=e[1];switch(t.toUpperCase()){case"RRULE":case"EXRULE":return M_(r);case"DTSTART":return js(r);default:throw new Error("Unsupported RFC prop ".concat(t," in ").concat(r))}}function M_(r){var e=r.replace(/^RRULE:/i,""),t=js(e),n=r.replace(/^(?:RRULE|EXRULE):/i,"").split(";");return n.forEach(function(i){var s=i.split("="),a=s[0],o=s[1];switch(a.toUpperCase()){case"FREQ":t.freq=oe[o.toUpperCase()];break;case"WKST":t.wkst=Ht[o.toUpperCase()];break;case"COUNT":case"INTERVAL":case"BYSETPOS":case"BYMONTH":case"BYMONTHDAY":case"BYYEARDAY":case"BYWEEKNO":case"BYHOUR":case"BYMINUTE":case"BYSECOND":var u=JM(o),l=a.toLowerCase();t[l]=u;break;case"BYWEEKDAY":case"BYDAY":t.byweekday=ex(o);break;case"DTSTART":case"TZID":var c=js(r);t.tzid=c.tzid,t.dtstart=c.dtstart;break;case"UNTIL":t.until=z.untilStringToDate(o);break;case"BYEASTER":t.byeaster=Number(o);break;default:throw new Error("Unknown RRULE property '"+a+"'")}}),t}function JM(r){if(r.indexOf(",")!==-1){var e=r.split(",");return e.map(x_)}return x_(r)}function x_(r){return/^[+-]?\d+$/.test(r)?Number(r):r}function ex(r){var e=r.split(",");return e.map(function(t){if(t.length===2)return Ht[t];var n=t.match(/^([+-]?\d{1,2})([A-Z]{2})$/);if(!n||n.length<3)throw new SyntaxError("Invalid weekday string: ".concat(t));var i=Number(n[1]),s=n[2],a=Ht[s].weekday;return new je(a,i)})}var kn=function(){function r(e,t){if(isNaN(e.getTime()))throw new RangeError("Invalid date passed to DateWithZone");this.date=e,this.tzid=t}return Object.defineProperty(r.prototype,"isUTC",{get:function(){return!this.tzid||this.tzid.toUpperCase()==="UTC"},enumerable:!1,configurable:!0}),r.prototype.toString=function(){var e=z.timeToUntilString(this.date.getTime(),this.isUTC);return this.isUTC?":".concat(e):";TZID=".concat(this.tzid,":").concat(e)},r.prototype.getTime=function(){return this.date.getTime()},r.prototype.rezonedDate=function(){var e;if(this.isUTC)return this.date;var t=Intl.DateTimeFormat().resolvedOptions().timeZone,n=new Date(this.date.toLocaleString(void 0,{timeZone:t})),i=new Date(this.date.toLocaleString(void 0,{timeZone:(e=this.tzid)!==null&&e!==void 0?e:"UTC"})),s=i.getTime()-n.getTime();return new Date(this.date.getTime()-s)},r}();function Bs(r){for(var e=[],t="",n=Object.keys(r),i=Object.keys($s),s=0;s<n.length;s++)if(n[s]!=="tzid"&&!!de(i,n[s])){var a=n[s].toUpperCase(),o=r[n[s]],u="";if(!(!Me(o)||at(o)&&!o.length)){switch(a){case"FREQ":u=Y.FREQUENCIES[r.freq];break;case"WKST":At(o)?u=new je(o).toString():u=o.toString();break;case"BYWEEKDAY":a="BYDAY",u=Qy(o).map(function(T){return T instanceof je?T:at(T)?new je(T[0],T[1]):new je(T)}).toString();break;case"DTSTART":t=tx(o,r.tzid);break;case"UNTIL":u=z.timeToUntilString(o,!r.tzid);break;default:if(at(o)){for(var l=[],c=0;c<o.length;c++)l[c]=String(o[c]);u=l.toString()}else u=String(o)}u&&e.push([a,u])}}var p=e.map(function(T){var y=T[0],E=T[1];return"".concat(y,"=").concat(E.toString())}).join(";"),m="";return p!==""&&(m="RRULE:".concat(p)),[t,m].filter(function(T){return!!T}).join(`
`)}function tx(r,e){return r?"DTSTART"+new kn(new Date(r),e).toString():""}function rx(r,e){return Array.isArray(r)?!Array.isArray(e)||r.length!==e.length?!1:r.every(function(t,n){return t.getTime()===e[n].getTime()}):r instanceof Date?e instanceof Date&&r.getTime()===e.getTime():r===e}var A_=function(){function r(){this.all=!1,this.before=[],this.after=[],this.between=[]}return r.prototype._cacheAdd=function(e,t,n){t&&(t=t instanceof Date?z.clone(t):z.cloneDates(t)),e==="all"?this.all=t:(n._value=t,this[e].push(n))},r.prototype._cacheGet=function(e,t){var n=!1,i=t?Object.keys(t):[],s=function(c){for(var p=0;p<i.length;p++){var m=i[p];if(!rx(t[m],c[m]))return!0}return!1},a=this[e];if(e==="all")n=this.all;else if(at(a))for(var o=0;o<a.length;o++){var u=a[o];if(!(i.length&&s(u))){n=u._value;break}}if(!n&&this.all){for(var l=new Mr(e,t),o=0;o<this.all.length&&l.accept(this.all[o]);o++);n=l.getValue(),this._cacheAdd(e,n,t)}return at(n)?z.cloneDates(n):n instanceof Date?z.clone(n):n},r}();var C_=x(x(x(x(x(x(x(x(x(x(x(x(x([],le(1,31),!0),le(2,28),!0),le(3,31),!0),le(4,30),!0),le(5,31),!0),le(6,30),!0),le(7,31),!0),le(8,31),!0),le(9,30),!0),le(10,31),!0),le(11,30),!0),le(12,31),!0),le(1,7),!0),P_=x(x(x(x(x(x(x(x(x(x(x(x(x([],le(1,31),!0),le(2,29),!0),le(3,31),!0),le(4,30),!0),le(5,31),!0),le(6,30),!0),le(7,31),!0),le(8,31),!0),le(9,30),!0),le(10,31),!0),le(11,30),!0),le(12,31),!0),le(1,7),!0),nx=Bt(1,29),ix=Bt(1,30),Xr=Bt(1,31),lt=Bt(1,32),N_=x(x(x(x(x(x(x(x(x(x(x(x(x([],lt,!0),ix,!0),lt,!0),Xr,!0),lt,!0),Xr,!0),lt,!0),lt,!0),Xr,!0),lt,!0),Xr,!0),lt,!0),lt.slice(0,7),!0),I_=x(x(x(x(x(x(x(x(x(x(x(x(x([],lt,!0),nx,!0),lt,!0),Xr,!0),lt,!0),Xr,!0),lt,!0),lt,!0),Xr,!0),lt,!0),Xr,!0),lt,!0),lt.slice(0,7),!0),sx=Bt(-28,0),ax=Bt(-29,0),Zr=Bt(-30,0),ct=Bt(-31,0),F_=x(x(x(x(x(x(x(x(x(x(x(x(x([],ct,!0),ax,!0),ct,!0),Zr,!0),ct,!0),Zr,!0),ct,!0),ct,!0),Zr,!0),ct,!0),Zr,!0),ct,!0),ct.slice(0,7),!0),L_=x(x(x(x(x(x(x(x(x(x(x(x(x([],ct,!0),sx,!0),ct,!0),Zr,!0),ct,!0),Zr,!0),ct,!0),ct,!0),Zr,!0),ct,!0),Zr,!0),ct,!0),ct.slice(0,7),!0),U_=[0,31,60,91,121,152,182,213,244,274,305,335,366],W_=[0,31,59,90,120,151,181,212,243,273,304,334,365],ff=function(){for(var r=[],e=0;e<55;e++)r=r.concat(Bt(7));return r}();function q_(r,e){var t=new Date(Date.UTC(r,0,1)),n=z.isLeapYear(r)?366:365,i=z.isLeapYear(r+1)?366:365,s=z.toOrdinal(t),a=z.getWeekday(t),o=_t(_t({yearlen:n,nextyearlen:i,yearordinal:s,yearweekday:a},ox(r)),{wnomask:null});if(Ct(e.byweekno))return o;o.wnomask=le(0,n+7);var u,l,c=u=ut(7-a+e.wkst,7);c>=4?(c=0,l=o.yearlen+ut(a-e.wkst,7)):l=n-c;for(var p=Math.floor(l/7),m=ut(l,7),T=Math.floor(p+m/4),y=0;y<e.byweekno.length;y++){var E=e.byweekno[y];if(E<0&&(E+=T+1),E>0&&E<=T){var M=void 0;E>1?(M=c+(E-1)*7,c!==u&&(M-=7-u)):M=c;for(var U=0;U<7&&(o.wnomask[M]=1,M++,o.wdaymask[M]!==e.wkst);U++);}}if(de(e.byweekno,1)){var M=c+T*7;if(c!==u&&(M-=7-u),M<n)for(var y=0;y<7&&(o.wnomask[M]=1,M+=1,o.wdaymask[M]!==e.wkst);y++);}if(c){var q=void 0;if(de(e.byweekno,-1))q=-1;else{var W=z.getWeekday(new Date(Date.UTC(r-1,0,1))),ie=ut(7-W.valueOf()+e.wkst,7),re=z.isLeapYear(r-1)?366:365,ve=void 0;ie>=4?(ie=0,ve=re+ut(W-e.wkst,7)):ve=n-c,q=Math.floor(52+ut(ve,7)/4)}if(de(e.byweekno,q))for(var M=0;M<c;M++)o.wnomask[M]=1}return o}function ox(r){var e=z.isLeapYear(r)?366:365,t=new Date(Date.UTC(r,0,1)),n=z.getWeekday(t);return e===365?{mmask:C_,mdaymask:I_,nmdaymask:L_,wdaymask:ff.slice(n),mrange:W_}:{mmask:P_,mdaymask:N_,nmdaymask:F_,wdaymask:ff.slice(n),mrange:U_}}function Y_(r,e,t,n,i,s){var a={lastyear:r,lastmonth:e,nwdaymask:[]},o=[];if(s.freq===Y.YEARLY)if(Ct(s.bymonth))o=[[0,t]];else for(var u=0;u<s.bymonth.length;u++)e=s.bymonth[u],o.push(n.slice(e-1,e+1));else s.freq===Y.MONTHLY&&(o=[n.slice(e-1,e+1)]);if(Ct(o))return a;a.nwdaymask=le(0,t);for(var u=0;u<o.length;u++)for(var l=o[u],c=l[0],p=l[1]-1,m=0;m<s.bynweekday.length;m++){var T=void 0,y=s.bynweekday[m],E=y[0],M=y[1];M<0?(T=p+(M+1)*7,T-=ut(i[T]-E,7)):(T=c+(M-1)*7,T+=ut(7-i[T]+E,7)),c<=T&&T<=p&&(a.nwdaymask[T]=1)}return a}function $_(r,e){e===void 0&&(e=0);var t=r%19,n=Math.floor(r/100),i=r%100,s=Math.floor(n/4),a=n%4,o=Math.floor((n+8)/25),u=Math.floor((n-o+1)/3),l=Math.floor(19*t+n-s-u+15)%30,c=Math.floor(i/4),p=i%4,m=Math.floor(32+2*a+2*c-l-p)%7,T=Math.floor((t+11*l+22*m)/451),y=Math.floor((l+m-7*T+114)/31),E=(l+m-7*T+114)%31+1,M=Date.UTC(r,y-1,E+e),U=Date.UTC(r,0,1);return[Math.ceil((M-U)/(1e3*60*60*24))]}var ux=function(){function r(e){this.options=e}return r.prototype.rebuild=function(e,t){var n=this.options;if(e!==this.lastyear&&(this.yearinfo=q_(e,n)),Ue(n.bynweekday)&&(t!==this.lastmonth||e!==this.lastyear)){var i=this.yearinfo,s=i.yearlen,a=i.mrange,o=i.wdaymask;this.monthinfo=Y_(e,t,s,a,o,n)}Me(n.byeaster)&&(this.eastermask=$_(e,n.byeaster))},Object.defineProperty(r.prototype,"lastyear",{get:function(){return this.monthinfo?this.monthinfo.lastyear:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"lastmonth",{get:function(){return this.monthinfo?this.monthinfo.lastmonth:null},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearlen",{get:function(){return this.yearinfo.yearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"yearordinal",{get:function(){return this.yearinfo.yearordinal},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mrange",{get:function(){return this.yearinfo.mrange},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wdaymask",{get:function(){return this.yearinfo.wdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mmask",{get:function(){return this.yearinfo.mmask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"wnomask",{get:function(){return this.yearinfo.wnomask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nwdaymask",{get:function(){return this.monthinfo?this.monthinfo.nwdaymask:[]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nextyearlen",{get:function(){return this.yearinfo.nextyearlen},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"mdaymask",{get:function(){return this.yearinfo.mdaymask},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"nmdaymask",{get:function(){return this.yearinfo.nmdaymask},enumerable:!1,configurable:!0}),r.prototype.ydayset=function(){return[Bt(this.yearlen),0,this.yearlen]},r.prototype.mdayset=function(e,t){for(var n=this.mrange[t-1],i=this.mrange[t],s=le(null,this.yearlen),a=n;a<i;a++)s[a]=a;return[s,n,i]},r.prototype.wdayset=function(e,t,n){for(var i=le(null,this.yearlen+7),s=z.toOrdinal(new Date(Date.UTC(e,t-1,n)))-this.yearordinal,a=s,o=0;o<7&&(i[s]=s,++s,this.wdaymask[s]!==this.options.wkst);o++);return[i,a,s]},r.prototype.ddayset=function(e,t,n){var i=le(null,this.yearlen),s=z.toOrdinal(new Date(Date.UTC(e,t-1,n)))-this.yearordinal;return i[s]=s,[i,s,s+1]},r.prototype.htimeset=function(e,t,n,i){var s=this,a=[];return this.options.byminute.forEach(function(o){a=a.concat(s.mtimeset(e,o,n,i))}),z.sort(a),a},r.prototype.mtimeset=function(e,t,n,i){var s=this.options.bysecond.map(function(a){return new fi(e,t,a,i)});return z.sort(s),s},r.prototype.stimeset=function(e,t,n,i){return[new fi(e,t,n,i)]},r.prototype.getdayset=function(e){switch(e){case oe.YEARLY:return this.ydayset.bind(this);case oe.MONTHLY:return this.mdayset.bind(this);case oe.WEEKLY:return this.wdayset.bind(this);case oe.DAILY:return this.ddayset.bind(this);default:return this.ddayset.bind(this)}},r.prototype.gettimeset=function(e){switch(e){case oe.HOURLY:return this.htimeset.bind(this);case oe.MINUTELY:return this.mtimeset.bind(this);case oe.SECONDLY:return this.stimeset.bind(this)}},r}(),j_=ux;function G_(r,e,t,n,i,s){for(var a=[],o=0;o<r.length;o++){var u=void 0,l=void 0,c=r[o];c<0?(u=Math.floor(c/e.length),l=ut(c,e.length)):(u=Math.floor((c-1)/e.length),l=ut(c-1,e.length));for(var p=[],m=t;m<n;m++){var T=s[m];!Me(T)||p.push(T)}var y=void 0;u<0?y=p.slice(u)[0]:y=p[u];var E=e[l],M=z.fromOrdinal(i.yearordinal+y),U=z.combine(M,E);de(a,U)||a.push(U)}return z.sort(a),a}function _o(r,e){var t=e.dtstart,n=e.freq,i=e.interval,s=e.until,a=e.bysetpos,o=e.count;if(o===0||i===0)return xr(r);var u=O_.fromDate(t),l=new j_(e);l.rebuild(u.year,u.month);for(var c=dx(l,u,e);;){var p=l.getdayset(n)(u.year,u.month,u.day),m=p[0],T=p[1],y=p[2],E=cx(m,T,y,l,e);if(Ue(a))for(var M=G_(a,c,T,y,l,m),U=0;U<M.length;U++){var q=M[U];if(s&&q>s)return xr(r);if(q>=t){var W=B_(q,e);if(!r.accept(W)||o&&(--o,!o))return xr(r)}}else for(var U=T;U<y;U++){var ie=m[U];if(!!Me(ie))for(var re=z.fromOrdinal(l.yearordinal+ie),ve=0;ve<c.length;ve++){var G=c[ve],q=z.combine(re,G);if(s&&q>s)return xr(r);if(q>=t){var W=B_(q,e);if(!r.accept(W)||o&&(--o,!o))return xr(r)}}}if(e.interval===0||(u.add(e,E),u.year>z.MAXYEAR))return xr(r);Ys(n)||(c=l.gettimeset(n)(u.hour,u.minute,u.second,0)),l.rebuild(u.year,u.month)}}function lx(r,e,t){var n=t.bymonth,i=t.byweekno,s=t.byweekday,a=t.byeaster,o=t.bymonthday,u=t.bynmonthday,l=t.byyearday;return Ue(n)&&!de(n,r.mmask[e])||Ue(i)&&!r.wnomask[e]||Ue(s)&&!de(s,r.wdaymask[e])||Ue(r.nwdaymask)&&!r.nwdaymask[e]||a!==null&&!de(r.eastermask,e)||(Ue(o)||Ue(u))&&!de(o,r.mdaymask[e])&&!de(u,r.nmdaymask[e])||Ue(l)&&(e<r.yearlen&&!de(l,e+1)&&!de(l,-r.yearlen+e)||e>=r.yearlen&&!de(l,e+1-r.yearlen)&&!de(l,-r.nextyearlen+e-r.yearlen))}function B_(r,e){return new kn(r,e.tzid).rezonedDate()}function xr(r){return r.getValue()}function cx(r,e,t,n,i){for(var s=!1,a=e;a<t;a++){var o=r[a];s=lx(n,o,i),s&&(r[o]=null)}return s}function dx(r,e,t){var n=t.freq,i=t.byhour,s=t.byminute,a=t.bysecond;return Ys(n)?S_(t):n>=Y.HOURLY&&Ue(i)&&!de(i,e.hour)||n>=Y.MINUTELY&&Ue(s)&&!de(s,e.minute)||n>=Y.SECONDLY&&Ue(a)&&!de(a,e.second)?[]:r.gettimeset(n)(e.hour,e.minute,e.second,e.millisecond)}var Ht={MO:new je(0),TU:new je(1),WE:new je(2),TH:new je(3),FR:new je(4),SA:new je(5),SU:new je(6)},$s={freq:oe.YEARLY,dtstart:null,interval:1,wkst:Ht.MO,count:null,until:null,tzid:null,bysetpos:null,bymonth:null,bymonthday:null,bynmonthday:null,byyearday:null,byweekno:null,byweekday:null,bynweekday:null,byhour:null,byminute:null,bysecond:null,byeaster:null},R_=Object.keys($s),Y=function(){function r(e,t){e===void 0&&(e={}),t===void 0&&(t=!1),this._cache=t?null:new A_,this.origOptions=df(e);var n=D_(e).parsedOptions;this.options=n}return r.parseText=function(e,t){return qs(e,t)},r.fromText=function(e,t){return E_(e,t)},r.fromString=function(e){return new r(r.parseString(e)||void 0)},r.prototype._iter=function(e){return _o(e,this.options)},r.prototype._cacheGet=function(e,t){return this._cache?this._cache._cacheGet(e,t):!1},r.prototype._cacheAdd=function(e,t,n){if(!!this._cache)return this._cache._cacheAdd(e,t,n)},r.prototype.all=function(e){if(e)return this._iter(new cf("all",{},e));var t=this._cacheGet("all");return t===!1&&(t=this._iter(new Mr("all",{})),this._cacheAdd("all",t)),t},r.prototype.between=function(e,t,n,i){if(n===void 0&&(n=!1),!z.isValidDate(e)||!z.isValidDate(t))throw new Error("Invalid date passed in to RRule.between");var s={before:t,after:e,inc:n};if(i)return this._iter(new cf("between",s,i));var a=this._cacheGet("between",s);return a===!1&&(a=this._iter(new Mr("between",s)),this._cacheAdd("between",a,s)),a},r.prototype.before=function(e,t){if(t===void 0&&(t=!1),!z.isValidDate(e))throw new Error("Invalid date passed in to RRule.before");var n={dt:e,inc:t},i=this._cacheGet("before",n);return i===!1&&(i=this._iter(new Mr("before",n)),this._cacheAdd("before",i,n)),i},r.prototype.after=function(e,t){if(t===void 0&&(t=!1),!z.isValidDate(e))throw new Error("Invalid date passed in to RRule.after");var n={dt:e,inc:t},i=this._cacheGet("after",n);return i===!1&&(i=this._iter(new Mr("after",n)),this._cacheAdd("after",i,n)),i},r.prototype.count=function(){return this.all().length},r.prototype.toString=function(){return Bs(this.origOptions)},r.prototype.toText=function(e,t,n){return w_(this,e,t,n)},r.prototype.isFullyConvertibleToText=function(){return k_(this)},r.prototype.clone=function(){return new r(this.origOptions)},r.FREQUENCIES=["YEARLY","MONTHLY","WEEKLY","DAILY","HOURLY","MINUTELY","SECONDLY"],r.YEARLY=oe.YEARLY,r.MONTHLY=oe.MONTHLY,r.WEEKLY=oe.WEEKLY,r.DAILY=oe.DAILY,r.HOURLY=oe.HOURLY,r.MINUTELY=oe.MINUTELY,r.SECONDLY=oe.SECONDLY,r.MO=Ht.MO,r.TU=Ht.TU,r.WE=Ht.WE,r.TH=Ht.TH,r.FR=Ht.FR,r.SA=Ht.SA,r.SU=Ht.SU,r.parseString=Gs,r.optionsToString=Bs,r}();function H_(r,e,t,n,i,s){var a={},o=r.accept;function u(m,T){t.forEach(function(y){y.between(m,T,!0).forEach(function(E){a[Number(E)]=!0})})}i.forEach(function(m){var T=new kn(m,s).rezonedDate();a[Number(T)]=!0}),r.accept=function(m){var T=Number(m);return isNaN(T)?o.call(this,m):!a[T]&&(u(new Date(T-1),new Date(T+1)),!a[T])?(a[T]=!0,o.call(this,m)):!0},r.method==="between"&&(u(r.args.after,r.args.before),r.accept=function(m){var T=Number(m);return a[T]?!0:(a[T]=!0,o.call(this,m))});for(var l=0;l<n.length;l++){var c=new kn(n[l],s).rezonedDate();if(!r.accept(new Date(c.getTime())))break}e.forEach(function(m){_o(r,m.options)});var p=r._result;switch(z.sort(p),r.method){case"all":case"between":return p;case"before":return p.length&&p[p.length-1]||null;case"after":default:return p.length&&p[0]||null}}var z_={dtstart:null,cache:!1,unfold:!1,forceset:!1,compatible:!1,tzid:null};function fx(r,e){var t=[],n=[],i=[],s=[],a=js(r),o=a.dtstart,u=a.tzid,l=yx(r,e.unfold);return l.forEach(function(c){var p;if(!!c){var m=gx(c),T=m.name,y=m.parms,E=m.value;switch(T.toUpperCase()){case"RRULE":if(y.length)throw new Error("unsupported RRULE parm: ".concat(y.join(",")));t.push(Gs(c));break;case"RDATE":var M=(p=/RDATE(?:;TZID=([^:=]+))?/i.exec(c))!==null&&p!==void 0?p:[],U=M[1];U&&!u&&(u=U),n=n.concat(V_(E,y));break;case"EXRULE":if(y.length)throw new Error("unsupported EXRULE parm: ".concat(y.join(",")));i.push(Gs(E));break;case"EXDATE":s=s.concat(V_(E,y));break;case"DTSTART":break;default:throw new Error("unsupported property: "+T)}}}),{dtstart:o,tzid:u,rrulevals:t,rdatevals:n,exrulevals:i,exdatevals:s}}function px(r,e){var t=fx(r,e),n=t.rrulevals,i=t.rdatevals,s=t.exrulevals,a=t.exdatevals,o=t.dtstart,u=t.tzid,l=e.cache===!1;if(e.compatible&&(e.forceset=!0,e.unfold=!0),e.forceset||n.length>1||i.length||s.length||a.length){var c=new mf(l);return c.dtstart(o),c.tzid(u||void 0),n.forEach(function(m){c.rrule(new Y(pf(m,o,u),l))}),i.forEach(function(m){c.rdate(m)}),s.forEach(function(m){c.exrule(new Y(pf(m,o,u),l))}),a.forEach(function(m){c.exdate(m)}),e.compatible&&e.dtstart&&c.rdate(o),c}var p=n[0]||{};return new Y(pf(p,p.dtstart||e.dtstart||o,p.tzid||e.tzid||u),l)}function To(r,e){return e===void 0&&(e={}),px(r,mx(e))}function pf(r,e,t){return _t(_t({},r),{dtstart:e,tzid:t})}function mx(r){var e=[],t=Object.keys(r),n=Object.keys(z_);if(t.forEach(function(i){de(n,i)||e.push(i)}),e.length)throw new Error("Invalid options: "+e.join(", "));return _t(_t({},z_),r)}function hx(r){if(r.indexOf(":")===-1)return{name:"RRULE",value:r};var e=Xy(r,":",1),t=e[0],n=e[1];return{name:t,value:n}}function gx(r){var e=hx(r),t=e.name,n=e.value,i=t.split(";");if(!i)throw new Error("empty property name");return{name:i[0].toUpperCase(),parms:i.slice(1),value:n}}function yx(r,e){if(e===void 0&&(e=!1),r=r&&r.trim(),!r)throw new Error("Invalid empty string");if(!e)return r.split(/\s/);for(var t=r.split(`
`),n=0;n<t.length;){var i=t[n]=t[n].replace(/\s+$/g,"");i?n>0&&i[0]===" "?(t[n-1]+=i.slice(1),t.splice(n,1)):n+=1:t.splice(n,1)}return t}function _x(r){r.forEach(function(e){if(!/(VALUE=DATE(-TIME)?)|(TZID=)/.test(e))throw new Error("unsupported RDATE/EXDATE parm: "+e)})}function V_(r,e){return _x(e),r.split(",").map(function(t){return z.untilStringToDate(t)})}function K_(r){var e=this;return function(t){if(t!==void 0&&(e["_".concat(r)]=t),e["_".concat(r)]!==void 0)return e["_".concat(r)];for(var n=0;n<e._rrule.length;n++){var i=e._rrule[n].origOptions[r];if(i)return i}}}var mf=function(r){ci(e,r);function e(t){t===void 0&&(t=!1);var n=r.call(this,{},t)||this;return n.dtstart=K_.apply(n,["dtstart"]),n.tzid=K_.apply(n,["tzid"]),n._rrule=[],n._rdate=[],n._exrule=[],n._exdate=[],n}return e.prototype._iter=function(t){return H_(t,this._rrule,this._exrule,this._rdate,this._exdate,this.tzid())},e.prototype.rrule=function(t){Q_(t,this._rrule)},e.prototype.exrule=function(t){Q_(t,this._exrule)},e.prototype.rdate=function(t){X_(t,this._rdate)},e.prototype.exdate=function(t){X_(t,this._exdate)},e.prototype.rrules=function(){return this._rrule.map(function(t){return To(t.toString())})},e.prototype.exrules=function(){return this._exrule.map(function(t){return To(t.toString())})},e.prototype.rdates=function(){return this._rdate.map(function(t){return new Date(t.getTime())})},e.prototype.exdates=function(){return this._exdate.map(function(t){return new Date(t.getTime())})},e.prototype.valueOf=function(){var t=[];return!this._rrule.length&&this._dtstart&&(t=t.concat(Bs({dtstart:this._dtstart}))),this._rrule.forEach(function(n){t=t.concat(n.toString().split(`
`))}),this._exrule.forEach(function(n){t=t.concat(n.toString().split(`
`).map(function(i){return i.replace(/^RRULE:/,"EXRULE:")}).filter(function(i){return!/^DTSTART/.test(i)}))}),this._rdate.length&&t.push(Z_("RDATE",this._rdate,this.tzid())),this._exdate.length&&t.push(Z_("EXDATE",this._exdate,this.tzid())),t},e.prototype.toString=function(){return this.valueOf().join(`
`)},e.prototype.clone=function(){var t=new e(!!this._cache);return this._rrule.forEach(function(n){return t.rrule(n.clone())}),this._exrule.forEach(function(n){return t.exrule(n.clone())}),this._rdate.forEach(function(n){return t.rdate(new Date(n.getTime()))}),this._exdate.forEach(function(n){return t.exdate(new Date(n.getTime()))}),t},e}(Y);function Q_(r,e){if(!(r instanceof Y))throw new TypeError(String(r)+" is not RRule instance");de(e.map(String),String(r))||e.push(r)}function X_(r,e){if(!(r instanceof Date))throw new TypeError(String(r)+" is not Date instance");de(e.map(Number),Number(r))||(e.push(r),z.sort(e))}function Z_(r,e,t){var n=!t||t.toUpperCase()==="UTC",i=n?"".concat(r,":"):"".concat(r,";TZID=").concat(t,":"),s=e.map(function(a){return z.timeToUntilString(a.valueOf(),n)}).join(",");return"".concat(i).concat(s)}function Ar(r,e){return r!==null&&e===null?-1:r===null&&e!==null?1:r!==null&&e!==null?r.isValid()&&!e.isValid()?-1:!r.isValid()&&e.isValid()||r.isAfter(e)?1:r.isBefore(e)?-1:0:0}var et=class{constructor({rrule:e,baseOnToday:t,referenceDate:n,startDate:i,scheduledDate:s,dueDate:a}){this.rrule=e,this.baseOnToday=t,this.referenceDate=n,this.startDate=i,this.scheduledDate=s,this.dueDate=a}static fromText({recurrenceRuleText:e,startDate:t,scheduledDate:n,dueDate:i}){try{let s=e.match(/^([a-zA-Z0-9, !]+?)( when done)?$/i);if(s==null)return null;let a=s[1].trim(),o=s[2]!==void 0,u=Y.parseText(a);if(u!==null){let l=null;i?l=window.moment(i):n?l=window.moment(n):t&&(l=window.moment(t)),!o&&l!==null?u.dtstart=window.moment(l).startOf("day").utc(!0).toDate():u.dtstart=window.moment().startOf("day").utc(!0).toDate();let c=new Y(u);return new et({rrule:c,baseOnToday:o,referenceDate:l,startDate:t,scheduledDate:n,dueDate:i})}}catch(s){s instanceof Error&&console.log(s.message)}return null}toText(){let e=this.rrule.toText();return this.baseOnToday&&(e+=" when done"),e}next(){let e=this.nextReferenceDate();if(e!==null){let t=null,n=null,i=null;if(this.referenceDate){if(this.startDate){let s=window.moment.duration(this.startDate.diff(this.referenceDate));t=window.moment(e),t.add(Math.round(s.asDays()),"days")}if(this.scheduledDate){let s=window.moment.duration(this.scheduledDate.diff(this.referenceDate));n=window.moment(e),n.add(Math.round(s.asDays()),"days")}if(this.dueDate){let s=window.moment.duration(this.dueDate.diff(this.referenceDate));i=window.moment(e),i.add(Math.round(s.asDays()),"days")}}return{startDate:t,scheduledDate:n,dueDate:i}}return null}identicalTo(e){return this.baseOnToday!==e.baseOnToday||Ar(this.startDate,e.startDate)!==0||Ar(this.scheduledDate,e.scheduledDate)!==0||Ar(this.dueDate,e.dueDate)!==0?!1:this.toText()===e.toText()}nextReferenceDate(){if(this.baseOnToday){let e=window.moment();return this.nextReferenceDateFromToday(e).toDate()}else return this.nextReferenceDateFromOriginalReferenceDate().toDate()}nextReferenceDateFromToday(e){let t=new Y(Dt(he({},this.rrule.origOptions),{dtstart:e.startOf("day").utc(!0).toDate()}));return this.nextAfter(e.endOf("day"),t)}nextReferenceDateFromOriginalReferenceDate(){var t;let e=window.moment((t=this.referenceDate)!=null?t:void 0).endOf("day");return this.nextAfter(e,this.rrule)}nextAfter(e,t){e.utc(!0);let n=window.moment(t.after(e.toDate())),i=this.toText(),s=i.match(/every( \d+)? month(s)?(.*)?/);s!==null&&(i.includes(" on ")||(n=et.nextAfterMonths(e,n,t,s[1])));let a=i.match(/every( \d+)? year(s)?(.*)?/);return a!==null&&(n=et.nextAfterYears(e,n,t,a[1])),et.addTimezone(n)}static nextAfterMonths(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));et.isSkippingTooManyMonths(e,t,s);)t=et.fromOneDayEarlier(e,n);return t}static isSkippingTooManyMonths(e,t,n){let i=t.month()-e.month();return i+=(t.year()-e.year())*12,i>n}static nextAfterYears(e,t,n,i){let s=1;for(i!==void 0&&(s=Number.parseInt(i.trim(),10));et.isSkippingTooManyYears(e,t,s);)t=et.fromOneDayEarlier(e,n);return t}static isSkippingTooManyYears(e,t,n){return t.year()-e.year()>n}static fromOneDayEarlier(e,t){e.subtract(1,"days").endOf("day");let n=t.origOptions;return n.dtstart=e.startOf("day").toDate(),t=new Y(n),window.moment(t.after(e.toDate()))}static addTimezone(e){return window.moment.utc(e).local(!0).startOf("day")}};var hf=5;function gf(r,e){let t=[r.startDateSymbol,r.scheduledDateSymbol,r.dueDateSymbol].join("|");return(n,i,s)=>{let a=[];return a=a.concat(bx(n,i,s,t,e)),a=a.concat(vx(n,i,s,r.recurrenceSymbol)),a=a.concat(Tx(n,i,s,r)),a.length>0&&!a.some(o=>o.suggestionType==="match")&&a.unshift({suggestionType:"empty",displayText:"\u23CE",appendText:`
`}),a=a.slice(0,s.autoSuggestMaxItems),a}}function Tx(r,e,t,n){let i=u=>Object.values(n.prioritySymbols).some(l=>l.length>0&&u.includes(l)),s=[];if(r.includes(n.dueDateSymbol)||s.push({displayText:`${n.dueDateSymbol} due date`,appendText:`${n.dueDateSymbol} `}),r.includes(n.startDateSymbol)||s.push({displayText:`${n.startDateSymbol} start date`,appendText:`${n.startDateSymbol} `}),r.includes(n.scheduledDateSymbol)||s.push({displayText:`${n.scheduledDateSymbol} scheduled date`,appendText:`${n.scheduledDateSymbol} `}),i(r)||(s.push({displayText:`${n.prioritySymbols.High} high priority`,appendText:`${n.prioritySymbols.High} `}),s.push({displayText:`${n.prioritySymbols.Medium} medium priority`,appendText:`${n.prioritySymbols.Medium} `}),s.push({displayText:`${n.prioritySymbols.Low} low priority`,appendText:`${n.prioritySymbols.Low} `}),s.push({displayText:`${n.prioritySymbols.Highest} highest priority`,appendText:`${n.prioritySymbols.Highest} `}),s.push({displayText:`${n.prioritySymbols.Lowest} lowest priority`,appendText:`${n.prioritySymbols.Lowest} `})),r.includes(n.recurrenceSymbol)||s.push({displayText:`${n.recurrenceSymbol} recurring (repeat)`,appendText:`${n.recurrenceSymbol} `}),!r.includes(n.createdDateSymbol)){let l=xt.parseDate("today",!0).format(H.dateFormat);s.push({textToMatch:`${n.createdDateSymbol} created`,displayText:`${n.createdDateSymbol} created today (${l})`,appendText:`${n.createdDateSymbol} ${l} `})}let a=yf(r,/([a-zA-Z'_-]*)/g,e),o=[];if(a&&a.length>0){let u=a[0];if(u.length>=Math.max(1,t.autoSuggestMinMatch)){let l=s.filter(c=>(c.textToMatch||c.displayText).toLowerCase().includes(u.toLowerCase()));for(let c of l)o.push({suggestionType:"match",displayText:c.displayText,appendText:c.appendText,insertAt:a.index,insertSkip:u.length})}}return o.length===0&&t.autoSuggestMinMatch===0?s:o}function bx(r,e,t,n,i){let s=["today","tomorrow","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","next week","next month","next year"],a=[],o=new RegExp(`(${n})\\s*([0-9a-zA-Z ]*)`,"ug"),u=yf(r,o,e);if(u&&u.length>=2){let l=u[1],c=u[2];if(c.length<t.autoSuggestMinMatch)return[];let p=c&&c.length>1?xt.parseDate(ui(c),!0):null;p&&p.isValid()&&a.push({displayText:`${p.format(H.dateFormat)}`,appendText:`${l} ${p.format(H.dateFormat)} `,insertAt:u.index,insertSkip:u[0].length});let m=1,T=s.filter(y=>c&&c.length>=m&&y.toLowerCase().includes(c.toLowerCase())).slice(0,i);T.length===0&&(T=s.slice(0,i));for(let y of T){let M=`${xt.parseDate(y,!0).format(H.dateFormat)}`;a.push({suggestionType:"match",displayText:`${y} (${M})`,appendText:`${l} ${M} `,insertAt:u.index,insertSkip:u[0].length})}}return a}function vx(r,e,t,n){var u;let i=["every","every day","every week","every month","every month on the","every year","every week on Sunday","every week on Monday","every week on Tuesday","every week on Wednesday","every week on Thursday","every week on Friday","every week on Saturday"],s=[],a=new RegExp(`(${n})\\s*([0-9a-zA-Z ]*)`,"ug"),o=yf(r,a,e);if(o&&o.length>=2){let l=o[1],c=o[2];if(c.length<t.autoSuggestMinMatch)return[];if(c.length>0){let y=(u=et.fromText({recurrenceRuleText:c,startDate:null,scheduledDate:null,dueDate:null}))==null?void 0:u.toText();if(y){let E=`${l} ${y} `;if(s.push({suggestionType:"match",displayText:`\u2705 ${y}`,appendText:E,insertAt:o.index,insertSkip:o[0].length}),o[0]==E)return[]}}let p=1,m=t.autoSuggestMaxItems/2,T=i.filter(y=>c&&c.length>=p&&y.toLowerCase().includes(c.toLowerCase())).slice(0,m);T.length===0&&c.trim().length===0&&(T=i.slice(0,m));for(let y of T)s.push({suggestionType:"match",displayText:`${y}`,appendText:`${l} ${y} `,insertAt:o.index,insertSkip:o[0].length})}return s}function yf(r,e,t){let n=r.matchAll(e);for(let i of n)if((i==null?void 0:i.index)&&i.index<=t&&t<=i.index+i[0].length)return i}var Hs=class{constructor(){this.hideTaskCount=!1;this.hideBacklinks=!1;this.hidePriority=!1;this.hideCreatedDate=!1;this.hideStartDate=!1;this.hideScheduledDate=!1;this.hideDoneDate=!1;this.hideDueDate=!1;this.hideRecurrenceRule=!1;this.hideEditButton=!1;this.hideUrgency=!0;this.hideTags=!1;this.shortMode=!1;this.explainQuery=!1}},Jr=class{constructor(e,t){this.defaultLayout=["description","priority","recurrenceRule","createdDate","startDate","scheduledDate","dueDate","doneDate","blockLink"];this.hiddenComponents=[];this.specificClasses=[];e?this.options=e:this.options=new Hs,t?this.layoutComponents=t:this.layoutComponents=this.defaultLayout,this.layoutComponents=this.applyOptions(this.options)}applyOptions(e){let t=(s,a,o)=>a?(this.specificClasses.push(`tasks-layout-hide-${o}`),this.hiddenComponents.push(o),s.filter(u=>u!=o)):s,n=(s,a)=>{s&&this.specificClasses.push(`tasks-layout-hide-${a}`)},i=this.layoutComponents;return i=t(i,e.hidePriority,"priority"),i=t(i,e.hideRecurrenceRule,"recurrenceRule"),i=t(i,e.hideCreatedDate,"createdDate"),i=t(i,e.hideStartDate,"startDate"),i=t(i,e.hideScheduledDate,"scheduledDate"),i=t(i,e.hideDueDate,"dueDate"),i=t(i,e.hideDoneDate,"doneDate"),n(e.hideTags,"tags"),n(e.hideUrgency,"urgency"),n(e.hideBacklinks,"backlinks"),n(e.hideEditButton,"edit-button"),e.shortMode&&this.specificClasses.push("tasks-layout-short-mode"),i}};var _f={prioritySymbols:{Highest:"\u{1F53A}",High:"\u23EB",Medium:"\u{1F53C}",Low:"\u{1F53D}",Lowest:"\u23EC",None:""},startDateSymbol:"\u{1F6EB}",createdDateSymbol:"\u2795",scheduledDateSymbol:"\u23F3",dueDateSymbol:"\u{1F4C5}",doneDateSymbol:"\u2705",recurrenceSymbol:"\u{1F501}",TaskFormatRegularExpressions:{priorityRegex:/([🔺⏫🔼🔽⏬])$/u,startDateRegex:/🛫 *(\d{4}-\d{2}-\d{2})$/u,createdDateRegex:/➕ *(\d{4}-\d{2}-\d{2})$/u,scheduledDateRegex:/[⏳⌛] *(\d{4}-\d{2}-\d{2})$/u,dueDateRegex:/[📅📆🗓] *(\d{4}-\d{2}-\d{2})$/u,doneDateRegex:/✅ *(\d{4}-\d{2}-\d{2})$/u,recurrenceRegex:/🔁 ?([a-zA-Z0-9, !]+)$/iu}},On=class{constructor(e){this.symbols=e}serialize(e){let t=new Jr,n="";for(let i of t.layoutComponents)n+=this.componentToString(e,t,i);return n}componentToString(e,t,n){var p;let{prioritySymbols:i,startDateSymbol:s,createdDateSymbol:a,scheduledDateSymbol:o,doneDateSymbol:u,recurrenceSymbol:l,dueDateSymbol:c}=this.symbols;switch(n){case"description":return e.description;case"priority":{let m="";return e.priority==="0"?m=" "+i.Highest:e.priority==="1"?m=" "+i.High:e.priority==="2"?m=" "+i.Medium:e.priority==="4"?m=" "+i.Low:e.priority==="5"&&(m=" "+i.Lowest),m}case"startDate":return e.startDate?t.options.shortMode?" "+s:` ${s} ${e.startDate.format(H.dateFormat)}`:"";case"createdDate":return e.createdDate?t.options.shortMode?" "+a:` ${a} ${e.createdDate.format(H.dateFormat)}`:"";case"scheduledDate":return!e.scheduledDate||e.scheduledDateIsInferred?"":t.options.shortMode?" "+o:` ${o} ${e.scheduledDate.format(H.dateFormat)}`;case"doneDate":return e.doneDate?t.options.shortMode?" "+u:` ${u} ${e.doneDate.format(H.dateFormat)}`:"";case"dueDate":return e.dueDate?t.options.shortMode?" "+c:` ${c} ${e.dueDate.format(H.dateFormat)}`:"";case"recurrenceRule":return e.recurrence?t.options.shortMode?" "+l:` ${l} ${e.recurrence.toText()}`:"";case"blockLink":return(p=e.blockLink)!=null?p:"";default:throw new Error(`Don't know how to render task component of type '${n}'`)}}parsePriority(e){let{prioritySymbols:t}=this.symbols;switch(e){case t.Lowest:return"5";case t.Low:return"4";case t.Medium:return"2";case t.High:return"1";case t.Highest:return"0";default:return"3"}}deserialize(e){let{TaskFormatRegularExpressions:t}=this.symbols,n,i="3",s=null,a=null,o=null,u=null,l=null,c="",p=null,m="",T=20,y=0;do{n=!1;let E=e.match(t.priorityRegex);E!==null&&(i=this.parsePriority(E[1]),e=e.replace(t.priorityRegex,"").trim(),n=!0);let M=e.match(t.doneDateRegex);M!==null&&(u=window.moment(M[1],H.dateFormat),e=e.replace(t.doneDateRegex,"").trim(),n=!0);let U=e.match(t.dueDateRegex);U!==null&&(o=window.moment(U[1],H.dateFormat),e=e.replace(t.dueDateRegex,"").trim(),n=!0);let q=e.match(t.scheduledDateRegex);q!==null&&(a=window.moment(q[1],H.dateFormat),e=e.replace(t.scheduledDateRegex,"").trim(),n=!0);let W=e.match(t.startDateRegex);W!==null&&(s=window.moment(W[1],H.dateFormat),e=e.replace(t.startDateRegex,"").trim(),n=!0);let ie=e.match(t.createdDateRegex);ie!==null&&(l=window.moment(ie[1],H.dateFormat),e=e.replace(t.createdDateRegex,"").trim(),n=!0);let re=e.match(t.recurrenceRegex);re!==null&&(c=re[1].trim(),e=e.replace(t.recurrenceRegex,"").trim(),n=!0);let ve=e.match(H.hashTagsFromEnd);if(ve!=null){e=e.replace(H.hashTagsFromEnd,"").trim(),n=!0;let G=ve[0].trim();m=m.length>0?[G,m].join(" "):G}y++}while(n&&y<=T);return c.length>0&&(p=et.fromText({recurrenceRuleText:c,startDate:s,scheduledDate:a,dueDate:o})),m.length>0&&(e+=" "+m),{description:e,priority:i,startDate:s,createdDate:l,scheduledDate:a,dueDate:o,doneDate:u,recurrence:p,tags:Te.extractHashtags(e)}}};function Dn(r){let e=["(?:",/(?=[^\]]+\])\[/,"|",/(?=[^)]+\))\(/,")",/ */,r,/ */,/[)\]]/,/(?: *,)?/,/$/].map(t=>t instanceof RegExp?t.source:t).join("");return new RegExp(e,r.flags)}var Tf={prioritySymbols:{Highest:"priority:: highest",High:"priority:: high",Medium:"priority:: medium",Low:"priority:: low",Lowest:"priority:: lowest",None:""},startDateSymbol:"start::",createdDateSymbol:"created::",scheduledDateSymbol:"scheduled::",dueDateSymbol:"due::",doneDateSymbol:"completion::",recurrenceSymbol:"repeat::",TaskFormatRegularExpressions:{priorityRegex:Dn(/priority:: *(highest|high|medium|low|lowest)/),startDateRegex:Dn(/start:: *(\d{4}-\d{2}-\d{2})/),createdDateRegex:Dn(/created:: *(\d{4}-\d{2}-\d{2})/),scheduledDateRegex:Dn(/scheduled:: *(\d{4}-\d{2}-\d{2})/),dueDateRegex:Dn(/due:: *(\d{4}-\d{2}-\d{2})/),doneDateRegex:Dn(/completion:: *(\d{4}-\d{2}-\d{2})/),recurrenceRegex:Dn(/repeat:: *([a-zA-Z0-9, !]+)/)}},bo=class extends On{constructor(){super(Tf)}parsePriority(e){switch(e){case"highest":return"0";case"high":return"1";case"medium":return"2";case"low":return"4";case"lowest":return"5";default:return"3"}}componentToString(e,t,n){let i=super.componentToString(e,t,n);return i!==""&&!["blockLink","description"].includes(n)?`  [${i.trim()}]`:i}};var zt=(a=>(a.TODO="TODO",a.DONE="DONE",a.IN_PROGRESS="IN_PROGRESS",a.CANCELLED="CANCELLED",a.NON_TASK="NON_TASK",a.EMPTY="EMPTY",a))(zt||{}),tt=class{constructor(e,t,n,i,s="TODO"){this.symbol=e,this.name=t,this.nextStatusSymbol=n,this.availableAsCommand=i,this.type=s}};var kt=class{get symbol(){return this.configuration.symbol}get name(){return this.configuration.name}get nextStatusSymbol(){return this.configuration.nextStatusSymbol}get nextSymbol(){return this.configuration.nextStatusSymbol}get availableAsCommand(){return this.configuration.availableAsCommand}get type(){return this.configuration.type}constructor(e){this.configuration=e}static makeDone(){return new kt(new tt("x","Done"," ",!0,"DONE"))}static makeEmpty(){return new kt(new tt("","EMPTY","",!0,"EMPTY"))}static makeTodo(){return new kt(new tt(" ","Todo","x",!0,"TODO"))}static makeCancelled(){return new kt(new tt("-","Cancelled"," ",!0,"CANCELLED"))}static makeInProgress(){return new kt(new tt("/","In Progress","x",!0,"IN_PROGRESS"))}static getTypeForUnknownSymbol(e){switch(e){case"x":case"X":return"DONE";case"/":return"IN_PROGRESS";case"-":return"CANCELLED";case"":return"EMPTY";case" ":default:return"TODO"}}static getTypeFromStatusTypeString(e){return zt[e]||"TODO"}static createUnknownStatus(e){return new kt(new tt(e,"Unknown","x",!1,"TODO"))}static createFromImportedValue(e){let t=e[0],n=kt.getTypeFromStatusTypeString(e[3]);return new kt(new tt(t,e[1],e[2],!1,n))}isCompleted(){return this.type==="DONE"}identicalTo(e){let t=["symbol","name","nextStatusSymbol","availableAsCommand","type"];for(let n of t)if(this[n]!==e[n])return!1;return!0}previewText(){let e="";return kt.tasksPluginCanCreateCommandsForStatuses()&&this.availableAsCommand&&(e=" Available as a command."),`- [${this.symbol}] => [${this.nextStatusSymbol}], name: '${this.name}', type: '${this.configuration.type}'.${e}`}static tasksPluginCanCreateCommandsForStatuses(){return!1}},ee=kt;ee.DONE=kt.makeDone(),ee.EMPTY=kt.makeEmpty(),ee.TODO=kt.makeTodo();var vo=class{constructor(e=!1,t=!1){this.ignoreSortInstructions=e,this.showTaskHiddenData=t}};var He=class{constructor(){this.coreStatuses=[ee.makeTodo().configuration,ee.makeDone().configuration],this.customStatuses=[ee.makeInProgress().configuration,ee.makeCancelled().configuration]}static addStatus(e,t){e.push(t)}static replaceStatus(e,t,n){let i=this.findStatusIndex(t,e);return i<=-1?!1:(e.splice(i,1,n),!0)}static findStatusIndex(e,t){let n=new ee(e);return t.findIndex(i=>new ee(i).previewText()==n.previewText())}static deleteStatus(e,t){let n=this.findStatusIndex(t,e);return n<=-1?!1:(e.splice(n,1),!0)}static deleteAllCustomStatuses(e){e.customStatuses.splice(0)}static resetAllCustomStatuses(e){He.deleteAllCustomStatuses(e),new He().customStatuses.forEach(n=>{He.addStatus(e.customStatuses,n)})}static bulkAddStatusCollection(e,t){let n=[];return t.forEach(i=>{e.customStatuses.find(a=>a.symbol==i[0]&&a.name==i[1]&&a.nextStatusSymbol==i[2])?n.push(`The status ${i[1]} (${i[0]}) is already added.`):He.addStatus(e.customStatuses,ee.createFromImportedValue(i))}),n}static applyToStatusRegistry(e,t){t.clearStatuses(),e.coreStatuses.forEach(n=>{t.add(n)}),e.customStatuses.forEach(n=>{t.add(n)})}};var J_=[{index:9999,internalName:"INTERNAL_TESTING_ENABLED_BY_DEFAULT",displayName:"Test Item. Used to validate the Feature Framework.",description:"Description",enabledByDefault:!0,stable:!1}];var pr=class{constructor(e,t,n,i,s,a){this.internalName=e;this.index=t;this.description=n;this.displayName=i;this.enabledByDefault=s;this.stable=a}static get values(){let e=[];return J_.forEach(t=>{e=[...e,new pr(t.internalName,t.index,t.description,t.displayName,t.enabledByDefault,t.stable)]}),e}static get settingsFlags(){let e={};return pr.values.forEach(t=>{e[t.internalName]=t.enabledByDefault}),e}static fromString(e){for(let t of pr.values)if(e===t.internalName)return t;throw new RangeError(`Illegal argument passed to fromString(): ${e} does not correspond to any available Feature ${this.prototype.constructor.name}`)}};function Eo(r){return r.replace(/([.*+?^${}()|[\]/\\])/g,"\\$1")}var Vt=class{static get(){let{globalFilter:e}=Q();return e}static set(e){Ge({globalFilter:e})}static reset(){Ge({globalFilter:Vt.empty})}static isEmpty(){return Vt.get()===Vt.empty}static equals(e){return Vt.get()===e}static includedIn(e){let t=Vt.get();return e.includes(t)}static prependTo(e){return Vt.get()+" "+e}static removeAsWordFromDependingOnSettings(e){let{removeGlobalFilter:t}=Q();return t?Vt.removeAsWordFrom(e):e}static removeAsWordFrom(e){if(Vt.isEmpty())return e;let t=RegExp("(^|\\s)"+Eo(Vt.get())+"($|\\s)","ug");return e.search(t)>-1&&(e=e.replace(t,"$1$2").replace("  "," ").trim()),e}static removeAsSubstringFrom(e){let t=Vt.get();return e.replace(t,"").trim()}},we=Vt;we.empty="";var Cr={tasksPluginEmoji:{displayName:"Tasks Emoji Format",taskSerializer:new On(_f),buildSuggestions:gf(_f,hf)},dataview:{displayName:"Dataview",taskSerializer:new bo,buildSuggestions:gf(Tf,hf)}},wx={globalQuery:"",globalFilter:we.empty,removeGlobalFilter:!1,taskFormat:"tasksPluginEmoji",setCreatedDate:!1,setDoneDate:!0,autoSuggestInEditor:!0,autoSuggestMinMatch:0,autoSuggestMaxItems:6,provideAccessKeys:!0,useFilenameAsScheduledDate:!1,filenameAsDateFolders:[],recurrenceOnNextLine:!1,statusSettings:new He,features:pr.settingsFlags,generalSettings:{},headingOpened:{},debugSettings:new vo},en=he({},wx),Q=()=>{for(let r in pr.settingsFlags)en.features[r]===void 0&&(en.features[r]=pr.settingsFlags[r]);return en.statusSettings.customStatuses.forEach((r,e,t)=>{var i,s;let n=ee.getTypeFromStatusTypeString(r.type);t[e]=new tt((i=r.symbol)!=null?i:" ",r.name,(s=r.nextStatusSymbol)!=null?s:"x",r.availableAsCommand,n)}),he({},en)},Ge=r=>(en=he(he({},en),r),Q());var Sn=(r,e)=>(en.generalSettings[r]=e,Q()),tT=r=>{var e;return(e=en.features[r])!=null?e:!1};function Vs(){return Cr[Q().taskFormat]}function bf(){return{source:Q().globalQuery}}var Re=class{constructor(){this._registeredStatuses=[];this.addDefaultStatusTypes()}get registeredStatuses(){return this._registeredStatuses.filter(({symbol:e})=>e!==ee.EMPTY.symbol)}static getInstance(){return Re.instance||(Re.instance=new Re),Re.instance}add(e){this.hasSymbol(e.symbol)||(e instanceof ee?this._registeredStatuses.push(e):this._registeredStatuses.push(new ee(e)))}bySymbol(e){return this.hasSymbol(e)?this.getSymbol(e):ee.EMPTY}bySymbolOrCreate(e){return this.hasSymbol(e)?this.getSymbol(e):ee.createUnknownStatus(e)}byName(e){return this._registeredStatuses.filter(({name:t})=>t===e).length>0?this._registeredStatuses.filter(({name:t})=>t===e)[0]:ee.EMPTY}resetToDefaultStatuses(){this.clearStatuses(),this.addDefaultStatusTypes()}clearStatuses(){this._registeredStatuses=[]}getNextStatus(e){if(e.nextStatusSymbol!==""){let t=this.bySymbol(e.nextStatusSymbol);if(t!==null)return t}return ee.EMPTY}getNextStatusOrCreate(e){let t=this.getNextStatus(e);return t.type!=="EMPTY"?t:ee.createUnknownStatus(e.nextStatusSymbol)}findUnknownStatuses(e){let t=e.filter(s=>!this.hasSymbol(s.symbol)),n=new Re,i=[];return t.forEach(s=>{if(n.hasSymbol(s.symbol))return;let a=Re.copyStatusWithNewName(s,`Unknown (${s.symbol})`);i.push(a),n.add(a)}),i}static copyStatusWithNewName(e,t){let n=new tt(e.symbol,t,e.nextStatusSymbol,e.availableAsCommand,e.type);return new ee(n)}getSymbol(e){return this._registeredStatuses.filter(({symbol:t})=>t===e)[0]}hasSymbol(e){return this._registeredStatuses.find(t=>t.symbol===e)!==void 0}addDefaultStatusTypes(){[ee.makeTodo(),ee.makeInProgress(),ee.makeDone(),ee.makeCancelled()].forEach(t=>{this.add(t)})}};var rr=class{static calculate(e){let t=0;if(e.dueDate!==null){let n=window.moment().startOf("day"),i=Math.round(n.diff(e.dueDate)/rr.milliSecondsPerDay),s;i>=7?s=1:i>=-14?s=(i+14)*.8/21+.2:s=.2,t+=s*rr.dueCoefficient}switch(e.scheduledDate!==null&&window.moment().isSameOrAfter(e.scheduledDate)&&(t+=1*rr.scheduledCoefficient),e.startDate!==null&&window.moment().isBefore(e.startDate)&&(t+=1*rr.startedCoefficient),e.priority){case"0":t+=1.5*rr.priorityCoefficient;break;case"1":t+=1*rr.priorityCoefficient;break;case"2":t+=.65*rr.priorityCoefficient;break;case"3":t+=.325*rr.priorityCoefficient;break;case"5":t-=.3*rr.priorityCoefficient;break}return t}},Pr=rr;Pr.dueCoefficient=12,Pr.scheduledCoefficient=5,Pr.startedCoefficient=-3,Pr.priorityCoefficient=6,Pr.milliSecondsPerDay=1e3*60*60*24;var dT=require("obsidian");var Xs=require("obsidian");var kx=require("obsidian"),sT=Wi(iT());var vf=class extends sT.EventEmitter2{constructor(){super(...arguments);this.options={minLevels:{"":"info",tasks:"info"}};this.consoleLoggerRegistered=!1;this.arrAvg=t=>t.reduce((n,i)=>n+i,0)/t.length}configure(t){return this.options=Object.assign({},this.options,t),this}getLogger(t){let n="none",i="";for(let s in this.options.minLevels)t.startsWith(s)&&s.length>=i.length&&(n=this.options.minLevels[s],i=s);return new Ef(this,t,n)}onLogEntry(t){return this.on("log",t),this}registerConsoleLogger(){return this.consoleLoggerRegistered?this:(this.onLogEntry(t=>{let n=`[${window.moment().format("YYYY-MM-DD-HH:mm:ss.SSS")}][${t.level}][${t.module}]`;switch(t.traceId&&(n+=`[${t.traceId}]`),n+=` ${t.message}`,t.objects===void 0&&(t.objects=""),t.level){case"trace":console.trace(n,t.objects);break;case"debug":console.debug(n,t.objects);break;case"info":console.info(n,t.objects);break;case"warn":console.warn(n,t.objects);break;case"error":console.error(n,t.objects);break;default:console.log(`{${t.level}} ${n}`,t.objects)}}),this.consoleLoggerRegistered=!0,this)}},wo=new vf,Ef=class{constructor(e,t,n){this.levels={trace:1,debug:2,info:3,warn:4,error:5};this.logManager=e,this.module=t,this.minLevel=this.levelToInt(n)}levelToInt(e){return e.toLowerCase()in this.levels?this.levels[e.toLowerCase()]:99}log(e,t,n){if(this.levelToInt(e)<this.minLevel)return;let s={level:e,module:this.module,message:t,objects:n,traceId:void 0};this.logManager.emit("log",s)}trace(e,t){this.log("trace",e,t)}debug(e,t){this.log("debug",e,t)}info(e,t){this.log("info",e,t)}warn(e,t){this.log("warn",e,t)}error(e,t){this.log("error",e,t)}logWithId(e,t,n,i){if(this.levelToInt(e)<this.minLevel)return;let a={level:e,module:this.module,message:n,objects:i,traceId:t};this.logManager.emit("log",a)}traceWithId(e,t,n){this.logWithId("trace",e,t,n)}debugWithId(e,t,n){this.logWithId("debug",e,t,n)}infoWithId(e,t,n){this.logWithId("info",e,t,n)}warnWithId(e,t,n){this.logWithId("warn",e,t,n)}errorWithId(e,t,n){this.logWithId("error",e,t,n)}};var Ks,wf,kf,Ox=["md"],Qs=wo.getLogger("tasks"),aT=({metadataCache:r,vault:e,workspace:t})=>{Ks=r,wf=e,kf=t},Do=t=>F(void 0,[t],function*({originalTask:r,newTasks:e}){if(wf===void 0||Ks===void 0||kf===void 0){ko("Tasks: cannot use File before initializing it.");return}Array.isArray(e)||(e=[e]),Qs.debug(`replaceTaskWithTasks entered. ${r.path}`),uT({originalTask:r,newTasks:e,vault:wf,metadataCache:Ks,workspace:kf,previousTries:0})});function ko(r){console.error(r),new Xs.Notice(r,15e3)}function oT(r){console.warn(r),new Xs.Notice(r,1e4)}function Dx(r){Qs.debug(r)}var tn=class extends Error{},Oo=class extends Error{},uT=a=>F(void 0,[a],function*({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s}){Qs.debug(`tryRepetitive after ${s} previous tries`);let o=()=>{if(s>10){let l=`Tasks: Could not find the correct task line to update.

The task line not updated is:
${r.originalMarkdown}

In this markdown file:
"${r.taskLocation.path}"

Note: further clicks on this checkbox will usually now be ignored until the file is opened (or certain, specific edits are made - it's complicated).

Recommendations:

1. Close all panes that have the above file open, and then re-open the file.

2. Check for exactly identical copies of the task line, in this file, and see if you can make them different.
`;ko(l);return}let u=Math.min(Math.pow(10,s),100);Qs.debug(`timeout = ${u}`),setTimeout(()=>{uT({originalTask:r,newTasks:e,vault:t,metadataCache:n,workspace:i,previousTries:s+1})},u)};try{let[u,l,c]=yield lT(r,t),p=[...c.slice(0,u),...e.map(m=>m.toFileLineString()),...c.slice(u+1)];yield t.modify(l,p.join(`
`))}catch(u){if(u instanceof tn)return u.message&&oT(u.message),o();if(u instanceof Oo)return o();u instanceof Error&&ko(u.message)}});function lT(r,e){return F(this,null,function*(){if(Ks===void 0)throw new tn;let t=e.getAbstractFileByPath(r.path);if(!(t instanceof Xs.TFile))throw new tn(`Tasks: No file found for task ${r.description}. Retrying ...`);if(!Ox.includes(t.extension))throw new Error(`Tasks: Does not support files with the ${t.extension} file extension.`);let n=Ks.getFileCache(t);if(n==null||n===null)throw new tn(`Tasks: No file cache found for file ${t.path}. Retrying ...`);let i=n.listItems;if(i===void 0||i.length===0)throw new tn(`Tasks: No list items found in file cache of ${t.path}. Retrying ...`);let a=(yield e.read(t)).split(`
`),o=Sx(r,a,i,Dx);if(o===void 0)throw new Oo;return[o,t,a]})}function Of(r,e){return F(this,null,function*(){try{let[t,n,i]=yield lT(r,e);return[t,n]}catch(t){t instanceof tn?t.message&&oT(t.message):t instanceof Error&&ko(t.message)}})}function cT(r,e){return r<e.length}function Sx(r,e,t,n){let i=Rx(r,e);return i!==void 0||(i=Mx(r,e),i!==void 0)?i:xx(r,e,t,n)}function Rx(r,e){let t=r.taskLocation.lineNumber;if(cT(t,e)&&e[t]===r.originalMarkdown)return Qs.debug(`Found original markdown at original line number ${t}`),t}function Mx(r,e){let t=[];for(let n=0;n<e.length;n++)e[n]===r.originalMarkdown&&t.push(n);if(t.length===1)return t[0]}function xx(r,e,t,n){let i,s=0;for(let a of t){let o=a.position.start.line;if(!cT(o,e))return;if(o<r.taskLocation.sectionStart||a.task===void 0)continue;let u=e[o];if(we.includedIn(u)){if(s===r.taskLocation.sectionIndex){if(u===r.originalMarkdown)i=o;else{n(`Tasks: Unable to find task in file ${r.taskLocation.path}.
Expected task:
${r.originalMarkdown}
Found task:
${u}`);return}break}s++}}return i}var mr=class{static priorityNameUsingNone(e){let t="ERROR";switch(e){case"1":t="High";break;case"0":t="Highest";break;case"2":t="Medium";break;case"3":t="None";break;case"4":t="Low";break;case"5":t="Lowest";break}return t}static priorityNameUsingNormal(e){return mr.priorityNameUsingNone(e).replace("None","Normal")}};var rn={description:"task-description",priority:"task-priority",dueDate:"task-due",startDate:"task-start",createdDate:"task-created",scheduledDate:"task-scheduled",doneDate:"task-done",recurrenceRule:"task-recurring",blockLink:""},Ax=7,Cx="far";function Px(r,e,t,n){return F(this,null,function*(){if(!n)throw new Error("Must call the Obsidian renderer with an Obsidian Component object");yield dT.MarkdownRenderer.renderMarkdown(r,e,t,n)})}function fT(r,e,t=null){return F(this,null,function*(){var o;t||(t=Px);let n=document.createElement("li");e.parentUlElement.appendChild(n),n.classList.add("task-list-item","plugin-tasks-list-item");let i=document.createElement("span");n.appendChild(i),i.classList.add("tasks-list-text");let s=yield Nx(r,e,i,t);for(let u in s)n.dataset[u]=s[u];let a=document.createElement("input");return n.appendChild(a),a.classList.add("task-list-item-checkbox"),a.type="checkbox",r.status.symbol!==" "&&(a.checked=!0,n.classList.add("is-checked")),a.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),a.disabled=!0;let l=r.toggleWithRecurrenceInUsersOrder();Do({originalTask:r,newTasks:l})}),n.prepend(a),n.setAttribute("data-task",r.status.symbol.trim()),n.setAttribute("data-line",e.listIndex.toString()),n.setAttribute("data-task-status-name",r.status.name),n.setAttribute("data-task-status-type",r.status.type),a.setAttribute("data-line",e.listIndex.toString()),(o=e.layoutOptions)!=null&&o.shortMode&&Wx({task:r,element:i,isFilenameUnique:e.isFilenameUnique}),n})}function Nx(r,e,t,n){return F(this,null,function*(){let i={},s=new Jr(e.layoutOptions),a=Cr.tasksPluginEmoji.taskSerializer;for(let o of s.layoutComponents){let u=a.componentToString(r,s,o);if(u){o==="description"&&(u=we.removeAsWordFromDependingOnSettings(u));let l=document.createElement("span");if(t.appendChild(l),l){let c=document.createElement("span");l.appendChild(c),yield Ix(c,u,o,r,n,e.obsidianComponent);let[p,m]=Df(o,r);Fx(o,c),l.classList.add(...p);for(let T in m)l.dataset[T]=m[T];i=he(he({},i),m)}}}for(let o of s.hiddenComponents){let[u,l]=Df(o,r);i=he(he({},i),l)}if(i.taskPriority===void 0){let[o,u]=Df("priority",r);i=he(he({},i),u)}return i})}function Ix(r,e,t,n,i,s){return F(this,null,function*(){if(t==="description"){let{debugSettings:a}=Q();a.showTaskHiddenData&&(e+=`<br>\u{1F41B} <b>${n.lineNumber}</b> . ${n.sectionStart} . ${n.sectionIndex} . '<code>${n.originalMarkdown}</code>'<br>'<code>${n.path}</code>' > '<code>${n.precedingHeader}</code>'<br>`),yield i(e,r,n.path,s);let o=r.querySelector("blockquote"),u=o!=null?o:r,l=u.querySelector("p");if(l!==null){for(;l.firstChild;)u.insertBefore(l.firstChild,l);l.remove()}r.querySelectorAll("p").forEach(c=>{c.hasChildNodes()||c.remove()}),r.querySelectorAll(".footnotes").forEach(c=>{c.remove()})}else r.innerHTML=e})}function Df(r,e){let t=[],n={};function i(s,a,o){if(s){t.push(a);let u=Lx(s);u&&(n[o]=u)}}switch(r){case"description":t.push(rn.description);break;case"priority":{n.taskPriority=mr.priorityNameUsingNormal(e.priority).toLocaleLowerCase(),t.push(rn.priority);break}case"createdDate":{i(e.createdDate,rn.createdDate,"taskCreated");break}case"dueDate":{i(e.dueDate,rn.dueDate,"taskDue");break}case"startDate":{i(e.startDate,rn.startDate,"taskStart");break}case"scheduledDate":{i(e.scheduledDate,rn.scheduledDate,"taskScheduled");break}case"doneDate":{i(e.doneDate,rn.doneDate,"taskDone");break}case"recurrenceRule":{t.push(rn.recurrenceRule);break}}return[t,n]}function Fx(r,e){if(r==="description"){let t=e.getElementsByClassName("tag");for(let n=0;n<t.length;n++){let i=t[n].textContent;if(i){let s=Ux(i),a=t[n];s&&(a.dataset.tagName=s)}}}}function Lx(r){let e=window.moment().startOf("day"),t="",n=e.diff(r,"days");return isNaN(n)?null:n===0?"today":(n>0?t+="past-":n<0&&(t+="future-"),Math.abs(n)<=Ax?t+=Math.abs(n).toString()+"d":t+=Cx,t)}function Ux(r){let e=/["&\x00\r\n]/g,t=r.replace(e,"-");return t=t.replace(/^[-_]+/,""),t.length>0?t:null}function Wx({task:r,element:e,isFilenameUnique:t}){let{recurrenceSymbol:n,startDateSymbol:i,createdDateSymbol:s,scheduledDateSymbol:a,dueDateSymbol:o,doneDateSymbol:u}=Cr.tasksPluginEmoji.taskSerializer.symbols;e.addEventListener("mouseenter",()=>{let l=e.createDiv();l.addClasses(["tooltip","pop-up"]),r.recurrence&&l.createDiv().setText(`${n} ${r.recurrence.toText()}`),Zs(l,r.createdDate,s),Zs(l,r.startDate,i),Zs(l,r.scheduledDate,a),Zs(l,r.dueDate,o),Zs(l,r.doneDate,u);let c=r.getLinkText({isFilenameUnique:t});c&&l.createDiv().setText(`\u{1F517} ${c}`),e.addEventListener("mouseleave",()=>{l.remove()})})}function Zs(r,e,t){e&&r.createDiv().setText(qx({signifier:t,date:e}))}function qx({signifier:r,date:e}){return`${r} ${e.format(H.dateFormat)} (${e.from(window.moment().startOf("day"))})`}var Ot=class{static fromPath(e){let{useFilenameAsScheduledDate:t,filenameAsDateFolders:n}=Q();return!t||!this.matchesAnyFolder(n,e)?null:this.extractDateFromPath(e)}static matchesAnyFolder(e,t){return e.length===0?!0:e.some(n=>t.startsWith(n+"/"))}static extractDateFromPath(e){let t=Math.max(0,e.lastIndexOf("/")+1),n=e.lastIndexOf("."),i=e.substring(t,n),s=/(\d{4})-(\d{2})-(\d{2})/.exec(i);if(s||(s=/(\d{4})(\d{2})(\d{2})/.exec(i)),s){let a=window.moment([parseInt(s[1]),parseInt(s[2])-1,parseInt(s[3])]);if(a.isValid())return a}return null}static canApplyFallback({startDate:e,scheduledDate:t,dueDate:n}){return e===null&&n===null&&t===null}static updateTaskPath(e,t,n){let i=e.scheduledDate,s=e.scheduledDateIsInferred;return n===null?s&&(s=!1,i=null):s?i=n:this.canApplyFallback(e)&&(i=n,s=!0),new Te(Dt(he({},e),{taskLocation:e.taskLocation.fromRenamedFile(t),scheduledDate:i,scheduledDateIsInferred:s}))}static removeInferredStatusIfNeeded(e,t){let n=e.scheduledDateIsInferred?e.scheduledDate:null;return t.map(i=>(n!==null&&!n.isSame(i.scheduledDate,"day")&&(i=new Te(Dt(he({},i),{scheduledDateIsInferred:!1}))),i))}};var Tt=class{constructor(e){this._date=null;this._date=e}get moment(){return this._date}formatAsDate(e=""){return this.format(H.dateFormat,e)}formatAsDateAndTime(e=""){return this.format(H.dateTimeFormat,e)}format(e,t=""){return this._date?this._date.format(e):t}toISOString(e){return this._date?this._date.toISOString(e):""}};var So=class{constructor(e){this._path=e}get path(){return this._path}get root(){let e=this.path.replace(/\\/g,"/");e.charAt(0)==="/"&&(e=e.substring(1));let t=e.indexOf("/");return t==-1?"/":e.substring(0,t+1)}get folder(){let e=this.path,t=this.filename,n=e.substring(0,e.lastIndexOf(t));return n===""?"/":n}get filename(){let e=this.path.match(/([^/]+)$/);return e!==null?e[1]:""}};var Pt=class{},H=Pt;H.dateFormat="YYYY-MM-DD",H.dateTimeFormat="YYYY-MM-DD HH:mm",H.indentationRegex=/^([\s\t>]*)/,H.listMarkerRegex=/([-*+]|[0-9]+\.)/,H.checkboxRegex=/\[(.)\]/u,H.afterCheckboxRegex=/ *(.*)/u,H.taskRegex=new RegExp(Pt.indentationRegex.source+Pt.listMarkerRegex.source+" +"+Pt.checkboxRegex.source+Pt.afterCheckboxRegex.source,"u"),H.nonTaskRegex=new RegExp(Pt.indentationRegex.source+Pt.listMarkerRegex.source+"? *("+Pt.checkboxRegex.source+")?"+Pt.afterCheckboxRegex.source,"u"),H.listItemRegex=new RegExp(Pt.indentationRegex.source+Pt.listMarkerRegex.source),H.blockLinkRegex=/ \^[a-zA-Z0-9-]+$/u,H.hashTags=/(^|\s)#[^ !@#$%^&*(),.?":{}|<>]*/g,H.hashTagsFromEnd=new RegExp(Pt.hashTags.source+"$");var Te=class{constructor({status:e,description:t,taskLocation:n,indentation:i,listMarker:s,priority:a,createdDate:o,startDate:u,scheduledDate:l,dueDate:c,doneDate:p,recurrence:m,blockLink:T,tags:y,originalMarkdown:E,scheduledDateIsInferred:M}){this._urgency=null;this.status=e,this.description=t,this.indentation=i,this.listMarker=s,this.taskLocation=n,this.tags=y,this.priority=a,this.createdDate=o,this.startDate=u,this.scheduledDate=l,this.dueDate=c,this.doneDate=p,this.recurrence=m,this.blockLink=T,this.originalMarkdown=E,this.scheduledDateIsInferred=M}static fromLine({line:e,taskLocation:t,fallbackDate:n}){let i=Te.extractTaskComponents(e);return i===null||!we.includedIn(i.body)?null:Te.parseTaskSignifiers(e,t,n)}static parseTaskSignifiers(e,t,n){let i=Te.extractTaskComponents(e);if(i===null)return null;let{taskSerializer:s}=Vs(),a=s.deserialize(i.body),o=!1;return Ot.canApplyFallback(a)&&n!==null&&(a.scheduledDate=n,o=!0),a.tags=a.tags.map(u=>u.trim()),a.tags=a.tags.filter(u=>!we.equals(u)),new Te(Dt(he(he({},i),a),{taskLocation:t,originalMarkdown:e,scheduledDateIsInferred:o}))}static extractTaskComponents(e){let t=e.match(H.taskRegex);if(t===null)return null;let n=t[1],i=t[2],s=t[3],a=Re.getInstance().bySymbolOrCreate(s),o=t[4].trim(),u=o.match(H.blockLinkRegex),l=u!==null?u[0]:"";return l!==""&&(o=o.replace(H.blockLinkRegex,"").trim()),{indentation:n,listMarker:i,status:a,body:o,blockLink:l}}toLi(e){return F(this,null,function*(){return fT(this,e)})}toString(){return Vs().taskSerializer.serialize(this)}toFileLineString(){return`${this.indentation}${this.listMarker} [${this.status.symbol}] ${this.toString()}`}toggle(){let e=Re.getInstance().getNextStatusOrCreate(this.status),t=null,n=null;if(e.isCompleted()){let{setDoneDate:a}=Q();a&&(t=window.moment()),this.recurrence!==null&&(n=this.recurrence.next())}let i=new Te(Dt(he({},this),{status:e,doneDate:t})),s=[];if(n!==null){let{setCreatedDate:a}=Q(),o=null;a&&(o=window.moment());let u=Re.getInstance().getNextStatusOrCreate(e),l=new Te(Dt(he(he({},this),n),{status:u,blockLink:"",createdDate:o}));s.push(l)}return s.push(i),s}toggleWithRecurrenceInUsersOrder(){let e=this.toggle(),{recurrenceOnNextLine:t}=Q();return t?e.reverse():e}get isDone(){return this.status.type==="DONE"||this.status.type==="CANCELLED"||this.status.type==="NON_TASK"}get priorityNumber(){return Number.parseInt(this.priority)}get descriptionWithoutTags(){return this.description.replace(H.hashTags,"").trim()}get priorityName(){return mr.priorityNameUsingNormal(this.priority)}get urgency(){return this._urgency===null&&(this._urgency=Pr.calculate(this)),this._urgency}get path(){return this.taskLocation.path}get created(){return new Tt(this.createdDate)}get done(){return new Tt(this.doneDate)}get due(){return new Tt(this.dueDate)}get scheduled(){return new Tt(this.scheduledDate)}get start(){return new Tt(this.startDate)}get happensDates(){return Array.of(this.startDate,this.scheduledDate,this.dueDate)}get happens(){let t=this.happensDates.sort(Ar);return new Tt(t[0])}get isRecurring(){return this.recurrence!==null}get recurrenceRule(){return this.recurrence?this.recurrence.toText():""}get heading(){return this.precedingHeader}get hasHeading(){return this.precedingHeader!==null}get file(){return new So(this.path)}get filename(){let e=this.path.match(/([^/]+)\.md$/);return e!==null?e[1]:null}get lineNumber(){return this.taskLocation.lineNumber}get sectionStart(){return this.taskLocation.sectionStart}get sectionIndex(){return this.taskLocation.sectionIndex}get precedingHeader(){return this.taskLocation.precedingHeader}getLinkText({isFilenameUnique:e}){let t;return e?t=this.filename:t="/"+this.path,t===null?null:(this.precedingHeader!==null&&this.precedingHeader!==t&&(t=t+" > "+this.precedingHeader),t)}static tasksListsIdentical(e,t){return e.length!==t.length?!1:e.every((n,i)=>n.identicalTo(t[i]))}identicalTo(e){let t=["description","path","indentation","listMarker","lineNumber","sectionStart","sectionIndex","precedingHeader","priority","blockLink","scheduledDateIsInferred"];for(let s of t)if(this[s]!==e[s])return!1;if(!this.status.identicalTo(e.status)||this.tags.length!==e.tags.length||!this.tags.every(function(s,a){return s===e.tags[a]}))return!1;t=["createdDate","startDate","scheduledDate","dueDate","doneDate"];for(let s of t){let a=this[s],o=e[s];if(Ar(a,o)!==0)return!1}let n=this.recurrence,i=e.recurrence;return n===null&&i!==null||n!==null&&i===null?!1:!(n&&i&&!n.identicalTo(i))}static extractHashtags(e){var t,n;return(n=(t=e.match(H.hashTags))==null?void 0:t.map(i=>i.trim()))!=null?n:[]}};var Js=class{constructor(e){this.fetch=e;this._value=void 0}get value(){return this._value===void 0&&(this._value=this.fetch()),this._value}};var ot=class{constructor(e,t,n,i,s){this._path=e,this._lineNumber=t,this._sectionStart=n,this._sectionIndex=i,this._precedingHeader=s}static fromUnknownPosition(e){return new ot(e,0,0,0,null)}fromRenamedFile(e){return new ot(e,this.lineNumber,this.sectionStart,this.sectionIndex,this.precedingHeader)}get path(){return this._path}get lineNumber(){return this._lineNumber}get sectionStart(){return this._sectionStart}get sectionIndex(){return this._sectionIndex}get precedingHeader(){return this._precedingHeader}};var Rn=class{constructor({metadataCache:e,vault:t,events:n}){this.metadataCache=e,this.metadataCacheEventReferences=[],this.vault=t,this.vaultEventReferences=[],this.events=n,this.eventsEventReferences=[],this.tasksMutex=new Ta,this.state="Cold",this.tasks=[],this.loadedAfterFirstResolve=!1,this.subscribeToCache(),this.subscribeToVault(),this.subscribeToEvents(),this.loadVault()}unload(){for(let e of this.metadataCacheEventReferences)this.metadataCache.offref(e);for(let e of this.vaultEventReferences)this.vault.offref(e);for(let e of this.eventsEventReferences)this.events.off(e)}getTasks(){return this.tasks}getState(){return this.state}notifySubscribers(){this.events.triggerCacheUpdate({tasks:this.tasks,state:this.state})}subscribeToCache(){let e=this.metadataCache.on("resolved",()=>F(this,null,function*(){this.loadedAfterFirstResolve||(this.loadedAfterFirstResolve=!0,this.loadVault())}));this.metadataCacheEventReferences.push(e);let t=this.metadataCache.on("changed",n=>{this.tasksMutex.runExclusive(()=>{this.indexFile(n)})});this.metadataCacheEventReferences.push(t)}subscribeToVault(){let{useFilenameAsScheduledDate:e}=Q(),t=this.vault.on("create",s=>{s instanceof pi.TFile&&this.tasksMutex.runExclusive(()=>{this.indexFile(s)})});this.vaultEventReferences.push(t);let n=this.vault.on("delete",s=>{s instanceof pi.TFile&&this.tasksMutex.runExclusive(()=>{this.tasks=this.tasks.filter(a=>a.path!==s.path),this.notifySubscribers()})});this.vaultEventReferences.push(n);let i=this.vault.on("rename",(s,a)=>{s instanceof pi.TFile&&this.tasksMutex.runExclusive(()=>{let o=new Js(()=>Ot.fromPath(s.path));this.tasks=this.tasks.map(u=>u.path===a?e?Ot.updateTaskPath(u,s.path,o.value):new Te(Dt(he({},u),{taskLocation:u.taskLocation.fromRenamedFile(s.path)})):u),this.notifySubscribers()})});this.vaultEventReferences.push(i)}subscribeToEvents(){let e=this.events.onRequestCacheUpdate(t=>{t({tasks:this.tasks,state:this.state})});this.eventsEventReferences.push(e)}loadVault(){return this.tasksMutex.runExclusive(()=>F(this,null,function*(){this.state="Initializing",yield Promise.all(this.vault.getMarkdownFiles().map(e=>this.indexFile(e))),this.state="Warm",this.notifySubscribers()}))}indexFile(e){return F(this,null,function*(){let t=this.metadataCache.getFileCache(e);if(t==null)return;let n=this.tasks.filter(a=>a.path===e.path),i=t.listItems,s=[];if(i!==void 0){let a=yield this.vault.cachedRead(e);s=this.getTasksFromFileContent(a,i,t,e)}Te.tasksListsIdentical(n,s)||(this.getState()=="Warm"&&console.debug(`At least one task, its line number or its heading has changed in ${e.path}: triggering a refresh of all active Tasks blocks in Live Preview and Reading mode views.`),this.tasks=this.tasks.filter(a=>a.path!==e.path),this.tasks.push(...s),this.notifySubscribers())})}getTasksFromFileContent(e,t,n,i){let s=[],a=e.split(`
`),o=a.length,u=new Js(()=>Ot.fromPath(i.path)),l=null,c=0;for(let p of t)if(p.task!==void 0){let m=p.position.start.line;if(m>=o)return console.log(`${i.path} Obsidian gave us a line number ${m} past the end of the file. ${o}.`),s;if((l===null||l.position.end.line<m)&&(l=Rn.getSection(m,n.sections),c=0),l===null)continue;let T=a[m];if(T===void 0){console.log(`${i.path}: line ${m} - ignoring 'undefined' line.`);continue}let y;try{y=Te.fromLine({line:T,taskLocation:new ot(i.path,m,l.position.start.line,c,Rn.getPrecedingHeader(m,n.headings)),fallbackDate:u.value})}catch(E){this.reportTaskParsingErrorToUser(E,i,p,T);continue}y!==null&&(c++,s.push(y))}return s}reportTaskParsingErrorToUser(e,t,n,i){let s=`There was an error reading one of the tasks in this vault.
The following task has been ignored, to prevent Tasks queries getting stuck with 'Loading Tasks ...'
Error: ${e}
File: ${t.path}
Line number: ${n.position.start.line}
Task line: ${i}

Please create a bug report for this message at
https://github.com/obsidian-tasks-group/obsidian-tasks/issues/new/choose
to help us find and fix the underlying issue.

Include:
- either a screenshot of the error popup, or copy the text from the console, if on a desktop machine.
- the output from running the Obsidian command 'Show debug info'

The error popup will only be shown when Tasks is starting up, but if the error persists,
it will be shown in the console every time this file is edited during the Obsidian
session.
`;console.error(s),e instanceof Error&&console.error(e.stack),this.state==="Initializing"&&new pi.Notice(s,1e4)}static getSection(e,t){if(t===void 0)return null;for(let n of t)if(n.position.start.line<=e&&n.position.end.line>=e)return n;return null}static getPrecedingHeader(e,t){if(t===void 0)return null;let n=null;for(let i of t){if(i.position.start.line>e)return n;n=i.heading}return n}};var Lf=require("obsidian");var AT=require("obsidian");function Nr(){}function Mf(r){return r()}function mT(){return Object.create(null)}function xn(r){r.forEach(Mf)}function Ro(r){return typeof r=="function"}function gT(r,e){return r!=r?e==e:r!==e||r&&typeof r=="object"||typeof r=="function"}function yT(r){return Object.keys(r).length===0}var _T=typeof window!="undefined"?window:typeof globalThis!="undefined"?globalThis:global,ta=class{constructor(e){this.options=e,this._listeners="WeakMap"in _T?new WeakMap:void 0}observe(e,t){return this._listeners.set(e,t),this._getObserver().observe(e,this.options),()=>{this._listeners.delete(e),this._observer.unobserve(e)}}_getObserver(){var e;return(e=this._observer)!==null&&e!==void 0?e:this._observer=new ResizeObserver(t=>{var n;for(let i of t)ta.entries.set(i.target,i),(n=this._listeners.get(i.target))===null||n===void 0||n(i)})}};ta.entries="WeakMap"in _T?new WeakMap:void 0;var TT=!1;function Yx(){TT=!0}function $x(){TT=!1}function R(r,e){r.appendChild(e)}function yi(r,e,t){r.insertBefore(e,t||null)}function An(r){r.parentNode&&r.parentNode.removeChild(r)}function xf(r,e){for(let t=0;t<r.length;t+=1)r[t]&&r[t].d(e)}function V(r){return document.createElement(r)}function jx(r){return document.createElementNS("http://www.w3.org/2000/svg",r)}function dt(r){return document.createTextNode(r)}function fe(){return dt(" ")}function bt(r,e,t,n){return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}function bT(r){return function(e){return e.preventDefault(),r.call(this,e)}}function N(r,e,t){t==null?r.removeAttribute(e):r.getAttribute(e)!==t&&r.setAttribute(e,t)}function vT(r){let e;return{p(...t){e=t,e.forEach(n=>r.push(n))},r(){e.forEach(t=>r.splice(r.indexOf(t),1))}}}function Gx(r){return Array.from(r.childNodes)}function Af(r,e){e=""+e,r.data!==e&&(r.data=e)}function nr(r,e){r.value=e==null?"":e}function Cf(r,e,t){for(let n=0;n<r.options.length;n+=1){let i=r.options[n];if(i.__value===e){i.selected=!0;return}}(!t||e!==void 0)&&(r.selectedIndex=-1)}function ET(r){let e=r.querySelector(":checked");return e&&e.__value}function ir(r,e,t){r.classList[t?"add":"remove"](e)}var Mn=class{constructor(e=!1){this.is_svg=!1,this.is_svg=e,this.e=this.n=null}c(e){this.h(e)}m(e,t,n=null){this.e||(this.is_svg?this.e=jx(t.nodeName):this.e=V(t.nodeType===11?"TEMPLATE":t.nodeName),this.t=t.tagName!=="TEMPLATE"?t:t.content,this.c(e)),this.i(n)}h(e){this.e.innerHTML=e,this.n=Array.from(this.e.nodeName==="TEMPLATE"?this.e.content.childNodes:this.e.childNodes)}i(e){for(let t=0;t<this.n.length;t+=1)yi(this.t,this.n[t],e)}p(e){this.d(),this.h(e),this.i(this.a)}d(){this.n.forEach(An)}};var ra;function ea(r){ra=r}function Bx(){if(!ra)throw new Error("Function called outside component initialization");return ra}function Pf(r){Bx().$$.on_mount.push(r)}var hi=[];var Mo=[],gi=[],hT=[],Hx=Promise.resolve(),Rf=!1;function zx(){Rf||(Rf=!0,Hx.then(wT))}function na(r){gi.push(r)}var Sf=new Set,mi=0;function wT(){if(mi!==0)return;let r=ra;do{try{for(;mi<hi.length;){let e=hi[mi];mi++,ea(e),Vx(e.$$)}}catch(e){throw hi.length=0,mi=0,e}for(ea(null),hi.length=0,mi=0;Mo.length;)Mo.pop()();for(let e=0;e<gi.length;e+=1){let t=gi[e];Sf.has(t)||(Sf.add(t),t())}gi.length=0}while(hi.length);for(;hT.length;)hT.pop()();Rf=!1,Sf.clear(),ea(r)}function Vx(r){if(r.fragment!==null){r.update(),xn(r.before_update);let e=r.dirty;r.dirty=[-1],r.fragment&&r.fragment.p(r.ctx,e),r.after_update.forEach(na)}}function Kx(r){let e=[],t=[];gi.forEach(n=>r.indexOf(n)===-1?e.push(n):t.push(n)),t.forEach(n=>n()),gi=e}var Qx=new Set;function Xx(r,e){r&&r.i&&(Qx.delete(r),r.i(e))}var Zx=["allowfullscreen","allowpaymentrequest","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","hidden","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected"],I2=new Set([...Zx]);function Jx(r,e,t,n){let{fragment:i,after_update:s}=r.$$;i&&i.m(e,t),n||na(()=>{let a=r.$$.on_mount.map(Mf).filter(Ro);r.$$.on_destroy?r.$$.on_destroy.push(...a):xn(a),r.$$.on_mount=[]}),s.forEach(na)}function kT(r,e){let t=r.$$;t.fragment!==null&&(Kx(t.after_update),xn(t.on_destroy),t.fragment&&t.fragment.d(e),t.on_destroy=t.fragment=null,t.ctx=[])}function eA(r,e){r.$$.dirty[0]===-1&&(hi.push(r),zx(),r.$$.dirty.fill(0)),r.$$.dirty[e/31|0]|=1<<e%31}function OT(r,e,t,n,i,s,a,o=[-1]){let u=ra;ea(r);let l=r.$$={fragment:null,ctx:[],props:s,update:Nr,not_equal:i,bound:mT(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(e.context||(u?u.$$.context:[])),callbacks:mT(),dirty:o,skip_bound:!1,root:e.target||u.$$.root};a&&a(l.root);let c=!1;if(l.ctx=t?t(r,e.props||{},(p,m,...T)=>{let y=T.length?T[0]:m;return l.ctx&&i(l.ctx[p],l.ctx[p]=y)&&(!l.skip_bound&&l.bound[p]&&l.bound[p](y),c&&eA(r,p)),m}):[],l.update(),c=!0,xn(l.before_update),l.fragment=n?n(l.ctx):!1,e.target){if(e.hydrate){Yx();let p=Gx(e.target);l.fragment&&l.fragment.l(p),p.forEach(An)}else l.fragment&&l.fragment.c();e.intro&&Xx(r.$$.fragment),Jx(r,e.target,e.anchor,e.customElement),$x(),wT()}ea(u)}var tA;typeof HTMLElement=="function"&&(tA=class extends HTMLElement{constructor(){super(),this.attachShadow({mode:"open"})}connectedCallback(){let{on_mount:r}=this.$$;this.$$.on_disconnect=r.map(Mf).filter(Ro);for(let e in this.$$.slotted)this.appendChild(this.$$.slotted[e])}attributeChangedCallback(r,e,t){this[r]=t}disconnectedCallback(){xn(this.$$.on_disconnect)}$destroy(){kT(this,1),this.$destroy=Nr}$on(r,e){if(!Ro(e))return Nr;let t=this.$$.callbacks[r]||(this.$$.callbacks[r]=[]);return t.push(e),()=>{let n=t.indexOf(e);n!==-1&&t.splice(n,1)}}$set(r){this.$$set&&!yT(r)&&(this.$$.skip_bound=!0,this.$$set(r),this.$$.skip_bound=!1)}});var xo=class{$destroy(){kT(this,1),this.$destroy=Nr}$on(e,t){if(!Ro(t))return Nr;let n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{let i=n.indexOf(t);i!==-1&&n.splice(i,1)}}$set(e){this.$$set&&!yT(e)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}};var If=Wi(Be());function DT(r,e,t){let n=r.slice();return n[45]=e[t],n}function ST(r,e,t){let n=r.slice();return n[48]=e[t].value,n[49]=e[t].label,n[50]=e[t].symbol,n[51]=e[t].accessKey,n[52]=e[t].accessKeyIndex,n}function rA(r){let e,t=r[50]+"",n;return{c(){e=V("span"),n=dt(t)},m(i,s){yi(i,e,s),R(e,n)},p:Nr,d(i){i&&An(e)}}}function RT(r){let e,t,n,i,s,a,o,u,l=r[49].substring(0,r[52])+"",c,p,m=r[49].substring(r[52],r[52]+1)+"",T,y,E=r[49].substring(r[52]+1)+"",M,U,q=r[50]&&r[50].charCodeAt(0)>=256,W,ie,re,ve,G,K=q&&rA(r);return re=vT(r[33][0]),{c(){e=V("span"),t=V("input"),a=fe(),o=V("label"),u=V("span"),c=dt(l),p=V("span"),T=dt(m),y=V("span"),M=dt(E),U=fe(),K&&K.c(),ie=fe(),N(t,"type","radio"),N(t,"id",n="priority-"+r[48]),t.__value=i=r[48],t.value=t.__value,N(t,"accesskey",s=r[15](r[51])),N(p,"class","accesskey"),N(o,"for",W="priority-"+r[48]),re.p(t)},m(Pe,Fe){yi(Pe,e,Fe),R(e,t),t.checked=t.__value===r[1].priority,R(e,a),R(e,o),R(o,u),R(u,c),R(o,p),R(p,T),R(o,y),R(y,M),R(o,U),K&&K.m(o,null),R(e,ie),ve||(G=bt(t,"change",r[32]),ve=!0)},p(Pe,Fe){Fe[0]&32768&&s!==(s=Pe[15](Pe[51]))&&N(t,"accesskey",s),Fe[0]&3&&(t.checked=t.__value===Pe[1].priority),q&&K.p(Pe,Fe)},d(Pe){Pe&&An(e),K&&K.d(),re.r(),ve=!1,G()}}}function MT(r){let e,t=r[45].name+"",n,i,s=r[45].symbol+"",a,o,u;return{c(){e=V("option"),n=dt(t),i=dt(" ["),a=dt(s),o=dt("]"),e.__value=u=r[45],e.value=e.__value},m(l,c){yi(l,e,c),R(e,n),R(e,i),R(e,a),R(e,o)},p(l,c){c[0]&1&&t!==(t=l[45].name+"")&&Af(n,t),c[0]&1&&s!==(s=l[45].symbol+"")&&Af(a,s),c[0]&1&&u!==(u=l[45])&&(e.__value=u,e.value=e.__value)},d(l){l&&An(e)}}}function nA(r){let e,t,n,i,s,a,o,u,l,c,p,m,T,y,E,M,U,q,W,ie,re,ve,G,K,Pe,Fe,j,A,w,B,C,X,d,f,h,g,_,v,k,S,O,D,P,me,ae,te,Se,I,Ae,Ce,rt,Tr,We,Le,np,Ci,ca,ip,ar,da,sp,$n,fa,ap,Lt,pa,op,Yr,Pi,ma,up,$r,ha,lp,Ni,mu,cp,ga,dp,Ii,hu,fp,ya,pp,jn,cn,mp,_a,hp,Fi,gu,gp,Gn=r[20],mt=[];for(let $=0;$<Gn.length;$+=1)mt[$]=RT(ST(r,Gn,$));let Bn=r[0],ht=[];for(let $=0;$<Bn.length;$+=1)ht[$]=MT(DT(r,Bn,$));return{c(){e=V("div"),t=V("form"),n=V("div"),i=V("label"),i.innerHTML='Descrip<span class="accesskey">t</span>ion',s=fe(),a=V("textarea"),u=fe(),l=V("div"),c=V("label"),p=dt("Priority"),T=fe();for(let $=0;$<mt.length;$+=1)mt[$].c();y=fe(),E=V("div"),M=V("label"),M.textContent="Recurs",U=fe(),q=V("input"),ie=fe(),re=V("code"),ve=dt(r[16]),G=fe(),K=new Mn(!1),Pe=fe(),Fe=V("label"),Fe.textContent="Due",j=fe(),A=V("input"),B=fe(),C=V("code"),X=dt(r[19]),d=fe(),f=new Mn(!1),h=fe(),g=V("label"),g.textContent="Scheduled",_=fe(),v=V("input"),S=fe(),O=V("code"),D=dt(r[18]),P=fe(),me=new Mn(!1),ae=fe(),te=V("label"),te.innerHTML='St<span class="accesskey">a</span>rt',Se=fe(),I=V("input"),Ce=fe(),rt=V("code"),Tr=dt(r[17]),We=fe(),Le=new Mn(!1),np=fe(),Ci=V("div"),ca=V("label"),ca.innerHTML=`Only
                    <span class="accesskey-first">future</span> dates:`,ip=fe(),ar=V("input"),sp=fe(),$n=V("div"),fa=V("label"),fa.innerHTML='Stat<span class="accesskey">u</span>s',ap=fe(),Lt=V("select");for(let $=0;$<ht.length;$+=1)ht[$].c();op=fe(),Yr=V("div"),Pi=V("div"),ma=V("label"),ma.textContent="Completed:",up=fe(),$r=V("input"),lp=fe(),Ni=V("div"),mu=V("span"),mu.textContent="Created on:",cp=fe(),ga=V("code"),dp=fe(),Ii=V("div"),hu=V("span"),hu.textContent="Done on:",fp=fe(),ya=V("code"),pp=fe(),jn=V("div"),cn=V("button"),mp=dt("Apply"),hp=fe(),Fi=V("button"),Fi.textContent="Cancel",N(i,"for","description"),N(a,"id","description"),N(a,"class","tasks-modal-description"),N(a,"placeholder","Take out the trash"),N(a,"accesskey",o=r[15]("t")),N(n,"class","tasks-modal-section"),N(c,"for",m="priority-"+r[1].priority),N(l,"class","tasks-modal-section tasks-modal-priorities"),N(M,"for","recurrence"),N(M,"class","accesskey-first"),N(q,"id","recurrence"),N(q,"type","text"),N(q,"placeholder","Try 'every 2 weeks on Thursday'."),N(q,"accesskey",W=r[15]("r")),ir(q,"tasks-modal-error",!r[8]),K.a=null,N(Fe,"for","due"),N(Fe,"class","accesskey-first"),N(A,"id","due"),N(A,"type","text"),N(A,"placeholder",Nf),N(A,"accesskey",w=r[15]("d")),ir(A,"tasks-modal-error",!r[7]),f.a=null,N(g,"for","scheduled"),N(g,"class","accesskey-first"),N(v,"id","scheduled"),N(v,"type","text"),N(v,"placeholder",Nf),N(v,"accesskey",k=r[15]("s")),ir(v,"tasks-modal-error",!r[5]),me.a=null,N(te,"for","start"),N(I,"id","start"),N(I,"type","text"),N(I,"placeholder",Nf),N(I,"accesskey",Ae=r[15]("a")),ir(I,"tasks-modal-error",!r[3]),Le.a=null,N(ca,"for","forwardOnly"),N(ar,"id","forwardOnly"),N(ar,"type","checkbox"),N(ar,"class","task-list-item-checkbox tasks-modal-checkbox"),N(ar,"accesskey",da=r[15]("f")),N(E,"class","tasks-modal-section tasks-modal-dates"),N(fa,"for","status"),N(Lt,"id","status-type"),N(Lt,"class","dropdown"),N(Lt,"accesskey",pa=r[15]("u")),r[1].status===void 0&&na(()=>r[39].call(Lt)),N($n,"class","tasks-modal-section"),N(ma,"for","status"),N($r,"id","status"),N($r,"type","checkbox"),N($r,"class","task-list-item-checkbox tasks-modal-checkbox"),$r.checked=ha=r[1].status.isCompleted(),$r.disabled=!0,N(Yr,"class","tasks-modal-section tasks-modal-status"),cn.disabled=_a=!r[14],N(cn,"type","submit"),N(cn,"class","mod-cta"),N(Fi,"type","button"),N(jn,"class","tasks-modal-section tasks-modal-buttons"),ir(t,"with-accesskeys",r[9]),N(e,"class","tasks-modal")},m($,pe){yi($,e,pe),R(e,t),R(t,n),R(n,i),R(n,s),R(n,a),nr(a,r[1].description),r[31](a),R(t,u),R(t,l),R(l,c),R(c,p),R(l,T);for(let ce=0;ce<mt.length;ce+=1)mt[ce]&&mt[ce].m(l,null);R(t,y),R(t,E),R(E,M),R(E,U),R(E,q),nr(q,r[1].recurrenceRule),R(E,ie),R(E,re),R(re,ve),R(re,G),K.m(r[12],re),R(E,Pe),R(E,Fe),R(E,j),R(E,A),nr(A,r[1].dueDate),R(E,B),R(E,C),R(C,X),R(C,d),f.m(r[6],C),R(E,h),R(E,g),R(E,_),R(E,v),nr(v,r[1].scheduledDate),R(E,S),R(E,O),R(O,D),R(O,P),me.m(r[4],O),R(E,ae),R(E,te),R(E,Se),R(E,I),nr(I,r[1].startDate),R(E,Ce),R(E,rt),R(rt,Tr),R(rt,We),Le.m(r[2],rt),R(E,np),R(E,Ci),R(Ci,ca),R(Ci,ip),R(Ci,ar),ar.checked=r[1].forwardOnly,R(t,sp),R(t,$n),R($n,fa),R($n,ap),R($n,Lt);for(let ce=0;ce<ht.length;ce+=1)ht[ce]&&ht[ce].m(Lt,null);Cf(Lt,r[1].status,!0),R(t,op),R(t,Yr),R(Yr,Pi),R(Pi,ma),R(Pi,up),R(Pi,$r),R(Yr,lp),R(Yr,Ni),R(Ni,mu),R(Ni,cp),R(Ni,ga),ga.innerHTML=r[11],R(Yr,dp),R(Yr,Ii),R(Ii,hu),R(Ii,fp),R(Ii,ya),ya.innerHTML=r[13],R(t,pp),R(t,jn),R(jn,cn),R(cn,mp),R(jn,hp),R(jn,Fi),gu||(gp=[bt(a,"input",r[30]),bt(a,"keydown",r[23]),bt(a,"paste",r[24]),bt(a,"drop",r[24]),bt(l,"keyup",r[21]),bt(q,"input",r[34]),bt(A,"input",r[35]),bt(v,"input",r[36]),bt(I,"input",r[37]),bt(ar,"change",r[38]),bt(Lt,"change",r[39]),bt(Fi,"click",r[22]),bt(t,"submit",bT(r[25]))],gu=!0)},p($,pe){if(pe[0]&32768&&o!==(o=$[15]("t"))&&N(a,"accesskey",o),pe[0]&3&&nr(a,$[1].description),pe[0]&3&&m!==(m="priority-"+$[1].priority)&&N(c,"for",m),pe[0]&1081346){Gn=$[20];let ce;for(ce=0;ce<Gn.length;ce+=1){let Li=ST($,Gn,ce);mt[ce]?mt[ce].p(Li,pe):(mt[ce]=RT(Li),mt[ce].c(),mt[ce].m(l,null))}for(;ce<mt.length;ce+=1)mt[ce].d(1);mt.length=Gn.length}if(pe[0]&32768&&W!==(W=$[15]("r"))&&N(q,"accesskey",W),pe[0]&3&&q.value!==$[1].recurrenceRule&&nr(q,$[1].recurrenceRule),pe[0]&256&&ir(q,"tasks-modal-error",!$[8]),pe[0]&4096&&K.p($[12]),pe[0]&32768&&w!==(w=$[15]("d"))&&N(A,"accesskey",w),pe[0]&3&&A.value!==$[1].dueDate&&nr(A,$[1].dueDate),pe[0]&128&&ir(A,"tasks-modal-error",!$[7]),pe[0]&64&&f.p($[6]),pe[0]&32768&&k!==(k=$[15]("s"))&&N(v,"accesskey",k),pe[0]&3&&v.value!==$[1].scheduledDate&&nr(v,$[1].scheduledDate),pe[0]&32&&ir(v,"tasks-modal-error",!$[5]),pe[0]&16&&me.p($[4]),pe[0]&32768&&Ae!==(Ae=$[15]("a"))&&N(I,"accesskey",Ae),pe[0]&3&&I.value!==$[1].startDate&&nr(I,$[1].startDate),pe[0]&8&&ir(I,"tasks-modal-error",!$[3]),pe[0]&4&&Le.p($[2]),pe[0]&32768&&da!==(da=$[15]("f"))&&N(ar,"accesskey",da),pe[0]&3&&(ar.checked=$[1].forwardOnly),pe[0]&1){Bn=$[0];let ce;for(ce=0;ce<Bn.length;ce+=1){let Li=DT($,Bn,ce);ht[ce]?ht[ce].p(Li,pe):(ht[ce]=MT(Li),ht[ce].c(),ht[ce].m(Lt,null))}for(;ce<ht.length;ce+=1)ht[ce].d(1);ht.length=Bn.length}pe[0]&32768&&pa!==(pa=$[15]("u"))&&N(Lt,"accesskey",pa),pe[0]&3&&Cf(Lt,$[1].status),pe[0]&3&&ha!==(ha=$[1].status.isCompleted())&&($r.checked=ha),pe[0]&2048&&(ga.innerHTML=$[11]),pe[0]&8192&&(ya.innerHTML=$[13]),pe[0]&16384&&_a!==(_a=!$[14])&&(cn.disabled=_a),pe[0]&512&&ir(t,"with-accesskeys",$[9])},i:Nr,o:Nr,d($){$&&An(e),r[31](null),xf(mt,$),xf(ht,$),gu=!1,xn(gp)}}}var Nf="Try 'Monday' or 'tomorrow', or [td|tm|yd|tw|nw|we] then space.";function iA(r,e,t){let n;var i;let{task:s}=e,{onSubmit:a}=e,{statusOptions:o}=e,{prioritySymbols:u,recurrenceSymbol:l,startDateSymbol:c,scheduledDateSymbol:p,dueDateSymbol:m}=Cr.tasksPluginEmoji.taskSerializer.symbols,T,y={description:"",status:ee.TODO,priority:"none",recurrenceRule:"",createdDate:"",startDate:"",scheduledDate:"",dueDate:"",doneDate:"",forwardOnly:!0},E=!0,M="",U="",q=!0,W="",ie=!0,re="",ve=!0,G="",K=!0,Pe="",Fe=!1,j=!0,A=!0,w=[{value:"lowest",label:"Lowest",symbol:u.Lowest,accessKey:"o",accessKeyIndex:1},{value:"low",label:"Low",symbol:u.Low,accessKey:"l",accessKeyIndex:0},{value:"none",label:"Normal",symbol:u.None,accessKey:"n",accessKeyIndex:0},{value:"medium",label:"Medium",symbol:u.Medium,accessKey:"m",accessKeyIndex:0},{value:"high",label:"High",symbol:u.High,accessKey:"h",accessKeyIndex:0},{value:"highest",label:"Highest",symbol:u.Highest,accessKey:"i",accessKeyIndex:1}];function B(I,Ae,Ce=void 0){if(!Ae)return`<i>no ${I} date</i>`;let rt=If.parseDate(Ae,Ce,{forwardDate:Ce!=null});return rt!==null?window.moment(rt).format("YYYY-MM-DD"):`<i>invalid ${I} date</i>`}function C(I,Ae){return B(I,Ae,y.forwardOnly?new Date:void 0)}function X(I){let Ae=null,Ce=If.parseDate(I,new Date,{forwardDate:y.forwardOnly});return Ce!==null&&(Ae=window.moment(Ce)),Ae}Pf(()=>{let{provideAccessKeys:I}=Q();t(9,j=I);let Ae=we.removeAsWordFrom(s.description);(Ae!=s.description||!we.includedIn(s.description))&&(Fe=!0);let Ce="none";s.priority==="5"?Ce="lowest":s.priority==="4"?Ce="low":s.priority==="2"?Ce="medium":s.priority==="1"?Ce="high":s.priority==="0"&&(Ce="highest"),t(1,y={description:Ae,status:s.status,priority:Ce,recurrenceRule:s.recurrence?s.recurrence.toText():"",createdDate:new Tt(s.createdDate).formatAsDate(),startDate:new Tt(s.startDate).formatAsDate(),scheduledDate:new Tt(s.scheduledDate).formatAsDate(),dueDate:new Tt(s.dueDate).formatAsDate(),doneDate:new Tt(s.doneDate).formatAsDate(),forwardOnly:!0}),setTimeout(()=>{T.focus()},10)});let d=I=>{if(I.key&&!I.altKey&&!I.ctrlKey){let Ae=w.find(Ce=>Ce.label.charAt(0).toLowerCase()==I.key);Ae&&t(1,y.priority=Ae.value,y)}},f=()=>{a([])},h=I=>{I.key==="Enter"&&(I.preventDefault(),A&&_())},g=()=>{setTimeout(()=>{t(1,y.description=y.description.replace(/[\r\n]+/g," "),y)},0)},_=()=>{let I=y.description.trim();Fe&&(I=we.prependTo(I));let Ae=X(y.startDate),Ce=X(y.scheduledDate),rt=X(y.dueDate),Tr=null;y.recurrenceRule&&(Tr=et.fromText({recurrenceRuleText:y.recurrenceRule,startDate:Ae,scheduledDate:Ce,dueDate:rt}));let We;switch(y.priority){case"lowest":We="5";break;case"low":We="4";break;case"medium":We="2";break;case"high":We="1";break;case"highest":We="0";break;default:We="3"}let Le=new Te(Object.assign(Object.assign({},s),{description:I,status:y.status,priority:We,recurrence:Tr,startDate:Ae,scheduledDate:Ce,dueDate:rt,doneDate:window.moment(y.doneDate,"YYYY-MM-DD").isValid()?window.moment(y.doneDate,"YYYY-MM-DD"):null}));a([Le])},v=[[]];function k(){y.description=this.value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function S(I){Mo[I?"unshift":"push"](()=>{T=I,t(10,T)})}function O(){y.priority=this.__value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function D(){y.recurrenceRule=this.value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function P(){y.dueDate=this.value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function me(){y.scheduledDate=this.value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function ae(){y.startDate=this.value,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function te(){y.forwardOnly=this.checked,t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}function Se(){y.status=ET(this),t(1,y),t(6,re),t(2,U),t(4,W),t(0,o)}return r.$$set=I=>{"task"in I&&t(26,s=I.task),"onSubmit"in I&&t(27,a=I.onSubmit),"statusOptions"in I&&t(0,o=I.statusOptions)},r.$$.update=()=>{if(r.$$.dirty[0]&512){e:t(15,n=I=>j?I:null)}if(r.$$.dirty[0]&66){e:t(1,y.dueDate=ui(y.dueDate),y),t(6,re=C("due",y.dueDate)),t(7,ve=!re.includes("invalid"))}if(r.$$.dirty[0]&6){e:t(1,y.startDate=ui(y.startDate),y),t(2,U=C("start",y.startDate)),t(3,q=!U.includes("invalid"))}if(r.$$.dirty[0]&18){e:t(1,y.scheduledDate=ui(y.scheduledDate),y),t(4,W=C("scheduled",y.scheduledDate)),t(5,ie=!W.includes("invalid"))}if(r.$$.dirty[0]&268435458){e:if(t(8,K=!0),!y.recurrenceRule)t(12,G="<i>not recurring</>");else{let I=t(28,i=et.fromText({recurrenceRuleText:y.recurrenceRule,startDate:null,scheduledDate:null,dueDate:null}))===null||i===void 0?void 0:i.toText();I?t(12,G=I):(t(12,G="<i>invalid recurrence rule</i>"),t(8,K=!1))}}if(r.$$.dirty[0]&2){e:t(29,E=y.description.trim()!=="")}if(r.$$.dirty[0]&536871336){e:t(14,A=ve&&K&&ie&&q&&E)}if(r.$$.dirty[0]&2){e:t(11,M=B("created",y.createdDate)),t(13,Pe=B("done",y.doneDate))}},[o,y,U,q,W,ie,re,ve,K,j,T,M,G,Pe,A,n,l,c,p,m,w,d,f,h,g,_,s,a,i,E,k,S,O,v,D,P,me,ae,te,Se]}var Ff=class extends xo{constructor(e){super(),OT(this,e,iA,nA,gT,{task:26,onSubmit:27,statusOptions:0},null,[-1,-1])}},xT=Ff;var nn=class extends AT.Modal{constructor({app:t,task:n,onSubmit:i}){super(t);this.task=n,this.onSubmit=s=>{s.length&&i(s),this.close()}}onOpen(){this.titleEl.setText("Create or edit Task");let{contentEl:t}=this,n=this.getKnownStatusesAndCurrentTaskStatusIfNotKnown();new xT({target:t,props:{task:this.task,statusOptions:n,onSubmit:this.onSubmit}})}getKnownStatusesAndCurrentTaskStatusIfNotKnown(){let t=Re.getInstance().registeredStatuses;return Re.getInstance().bySymbol(this.task.status.symbol)===ee.EMPTY&&t.push(this.task.status),t}onClose(){let{contentEl:t}=this;t.empty()}};var Ao=({line:r,path:e})=>{var T,y;let t=Te.parseTaskSignifiers(r,ot.fromUnknownPosition(e),Ot.fromPath(e));if(t!==null)return t;let{setCreatedDate:n}=Q(),i=n?window.moment():null,s=r.match(H.nonTaskRegex);if(s===null)return console.error("Tasks: Cannot create task on line:",r),new Te({status:ee.TODO,description:"",taskLocation:ot.fromUnknownPosition(e),indentation:"",listMarker:"-",priority:"3",createdDate:i,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,recurrence:null,blockLink:"",tags:[],originalMarkdown:"",scheduledDateIsInferred:!1});let a=s[1],o=(T=s[2])!=null?T:"-",u=(y=s[4])!=null?y:" ",l=Re.getInstance().bySymbolOrCreate(u),c=s[5],p=r.match(H.blockLinkRegex),m=p!==null?p[0]:"";return m!==""&&(c=c.replace(H.blockLinkRegex,"")),new Te({status:l,description:c,taskLocation:ot.fromUnknownPosition(e),indentation:a,listMarker:o,blockLink:m,priority:"3",createdDate:i,startDate:null,scheduledDate:null,dueDate:null,doneDate:null,recurrence:null,tags:[],originalMarkdown:"",scheduledDateIsInferred:!1})};var CT=(r,e,t,n)=>{var p;if(r)return t instanceof Lf.MarkdownView;if(!(t instanceof Lf.MarkdownView))return;let i=(p=t.file)==null?void 0:p.path;if(i===void 0)return;let a=e.getCursor().line,o=e.getLine(a),u=Ao({line:o,path:i}),l=m=>{let T=Ot.removeInferredStatusIfNeeded(u,m).map(y=>y.toFileLineString()).join(`
`);e.setLine(a,T)};new nn({app:n,task:u,onSubmit:l}).open()};var Uf=require("obsidian");var PT=(r,e,t)=>{var u;if(r)return t instanceof Uf.MarkdownView;if(!(t instanceof Uf.MarkdownView))return;let n=(u=t.file)==null?void 0:u.path;if(n===void 0)return;let i=e.getCursor(),s=i.line,a=e.getLine(s),o=sA(a,n);e.setLine(s,o.text),e.setCursor(aA(i,o))},sA=(r,e)=>{let t=Te.fromLine({line:r,taskLocation:ot.fromUnknownPosition(e),fallbackDate:null});if(t!==null){let n=t.toggleWithRecurrenceInUsersOrder().map(i=>i.toFileLineString());return{text:n.join(`
`),moveTo:{line:n.length-1}}}else{let n=r.match(H.taskRegex);if(n!==null){let i=n[3],a=Re.getInstance().bySymbol(i).nextStatusSymbol;return{text:r.replace(H.taskRegex,`$1- [${a}] $4`)}}else if(H.listItemRegex.test(r)){let i=r.replace(H.listItemRegex,"$1$2 [ ]");return{text:i,moveTo:{ch:i.length}}}else{let i=r.replace(H.indentationRegex,"$1- ");return{text:i,moveTo:{ch:i.length}}}}},aA=(r,e)=>{var s;let t={line:0,ch:r.ch},n=he(he({},t),(s=e.moveTo)!=null?s:{}),i=e.text.split(`
`)[n.line].length;return{line:r.line+n.line,ch:Math.min(n.ch,i)}};var Co=class{get app(){return this.plugin.app}constructor({plugin:e}){this.plugin=e,e.addCommand({id:"edit-task",name:"Create or edit task",icon:"pencil",editorCheckCallback:(t,n,i)=>CT(t,n,i,this.app)}),e.addCommand({id:"toggle-done",name:"Toggle task done",icon:"check-in-circle",editorCheckCallback:PT})}};var Po=class{constructor({obsidianEvents:e}){this.obsidianEvents=e}onCacheUpdate(e){return this.obsidianEvents.on("obsidian-tasks-plugin:cache-update",e)}triggerCacheUpdate(e){this.obsidianEvents.trigger("obsidian-tasks-plugin:cache-update",e)}onRequestCacheUpdate(e){return this.obsidianEvents.on("obsidian-tasks-plugin:request-cache-update",e)}triggerRequestCacheUpdate(e){this.obsidianEvents.trigger("obsidian-tasks-plugin:request-cache-update",e)}off(e){this.obsidianEvents.offref(e)}};var NT=require("obsidian");var No=class{constructor({plugin:e}){this.markdownPostProcessor=this._markdownPostProcessor.bind(this);e.registerMarkdownPostProcessor(this._markdownPostProcessor.bind(this))}_markdownPostProcessor(e,t){return F(this,null,function*(){var c;let n=new NT.MarkdownRenderChild(e);t.addChild(n);let i=e.findAll(".task-list-item").filter(p=>{var y;let m=(y=p.textContent)==null?void 0:y.split(`
`);if(m===void 0)return!1;let T=null;for(let E=0;E<m.length;E=E+1)if(m[E]!==""){T=m[E];break}return T===null?!1:we.includedIn(T)});if(i.length===0)return;let s=t.sourcePath,a=t.getSectionInfo(e);if(a===null)return;let o=a.text.split(`
`),u=0,l=[];for(let p=a.lineStart;p<=a.lineEnd;p++){let m=o[p];if(m===void 0)continue;let T=null,y=Te.fromLine({line:m,taskLocation:new ot(s,p,a.lineStart,u,T),fallbackDate:null});y!==null&&(l.push(y),u++)}for(let p=0;p<i.length;p++){let m=l[p],T=i[p];if(m===void 0||T===void 0)continue;let y=(c=T.getAttr("data-line"))!=null?c:"0",E=Number.parseInt(y,10),M=yield m.toLi({parentUlElement:e,listIndex:E,obsidianComponent:n}),U=T.childNodes;for(let ie=0;ie<U.length;ie=ie+1){let re=U[ie];re.nodeName.toLowerCase()==="div"?M.prepend(re):re.nodeName.toLowerCase()==="ul"&&M.append(re)}let q=T.querySelectorAll("[data-footnote-id]"),W=M.querySelectorAll("[data-footnote-id]");if(q.length===W.length)for(let ie=0;ie<q.length;ie++)W[ie].replaceWith(q[ie]);T.replaceWith(M)}})}};var IT=require("@codemirror/view"),FT=require("obsidian");var LT=()=>IT.ViewPlugin.fromClass(Wf),Wf=class{constructor(e){this.view=e,this.handleClickEvent=this.handleClickEvent.bind(this),this.view.dom.addEventListener("click",this.handleClickEvent)}destroy(){this.view.dom.removeEventListener("click",this.handleClickEvent)}handleClickEvent(e){let{target:t}=e;if(!t||!(t instanceof HTMLInputElement)||t.type!=="checkbox")return!1;let n=t.closest("ul.plugin-tasks-query-result, div.callout-content");if(n){if(n.matches("div.callout-content")){let m=`obsidian-tasks-plugin warning: Tasks cannot add or remove completion dates or make the next copy of a recurring task for tasks written inside a callout when you click their checkboxes in Live Preview. 
If you wanted Tasks to do these things, please undo your change, then either click the line of the task and use the "Toggle Task Done" command, or switch to Reading View to click the checkbox.`;console.warn(m),new FT.Notice(m,45e3)}return!1}let{state:i}=this.view,s=this.view.posAtDOM(t),a=i.doc.lineAt(s),o=Te.fromLine({line:a.text,taskLocation:ot.fromUnknownPosition(""),fallbackDate:null});if(console.debug(`Live Preview Extension: toggle called. Position: ${s} Line: ${a.text}`),o===null)return!1;e.preventDefault();let l=o.toggleWithRecurrenceInUsersOrder().map(m=>m.toFileLineString()).join(i.lineBreak),c=i.update({changes:{from:a.from,to:a.to,insert:l}});this.view.dispatch(c);let p=t.checked;return setTimeout(()=>{t.checked=p},1),!0}};var xi=require("obsidian");function Cn(r,e){let t=`Error: ${r}.
The error message was:
    `,n="";return e instanceof Error?n+=e:n+="Unknown error",`${t}"${n}"`}var Ir=class{constructor(e,t,n){this.property=e,this.comparator=Ir.maybeReverse(n,t)}static maybeReverse(e,t){return e?Ir.makeReversedComparator(t):t}static makeReversedComparator(e){return(t,n)=>e(t,n)*-1}};var sn=class{constructor(e,t,n){this.property=e,this.grouper=t,this.reverse=n}};var ke=class{canCreateFilterForLine(e){return ke.lineMatchesFilter(this.filterRegExp(),e)}static lineMatchesFilter(e,t){return e?e.test(t):!1}static getMatch(e,t){return e?t.match(e):null}fieldNameSingular(){return this.fieldName()}fieldNameSingularEscaped(){return Eo(this.fieldNameSingular())}supportsSorting(){return!1}createSorterFromLine(e){if(!this.supportsSorting())return null;let t=ke.getMatch(this.sorterRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createSorter(n)}sorterRegExp(){if(!this.supportsSorting())throw Error(`sorterRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^sort by ${this.fieldNameSingularEscaped()}( reverse)?`)}comparator(){throw Error(`comparator() unimplemented for ${this.fieldNameSingular()}`)}createSorter(e){return new Ir(this.fieldNameSingular(),this.comparator(),e)}createNormalSorter(){return this.createSorter(!1)}createReverseSorter(){return this.createSorter(!0)}supportsGrouping(){return!1}createGrouperFromLine(e){if(!this.supportsGrouping())return null;let t=ke.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1];return this.createGrouper(n)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)?$`)}grouper(){throw Error(`grouper() unimplemented for ${this.fieldNameSingular()}`)}createGrouper(e){return new sn(this.fieldNameSingular(),this.grouper(),e)}createNormalGrouper(){return this.createGrouper(!1)}createReverseGrouper(){return this.createGrouper(!0)}};var Oe=class{constructor(e,t=[],n=""){this.description=e,this.symbol=n,this.children=t}static booleanAnd(e){return this.combineOrCreateExplanation("All of",e,"AND")}static booleanOr(e){return this.combineOrCreateExplanation("At least one of",e,"OR")}static booleanNot(e){return new Oe("None of",e,"NOT")}static booleanXor(e){return new Oe("Exactly one of",e,"XOR")}asString(e=""){if(this.children.length==0)return e+this.description;let t=e;this.symbol===""?t+=this.description:(t+=this.symbol,this.children.length>1&&(t+=` (${this.description})`),t+=":");let n=e+"  ";for(let i=0;i<this.children.length;i++)t+=`
${this.children[i].asString(n)}`;return t}static combineOrCreateExplanation(e,t,n){if(t.length===2){let i=t[0],s=t[1];if(i.symbol===n&&s.symbol==="")return i.children.push(s),i}return new Oe(e,t,n)}};var ft=class{constructor(e,t,n){this.instruction=e,this.explanation=n,this.filterFunction=t}explainFilterIndented(e){let t=this.explanation;return t.asString()===this.instruction?`${e}${this.instruction}
`:`${e}${this.instruction} =>
${t.asString("  ")}
`}};var hr=class{constructor(e){this.instruction=e}get queryComponent(){return this._queryComponent}set queryComponent(e){this._queryComponent=e}get error(){return this._error}set error(e){this._error=e}static fromObject(e,t){let n=new hr(e);return n._queryComponent=t,n}static fromError(e,t){let n=new hr(e);return n._error=t,n}};var Z=class{constructor(e){this.object=e}get instruction(){return this.object.instruction}get filter(){return this.object.queryComponent}get error(){return this.object.error}get filterFunction(){if(this.filter)return this.filter.filterFunction}static fromFilter(e){return new Z(hr.fromObject(e.instruction,e))}static fromError(e,t){return new Z(hr.fromError(e,t))}};var Io=class{constructor(e,t){this._instruction=e,this._filter=t}canCreateFilterForLine(e){return e==this._instruction}createFilterOrErrorMessage(e){return e===this._instruction?Z.fromFilter(new ft(e,this._filter,new Oe(e))):Z.fromError(e,`do not understand filter: ${e}`)}};var gr=class{constructor(){this._filters=[]}add(e,t){this._filters.push(new Io(e,t))}canCreateFilterForLine(e){for(let t of this._filters)if(t.canCreateFilterForLine(e))return!0;return!1}createFilterOrErrorMessage(e){for(let t of this._filters){let n=t.createFilterOrErrorMessage(e);if(n.error===void 0)return n}return Z.fromError(e,`do not understand filter: ${e}`)}};var an=class extends ke{constructor(){super(...arguments);this._filters=new gr}canCreateFilterForLine(t){return this._filters.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){return this._filters.createFilterOrErrorMessage(t)}filterRegExp(){return null}};var yr=class extends an{constructor(){super(),this._filters.add("done",e=>e.isDone),this._filters.add("not done",e=>!e.isDone)}fieldName(){return"status"}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=yr.oldStatusName(e),i=yr.oldStatusName(t);return n<i?1:n>i?-1:0}}static oldStatusName(e){return e.status.symbol===" "?"Todo":"Done"}supportsGrouping(){return!0}grouper(){return e=>[yr.oldStatusName(e)]}};var pt=class extends ke{constructor(t=null){super();t!==null?this.filterInstructions=t:(this.filterInstructions=new gr,this.filterInstructions.add(`has ${this.fieldName()} date`,n=>this.date(n)!==null),this.filterInstructions.add(`no ${this.fieldName()} date`,n=>this.date(n)===null),this.filterInstructions.add(`${this.fieldName()} date is invalid`,n=>{let i=this.date(n);return i!==null&&!i.isValid()}))}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);if(n.filter!==void 0)return n;let i=ke.getMatch(this.filterRegExp(),t);if(i===null)return Z.fromError(t,"do not understand query filter ("+this.fieldName()+" date)");let s=i[1],a=i[2],o=i[3],u=xt.parseDateRange(o);if(!u.isValid()){let p=xt.parseDate(s);p.isValid()&&(u=new yt(p,p))}if(!u.isValid())return Z.fromError(t,"do not understand "+this.fieldName()+" date");let l=this.buildFilterFunction(a,u),c=pt.buildExplanation(this.fieldNameForExplanation(),a,this.filterResultIfFieldMissing(),u);return Z.fromFilter(new ft(t,l,c))}buildFilterFunction(t,n){let i;return t==="before"?i=s=>s?s.isBefore(n.start):this.filterResultIfFieldMissing():t==="after"?i=s=>s?s.isAfter(n.end):this.filterResultIfFieldMissing():i=s=>s?s.isSameOrAfter(n.start)&&s.isSameOrBefore(n.end):this.filterResultIfFieldMissing(),this.getFilter(i)}getFilter(t){return n=>t(this.date(n))}filterRegExp(){return new RegExp(`^${this.fieldNameForFilterInstruction()} ((before|after|on|in)? ?(.*))`)}fieldNameForFilterInstruction(){return this.fieldName()}static buildExplanation(t,n,i,s){let a,o="YYYY-MM-DD (dddd Do MMMM YYYY)",u;switch(n){case"before":a=n,u=s.start.format(o);break;case"after":a=n,u=s.end.format(o);break;default:if(!s.start.isSame(s.end)){let c=`${t} date is between:`,p=[new Oe(`${s.start.format(o)} and`),new Oe(`${s.end.format(o)} inclusive`)];return i&&p.push(new Oe(`OR no ${t} date`)),new Oe(c,p)}a="on",u=s.start.format(o);break}let l=`${t} date is ${a} ${u}`;return i&&(l+=` OR no ${t} date`),new Oe(l)}fieldNameForExplanation(){return this.fieldName()}supportsSorting(){return!0}comparator(){return(t,n)=>Ar(this.date(t),this.date(n))}supportsGrouping(){return!0}grouper(){return t=>{let n=this.date(t);return n===null?["No "+this.fieldName()+" date"]:[n.format("YYYY-MM-DD dddd")]}}};var _i=class extends pt{fieldName(){return"due"}date(e){return e.dueDate}filterResultIfFieldMissing(){return!1}};var qf=class extends ke{createFilterOrErrorMessage(e){let t=ke.getMatch(this.filterRegExp(),e);if(t!==null){let n=t[5],i=null;switch(n){case"lowest":i="5";break;case"low":i="4";break;case"none":i="3";break;case"medium":i="2";break;case"high":i="1";break;case"highest":i="0";break}if(i===null)return Z.fromError(e,"do not understand priority");let s=e,a;switch(t[3]){case"above":a=o=>o.priority.localeCompare(i)<0;break;case"below":a=o=>o.priority.localeCompare(i)>0;break;case"not":a=o=>o.priority!==i;break;default:a=o=>o.priority===i,s=`${this.fieldName()} is ${n}`}return Z.fromFilter(new ft(e,a,new Oe(s)))}else return Z.fromError(e,"do not understand query filter (priority)")}fieldName(){return"priority"}filterRegExp(){return qf.priorityRegexp}supportsSorting(){return!0}comparator(){return(e,t)=>e.priority.localeCompare(t.priority)}supportsGrouping(){return!0}grouper(){return e=>{let t=mr.priorityNameUsingNormal(e.priority);return[`%%${e.priority}%%${t} priority`]}}},Pn=qf;Pn.priorityRegexp=/^priority(\s+is)?(\s+(above|below|not))?(\s+(lowest|low|none|medium|high|highest))$/;var Ti=class{matchesAnyOf(e){return e.some(t=>this.matches(t))}};var bi=class extends Ti{constructor(t){super();this.stringToFind=t}matches(t){return bi.stringIncludesCaseInsensitive(t,this.stringToFind)}static stringIncludesCaseInsensitive(t,n){return t.toLocaleLowerCase().includes(n.toLocaleLowerCase())}explanation(t){return new Oe(t)}};var on=class extends Ti{constructor(t){super();this.regex=t}static validateAndConstruct(t){let n=/^\/(.+)\/([^/]*)$/,i=t.match(n);if(i!==null){let s=new RegExp(i[1],i[2]);return new on(s)}else return null}matches(t){return t.match(this.regex)!==null}static helpMessage(){return String.raw`See https://publish.obsidian.md/tasks/Queries/Regular+Expressions

Regular expressions must look like this:
    /pattern/
or this:
    /pattern/flags

Where:
- pattern: The 'regular expression' pattern to search for.
- flags:   Optional characters that modify the search.
           i => make the search case-insensitive
           u => add Unicode support

Examples:  /^Log/
           /^Log/i
           /File Name\.md/
           /waiting|waits|waited/i
           /\d\d:\d\d/

The following characters have special meaning in the pattern:
to find them literally, you must add a \ before them:
    [\^$.|?*+()

CAUTION! Regular expression (or 'regex') searching is a powerful
but advanced feature that requires thorough knowledge in order to
use successfully, and not miss intended search results.
`}explanation(t){let i=oA(t,"using regex: ",this.regexAsString());return new Oe(i)}regexAsString(){let t=`'${this.regex.source}' with `;switch(this.regex.flags.length){case 0:t+="no flags";break;case 1:t+=`flag '${this.regex.flags}'`;break;default:t+=`flags '${this.regex.flags}'`;break}return t}};function oA(r,e,t){var o;let n=r.match(/\//);if(!n)return"Error explaining instruction. Could not find a slash character";let i=2,s=((o=n.index)!=null?o:i)-i;return`${e.padEnd(s)}${t}`}var xe=class extends ke{createFilterOrErrorMessage(e){let t=ke.getMatch(this.filterRegExp(),e);if(t===null)return Z.fromError(e,`do not understand query filter (${this.fieldName()})`);let[n,i,s]=t,a=null;if(i.includes("include"))a=new bi(s);else if(i.includes("regex")){try{a=on.validateAndConstruct(s)}catch(l){let c=Cn("Parsing regular expression",l)+`

${on.helpMessage()}`;return Z.fromError(e,c)}if(a===null)return Z.fromError(e,`Invalid instruction: '${e}'

${on.helpMessage()}`)}if(a===null)return Z.fromError(e,`do not understand query filter (${this.fieldName()})`);let o=i.match(/not/)!==null,u=new ft(e,this.getFilter(a,o),a.explanation(e));return Z.fromFilter(u)}fieldPattern(){return this.fieldNameSingularEscaped()}filterOperatorPattern(){return"includes|does not include|regex matches|regex does not match"}filterRegExp(){return new RegExp(`^(?:${this.fieldPattern()}) (${this.filterOperatorPattern()}) (.*)`)}getFilter(e,t){return n=>{let i=e.matches(this.value(n));return t?!i:i}}comparator(){return(e,t)=>this.value(e).localeCompare(this.value(t),void 0,{numeric:!0})}grouper(){return e=>[this.value(e)]}static escapeMarkdownCharacters(e){return e.replace(/\\/g,"\\\\").replace(/_/g,"\\_")}};var vi=class extends xe{fieldName(){return"path"}value(e){return e.path}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>[xe.escapeMarkdownCharacters(e.path.replace(".md",""))]}};var Ei=class extends ke{canCreateFilterForLine(e){return!1}createFilterOrErrorMessage(e){return Z.fromError(e,"Filtering by urgency is not yet supported")}fieldName(){return"urgency"}filterRegExp(){throw Error(`filterRegExp() unimplemented for ${this.fieldName()}`)}supportsSorting(){return!0}comparator(){return(e,t)=>t.urgency-e.urgency}supportsGrouping(){return!0}grouper(){return e=>[`${e.urgency.toFixed(2)}`]}createGrouper(e){return super.createGrouper(!e)}};var wi=class{static by(e,t){let n=[new Ei().comparator(),new yr().comparator(),new _i().comparator(),new Pn().comparator(),new vi().comparator()],i=[];for(let s of e)i.push(s.comparator);return t.sort(wi.makeCompositeComparator([...i,...n]))}static makeCompositeComparator(e){return(t,n)=>{for(let i of e){let s=i(t,n);if(s!==0)return s}return 0}}};var Fo=class{constructor(e,t,n){this.nestingLevel=e,this.displayName=t,this.property=n}};var Lo=class{constructor(e,t){this.lastHeadingAtLevel=new Array;this.groupers=t;let i=e.keys().next().value.length;for(let s=0;s<i;s++)this.lastHeadingAtLevel.push("")}getHeadingsForTaskGroup(e){let t=new Array;for(let n=0;n<e.length;n++){let i=e[n];if(i!=this.lastHeadingAtLevel[n]){t.push(new Fo(n,i,this.groupers[n].property));for(let s=n;s<e.length;s++)this.lastHeadingAtLevel[s]="";this.lastHeadingAtLevel[n]=i}}return t}};var Uo=class{constructor(e){this.children=new Map;this.values=[];this.values=e}generateAllPaths(e=[]){let t=new Map;if(this.children.size==0)return t.set([...e],this.values),t;for(let[n,i]of this.children)e.push(n),i.generateAllPaths(e).forEach((a,o)=>t.set(o,a)),e.pop();return t}};var Wo=class extends Uo{},qo=class{constructor(e,t){this.root=new Wo(t),this.buildGroupingTree(e)}buildGroupingTree(e){let t=[this.root];for(let n of e){let i=[];for(let s of t)for(let a of s.values){let o=n.grouper(a);o.length===0&&o.push("");for(let u of o){let l=s.children.get(u);l===void 0&&(l=new Wo([]),s.children.set(u,l),i.push(l)),l.values.push(a)}}t=i}}generateTaskTreeStorage(){return this.root.generateAllPaths()}};var Yo=class{constructor(e,t){this.groups=e,this.groupHeadings=[],this.tasks=t}setGroupHeadings(e){for(let t of e)this.groupHeadings.push(t)}applyTaskLimit(e){this.tasks=this.tasks.slice(0,e)}tasksAsStringOfLines(){let e="";for(let t of this.tasks)e+=t.toFileLineString()+`
`;return e}toString(){let e=`
`;e+=`Group names: [${this.groups}]
`;for(let t of this.groupHeadings)e+=`${"#".repeat(4+t.nestingLevel)} [${t.property}] ${t.displayName}
`;return e+=this.tasksAsStringOfLines(),e}};var ki=class{constructor(e,t){this._groups=new Array;this._totalTaskCount=0;this._totalTaskCount=t.length,this._groupers=e;let i=new qo(e,t).generateTaskTreeStorage();this.addTaskGroups(i),this.sortTaskGroups(),this.setGroupsHeadings(i)}get groupers(){return this._groupers}get groups(){return this._groups}totalTasksCount(){return this._totalTaskCount}toString(){let e="";e+=`Groupers (if any):
`;for(let n of this._groupers){let i=n.reverse?" reverse":"";e+=`- ${n.property}${i}
`}for(let n of this.groups)e+=n.toString(),e+=`
---
`;return e+=`
${this.totalTasksCount()} tasks
`,e}addTaskGroups(e){for(let[t,n]of e){let i=new Yo(t,n);this.addTaskGroup(i)}}addTaskGroup(e){this._groups.push(e)}sortTaskGroups(){let e=(t,n)=>{let i=t.groups,s=n.groups;for(let a=0;a<i.length;a++){let o=this._groupers[a],u=i[a].localeCompare(s[a],void 0,{numeric:!0});if(u!==0)return o.reverse?-u:u}return 0};this._groups.sort(e)}setGroupsHeadings(e){let t=new Lo(e,this._groupers);for(let n of this._groups)n.setGroupHeadings(t.getHeadingsForTaskGroup(n.groups))}applyTaskLimit(e){this._groupers.length!==0&&(this._groups.forEach(t=>{t.applyTaskLimit(e)}),this.recalculateTotalTaskCount())}recalculateTotalTaskCount(){let e=[];this._groups.forEach(n=>{e=[...e,...n.tasks]});let t=[...new Set(e)];this._totalTaskCount=t.length}};var Nn=class extends xe{fieldName(){return"description"}value(e){return we.removeAsSubstringFrom(e.description)}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=Nn.cleanDescription(e.description),i=Nn.cleanDescription(t.description);return n.localeCompare(i,void 0,{numeric:!0})}}static cleanDescription(e){e=we.removeAsSubstringFrom(e);let t=/^\[\[?([^\]]*)]]?/,n=e.match(t);if(n!==null){let i=n[1];e=i.substring(i.indexOf("|")+1)+e.replace(t,"")}return e=this.replaceFormatting(e,/^\*\*([^*]+)\*\*/),e=this.replaceFormatting(e,/^\*([^*]+)\*/),e=this.replaceFormatting(e,/^==([^=]+)==/),e=this.replaceFormatting(e,/^__([^_]+)__/),e=this.replaceFormatting(e,/^_([^_]+)_/),e}static replaceFormatting(e,t){let n=e.match(t);return n!==null&&(e=n[1]+e.replace(t,"")),e}};var $o=class extends pt{fieldName(){return"created"}date(e){return e.createdDate}filterResultIfFieldMissing(){return!1}};var jo=class extends pt{fieldName(){return"done"}date(e){return e.doneDate}filterResultIfFieldMissing(){return!1}};var Go=class extends an{constructor(){super(),this._filters.add("exclude sub-items",e=>{if(e.indentation==="")return!0;let t=e.indentation.lastIndexOf(">");return t===-1?!1:/^ ?$/.test(e.indentation.slice(t+1))})}fieldName(){return"exclude"}};var ia=class extends hr{};function sa(r){return[["task",r]]}function Yf(r,e){let t=r.map(([n])=>n);try{let n=e.includes("return")?e:`return ${e}`,i=e&&new Function(...t,n);return i instanceof Function?ia.fromObject(e,i):ia.fromError(e,"Error parsing group function")}catch(n){return ia.fromError(e,Cn(`Failed parsing expression "${e}"`,n))}}function $f(r,e){let t=e.map(([n,i])=>i);return r(...t)}function jf(r,e,t){try{return $f(r,e)}catch(n){return Cn(`Failed calculating expression "${t}"`,n)}}function UT(r,e){let t=sa(r),n=Yf(t,e);return n.error?n.error:jf(n.queryComponent,t,e)}var Bo=class{constructor(e){this.line=e,this.functionOrError=Yf(sa(null),e)}isValid(){return this.functionOrError.error===void 0}get parseError(){return this.functionOrError.error}evaluate(e){if(!this.isValid())throw Error(`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`);return $f(this.functionOrError.queryComponent,sa(e))}evaluateOrCatch(e){return this.isValid()?jf(this.functionOrError.queryComponent,sa(e),this.line):`Error: Cannot evaluate an expression which is not valid: "${this.line}" gave error: "${this.parseError}"`}};var Ho=class extends ke{createFilterOrErrorMessage(e){let t=ke.getMatch(this.filterRegExp(),e);if(t===null)return Z.fromError(e,"Unable to parse line");let n=t[1],i=new Bo(n);return i.isValid()?Z.fromFilter(new ft(e,uA(i),new Oe(e))):Z.fromError(e,i.parseError)}fieldName(){return"function"}filterRegExp(){return new RegExp(`^filter by ${this.fieldNameSingularEscaped()} (.*)`)}supportsGrouping(){return!0}createGrouperFromLine(e){let t=ke.getMatch(this.grouperRegExp(),e);if(t===null)return null;let n=!!t[1],i=t[2];return new sn("function",cA(i),n)}grouperRegExp(){return new RegExp(`^group by ${this.fieldNameSingularEscaped()}( reverse)? (.*)`)}grouper(){throw Error("grouper() function not valid for FunctionField. Use createGrouperFromLine() instead.")}};function uA(r){return e=>lA(r,e)}function lA(r,e){let t=r.evaluate(e);if(typeof t=="boolean")return t;throw Error(`filtering function must return true or false. This returned "${t}".`)}function cA(r){return e=>dA(e,r)}function dA(r,e){try{let t=UT(r,e);return Array.isArray(t)?t.map(i=>i.toString()):t===null?[]:[t.toString()]}catch(t){let n=`Error: Failed calculating expression "${e}". The error message was: `;return t instanceof Error?[n+t.message]:[n+"Unknown error"]}}var zo=class extends xe{fieldName(){return"heading"}value(e){return e.precedingHeader?e.precedingHeader:""}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>e.precedingHeader===null||e.precedingHeader.length===0?["(No heading)"]:[e.precedingHeader]}};var Vo=class extends pt{fieldName(){return"scheduled"}date(e){return e.scheduledDate}filterResultIfFieldMissing(){return!1}};var Ko=class extends pt{fieldName(){return"start"}fieldNameForFilterInstruction(){return"starts"}date(e){return e.startDate}filterResultIfFieldMissing(){return!0}};var Qo=class extends pt{constructor(){let e=new gr;e.add("has happens date",t=>this.dates(t).some(n=>n!==null)),e.add("no happens date",t=>!this.dates(t).some(n=>n!==null)),super(e)}fieldName(){return"happens"}fieldNameForExplanation(){return"due, start or scheduled"}date(e){return this.earliestDate(e)}dates(e){return e.happensDates}earliestDate(e){return e.happens.moment}filterResultIfFieldMissing(){return!1}getFilter(e){return t=>this.dates(t).some(n=>e(n))}};var Xo=class extends an{constructor(){super(),this._filters.add("is recurring",e=>e.recurrence!==null),this._filters.add("is not recurring",e=>e.recurrence===null)}fieldName(){return"recurring"}supportsSorting(){return!0}comparator(){return(e,t)=>e.recurrence!==null&&t.recurrence===null?-1:e.recurrence===null&&t.recurrence!==null?1:0}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?["Recurring"]:["Not Recurring"]}};var Zo=class extends xe{fieldNamePlural(){return this.fieldNameSingular()+"s"}fieldName(){return`${this.fieldNameSingular()}/${this.fieldNamePlural()}`}fieldPattern(){return`${this.fieldNameSingular()}|${this.fieldNamePlural()}`}filterOperatorPattern(){return`${super.filterOperatorPattern()}|include|do not include`}value(e){return this.values(e).join(", ")}getFilter(e,t){return n=>{let i=e.matchesAnyOf(this.values(n));return t?!i:i}}createGrouper(e){return new sn(this.fieldNamePlural(),this.grouper(),e)}grouperRegExp(){if(!this.supportsGrouping())throw Error(`grouperRegExp() unimplemented for ${this.fieldNameSingular()}`);return new RegExp(`^group by ${this.fieldNamePlural()}( reverse)?$`)}};var In=class extends Zo{constructor(){super();this.filterInstructions=new gr,this.filterInstructions.add(`has ${this.fieldNameSingular()}`,t=>this.values(t).length>0),this.filterInstructions.add(`has ${this.fieldNamePlural()}`,t=>this.values(t).length>0),this.filterInstructions.add(`no ${this.fieldNameSingular()}`,t=>this.values(t).length===0),this.filterInstructions.add(`no ${this.fieldNamePlural()}`,t=>this.values(t).length===0)}createFilterOrErrorMessage(t){let n=this.filterInstructions.createFilterOrErrorMessage(t);return n.filter!==void 0?n:super.createFilterOrErrorMessage(t)}canCreateFilterForLine(t){return this.filterInstructions.canCreateFilterForLine(t)?!0:super.canCreateFilterForLine(t)}fieldNameSingular(){return"tag"}values(t){return t.tags}supportsSorting(){return!0}createSorterFromLine(t){let n=t.match(this.sorterRegExp());if(n===null)return null;let i=!!n[1],s=isNaN(+n[2])?1:+n[2],a=In.makeCompareByTagComparator(s);return new Ir(this.fieldNameSingular(),a,i)}sorterRegExp(){return/^sort by tag( reverse)?[\s]*(\d+)?/}comparator(){return In.makeCompareByTagComparator(1)}static makeCompareByTagComparator(t){return(n,i)=>{if(n.tags.length===0&&i.tags.length===0)return 0;if(n.tags.length===0)return 1;if(i.tags.length===0)return-1;let s=t-1;if(n.tags.length<t&&i.tags.length>=t)return 1;if(i.tags.length<t&&n.tags.length>=t)return-1;if(n.tags.length<t&&i.tags.length<t)return 0;let a=n.tags[s],o=i.tags[s];return a.localeCompare(o,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return t=>t.tags.length==0?["(No tags)"]:t.tags}};var XT=Wi(QT());var ru=class extends ke{constructor(){super(...arguments);this.basicBooleanRegexp=/(.*(AND|OR|XOR|NOT)\s*[("].*|\(.+\))/g;this.supportedOperators=["AND","OR","XOR","NOT"];this.subFields={}}filterRegExp(){return this.basicBooleanRegexp}createFilterOrErrorMessage(t){return this.parseLine(t)}fieldName(){return"boolean query"}parseLine(t){if(t.length===0)return Z.fromError(t,"empty line");let n=this.preprocessExpression(t);try{let i=(0,XT.parse)(n);for(let o of i)if(o.name==="IDENTIFIER"&&o.value){let u=o.value.trim();if(!(u in this.subFields)){let l=nu(u);if(l===null)return Z.fromError(t,`couldn't parse sub-expression '${u}'`);if(l.error)return Z.fromError(t,`couldn't parse sub-expression '${u}': ${l.error}`);l.filter&&(this.subFields[u]=l.filter)}}else if(o.name==="OPERATOR"){if(o.value==null)return Z.fromError(t,"empty operator in boolean query");if(!this.supportedOperators.includes(o.value))return Z.fromError(t,`unknown boolean operator '${o.value}'`)}let s=o=>this.filterTaskWithParsedQuery(o,i),a=this.constructExplanation(i);return Z.fromFilter(new ft(t,s,a))}catch(i){let s=i instanceof Error?i.message:"unknown error type";return Z.fromError(t,`malformed boolean query -- ${s} (check the documentation for guidelines)`)}}preprocessExpression(t){return t.replace(/\(([^()]+)\)/g,'("$1")')}filterTaskWithParsedQuery(t,n){let i=o=>o==="true",s=o=>o?"true":"false",a=[];for(let o of n)if(o.name==="IDENTIFIER"){if(o.value==null)throw Error("null token value");let l=this.subFields[o.value.trim()].filterFunction(t);a.push(s(l))}else if(o.name==="OPERATOR")if(o.value==="NOT"){let u=i(a.pop());a.push(s(!u))}else if(o.value==="OR"){let u=i(a.pop()),l=i(a.pop());a.push(s(u||l))}else if(o.value==="AND"){let u=i(a.pop()),l=i(a.pop());a.push(s(u&&l))}else if(o.value==="XOR"){let u=i(a.pop()),l=i(a.pop());a.push(s(u&&!l||!u&&l))}else throw Error("Unsupported operator: "+o.value);else throw Error("Unsupported token type: "+o);return i(a[0])}constructExplanation(t){let n=[];for(let i of t)if(i.name==="IDENTIFIER"){if(i.value==null)throw Error("null token value");let s=this.subFields[i.value.trim()];n.push(s.explanation)}else if(i.name==="OPERATOR")if(i.value==="NOT"){let s=n.pop();n.push(Oe.booleanNot([s]))}else if(i.value==="OR"){let s=n.pop(),a=n.pop();n.push(Oe.booleanOr([a,s]))}else if(i.value==="AND"){let s=n.pop(),a=n.pop();n.push(Oe.booleanAnd([a,s]))}else if(i.value==="XOR"){let s=n.pop(),a=n.pop();n.push(Oe.booleanXor([a,s]))}else throw Error("Unsupported operator: "+i.value);else throw Error("Unsupported token type: "+i.name);return n[0]}};var iu=class extends xe{fieldName(){return"filename"}value(e){let t=e.filename;return t===null?"":t+".md"}supportsSorting(){return!0}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;return t===null?["Unknown Location"]:["[["+t+"]]"]}}};var su=class extends xe{constructor(){super()}fieldName(){return"status.name"}value(e){return e.status.name}supportsSorting(){return!0}supportsGrouping(){return!0}};var ln=class extends ke{canCreateFilterForLine(e){let t=new RegExp(`^(?:${this.fieldNameSingularEscaped()})`);return ke.lineMatchesFilter(t,e)}createFilterOrErrorMessage(e){let t=ke.getMatch(this.filterRegExp(),e);if(t===null)return this.helpMessage(e);let[n,i,s]=t,a=zt[s.toUpperCase()];if(!a)return this.helpMessage(e);let o;switch(i){case"is":o=u=>u.status.type===a;break;case"is not":o=u=>u.status.type!==a;break;default:return this.helpMessage(e)}return Z.fromFilter(new ft(e,o,new Oe(e)))}filterRegExp(){return new RegExp(`^(?:${this.fieldNameSingularEscaped()}) (is|is not) ([^ ]+)$`)}helpMessage(e){let t=Object.values(zt).filter(i=>i!=="EMPTY").join(" "),n=`Invalid ${this.fieldNameSingular()} instruction: '${e}'.
    Allowed options: 'is' and 'is not' (without quotes).
    Allowed values:  ${t}
                     Note: values are case-insensitive,
                           so 'in_progress' works too, for example.
    Example:         ${this.fieldNameSingular()} is not NON_TASK`;return Z.fromError(e,n)}fieldName(){return"status.type"}value(e){return e.status.type}supportsSorting(){return!0}comparator(){return(e,t)=>{let n=ln.groupName(e),i=ln.groupName(t);return n.localeCompare(i,void 0,{numeric:!0})}}supportsGrouping(){return!0}grouper(){return e=>[ln.groupName(e)]}static groupName(e){let t;switch(e.status.type){case"IN_PROGRESS":t="1";break;case"TODO":t="2";break;case"DONE":t="3";break;case"CANCELLED":t="4";break;case"NON_TASK":t="5";break;case"EMPTY":t="6";break}return`%%${t}%%${e.status.type}`}};var au=class extends xe{fieldName(){return"recurrence"}value(e){return e.recurrence!==null?e.recurrence.toText():""}supportsGrouping(){return!0}grouper(){return e=>e.recurrence!==null?[e.recurrence.toText()]:["None"]}};var ou=class extends xe{fieldName(){return"folder"}value(e){return e.file.folder}supportsGrouping(){return!0}grouper(){return e=>[xe.escapeMarkdownCharacters(this.value(e))]}};var uu=class extends xe{fieldName(){return"root"}value(e){return e.file.root}supportsGrouping(){return!0}grouper(){return e=>[xe.escapeMarkdownCharacters(this.value(e))]}};var lu=class extends xe{fieldName(){return"backlink"}value(e){let t=e.getLinkText({isFilenameUnique:!0});return t===null?"Unknown Location":t}createFilterOrErrorMessage(e){return Z.fromError(e,"backlink field does not support filtering")}canCreateFilterForLine(e){return!1}supportsGrouping(){return!0}grouper(){return e=>{let t=e.filename;if(t===null)return["Unknown Location"];let n=xe.escapeMarkdownCharacters(t);return e.precedingHeader&&e.precedingHeader!==t&&(n+=" > "+e.precedingHeader),[n]}}};var tp=[()=>new su,()=>new ln,()=>new yr,()=>new Xo,()=>new Pn,()=>new Qo,()=>new $o,()=>new Ko,()=>new Vo,()=>new _i,()=>new jo,()=>new vi,()=>new ou,()=>new uu,()=>new lu,()=>new Nn,()=>new In,()=>new zo,()=>new Go,()=>new iu,()=>new Ei,()=>new au,()=>new Ho,()=>new ru];function nu(r){for(let e of tp){let t=e();if(t.canCreateFilterForLine(r))return t.createFilterOrErrorMessage(r)}return null}function ZT(r){let e=/^sort by /;if(r.match(e)===null)return null;for(let t of tp){let i=t().createSorterFromLine(r);if(i)return i}return null}function JT(r){let e=/^group by /;if(r.match(e)===null)return null;for(let t of tp){let i=t().createGrouperFromLine(r);if(i)return i}return null}var Wn=class{constructor(e){this._searchErrorMessage=void 0;this.taskGroups=e}get searchErrorMessage(){return this._searchErrorMessage}set searchErrorMessage(e){this._searchErrorMessage=e}get totalTasksCount(){return this.taskGroups.totalTasksCount()}get groups(){return this.taskGroups.groups}static fromError(e){let t=new Wn(new ki([],[]));return t._searchErrorMessage=e,t}};var qr=class{constructor({source:e}){this._limit=void 0;this._taskGroupLimit=void 0;this._layoutOptions=new Hs;this._filters=[];this._error=void 0;this._sorting=[];this._grouping=[];this.hideOptionsRegexp=/^(hide|show) (task count|backlink|priority|created date|start date|scheduled date|done date|due date|recurrence rule|edit button|urgency|tags)/;this.shortModeRegexp=/^short/;this.explainQueryRegexp=/^explain/;this.limitRegexp=/^limit (groups )?(to )?(\d+)( tasks?)?/;this.commentRegexp=/^#.*/;this.source=e,e.split(`
`).map(t=>t.trim()).forEach(t=>{switch(!0){case t==="":break;case this.shortModeRegexp.test(t):this._layoutOptions.shortMode=!0;break;case this.explainQueryRegexp.test(t):this._layoutOptions.explainQuery=!0;break;case this.limitRegexp.test(t):this.parseLimit({line:t});break;case this.parseSortBy({line:t}):break;case this.parseGroupBy({line:t}):break;case this.hideOptionsRegexp.test(t):this.parseHideOptions({line:t});break;case this.commentRegexp.test(t):break;case this.parseFilter(t):break;default:this.setError("do not understand query",t)}})}append(e){return this.source===""?e:e.source===""?this:new qr({source:`${this.source}
${e.source}`})}explainQuery(){let e="",t=this.filters.length;if(t===0)e+="No filters supplied. All tasks will match the query.";else for(let i=0;i<t;i++)i>0&&(e+=`
`),e+=this.filters[i].explainFilterIndented("");e+=this.explainQueryLimits();let{debugSettings:n}=Q();return n.ignoreSortInstructions&&(e+=`

NOTE: All sort instructions, including default sort order, are disabled, due to 'ignoreSortInstructions' setting.`),e}explainQueryLimits(){let e="";function t(n){let i=`

At most ${n} task`;return n!==1&&(i+="s"),i}return this._limit!==void 0&&(e+=t(this._limit),e+=`.
`),this._taskGroupLimit!==void 0&&(e+=t(this._taskGroupLimit),e+=` per group (if any "group by" options are supplied).
`),e}get limit(){return this._limit}get layoutOptions(){return this._layoutOptions}get filters(){return this._filters}get sorting(){return this._sorting}get grouping(){return this._grouping}get error(){return this._error}setError(e,t){this._error=`${e}
Problem line: "${t}"`}applyQueryToTasks(e){try{this.filters.forEach(a=>{e=e.filter(a.filterFunction)});let{debugSettings:t}=Q(),i=(t.ignoreSortInstructions?e:wi.by(this.sorting,e)).slice(0,this.limit),s=new ki(this.grouping,i);return this._taskGroupLimit!==void 0&&s.applyTaskLimit(this._taskGroupLimit),new Wn(s)}catch(t){let n="Search failed";return Wn.fromError(Cn(n,t))}}parseHideOptions({line:e}){let t=e.match(this.hideOptionsRegexp);if(t!==null){let n=t[1]==="hide";switch(t[2]){case"task count":this._layoutOptions.hideTaskCount=n;break;case"backlink":this._layoutOptions.hideBacklinks=n;break;case"priority":this._layoutOptions.hidePriority=n;break;case"created date":this._layoutOptions.hideCreatedDate=n;break;case"start date":this._layoutOptions.hideStartDate=n;break;case"scheduled date":this._layoutOptions.hideScheduledDate=n;break;case"due date":this._layoutOptions.hideDueDate=n;break;case"done date":this._layoutOptions.hideDoneDate=n;break;case"recurrence rule":this._layoutOptions.hideRecurrenceRule=n;break;case"edit button":this._layoutOptions.hideEditButton=n;break;case"urgency":this._layoutOptions.hideUrgency=n;break;case"tags":this._layoutOptions.hideTags=n;break;default:this.setError("do not understand hide/show option",e)}}}parseFilter(e){var n;let t=nu(e);return t!=null?(t.filter?this._filters.push(t.filter):this.setError((n=t.error)!=null?n:"Unknown error",e),!0):!1}parseLimit({line:e}){let t=e.match(this.limitRegexp);if(t===null){this.setError("do not understand query limit",e);return}let n=Number.parseInt(t[3],10);t[1]!==void 0?this._taskGroupLimit=n:this._limit=n}parseSortBy({line:e}){let t=ZT(e);return t?(this._sorting.push(t),!0):!1}parseGroupBy({line:e}){let t=JT(e);return t?(this._grouping.push(t),!0):!1}};function eb(r){let e="";we.isEmpty()||(e+=`Only tasks containing the global filter '${we.get()}'.

`);let t=new qr(bf());return t.source.trim()!==""&&(e+=`Explanation of the global query:

${t.explainQuery()}
`),e+=`Explanation of this Tasks code block query:

${new qr({source:r}).explainQuery()}`,e}function cu(r){return new qr(bf()).append(new qr({source:r}))}var du=class{constructor({plugin:e,events:t}){this.addQueryRenderChild=this._addQueryRenderChild.bind(this);this.app=e.app,this.events=t,e.registerMarkdownCodeBlockProcessor("tasks",this._addQueryRenderChild.bind(this))}_addQueryRenderChild(e,t,n){return F(this,null,function*(){n.addChild(new rp({app:this.app,events:this.events,container:t,source:e,filePath:n.sourcePath}))})}},rp=class extends xi.MarkdownRenderChild{constructor({app:t,events:n,container:i,source:s,filePath:a}){super(i);switch(this.app=t,this.events=n,this.source=s,this.filePath=a,this.containerEl.className){case"block-language-tasks":this.query=cu(this.source),this.queryType="tasks";break;default:this.query=cu(this.source),this.queryType="tasks";break}}onload(){this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.renderEventRef=this.events.onCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()}onunload(){this.renderEventRef!==void 0&&this.events.off(this.renderEventRef),this.queryReloadTimeout!==void 0&&clearTimeout(this.queryReloadTimeout)}reloadQueryAtMidnight(){let t=new Date;t.setHours(24,0,0,0);let n=new Date,i=t.getTime()-n.getTime();this.queryReloadTimeout=setTimeout(()=>{this.query=cu(this.source),this.events.triggerRequestCacheUpdate(this.render.bind(this)),this.reloadQueryAtMidnight()},i+1e3)}render(i){return F(this,arguments,function*({tasks:t,state:n}){var a;let s=this.containerEl.createEl("div");n==="Warm"&&this.query.error===void 0?yield this.renderQuerySearchResults(t,n,s):this.query.error!==void 0?this.renderErrorMessage(s,this.query.error):this.renderLoadingMessage(s),(a=this.containerEl.firstChild)==null||a.replaceWith(s)})}renderQuerySearchResults(t,n,i){return F(this,null,function*(){console.debug(`Render ${this.queryType} called for a block in active file "${this.filePath}", to select from ${t.length} tasks: plugin state: ${n}`),this.query.layoutOptions.explainQuery&&this.createExplanation(i);let s=this.query.applyQueryToTasks(t);if(s.searchErrorMessage!==void 0){this.renderErrorMessage(i,s.searchErrorMessage);return}yield this.addAllTaskGroups(s.taskGroups,i);let a=s.totalTasksCount;console.debug(`${a} of ${t.length} tasks displayed in a block in "${this.filePath}"`),this.addTaskCount(i,a)})}renderErrorMessage(t,n){t.createDiv().innerHTML=`<pre>Tasks query: ${n.replace(/\n/g,"<br>")}</pre>`}renderLoadingMessage(t){t.setText("Loading Tasks ...")}createExplanation(t){let n=eb(this.source),i=t.createEl("pre");i.addClasses(["plugin-tasks-query-explanation"]),i.setText(n),t.appendChild(i)}createTasksList(i){return F(this,arguments,function*({tasks:t,content:n}){let s=t.length,a=new Jr(this.query.layoutOptions),o=n.createEl("ul");o.addClasses(["contains-task-list","plugin-tasks-query-result"]),o.addClasses(a.specificClasses);let u=this.getGroupingAttribute();u&&u.length>0&&(o.dataset.taskGroupBy=u);for(let l=0;l<s;l++){let c=t[l],p=this.isFilenameUnique({task:c}),m=yield c.toLi({parentUlElement:o,listIndex:l,layoutOptions:this.query.layoutOptions,isFilenameUnique:p,taskLayout:a,obsidianComponent:this});m.querySelectorAll("[data-footnote-id]").forEach(M=>M.remove());let y=this.query.layoutOptions.shortMode,E=m.createSpan("task-extras");this.query.layoutOptions.hideUrgency||this.addUrgency(E,c),this.query.layoutOptions.hideBacklinks||this.addBacklinks(E,c,y,p),this.query.layoutOptions.hideEditButton||this.addEditButton(E,c),o.appendChild(m)}return{taskList:o,tasksCount:s}})}addEditButton(t,n){t.createEl("a",{cls:"tasks-edit"}).onClickEvent(s=>{s.preventDefault();let a=u=>{Do({originalTask:n,newTasks:Ot.removeInferredStatusIfNeeded(n,u)})};new nn({app:this.app,task:n,onSubmit:a}).open()})}addUrgency(t,n){let i=new Intl.NumberFormat().format(n.urgency);t.createSpan({text:i,cls:"tasks-urgency"})}addAllTaskGroups(t,n){return F(this,null,function*(){for(let i of t.groups){this.addGroupHeadings(n,i.groupHeadings);let{taskList:s}=yield this.createTasksList({tasks:i.tasks,content:n});n.appendChild(s)}})}addGroupHeadings(t,n){for(let i of n)this.addGroupHeading(t,i)}addGroupHeading(t,n){return F(this,null,function*(){let i="h6";n.nestingLevel===0?i="h4":n.nestingLevel===1&&(i="h5");let s=t.createEl(i,{cls:"tasks-group-heading"});yield xi.MarkdownRenderer.renderMarkdown(n.displayName,s,this.filePath,this)})}addBacklinks(t,n,i,s){var c;let a=t.createSpan({cls:"tasks-backlink"});i||a.append(" (");let o=a.createEl("a");o.rel="noopener",o.target="_blank",o.addClass("internal-link"),i&&o.addClass("internal-link-short-mode");let u;i?u=" \u{1F517}":u=(c=n.getLinkText({isFilenameUnique:s}))!=null?c:"",o.setText(u);let l=this.app.vault;o.addEventListener("click",p=>F(this,null,function*(){let m=yield Of(n,l);if(m){let[T,y]=m;yield this.app.workspace.getLeaf(xi.Keymap.isModEvent(p)).openFile(y,{eState:{line:T}})}})),o.addEventListener("mousedown",p=>F(this,null,function*(){if(p.button===1){let m=yield Of(n,l);if(m){let[T,y]=m;yield this.app.workspace.getLeaf("tab").openFile(y,{eState:{line:T}})}}})),i||a.append(")")}addTaskCount(t,n){this.query.layoutOptions.hideTaskCount||t.createDiv({text:`${n} task${n!==1?"s":""}`,cls:"tasks-count"})}isFilenameUnique({task:t}){let n=t.path.match(/([^/]*)\..+$/i);if(n===null)return;let i=n[1];return this.app.vault.getMarkdownFiles().filter(a=>{if(a.basename===i)return!0}).length<2}getGroupingAttribute(){let t=[];for(let n of this.query.grouping)t.push(n.property);return t.join(",")}};var be=require("obsidian");function tb(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],['"',"Quote","x","TODO"],["0","Speech bubble 0","0","NON_TASK"],["1","Speech bubble 1","1","NON_TASK"],["2","Speech bubble 2","2","NON_TASK"],["3","Speech bubble 3","3","NON_TASK"],["4","Speech bubble 4","4","NON_TASK"],["5","Speech bubble 5","5","NON_TASK"],["6","Speech bubble 6","6","NON_TASK"],["7","Speech bubble 7","7","NON_TASK"],["8","Speech bubble 8","8","NON_TASK"],["9","Speech bubble 9","9","NON_TASK"]]}function rb(){return[[" ","incomplete","x","TODO"],["x","complete / done"," ","DONE"],["-","cancelled"," ","CANCELLED"],[">","deferred","x","TODO"],["/","in progress, or half-done","x","IN_PROGRESS"],["!","Important","x","TODO"],["?","question","x","TODO"],["R","review","x","TODO"],["+","Inbox / task that should be processed later","x","TODO"],["b","bookmark","x","TODO"],["B","brainstorm","x","TODO"],["D","deferred or scheduled","x","TODO"],["I","Info","x","TODO"],["i","idea","x","TODO"],["N","note","x","TODO"],["Q","quote","x","TODO"],["W","win / success / reward","x","TODO"],["P","pro","x","TODO"],["C","con","x","TODO"]]}function nb(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],[">","Deferred","x","TODO"],["!","Important","x","TODO"],["?","Question","x","TODO"],["r","Review","x","TODO"]]}function ib(){return[[" ","Unchecked","x","TODO"],["x","Regular"," ","DONE"],["X","Checked"," ","DONE"],["-","Dropped"," ","CANCELLED"],[">","Forward","x","TODO"],["D","Date","x","TODO"],["?","Question","x","TODO"],["/","Half Done","x","IN_PROGRESS"],["+","Add","x","TODO"],["R","Research","x","TODO"],["!","Important","x","TODO"],["i","Idea","x","TODO"],["B","Brainstorm","x","TODO"],["P","Pro","x","TODO"],["C","Con","x","TODO"],["Q","Quote","x","TODO"],["N","Note","x","TODO"],["b","Bookmark","x","TODO"],["I","Information","x","TODO"],["p","Paraphrase","x","TODO"],["L","Location","x","TODO"],["E","Example","x","TODO"],["A","Answer","x","TODO"],["r","Reward","x","TODO"],["c","Choice","x","TODO"],["d","Doing","x","IN_PROGRESS"],["T","Time","x","TODO"],["@","Character / Person","x","TODO"],["t","Talk","x","TODO"],["O","Outline / Plot","x","TODO"],["~","Conflict","x","TODO"],["W","World","x","TODO"],["f","Clue / Find","x","TODO"],["F","Foreshadow","x","TODO"],["H","Favorite / Health","x","TODO"],["&","Symbolism","x","TODO"],["s","Secret","x","TODO"]]}function sb(){return[[" ","Unchecked","x","TODO"],["x","Checked"," ","DONE"],[">","Rescheduled","x","TODO"],["<","Scheduled","x","TODO"],["!","Important","x","TODO"],["-","Cancelled"," ","CANCELLED"],["/","In Progress","x","IN_PROGRESS"],["?","Question","x","TODO"],["*","Star","x","TODO"],["n","Note","x","TODO"],["l","Location","x","TODO"],["i","Information","x","TODO"],["I","Idea","x","TODO"],["S","Amount","x","TODO"],["p","Pro","x","TODO"],["c","Con","x","TODO"],["b","Bookmark","x","TODO"],["f","Fire","x","TODO"],["k","Key","x","TODO"],["w","Win","x","TODO"],["u","Up","x","TODO"],["d","Down","x","TODO"]]}function ab(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}function ob(){return[[" ","to-do","x","TODO"],["/","incomplete","x","IN_PROGRESS"],["x","done"," ","DONE"],["-","canceled"," ","CANCELLED"],[">","forwarded","x","TODO"],["<","scheduling","x","TODO"],["?","question","x","TODO"],["!","important","x","TODO"],["*","star","x","TODO"],['"',"quote","x","TODO"],["l","location","x","TODO"],["b","bookmark","x","TODO"],["i","information","x","TODO"],["S","savings","x","TODO"],["I","idea","x","TODO"],["p","pros","x","TODO"],["c","cons","x","TODO"],["f","fire","x","TODO"],["k","key","x","TODO"],["w","win","x","TODO"],["u","up","x","TODO"],["d","down","x","TODO"]]}var ub=[{text:"Core Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:"<p>These are the core statuses that Tasks supports natively, with no need for custom CSS styling or theming.</p><p>You can add edit and add your own custom statuses in the section below.</p>"},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertTaskCoreStatusSettings",featureFlag:"",notice:null}]},{text:"Custom Statuses",level:"h3",class:"",open:!0,notice:{class:"setting-item-description",text:null,html:`<p>You should first <b>select and install a CSS Snippet or Theme</b> to style custom checkboxes.</p><p>Then, use the buttons below to set up your custom statuses, to match your chosen CSS checkboxes.</p><p><b>Note</b> Any statuses with the same symbol as any earlier statuses will be ignored. You can confirm the actually loaded statuses by running the 'Create or edit task' command and looking at the Status drop-down.</p><p></p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Statuses">documentation</a> to get started!</p>`},settings:[{name:"",description:"",type:"function",initialValue:"",placeholder:"",settingName:"insertCustomTaskStatusSettings",featureFlag:"",notice:null}]}];var sr=require("obsidian");var qn=class{validate(e){let t=[];return t.push(...this.validateSymbol(e)),t.push(...this.validateName(e)),t.push(...this.validateNextSymbol(e)),t}validateStatusCollectionEntry(e){let[t,n,i,s]=e,a=[];if(a.push(...this.validateType(s)),t===i&&s!=="NON_TASK"&&a.push(`Status symbol '${t}' toggles to itself`),a.length>0)return a;let o=ee.createFromImportedValue(e).configuration;return a.push(...this.validateSymbolTypeConventions(o)),a.push(...this.validate(o)),a}validateSymbol(e){return qn.validateOneSymbol(e.symbol,"Task Status Symbol")}validateNextSymbol(e){return qn.validateOneSymbol(e.nextStatusSymbol,"Task Next Status Symbol")}validateName(e){let t=[];return e.name.length===0&&t.push("Task Status Name cannot be empty."),t}validateType(e){let t=zt[e],n=[];return t||n.push(`Status Type "${e}" is not a valid type`),t=="EMPTY"&&n.push('Status Type "EMPTY" is not permitted in user data'),n}validateSymbolTypeConventions(e){let t=[],n=e.symbol,i=new Re,s=n==="X"?"x":n,a=i.bySymbol(s);return a.type!=="EMPTY"&&(e.nextStatusSymbol!==a.nextStatusSymbol&&t.push(`Next Status Symbol for symbol '${n}': '${e.nextStatusSymbol}' is inconsistent with convention '${a.nextStatusSymbol}'`),e.type!==a.type&&t.push(`Status Type for symbol '${n}': '${e.type}' is inconsistent with convention '${a.type}'`)),t}static validateOneSymbol(e,t){let n=[];return e.length===0&&n.push(`${t} cannot be empty.`),e.length>1&&n.push(`${t} ("${e}") must be a single character.`),n}};var Yn=new qn,It=class extends sr.Modal{constructor(t,n,i){super(t.app);this.plugin=t;this.saved=!1;this.error=!1;this.statusSymbol=n.symbol,this.statusName=n.name,this.statusNextSymbol=n.nextStatusSymbol,this.statusAvailableAsCommand=n.availableAsCommand,this.type=n.type,this.isCoreStatus=i}statusConfiguration(){return new tt(this.statusSymbol,this.statusName,this.statusNextSymbol,this.statusAvailableAsCommand,this.type)}display(){return F(this,null,function*(){let{contentEl:t}=this;t.empty();let n=t.createDiv(),i;new sr.Setting(n).setName("Task Status Symbol").setDesc("This is the character between the square braces. (It can only be edited for Custom statuses, and not Core statuses.)").addText(l=>{i=l,l.setValue(this.statusSymbol).onChange(c=>{this.statusSymbol=c,It.setValid(l,Yn.validateSymbol(this.statusConfiguration()))})}).setDisabled(this.isCoreStatus).then(l=>{It.setValid(i,Yn.validateSymbol(this.statusConfiguration()))});let s;new sr.Setting(n).setName("Task Status Name").setDesc("This is the friendly name of the task status.").addText(l=>{s=l,l.setValue(this.statusName).onChange(c=>{this.statusName=c,It.setValid(l,Yn.validateName(this.statusConfiguration()))})}).then(l=>{It.setValid(s,Yn.validateName(this.statusConfiguration()))});let a;new sr.Setting(n).setName("Task Next Status Symbol").setDesc("When clicked on this is the symbol that should be used next.").addText(l=>{a=l,l.setValue(this.statusNextSymbol).onChange(c=>{this.statusNextSymbol=c,It.setValid(l,Yn.validateNextSymbol(this.statusConfiguration()))})}).then(l=>{It.setValid(a,Yn.validateNextSymbol(this.statusConfiguration()))}),new sr.Setting(n).setName("Task Status Type").setDesc("Control how the status behaves for searching and toggling.").addDropdown(l=>{["TODO","IN_PROGRESS","DONE","CANCELLED","NON_TASK"].forEach(p=>{l.addOption(p,p)}),l.setValue(this.type).onChange(p=>{this.type=ee.getTypeFromStatusTypeString(p)})}),ee.tasksPluginCanCreateCommandsForStatuses()&&new sr.Setting(n).setName("Available as command").setDesc("If enabled this status will be available as a command so you can assign a hotkey and toggle the status using it.").addToggle(l=>{l.setValue(this.statusAvailableAsCommand).onChange(c=>F(this,null,function*(){this.statusAvailableAsCommand=c}))});let o=t.createDiv(),u=new sr.Setting(o);u.addButton(l=>(l.setTooltip("Save").setIcon("checkmark").onClick(()=>F(this,null,function*(){let c=Yn.validate(this.statusConfiguration());if(c.length>0){let p=c.join(`
`)+`

Fix errors before saving.`;new sr.Notice(p);return}this.saved=!0,this.close()})),l)),u.addExtraButton(l=>(l.setIcon("cross").setTooltip("Cancel").onClick(()=>{this.saved=!1,this.close()}),l))})}onOpen(){this.display()}static setValidationError(t){t.inputEl.addClass("tasks-settings-is-invalid")}static removeValidationError(t){t.inputEl.removeClass("tasks-settings-is-invalid")}static setValid(t,n){n.length===0?It.removeValidationError(t):It.setValidationError(t)}};var Ft=class extends be.PluginSettingTab{constructor({plugin:t}){super(t.app,t);this.customFunctions={insertTaskCoreStatusSettings:this.insertTaskCoreStatusSettings.bind(this),insertCustomTaskStatusSettings:this.insertCustomTaskStatusSettings.bind(this)};this.plugin=t}saveSettings(t){return F(this,null,function*(){yield this.plugin.saveSettings(),t&&this.display()})}display(){let{containerEl:t}=this;t.empty(),this.containerEl.addClass("tasks-settings"),t.createEl("h3",{text:"Tasks Settings"}),t.createEl("p",{cls:"tasks-setting-important",text:"Changing any settings requires a restart of obsidian."}),t.createEl("h4",{text:"Task Format Settings"}),new be.Setting(t).setName("Task Format").setDesc(Ft.createFragmentWithHTML('<p>The format that Tasks uses to read and write tasks.</p><p><b>Important:</b> Tasks currently only supports one format at a time. Selecting Dataview will currently <b>stop Tasks reading its own emoji signifiers</b>.</p><p>See the <a href="https://publish.obsidian.md/tasks/Reference/Task+Formats/About+Task+Formats">documentation</a>.</p>')).addDropdown(i=>{for(let s of Object.keys(Cr))i.addOption(s,Cr[s].displayName);i.setValue(Q().taskFormat).onChange(s=>F(this,null,function*(){Ge({taskFormat:s}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Global filter Settings"}),new be.Setting(t).setName("Global task filter").setDesc(Ft.createFragmentWithHTML('<p><b>Recommended: Leave empty if you want all checklist items in your vault to be tasks managed by this plugin.</b></p><p>Use a global filter if you want Tasks to only act on a subset of your "<code>- [ ]</code>" checklist items, so that a checklist item must include the specified string in its description in order to be considered a task.<p><p>For example, if you set the global filter to <code>#task</code>, the Tasks plugin will only handle checklist items tagged with <code>#task</code>.</br>Other checklist items will remain normal checklist items and not appear in queries or get a done date set.</p><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Global+Filter">documentation</a>.</p>')).addText(i=>{i.setPlaceholder("e.g. #task or TODO").setValue(we.get()).onChange(s=>F(this,null,function*(){we.set(s),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Remove global filter from description").setDesc("Enabling this removes the string that you set as global filter from the task description when displaying a task.").addToggle(i=>{let s=Q();i.setValue(s.removeGlobalFilter).onChange(a=>F(this,null,function*(){Ge({removeGlobalFilter:a}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Global Query"}),kA(new be.Setting(t).setDesc(Ft.createFragmentWithHTML('<p>A query that is automatically included at the start of every Tasks block in the vault. Useful for adding default filters, or layout options.</p><p>See the <a href="https://publish.obsidian.md/tasks/Queries/Global+Query">documentation</a>.</p>')).addTextArea(i=>{let s=Q();i.inputEl.rows=4,i.setPlaceholder(`# For example...
path does not include _templates/
limit 300
show urgency`).setValue(s.globalQuery).onChange(a=>F(this,null,function*(){Ge({globalQuery:a}),yield this.plugin.saveSettings()}))})),t.createEl("h4",{text:"Task Statuses"});let{headingOpened:n}=Q();ub.forEach(i=>{this.addOneSettingsBlock(t,i,n)}),t.createEl("h4",{text:"Date Settings"}),new be.Setting(t).setName("Set created date on every added task").setDesc(Ft.createFragmentWithHTML(`Enabling this will add a timestamp \u2795 YYYY-MM-DD before other date values, when a task is created with 'Create or edit task', or by completing a recurring task.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Created+date">documentation</a>.</p>`)).addToggle(i=>{let s=Q();i.setValue(s.setCreatedDate).onChange(a=>F(this,null,function*(){Ge({setCreatedDate:a}),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Set done date on every completed task").setDesc(Ft.createFragmentWithHTML('Enabling this will add a timestamp \u2705 YYYY-MM-DD at the end when a task is toggled to done.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Dates#Done+date">documentation</a>.</p>')).addToggle(i=>{let s=Q();i.setValue(s.setDoneDate).onChange(a=>F(this,null,function*(){Ge({setDoneDate:a}),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Use filename as Scheduled date for undated tasks").setDesc(Ft.createFragmentWithHTML('Save time entering Scheduled (\u23F3) dates.</br>If this option is enabled, any undated tasks will be given a default Scheduled date extracted from their file name.</br>The date in the file name must be in one of <code>YYYY-MM-DD</code> or <code>YYYYMMDD</code> formats.</br>Undated tasks have none of Due (\u{1F4C5} ), Scheduled (\u23F3) and Start (\u{1F6EB}) dates.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Use+Filename+as+Default+Date">documentation</a>.</p>')).addToggle(i=>{let s=Q();i.setValue(s.useFilenameAsScheduledDate).onChange(a=>F(this,null,function*(){Ge({useFilenameAsScheduledDate:a}),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Folders with default Scheduled dates").setDesc("Leave empty if you want to use default Scheduled dates everywhere, or enter a comma-separated list of folders.").addText(i=>F(this,null,function*(){let s=Q();yield this.plugin.saveSettings(),i.setValue(Ft.renderFolderArray(s.filenameAsDateFolders)).onChange(a=>F(this,null,function*(){let o=Ft.parseCommaSeparatedFolders(a);Ge({filenameAsDateFolders:o}),yield this.plugin.saveSettings()}))})),t.createEl("h4",{text:"Recurring task Settings"}),new be.Setting(t).setName("Next recurrence appears on the line below").setDesc(Ft.createFragmentWithHTML('Enabling this will make the next recurrence of a task appear on the line below the completed task. Otherwise the next recurrence will appear before the completed one.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Recurring+Tasks">documentation</a>.</p>')).addToggle(i=>{let{recurrenceOnNextLine:s}=Q();i.setValue(s).onChange(a=>F(this,null,function*(){Ge({recurrenceOnNextLine:a}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Auto-suggest Settings"}),new be.Setting(t).setName("Auto-suggest task content").setDesc(Ft.createFragmentWithHTML('Enabling this will open an intelligent suggest menu while typing inside a recognized task line.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Auto-Suggest">documentation</a>.</p>')).addToggle(i=>{let s=Q();i.setValue(s.autoSuggestInEditor).onChange(a=>F(this,null,function*(){Ge({autoSuggestInEditor:a}),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Minimum match length for auto-suggest").setDesc("If higher than 0, auto-suggest will be triggered only when the beginning of any supported keywords is recognized.").addSlider(i=>{let s=Q();i.setLimits(0,3,1).setValue(s.autoSuggestMinMatch).setDynamicTooltip().onChange(a=>F(this,null,function*(){Ge({autoSuggestMinMatch:a}),yield this.plugin.saveSettings()}))}),new be.Setting(t).setName("Maximum number of auto-suggestions to show").setDesc('How many suggestions should be shown when an auto-suggest menu pops up (including the "\u23CE" option).').addSlider(i=>{let s=Q();i.setLimits(3,12,1).setValue(s.autoSuggestMaxItems).setDynamicTooltip().onChange(a=>F(this,null,function*(){Ge({autoSuggestMaxItems:a}),yield this.plugin.saveSettings()}))}),t.createEl("h4",{text:"Dialog Settings"}),new be.Setting(t).setName("Provide access keys in dialogs").setDesc(Ft.createFragmentWithHTML('If the access keys (keyboard shortcuts) for various controls in dialog boxes conflict with system keyboard shortcuts or assistive technology functionality that is important for you, you may want to deactivate them here.</br><p>See the <a href="https://publish.obsidian.md/tasks/Getting+Started/Create+or+edit+Task#Keyboard+shortcuts">documentation</a>.</p>')).addToggle(i=>{let s=Q();i.setValue(s.provideAccessKeys).onChange(a=>F(this,null,function*(){Ge({provideAccessKeys:a}),yield this.plugin.saveSettings()}))})}addOneSettingsBlock(t,n,i){let s=t.createEl("details",{cls:"tasks-nested-settings",attr:he({},n.open||i[n.text]?{open:!0}:{})});s.empty(),s.ontoggle=()=>{i[n.text]=s.open,Ge({headingOpened:i}),this.plugin.saveSettings()};let a=s.createEl("summary");if(new be.Setting(a).setHeading().setName(n.text),a.createDiv("collapser").createDiv("handle"),n.notice!==null){let o=s.createEl("div",{cls:n.notice.class,text:n.notice.text});n.notice.html!==null&&o.insertAdjacentHTML("beforeend",n.notice.html)}n.settings.forEach(o=>{if(!(o.featureFlag!==""&&!tT(o.featureFlag))&&(o.type==="checkbox"?new be.Setting(s).setName(o.name).setDesc(o.description).addToggle(u=>{let l=Q();l.generalSettings[o.settingName]||Sn(o.settingName,o.initialValue),u.setValue(l.generalSettings[o.settingName]).onChange(c=>F(this,null,function*(){Sn(o.settingName,c),yield this.plugin.saveSettings()}))}):o.type==="text"?new be.Setting(s).setName(o.name).setDesc(o.description).addText(u=>{let l=Q();l.generalSettings[o.settingName]||Sn(o.settingName,o.initialValue);let c=p=>F(this,null,function*(){Sn(o.settingName,p),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,be.debounce)(c,500,!0))}):o.type==="textarea"?new be.Setting(s).setName(o.name).setDesc(o.description).addTextArea(u=>{let l=Q();l.generalSettings[o.settingName]||Sn(o.settingName,o.initialValue);let c=p=>F(this,null,function*(){Sn(o.settingName,p),yield this.plugin.saveSettings()});u.setPlaceholder(o.placeholder.toString()).setValue(l.generalSettings[o.settingName].toString()).onChange((0,be.debounce)(c,500,!0)),u.inputEl.rows=8,u.inputEl.cols=40}):o.type==="function"&&this.customFunctions[o.settingName](s,this),o.notice!==null)){let u=s.createEl("p",{cls:o.notice.class,text:o.notice.text});o.notice.html!==null&&u.insertAdjacentHTML("beforeend",o.notice.html)}})}static parseCommaSeparatedFolders(t){return t.split(",").map(n=>n.trim()).map(n=>n.replace(/^\/|\/$/g,"")).filter(n=>n!=="")}static renderFolderArray(t){return t.join(",")}insertTaskCoreStatusSettings(t,n){let{statusSettings:i}=Q();i.coreStatuses.forEach(s=>{lb(t,s,i.coreStatuses,i,n,n.plugin,!0)})}insertCustomTaskStatusSettings(t,n){let{statusSettings:i}=Q();i.customStatuses.forEach(l=>{lb(t,l,i.customStatuses,i,n,n.plugin,!1)}),t.createEl("div"),new be.Setting(t).addButton(l=>{l.setButtonText("Add New Task Status").setCta().onClick(()=>F(this,null,function*(){He.addStatus(i.customStatuses,new tt("","","",!1,"TODO")),yield Ai(i,n)}))}).infoEl.remove();let a=[["AnuPpuccin Theme",tb()],["Aura Theme",rb()],["Ebullientworks Theme",nb()],["ITS Theme & SlRvb Checkboxes",ib()],["Minimal Theme",ab()],["Things Theme",ob()],["LYT Mode Theme (Dark mode only)",sb()]];for(let[l,c]of a)new be.Setting(t).addButton(m=>{let T=`${l}: Add ${c.length} supported Statuses`;m.setButtonText(T).onClick(()=>F(this,null,function*(){yield wA(c,i,n)}))}).infoEl.remove();new be.Setting(t).addButton(l=>{l.setButtonText("Add All Unknown Status Types").setCta().onClick(()=>F(this,null,function*(){let p=this.plugin.getTasks().map(T=>T.status),m=Re.getInstance().findUnknownStatuses(p);m.length!==0&&(m.forEach(T=>{He.addStatus(i.customStatuses,T)}),yield Ai(i,n))}))}).infoEl.remove(),new be.Setting(t).addButton(l=>{l.setButtonText("Reset Custom Status Types to Defaults").setWarning().onClick(()=>F(this,null,function*(){He.resetAllCustomStatuses(i),yield Ai(i,n)}))}).infoEl.remove()}},la=Ft;la.createFragmentWithHTML=t=>createFragment(n=>n.createDiv().innerHTML=t);function lb(r,e,t,n,i,s,a){let o=r.createEl("pre");o.addClass("row-for-status"),o.textContent=new ee(e).previewText();let u=new be.Setting(r);u.infoEl.replaceWith(o),a||u.addExtraButton(l=>{l.setIcon("cross").setTooltip("Delete").onClick(()=>F(this,null,function*(){He.deleteStatus(t,e)&&(yield Ai(n,i))}))}),u.addExtraButton(l=>{l.setIcon("pencil").setTooltip("Edit").onClick(()=>F(this,null,function*(){let c=new It(s,e,a);c.onClose=()=>F(this,null,function*(){c.saved&&He.replaceStatus(t,e,c.statusConfiguration())&&(yield Ai(n,i))}),c.open()}))}),u.infoEl.remove()}function wA(r,e,t){return F(this,null,function*(){He.bulkAddStatusCollection(e,r).forEach(i=>{new be.Notice(i)}),yield Ai(e,t)})}function Ai(r,e){return F(this,null,function*(){Ge({statusSettings:r}),He.applyToStatusRegistry(r,Re.getInstance()),yield e.saveSettings(!0)})}function kA(r){let{settingEl:e,infoEl:t,controlEl:n}=r,i=n.querySelector("textarea");i!==null&&(e.style.display="block",t.style.marginRight="0px",i.style.minWidth="-webkit-fill-available")}var cb=require("obsidian");var fu=class extends cb.EditorSuggest{constructor(t,n){super(t);this.settings=n}onTrigger(t,n,i){if(!this.settings.autoSuggestInEditor)return null;let s=n.getLine(t.line);return we.includedIn(s)&&s.match(H.taskRegex)?{start:{line:t.line,ch:0},end:{line:t.line,ch:s.length},query:s}:null}getSuggestions(t){var a,o,u;let n=t.query,i=t.editor.getCursor();return((u=(o=(a=Vs()).buildSuggestions)==null?void 0:o.call(a,n,i.ch,this.settings))!=null?u:[]).map(l=>Dt(he({},l),{context:t}))}renderSuggestion(t,n){n.setText(t.displayText)}selectSuggestion(t,n){var u,l,c;let i=t.context.editor;if(t.suggestionType==="empty"){this.close();let p=new KeyboardEvent("keydown",{code:"Enter",key:"Enter"});(l=(u=i==null?void 0:i.cm)==null?void 0:u.contentDOM)==null||l.dispatchEvent(p);return}let s=t.context.editor.getCursor(),a={line:s.line,ch:(c=t.insertAt)!=null?c:s.ch},o=t.insertSkip?{line:s.line,ch:a.ch+t.insertSkip}:void 0;t.context.editor.replaceRange(t.appendText,a,o),t.context.editor.setCursor({line:s.line,ch:a.ch+t.appendText.length})}};var db=(r,e)=>{let t,n=new Promise((a,o)=>{t=a});return e(r,a=>{let o=a.map(u=>u.toFileLineString()).join(`
`);t(o)}).open(),n};var fb=(r,e)=>{let t=Ao({line:"",path:""});return new nn({app:r,task:t,onSubmit:e})};var pb=r=>({createTaskLineModal:()=>db(r,fb)});var pu=class extends mb.Plugin{get apiV1(){return pb(app)}onload(){return F(this,null,function*(){wo.registerConsoleLogger(),console.log('loading plugin "tasks"'),yield this.loadSettings(),this.addSettingTab(new la({plugin:this})),aT({metadataCache:this.app.metadataCache,vault:this.app.vault,workspace:this.app.workspace}),yield this.loadTaskStatuses();let t=new Po({obsidianEvents:this.app.workspace});this.cache=new Rn({metadataCache:this.app.metadataCache,vault:this.app.vault,events:t}),this.inlineRenderer=new No({plugin:this}),this.queryRenderer=new du({plugin:this,events:t}),this.registerEditorExtension(LT()),this.registerEditorSuggest(new fu(this.app,Q())),new Co({plugin:this})})}loadTaskStatuses(){return F(this,null,function*(){let{statusSettings:t}=Q();He.applyToStatusRegistry(t,Re.getInstance())})}onunload(){var t;console.log('unloading plugin "tasks"'),(t=this.cache)==null||t.unload()}loadSettings(){return F(this,null,function*(){let t=yield this.loadData();Ge(t),yield this.loadTaskStatuses()})}saveSettings(){return F(this,null,function*(){yield this.saveData(Q())})}getTasks(){var t;return(t=this.cache)==null?void 0:t.getTasks()}};
/*!
 * EventEmitter2
 * https://github.com/hij1nx/EventEmitter2
 *
 * Copyright (c) 2013 hij1nx
 * Licensed under the MIT license.
 */
