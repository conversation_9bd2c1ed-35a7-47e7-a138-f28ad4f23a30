---
banner: "40 - Obsidian/附件/banners/flowers.gif"
cssclasses:
  - myhome
banner_x: 0.62858
banner_y: 0.38648
status: 进行中
target: 10000
banner_icon: 🌈
searchTerm: DailyNote
searchType: tags
searchDate: 2025-07-08
---


# Muzi'Notes
- **AGENDA**
	- [今日笔记](obsidian://advanced-uri?vault=Rainbell&daily=true)
	- [每周笔记](obsidian://advanced-uri?vault=Rainbell&commandid=periodic-notes%253Aopen-weekly-note)
	- [月度笔记]()
	- [季度OKR](obsidian://advanced-uri?vault=Rainbell&commandid=periodic-notes%253Aopen-quarterly-note)
- **LIFE**
	- [[00. 图书馆|图书馆]]
	- [[木子金又二丨|个人主页]]
	- [[00. 文件管理|文件管理]]
	- [[00. 代码库|代码管理]]
- **WORK**
	- [[00. 学习|专业学习]]
	- [[00. 工作|工作兼职]]
	- [[00. 卡片盒|卡片盒子]]
	- [[00. 参考汇总|参考汇总]]
- **PEOPLE**
	- [[00. 人员|人员管理]]
	- [[00. 宠物|宠物管理]]

---

<div class="file-search-section">

## 🔍 文件搜索

```dataviewjs
// 主页交互式搜索组件
// 读取当前页面的搜索配置
let searchType = dv.current().searchType || 'tags';
let searchTerm = dv.current().searchTerm || '';
let searchDate = dv.current().searchDate || new Date().toISOString().split('T')[0];

// 简化的字数统计函数
function getWordCount(text) {
	if (!text) return 0;
	
	// 移除Markdown语法
	let cleanText = text
		.replace(/```[\s\S]*?```/g, '') // 代码块
		.replace(/`[^`]*`/g, '') // 行内代码
		.replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // 链接
		.replace(/!\[([^\]]*)\]\([^)]*\)/g, '$1') // 图片
		.replace(/#{1,6}\s*/g, '') // 标题
		.replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
		.replace(/\*(.*?)\*/g, '$1') // 斜体
		.replace(/~~(.*?)~~/g, '$1') // 删除线
		.replace(/==(.*?)==/g, '$1') // 高亮
		.replace(/\[\[(.*?)\]\]/g, '$1') // Obsidian内链
		.replace(/---+/g, '') // 分隔线
		.replace(/^[\s]*-[\s]+/gm, '') // 列表项
		.replace(/^[\s]*\d+\.[\s]+/gm, '') // 有序列表
		.replace(/^\s*>\s+/gm, '') // 引用
		.replace(/<!--[\s\S]*?-->/g, '') // HTML注释
		.replace(/%%[\s\S]*?%%/g, ''); // Obsidian注释

	// 简单的中英文字数统计
	let chineseMatches = cleanText.match(/[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/g) || [];
	let englishMatches = cleanText.match(/[a-zA-Z0-9]+/g) || [];
	
	return chineseMatches.length + englishMatches.length;
}

// 日期格式化函数
function formatDateForSearch(date){
	var d = new Date(date),
		month = '' + (d.getMonth() + 1),
		day = '' + d.getDate(),
		year = d.getFullYear();

	if (month.length < 2)
		month = '0' + month;
	if (day.length < 2)
		day = '0' + day;

	return [year, month, day].join('-');
}

// 获取当前文件
const currentFile = app.workspace.getActiveFile();

// 更新前置属性的函数
async function updateFrontmatter(property, value) {
	try {
		await app.fileManager.processFrontMatter(currentFile, (frontmatter) => {
			frontmatter[property] = value;
		});
		// 刷新当前视图
		setTimeout(() => {
			const activeLeaf = app.workspace.activeLeaf;
			if (activeLeaf) {
				activeLeaf.rebuildView();
			}
		}, 100);
	} catch (error) {
		console.error('更新前置属性失败:', error);
		if (typeof Notice !== 'undefined') {
			new Notice('更新失败，请重试');
		}
	}
}

// 创建搜索类型下拉框
const createSearchTypeDropdown = () => {
	const optionsText = ["🏷️ 按标签搜索", "📅 按日期搜索"];
	const optionsValue = ["tags", "mdate"];
	const dropdown = this.container.createEl('select');
	dropdown.style.cssText = `
		padding: 12px 16px;
		border: 1px solid var(--background-modifier-border, #e1e5e9);
		border-radius: 6px;
		background: var(--background-primary, white);
		font-size: 14px;
		color: var(--text-normal, #2c3e50);
		cursor: pointer;
		min-width: 180px;
		height: 42px;
		font-family: var(--font-interface);
		transition: all 0.2s ease;
		position: relative;
		z-index: 99999;
		line-height: 1.4;
	`;
	
	// 悬停效果
	dropdown.addEventListener('mouseenter', () => {
		dropdown.style.borderColor = 'var(--interactive-accent, #6366f1)';
		dropdown.style.boxShadow = '0 0 0 2px var(--background-modifier-border-focus, rgba(99, 102, 241, 0.1))';
	});
	
	dropdown.addEventListener('mouseleave', () => {
		dropdown.style.borderColor = 'var(--background-modifier-border, #e1e5e9)';
		dropdown.style.boxShadow = 'none';
	});
	
	optionsValue.forEach((value, index) => {
		const option = dropdown.createEl('option');
		option.text = optionsText[index];
		option.value = value;
		option.style.cssText = `
			padding: 10px 12px;
			background: var(--background-primary, white);
			color: var(--text-normal, #2c3e50);
			font-size: 14px;
			line-height: 1.4;
			min-height: 32px;
		`;
	});
	
	dropdown.selectedIndex = optionsValue.indexOf(searchType) >= 0 ? optionsValue.indexOf(searchType) : 0;

	dropdown.addEventListener('change', async evt => {
		evt.preventDefault();
		const newValue = optionsValue[dropdown.selectedIndex];
		console.log('搜索类型改变:', newValue);
		await updateFrontmatter('searchType', newValue);
	});
	
	return dropdown;
}

// 创建标签下拉框
const createTagsDropdown = () => {
	const tags = Object.keys(app.metadataCache.getTags()).sort();
	tags.unshift("#");
	const dropdown = this.container.createEl('select');
	dropdown.style.cssText = `
		padding: 12px 16px;
		border: 1px solid var(--background-modifier-border, #e1e5e9);
		border-radius: 6px;
		background: var(--background-primary, white);
		font-size: 14px;
		color: var(--text-normal, #2c3e50);
		cursor: pointer;
		min-width: 220px;
		height: 42px;
		font-family: var(--font-interface);
		transition: all 0.2s ease;
		position: relative;
		z-index: 99999;
		line-height: 1.4;
	`;

	// 悬停效果
	dropdown.addEventListener('mouseenter', () => {
		dropdown.style.borderColor = 'var(--interactive-accent, #6366f1)';
		dropdown.style.boxShadow = '0 0 0 2px var(--background-modifier-border-focus, rgba(99, 102, 241, 0.1))';
	});
	
	dropdown.addEventListener('mouseleave', () => {
		dropdown.style.borderColor = 'var(--background-modifier-border, #e1e5e9)';
		dropdown.style.boxShadow = 'none';
	});

	tags.forEach((tag, index) => {
		const option = dropdown.createEl('option');
		option.textContent = tag === "#" ? "🌍 全部文件" : tag;
		option.value = tag;
		option.style.cssText = `
			padding: 10px 12px;
			background: var(--background-primary, white);
			color: var(--text-normal, #2c3e50);
			font-size: 14px;
			line-height: 1.4;
			min-height: 32px;
		`;
	});

	// 设置当前选中的标签
	let currentSearchTerm = searchTerm || '';
	let tagToFind = currentSearchTerm ? "#" + currentSearchTerm : "#";
	let tagIndex = tags.indexOf(tagToFind);
	dropdown.selectedIndex = tagIndex >= 0 ? tagIndex : 0;

	dropdown.addEventListener('change', async evt => {
		evt.preventDefault();
		let selectedTag = tags[dropdown.selectedIndex];
		let tagValue = selectedTag === "#" ? "" : selectedTag.slice(1);
		console.log('标签改变:', tagValue);
		await updateFrontmatter('searchTerm', tagValue);
	});
	
	return dropdown;
}

// 创建日期选择器
const createDatePicker = () => {
	let currentSearchDate = formatDateForSearch(searchDate);

	const datePicker = this.container.createEl('input');
	datePicker.type = "date";
	datePicker.value = currentSearchDate;
	datePicker.style.cssText = `
		padding: 12px 16px;
		border: 1px solid var(--background-modifier-border, #e1e5e9);
		border-radius: 6px;
		background: var(--background-primary, white);
		font-size: 14px;
		color: var(--text-normal, #2c3e50);
		cursor: pointer;
		min-width: 200px;
		height: 42px;
		font-family: var(--font-interface);
		transition: all 0.2s ease;
		position: relative;
		z-index: 99999;
		line-height: 1.4;
	`;
	
	// 悬停和焦点效果
	datePicker.addEventListener('mouseenter', () => {
		datePicker.style.borderColor = 'var(--interactive-accent, #6366f1)';
	});
	
	datePicker.addEventListener('mouseleave', () => {
		if (document.activeElement !== datePicker) {
			datePicker.style.borderColor = 'var(--background-modifier-border, #e1e5e9)';
		}
	});
	
	datePicker.addEventListener('focus', () => {
		datePicker.style.borderColor = 'var(--interactive-accent, #6366f1)';
		datePicker.style.boxShadow = '0 0 0 2px var(--background-modifier-border-focus, rgba(99, 102, 241, 0.1))';
	});
	
	datePicker.addEventListener('blur', () => {
		datePicker.style.borderColor = 'var(--background-modifier-border, #e1e5e9)';
		datePicker.style.boxShadow = 'none';
	});
	
	datePicker.addEventListener('change', async (evt) => {
		evt.preventDefault();
		console.log('日期改变:', datePicker.value);
		await updateFrontmatter('searchDate', datePicker.value);
	});
	
	return datePicker;
}

// 显示搜索面板
dv.paragraph(`
<div style="background: var(--background-secondary, #f6f8fa); 
           border: 1px solid var(--background-modifier-border, #e1e5e9);
           border-radius: 8px; 
           padding: 20px; 
           margin-bottom: 20px;
           position: relative;
           overflow: visible;
           z-index: 10000;">
    <div style="display: flex; 
                align-items: center; 
                gap: 20px; 
                flex-wrap: wrap;
                position: relative; 
                z-index: 99998;">
        <div style="display: flex; align-items: center; gap: 10px;">
            <label style="font-weight: 500; color: var(--text-muted); font-size: 14px; white-space: nowrap;">搜索方式</label>
            <div id="search-type-container" style="position: relative; z-index: 99999;"></div>
        </div>
        
        <div style="display: flex; align-items: center; gap: 10px;">
            <label style="font-weight: 500; color: var(--text-muted); font-size: 14px; white-space: nowrap;">筛选条件</label>
            <div id="search-options-container" style="position: relative; z-index: 99999;"></div>
        </div>
    </div>
</div>
`);

// 插入下拉框控件
const searchTypeContainer = this.container.querySelector('#search-type-container');
const searchOptionsContainer = this.container.querySelector('#search-options-container');

// 确保容器有足够的空间显示下拉选项
if (searchTypeContainer) {
	searchTypeContainer.style.cssText = `
		position: relative; 
		z-index: 99999; 
		min-height: 60px; 
		overflow: visible;
	`;
	searchTypeContainer.appendChild(createSearchTypeDropdown());
}

if (searchOptionsContainer) {
	searchOptionsContainer.style.cssText = `
		position: relative; 
		z-index: 99999; 
		min-height: 60px; 
		overflow: visible;
	`;
	if (searchType === "tags") {
		searchOptionsContainer.appendChild(createTagsDropdown());
	} else if (searchType === "mdate") {
		searchOptionsContainer.appendChild(createDatePicker());
	}
}

// 获取搜索结果
let searchPagePaths = [];
let valueOfSearchTerm = "";

if(searchType === "tags"){
	if(searchTerm && searchTerm !== ""){
		valueOfSearchTerm = "#"+searchTerm;
		searchPagePaths = dv.pages(valueOfSearchTerm);
	} else {
		valueOfSearchTerm = "";
		searchPagePaths = dv.pages();
	}
} else if(searchType === "mdate") {
	valueOfSearchTerm = formatDateForSearch(searchDate);
	searchPagePaths = dv.pages().where(p => formatDateForSearch(p.file.mtime) == valueOfSearchTerm);
}

// 确保searchPagePaths是数组格式
if(searchPagePaths && searchPagePaths.file) {
	searchPagePaths = searchPagePaths.file.path;
} else if(searchPagePaths.length === 0) {
	searchPagePaths = [];
}

// 显示当前搜索状态
dv.paragraph(`
<div style="background: var(--background-primary-alt, #f6f8fa); 
           border: 1px solid var(--background-modifier-border, #e1e5e9);
           border-radius: 6px;
           padding: 14px 18px; 
           margin-bottom: 16px;">
    <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap; font-size: 14px;">
        <span style="font-weight: 600; color: var(--text-normal); font-size: 15px;">📊 搜索结果</span>
        <span style="background: var(--interactive-accent, #6366f1);
                     color: white;
                     padding: 4px 10px; 
                     border-radius: 14px; 
                     font-size: 12px;
                     font-weight: 500;
                     box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            ${searchType === 'tags' ? '🏷️ 标签' : '📅 日期'}: 
            ${searchType === 'tags' ? (searchTerm || '全部') : formatDateForSearch(searchDate)}
        </span>
        <span style="background: var(--color-green, #22c55e);
                     color: white;
                     padding: 4px 10px; 
                     border-radius: 14px; 
                     font-size: 12px;
                     font-weight: 500;
                     box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            📄 找到 ${searchPagePaths.length} 个文件
        </span>
        <a href="obsidian://open?vault=Rainbell&file=40%20-%20Obsidian%2F%E4%B8%BB%E9%A1%B5%2F00.%20%E6%96%87%E4%BB%B6%E7%AE%A1%E7%90%86" 
           style="color: var(--interactive-accent, #6366f1); 
                  text-decoration: none; 
                  margin-left: auto; 
                  font-size: 13px;
                  padding: 2px 6px;
                  border-radius: 4px;
                  transition: background-color 0.2s;
                  border: 1px solid var(--interactive-accent, #6366f1);"
           onmouseover="this.style.backgroundColor='var(--interactive-accent-hover)'"
           onmouseout="this.style.backgroundColor='transparent'">
            ⚙️ 高级搜索
        </a>
    </div>
</div>
`);

// 构建搜索结果
const getSearchResults = async() => {
	let results = [];
	let totalWords = 0;
	let totalChars = 0;

	// 限制显示前10个文件以保持主页简洁
	const maxResults = 10;
	const limitedPaths = searchPagePaths.slice(0, maxResults);

	for(let i = 0; i < limitedPaths.length; i++){
		let page = app.vault.getAbstractFileByPath(limitedPaths[i]);
		if(page) {
			const content = await app.vault.cachedRead(page);
			let wordCount = getWordCount(content);
			let charCount = content.length;
			let mtime = formatDateForSearch(page.stat.mtime);
			
			totalWords += wordCount;
			totalChars += charCount;

			results.push([
				`[[${page.basename}]]`,
				mtime,
				wordCount,
				charCount
			]);
		}
	}

	// 添加总计行
	if(results.length > 0) {
		results.push([
			"**📊 总计**",
			"",
			`**${totalWords}**`,
			`**${totalChars}**`
		]);
	}

	return results;
}

// 显示结果表格
if(searchPagePaths.length > 0) {
	dv.table(["📄 文件名", "📅 修改日期", "📝 字数", "📊 字符数"], await getSearchResults());
	
	// 如果文件超过10个，显示提示
	if(searchPagePaths.length > 10) {
		dv.paragraph(`
<div style="text-align: center; 
           color: var(--text-muted); 
           font-style: italic; 
           margin-top: 12px;
           font-size: 14px;">
    只显示前10个文件，共有 ${searchPagePaths.length} 个文件匹配。
    <a href="obsidian://open?vault=Rainbell&file=40%20-%20Obsidian%2F%E4%B8%BB%E9%A1%B5%2F00.%20%E6%96%87%E4%BB%B6%E7%AE%A1%E7%90%86" 
       style="color: var(--interactive-accent); text-decoration: none;">查看全部结果</a>
</div>
		`);
	}
} else {
	dv.paragraph(`
<div style="text-align: center; 
           color: var(--text-muted); 
           padding: 24px; 
           border: 1px dashed var(--background-modifier-border); 
           border-radius: 8px;
           background: var(--background-primary-alt);">
    <div style="font-size: 2em; margin-bottom: 12px;">🔍</div>
    <div style="font-size: 16px; margin-bottom: 8px; color: var(--text-normal);">没有找到匹配的文件</div>
    <div style="font-size: 14px;">
        <a href="obsidian://open?vault=Rainbell&file=40%20-%20Obsidian%2F%E4%B8%BB%E9%A1%B5%2F00.%20%E6%96%87%E4%BB%B6%E7%AE%A1%E7%90%86"
           style="color: var(--interactive-accent); text-decoration: none;">调整搜索条件</a>
    </div>
</div>
	`);
}
```

</div>

---

## 📊 项目追踪

````ad-success
title: 📊 项目进度追踪仪表板

%%
__Notes to display__
*Gets either notes in a folder or notes with a certain tag. Leave one of them empty.*
sourceFolder:: 
sourceTag:: #project/open   

__Notes to exclude__
*Leave empty to disable. Notes with the yaml-key `status` and value `exclude` for that key are also excluded.)*
excludeTag::  #exclude 

__Counting Settings__
*"chars" or "words"*
toCount:: words
target:: 70000

*words or characters per page, depending on setting above. Set to zero to ignore.*
wordsPerPage:: 350
charsPerPage:: 2000

includeFootnotes:: true
charactersIncludeSpaces:: true
excludeComments:: true
cumulativeShare:: false
groupedCount:: true

__Bibliography Estimate for Pandoc Citations__
includeBibliographyEstimate:: true
wordsPerCitation:: 22
charsPerCitation:: 155

__Longform Plugin__
*Leave empty to sort alphabetically. Enter the path to the index file of a longform project to order sections by their order in the longform plugin. (The `sourceFolder` setting further above has to be a Longform Drafts folder. )*
pathToIndexFile::

*Begin a filename with this character and it will be treated as subsection*
subsectionStartChar:: _

__Purely visual__
useThousandSeperator:: true
thousandSeperator:: .
naChar:: —
mostRecentIcon:: 🕙
%%

```dataviewjs
// Word Count Dashboard

// a dataviewjs snippet by @pseudometa, https://gist.github.com/chrisgrieser/ac16a80cdd9e8e0e84606cc24e35ad99

// version 1.10.2

// last update: 2022-01-25

//----------------------------------------------------

// Import configuration

//----------------------------------------------------

const source = dv.current();

const sourceFolder = source.sourceFolder;

const target = source.target;

const toCount = source.toCount;

const includeFootnotes = source.includeFootnotes;

const charactersIncludeSpaces = source.charactersIncludeSpaces;

const excludeComments = source.excludeComments;

const includeBibliographyEstimate = source.includeBibliographyEstimate;

const wordsPerCitation = source.wordsPerCitation;

const charsPerCitation = source.charsPerCitation;

const thousandSeperator = source.thousandSeperator;

const useThousandSeperator = source.useThousandSeperator;

const naChar = source.naChar;

const subsectionStartChar = source.subsectionStartChar;

const wordsPerPage = source.wordsPerPage;

const charsPerPage = source.charsPerPage;

const pathToIndexFile = source.pathToIndexFile;

const cumulativeShare = source.cumulativeShare;

const groupedCount = source.groupedCount;

const mostRecentIcon = source.mostRecentIcon;

let sourceTag = source.sourceTag;

let excludeTag = source.excludeTag;

// prepend hashtags for tags

if (sourceTag) if (!sourceTag.startsWith("#")) sourceTag = "#" + sourceTag;

if (excludeTag) if (!excludeTag.startsWith("#")) excludeTag = "#" + excludeTag;

//----------------------------------------------------

// Functions

//----------------------------------------------------

function getWordCount(text) {
	// 简化的字数统计函数，避免复杂的正则表达式错误
	if (!text) return 0;
	
	// 移除Markdown语法
	let cleanText = text
		.replace(/```[\s\S]*?```/g, '') // 代码块
		.replace(/`[^`]*`/g, '') // 行内代码
		.replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // 链接
		.replace(/!\[([^\]]*)\]\([^)]*\)/g, '$1') // 图片
		.replace(/#{1,6}\s*/g, '') // 标题
		.replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
		.replace(/\*(.*?)\*/g, '$1') // 斜体
		.replace(/~~(.*?)~~/g, '$1') // 删除线
		.replace(/==(.*?)==/g, '$1') // 高亮
		.replace(/\[\[(.*?)\]\]/g, '$1') // Obsidian内链
		.replace(/---+/g, '') // 分隔线
		.replace(/^[\s]*-[\s]+/gm, '') // 列表项
		.replace(/^[\s]*\d+\.[\s]+/gm, '') // 有序列表
		.replace(/^\s*>\s+/gm, '') // 引用
		.replace(/<!--[\s\S]*?-->/g, '') // HTML注释
		.replace(/%%[\s\S]*?%%/g, ''); // Obsidian注释

	// 简单的中英文字数统计
	// 中文字符 (包括中文标点)
	let chineseMatches = cleanText.match(/[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/g) || [];
	// 英文单词 (字母、数字组合)
	let englishMatches = cleanText.match(/[a-zA-Z0-9]+/g) || [];
	
	return chineseMatches.length + englishMatches.length;
}

function getCharacterCount(text) {

if (charactersIncludeSpaces) return text.length;

return text.replaceAll(" ", "").length;

}

function insert1000sep (num) {

let numText = String(num);

if (!useThousandSeperator) return numText;

if (num >= 10000) numText = numText.slice(0, -3) + thousandSeperator + numText.slice (-3); // eslint-disable-line no-magic-numbers

return numText;

}

String.prototype.strong = function () {

if (this === " ") return " ";

return "**" + this + "**";

};

function removeMarkdown (text) {

let plaintext = text

.replace(/`\$?=[^`]+`/g, "") // inline dataview

.replace(/^---\n.*?\n---\n/s, "") // YAML Header

.replace(/!?\[(.+)\]\(.+\)/g, "$1") // URLs & Image Captions

.replace(/\*|_|\[\[|\]\]|\||==|~~|---|#|> |`/g, ""); // Markdown Syntax

if (excludeComments) {

plaintext = plaintext

.replace(/<!--.*?-->/sg, "")

.replace(/%%.*?%%/sg, "");

}

else {

plaintext = plaintext

.replace(/%%|<!--|-->/g, ""); // remove only comment syntax

}

return plaintext;

}

function removeFootnotes (text) {

return text

.replace(/^\[\^[A-Za-z0-9-]+\]:.*$/gm, "") // footnote at the end

.replace(/\[\^[A-Za-z0-9-]+\]/g, ""); // footnote reference inline

}

function countPandocCitations (text) {

const citations = text.match(/@[A-Za-z0-9-]+(?=[,;\] ])/gi);

if (!citations) return 0;

const uniqCitations = [...new Set(citations)]; // only unique citations

return uniqCitations.length;

}


function toTheProgressBar (share) {

let progressBar =

" <progress max=\"100\" value=\""

+ (share * 100).toFixed(1).toString()

+ "\"> </progress>";
	
return progressBar;
	
}


function toPercentStr (share) {

return (share * 100).toFixed(0).toString() + " %";

}

//----------------------------------------------------

// Table Construction

//----------------------------------------------------

async function getTableContents () {

const output = [];

let completeText = "";

let total = 0;

let share = 0;

let sectionCounter = 0;

let subsectionCounter = 0;

let totalTasks = 0;

// get sections via folder or via tag

let sections;

if (sourceFolder) {

sections = dv.pages("\"" + sourceFolder + "\"");

if (sections.length == 0) return null ;

}

else {

sections = dv.pages(sourceTag);

if (sections.length == 1) return null ;

}

// exclude certain notes

numExcludeStatus = sections.filter(t => t.status === "exclude").length;

sections = sections.filter(t => t.status !== "exclude");

if (excludeTag !== "") {

numExcludedNotes = sections.filter(t => t.file.tags.includes(excludeTag)).length;

sections = sections.filter(t => !t.file.tags.includes(excludeTag));

}

// most recent note

sections = sections.sort (s => s.file.mtime, "desc");

const mostRecentNote = sections[0].file.name;

// SORT sections

if (pathToIndexFile) {

const draftName = sourceFolder.split("/").pop();

const longformOrder =

dv.page(pathToIndexFile) // do not wrap in ", as with db.pages

.drafts

.filter(d => d.name === draftName)

.scenes;

sections = sections.sort(

s => s.file.name,

"desc",

(a, b) => longformOrder.indexOf(b) - longformOrder.indexOf(a)

);

} else {

sections = sections.sort(s => s.file.name);

}

//-------------------------------------------------

// SECTIONS LOOP

//-------------------------------------------------

for (const section of sections) {

// read page content

let content = await dv.io.load(section.file.path); // eslint-disable-line no-await-in-loop

// count markdown tasks

// need to be counted before cleanup

let tasks = content.match(/- \[ ] /g);

let taskNum = 0;

let taskStr = "";

let finishedTasksNum = 0;
	
let finishedTasksStr = "";

if (tasks) {

taskNum = tasks.length;

taskStr = taskNum.toString();

}

let finishedTasks = content.match(/- \[x] /g)

if (finishedTasks) {

finishedTasksNum = finishedTasks.length;

finishedTasksStr = finishedTasksNum.toString()

}

// clean up

content = removeMarkdown (content);

if (!includeFootnotes) content = removeFootnotes (content);

content = content

.replace(/(^\s*)|(\s*$)/g, "") // remove the start and end spaces of the given string

.replace(/ {2,}/g, " "); // reduce multiple spaces to a single space

// Table Values: Count & Share

let wcCount = 0;

if (toCount === "words") wcCount = getWordCount(content);

if (toCount === "chars") wcCount = getCharacterCount(content);

if (section.target == "tasks") share = (finishedTasksNum / (taskNum + finishedTasksNum));

else share = (wcCount / section.target);

// shareType

let shareType = ""

if (section.target == "tasks") shareType = "<span id='task' style='white-space: nowrap'>任务</span>"
//+ taskNum + "/" + (taskNum + finishedTasksNum)

else shareType = "<span id='word'>字数</span> "
//+ wcCount + "/" + section.target

// Status

let status = section.status;

if (!status) status = " ";

// Section numbering

const isSubsection = section.file.name.startsWith(subsectionStartChar);

let sectionNumbering;

let sectionLink;

if (isSubsection) {

subsectionCounter++;

sectionNumbering = "<small>" + sectionCounter.toString() + "." + subsectionCounter.toString() + "</small>";

sectionLink =

"<small>[["

+ section.file.path

+ "|"

+ section.file.name.slice(1)

+ "]]</small>";

} else {

subsectionCounter = 0;

sectionCounter++;

sectionNumbering = sectionCounter.toString().strong();

sectionLink = "__" + section.file.link + "__";

}

// Most Recent Note

if (section.file.name === mostRecentNote) sectionLink += "&nbsp;&nbsp;&nbsp;" + mostRecentIcon;

// push table values

output.push([



sectionLink,

shareType,

share,



"<span style='white-space: nowrap'>"+status + "</span>"

]);

// add to totals & bibliography calculation

totalTasks += taskNum;

total += wcCount;

if (includeBibliographyEstimate) completeText += content;

}

// Add Subsections counts to the sections

//-------------------------------------------------

if (groupedCount) {

let upperSectionID = -1;

for (var i = 0; i < output.length; i++) {

let isSubsection = output[i][0].includes(".");

let firstSectionFound = (upperSectionID !== -1);

if (!firstSectionFound && isSubsection) continue;

if (isSubsection) {

output[upperSectionID][2] += output[i][2]; // add count

if (!cumulativeShare) output[upperSectionID][3] += output[i][3]; // add share

}

if (!isSubsection) upperSectionID = i;

}

output.map(row => {

//row[1] = insert1000sep(row[1]);

row[2] = toTheProgressBar(row[2]);

return row;

});

}

//-------------------------------------------------

// OVERALL

//-------------------------------------------------



return output;

}

//----------------------------------------------------

// Main

//----------------------------------------------------

// Print Table

let numExcludedNotes = 0;

let numExcludeStatus = 0;

let countedEntity = "Words";

if (toCount === "chars") countedEntity = "Chars";

let typeOfShare = "Share";

if (cumulativeShare === "true") countedEntity = "Target";

const tcontent = await getTableContents();

// 显示项目跟踪表格
if (tcontent && tcontent.length > 0) {
    dv.table(["📋 项目","📊 标准","📈 进度", "⚡ 状态"], tcontent);
} else {
    dv.paragraph("💡 暂无项目数据，请检查标签设置或文件夹配置");
}
```
````

---

## 📊 活动面板

````ad-info
title: 📝 每日总结追踪 (最近7天)

```dataview
table without id 
link(file.link, regexreplace(regexreplace(file.name, "^.*(\d{4})年0?", ""), "月0?", "月")) as "📅 日期", 
总结 as "📋 每日总结"
from #DailyNote
where 总结 != null
sort file.name desc
limit 7
```
````

````ad-info
title: 💪 健身追踪记录 (最近7天)

```dataview
table without id 
link(file.link, regexreplace(regexreplace(file.name, "^.*(\d{4})年0?", ""), "月0?", "月")) as "📅 日期", 
健康记录 as "🏃 健康记录",
体重记录 as "⚖️ 体重记录"
from #DailyNote
where 健康记录 != null
or 体重记录 != null
sort file.name desc
limit 7
```
````

---

## 🐱 宠物追踪

````ad-info
title: 🐱 宠物记录追踪 (最近7天)

```dataviewjs
let diaryFolderPath = "00 - 每日日记";
const diaryFiles = app.vault.getMarkdownFiles().filter(file => file.path.includes(diaryFolderPath));

let diaryContentPromises = diaryFiles.map(file => {
    return app.vault.cachedRead(file).then(content => {
        const diaryName = file.name.split(".")[0];
        const formattedDate = moment(diaryName, "YYYY年MM月DD日").format("M月D日");
        const logLines = content.split("\n").filter(line => line.includes("#记录")).map(line => `${line.replace(/^[\s\-\*]*/, "").replace("#记录", "").trim()}`);
        return [dv.fileLink(diaryName, false, formattedDate), logLines.join(' • '), diaryName];
    });
});

Promise.all(diaryContentPromises).then(diaryContents => {
    const sortedDiaryContents = diaryContents
        .filter(([link, lines]) => lines && lines.trim() !== '' && link != "[[未命名 10]]")
        .sort(([_linkA, _linesA, diaryNameA], [_linkB, _linesB, diaryNameB]) => diaryNameB.localeCompare(diaryNameA))
        .slice(0, 7)
        .map(([link, lines]) => [link, lines]); // Remove diaryName used for sorting

    if (sortedDiaryContents.length > 0) {
        dv.table(["📅 日期", "🐾 宠物记录"], sortedDiaryContents);
    } else {
        dv.paragraph(`
<div style="text-align: center; 
           color: var(--text-muted); 
           padding: 24px; 
           border: 1px dashed var(--background-modifier-border); 
           border-radius: 8px;
           background: var(--background-primary-alt);">
    <div style="font-size: 2em; margin-bottom: 12px;">🐱</div>
    <div style="font-size: 16px; margin-bottom: 8px; color: var(--text-normal);">暂无宠物记录</div>
    <div style="font-size: 14px;">在每日日记中添加包含 "#记录" 的内容即可显示</div>
</div>
        `);
    }
});
```
````

---

## 📈 Obsidian 使用统计

````ad-warning
title: 📈 使用统计概览

```dataviewjs
let ftMd = dv.pages("").file.sort(t => t.cday)[0]
let total = parseInt([new Date() - ftMd.ctime] / (60*60*24*1000))
let nofold = '!"misc/templates"'
let allFile = dv.pages(nofold).file
let totalMd = allFile.length
let totalTag = allFile.etags.distinct().length
let totalTask = allFile.tasks.length

// 显示统计信息表格
dv.table(["📊 统计项目", "📈 数值", "💡 说明"], [
    ["🕒 使用天数", `**${total}**`, "从首个笔记创建到现在"],
    ["📝 笔记总数", `**${totalMd}**`, "不包括模板文件"],
    ["🏷️ 标签数量", `**${totalTag}**`, "所有标签类型"],
    ["✅ 待办事项", `**${totalTask}**`, "全库待完成任务"]
]);
```
````


---

## 📄 文件动态

````ad-info
title: ✏️ 最近编辑文件 (Top 10)

```dataview
table without id
link(file.link, file.name) as "📄 文件名",
dateformat(file.mtime, "MM-dd HH:mm") as "⏰ 修改时间"
from ""
where !contains(file.path, "模板") and !contains(file.path, "kanban") and !contains(file.path, "附件")
sort file.mtime desc
limit 10
```
````

````ad-info
title: ✨ 最近创建文件 (3天内)

```dataview
table without id
link(file.link, file.name) as "📄 文件名",
dateformat(file.ctime, "MM-dd HH:mm") as "🆕 创建时间"
from ""
where !contains(file.path, "模板") and !contains(file.path, "kanban") and !contains(file.path, "附件")
where date(today) - file.ctime <= dur(3 days)
sort file.ctime desc
limit 10
```
````