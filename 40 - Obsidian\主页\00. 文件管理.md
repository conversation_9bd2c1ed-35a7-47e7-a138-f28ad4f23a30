---
searchTerm: Redis
searchType: tags
searchDate: 2025-07-08
---

```dataviewjs

// [obsidian-snippets/dataviewjs.acrossVaultWordCount-v0.0.11.md at main · torantine/obsidian-snippets (github.com)](https://github.com/torantine/obsidian-snippets/blob/main/dataviewjs.acrossVaultWordCount-v0.0.11.md)
// modified by Blue-topaz-example Cuman
dv.paragraph("### 通过下拉框筛选检索条件<br><br>")

// 检查MetaEdit插件是否启用
const metaeditEnabled = app.plugins.enabledPlugins.has("metaedit");

if(metaeditEnabled === true) {
	
	let searchType = dv.current().searchType;	
	let searchTerm = dv.current().searchTerm??'';
	let searchDate =  dv.current().searchDate;

	
	function formatDate(date){
		var d = new Date(date),
			month = '' + (d.getMonth() + 1),
			day = '' + d.getDate(),
			year = d.getFullYear();

		if (month.length < 2) 
			month = '0' + month;
		if (day.length < 2) 
			day = '0' + day;
		return [year, month, day].join('-');
	}
	searchDate = formatDate(searchDate);

	// 获取当前文件
	const currentFile = app.workspace.getActiveFile();
	
	// 更新前置属性的函数
	async function updateFrontmatter(property, value) {
		try {
			await app.fileManager.processFrontMatter(currentFile, (frontmatter) => {
				frontmatter[property] = value;
			});
			// 刷新当前视图
			setTimeout(() => {
				const activeLeaf = app.workspace.activeLeaf;
				if (activeLeaf) {
					activeLeaf.rebuildView();
				}
			}, 100);
		} catch (error) {
			console.error('更新前置属性失败:', error);
			// 安全地显示通知
			if (typeof Notice !== 'undefined') {
				new Notice('更新失败，请检查MetaEdit插件是否正确安装');
			}
		}
	}

	const dateMaker = () => {
		let currentSearchDate = formatDate(searchDate);
	
		const textBox = this.container.createEl('input');
		textBox.type = "date";
		textBox.value = currentSearchDate;
		textBox.placeholder = "选择日期";
		
		textBox.addEventListener('change', async (evt) => {
			evt.preventDefault();
			console.log('日期改变:', textBox.value);
			await updateFrontmatter('searchDate', textBox.value);
		})
		return textBox
	}

	const searchTypeDropdownMaker = () => {
		const optionsText = ["按标签", "按修改日期"];
		const optionsValue = ["tags", "mdate"]
		const dropdown = this.container.createEl('select');
		
		optionsValue.forEach((value, index) => {
			const option = dropdown.createEl('option');
			option.text = optionsText[index];
			option.value = value;
		});
		
		dropdown.selectedIndex = optionsValue.indexOf(searchType) >= 0 ? optionsValue.indexOf(searchType) : 0;

		dropdown.addEventListener('change', async evt => {
			evt.preventDefault();
			const newValue = optionsValue[dropdown.selectedIndex];
			console.log('检索类型改变:', newValue);
			await updateFrontmatter('searchType', newValue);
		})
		
		return dropdown
	}

	const tagsDropdownMaker = () => {
		const tags = Object.keys(app.metadataCache.getTags()).sort();
		tags.unshift("#");
		const dropdown = this.container.createEl('select');

		tags.forEach((tag, index) => {
			const option = dropdown.createEl('option');
			option.textContent = tag === "#" ? "All Pages" : tag;
			option.value = tag;
		});

		// 设置当前选中的标签
		let currentSearchTerm = searchTerm || '';
		let tagToFind = currentSearchTerm ? "#" + currentSearchTerm : "#";
		let tagIndex = tags.indexOf(tagToFind);
		dropdown.selectedIndex = tagIndex >= 0 ? tagIndex : 0;

		dropdown.addEventListener('change', async evt => {
			evt.preventDefault();
			let selectedTag = tags[dropdown.selectedIndex];
			let tagValue = selectedTag === "#" ? "" : selectedTag.slice(1);
			console.log('标签改变:', tagValue);
			await updateFrontmatter('searchTerm', tagValue);
		})
		return dropdown
	}

	// Output
	dv.el("dvjs", "");
	dv.paragraph("## 检索类型: ")
	dv.paragraph(searchTypeDropdownMaker())
	dv.paragraph("## 选项: ")
	if(searchType === "tags"){
		dv.paragraph(tagsDropdownMaker());
	} else if (searchType === "mdate"){
		dv.paragraph(dateMaker());
	} else {
		dv.paragraph("请选择有效的搜索类型")
	}
} else {
	// 如果MetaEdit不可用，提供备用方案
	dv.paragraph("<strong>⚠️ MetaEdit 插件未启用</strong>");
	dv.paragraph("请启用MetaEdit插件以使用下拉框筛选功能。");
	dv.paragraph("或者直接在文件头部的YAML属性中修改以下值：");
	dv.paragraph("- `searchType`: 设置为 `tags` 或 `mdate`");
	dv.paragraph("- `searchTerm`: 设置标签名称（不含#号）");
	dv.paragraph("- `searchDate`: 设置日期（格式：YYYY-MM-DD）");
	
	// 显示当前筛选条件
	let searchType = dv.current().searchType;
	let searchTerm = dv.current().searchTerm;
	let searchDate = dv.current().searchDate;
	
	dv.paragraph("### 当前筛选条件：");
	dv.paragraph(`- 检索类型: **${searchType === 'tags' ? '按标签' : searchType === 'mdate' ? '按修改日期' : '未设置'}**`);
	if(searchType === 'tags') {
		dv.paragraph(`- 标签: **${searchTerm || '全部页面'}**`);
	} else if(searchType === 'mdate') {
		dv.paragraph(`- 日期: **${searchDate || '未设置'}**`);
	}
}

```
## 统计列表
```dataviewjs
// Modified by cuman

// Word Count Dashboard
// a dataviewjs snippet by @pseudometa, https://gist.github.com/chrisgrieser/ac16a80cdd9e8e0e84606cc24e35ad99
// version 1.10.2
// last update: 2022-01-25

//----------------------------------------------------
// Functions
//----------------------------------------------------

function getWordCount(text) {
	// 简化的字数统计函数，避免复杂的正则表达式错误
	if (!text) return 0;
	
	// 移除Markdown语法
	let cleanText = text
		.replace(/```[\s\S]*?```/g, '') // 代码块
		.replace(/`[^`]*`/g, '') // 行内代码
		.replace(/\[([^\]]*)\]\([^)]*\)/g, '$1') // 链接
		.replace(/!\[([^\]]*)\]\([^)]*\)/g, '$1') // 图片
		.replace(/#{1,6}\s*/g, '') // 标题
		.replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
		.replace(/\*(.*?)\*/g, '$1') // 斜体
		.replace(/~~(.*?)~~/g, '$1') // 删除线
		.replace(/==(.*?)==/g, '$1') // 高亮
		.replace(/\[\[(.*?)\]\]/g, '$1') // Obsidian内链
		.replace(/---+/g, '') // 分隔线
		.replace(/^[\s]*-[\s]+/gm, '') // 列表项
		.replace(/^[\s]*\d+\.[\s]+/gm, '') // 有序列表
		.replace(/^\s*>\s+/gm, '') // 引用
		.replace(/<!--[\s\S]*?-->/g, '') // HTML注释
		.replace(/%%[\s\S]*?%%/g, ''); // Obsidian注释

	// 简单的中英文字数统计
	// 中文字符 (包括中文标点)
	let chineseMatches = cleanText.match(/[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/g) || [];
	// 英文单词 (字母、数字组合)
	let englishMatches = cleanText.match(/[a-zA-Z0-9]+/g) || [];
	
	return chineseMatches.length + englishMatches.length;
}

function getCharacterCount(text) {
	return text.length;
}

function insert1000sep (num) {
	let numText = String(num);
	if (!useThousandSeperator) return numText;
	if (num >= 10000) numText = numText.slice(0, -3) + thousandSeperator + numText.slice (-3); // eslint-disable-line no-magic-numbers
	return numText;
}

String.prototype.strong = function () {
	if (this === " ") return " ";
	return "**" + this + "**";
};

function removeMarkdown (text) {
	let plaintext = text
		.replace(/`\$?=[^`]+`/g, "") // inline dataview
		.replace(/^---\n.*?\n---\n/s, "") // YAML Header
		.replace(/!?\[(.+)\]\(.+\)/g, "$1") // URLs & Image Captions
		.replace(/\*|_|\[\[|\]\]|\||==|~~|---|#|> |`/g, ""); // Markdown Syntax
	plaintext = plaintext
		.replace(/<!--.*?-->/sg, "")
		.replace(/%%.*?%%/sg, "");
	return plaintext;
}

function removeFootnotes (text) {
	return text
		.replace(/^\[\^[A-Za-z0-9-]+\]:.*$/gm, "") // footnote at the end
		.replace(/\[\^[A-Za-z0-9-]+\]/g, ""); // footnote reference inline
}

function toTheProgressBar (share) {
	let progressBar =
		" <progress max=\"100\" value=\""
		+ (share * 100).toFixed(0).toString()
		+ "\"> </progress>";
	return progressBar;
}

function toPercentStr (share) {
	return (share * 100).toFixed(0).toString() + " %";
}

function getSentenceCount(text) {
	// Thanks to Extract Highlights plugin and AngelusDomini
	// Also https://stackoverflow.com/questions/5553410
	var sentences = ((text || "").match(/[^.。!！?？\s][^.。!！?？]*(?:[.!?](?!['''"""「」『』]?\s|$)[^.。!！?？]*)*[.。!！?？]?[''""」』]?(?=\s||$)/gm) || []);
	var sentencesLength = sentences.length;
	return sentencesLength;
}

/* =================== Search Logic =================== */

// before search field we need a searchType tag, mdate etc.
let searchType = dv.current().searchType
let searchTerm = dv.current().searchTerm
let searchDate = dv.current().searchDate;

// 使用统一的日期格式化函数
function formatDateForSearch(date){
	var d = new Date(date),
		month = '' + (d.getMonth() + 1),
		day = '' + d.getDate(),
		year = d.getFullYear();

	if (month.length < 2)
		month = '0' + month;
	if (day.length < 2)
		day = '0' + day;

	return [year, month, day].join('-');
}

searchDate = formatDateForSearch(searchDate);
let valueOfSearchTerm = "";
let searchPagePaths = [];

if(searchType === "tags"){
	if(searchTerm && searchTerm !== ""){
		valueOfSearchTerm = "#"+searchTerm; // value of "searchTerm"
		searchPagePaths = dv.pages(valueOfSearchTerm);
	} else {
		// 如果没有指定标签，显示所有页面
		valueOfSearchTerm = "";
		searchPagePaths = dv.pages();
	}
} else if(searchType === "mdate") {
	valueOfSearchTerm = searchDate; // value of "searchTerm"
	searchPagePaths = dv.pages().where(p => formatDateForSearch(p.file.mtime) == searchDate);
}

// 确保searchPagePaths是数组格式
if(searchPagePaths && searchPagePaths.file) {
	searchPagePaths = searchPagePaths.file.path;
} else if(searchPagePaths.length === 0) {
	searchPagePaths = [];
}

/* ==================== MAKE TABLE ==================== */

let allNames = new Array();
let allWordCount = new Array();
let wordCountSum = new Array();
let allCharCount = new Array();
let charCountSum = new Array()
let allSentenceCount = new Array();
let sentenceCountSum = new Array();
let allMdate = new Array();
let pageCount = new Array();

const getTableContents = async() => {
	let name = new Array();
	let text = new Array();
	let mtime = new Array();
	let output = new Array()

	// fill text and name with values
	for(let i=0; i < searchPagePaths.length; i++){
		let page = this.app.vault.getAbstractFileByPath(searchPagePaths[i]);
		if(page) {
			const content = await app.vault.cachedRead(page);
			mtime.push(page.stat.mtime);
			name.push("[["+page.basename+"]]");
			text.push(content || "");
		}
	}

	mtime.forEach((date, index) => {
		mtime[index] = formatDateForSearch(date);
	})

	// do word, character, and sentence count on pages and format for dv.table
	for(let i=0; i<text.length; i++){
		// https://www.tutorialspoint.com/how-to-count-a-number-of-words-in-given-string-in-javascript
		text[i] = text[i].replace(/(^\\s\*)|(\\s\*$)/gi,""); // remove the start and end spaces of the given string
		text[i] = text[i].replace(/\[ \]{2,}/gi," "); // reduce multiple spaces to a single space
		text[i] = text[i].replace(/\\n /,"\\n"); // exclude a new line with a start spacing

		let wordCount = getWordCount(text[i]) || 0;
		let characterCount = getCharacterCount(text[i]) || 0;
		let sentenceCount = getSentenceCount(text[i]) || 0;

		allNames.push([name[i]])
		allWordCount.push(wordCount);
		allCharCount.push(characterCount);
		allSentenceCount.push(sentenceCount);
		allMdate.push(mtime[i])

		output.push([name[i], mtime[i], wordCount, characterCount, sentenceCount]); // array with link to file, word count, character count, and sentence count
	}

	if(allWordCount.length > 0) {
		pageCount.push(allNames.length)
		wordCountSum.push(allWordCount.reduce((a, b) => a + b, 0))
		charCountSum.push(allCharCount.reduce((a, b) => a + b, 0))
		sentenceCountSum.push(allSentenceCount.reduce((a, b) => a + b, 0))
		output.push(["<strong>总计</strong>", "", wordCountSum.toString(), charCountSum.toString(), sentenceCountSum.toString() ])
	}

	return output;
}

dv.table(["文件名", "修改日期", "字数", "字符数", "段落数"], await getTableContents())

if(searchType == "mdate" && searchPagePaths.length < 1){
	dv.paragraph("<strong>该日期没有修改的页面</strong>")
}

```

注：该脚本来源于Cuman的Blue Topaz example vault，一个十分值得学习的示例库. 你可以前往[此地址](https://github.com/cumany/Blue-topaz-examples)下载该库。