.theme-light {
    --arrows-up: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='21' height='4' viewBox='0 0 21 4'%3E%3Cpath d='M6.5 4l4-4 4 4z'/%3E%3C/svg%3E");
    --arrows-down: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='21' height='4' viewBox='0 0 21 4'%3E%3Cpath d='M14.5 0l-4 4-4-4z'/%3E%3C/svg%3E");
}

.theme-dark {
    --arrows-up: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='21' height='4' viewBox='0 0 21 4'%3E%3Cpath fill='grey' d='M6.5 4l4-4 4 4z'/%3E%3C/svg%3E");
    --arrows-down: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='21' height='4' viewBox='0 0 21 4'%3E%3Cpath fill='grey' d='M14.5 0l-4 4-4-4z'/%3E%3C/svg%3E");
}

.markdown-preview-view th,
.table-view-table > thead > tr > th,
.markdown-source-view.mod-cm6 .dataview.table-view-table thead.table-view-thead tr th {
    background-image: none;
    background-repeat: no-repeat;
    background-position: right, left;
    padding-right: 21px;
    padding-left: 21px;
    cursor: pointer;
}

th[sortable-style="sortable-asc"] {
    background-image: var(--arrows-up) !important;
}

th[sortable-style="sortable-desc"] {
    background-image: var(--arrows-down) !important;
}