/*
- THIS IS A GENERATED/BUNDLED FILE BY ESBUILD -
Please visit the repository linked to view the source code:
https://github.com/noatpad/obsidian-banners
*/
var Zn=Object.create;var Ie=Object.defineProperty;var ei=Object.getOwnPropertyDescriptor;var ui=Object.getOwnPropertyNames;var ti=Object.getPrototypeOf,ri=Object.prototype.hasOwnProperty;var Pu=t=>Ie(t,"__esModule",{value:!0});var o=(t,u)=>()=>(u||t((u={exports:{}}).exports,u),u.exports),ai=(t,u)=>{Pu(t);for(var r in u)Ie(t,r,{get:u[r],enumerable:!0})},ni=(t,u,r)=>{if(u&&typeof u=="object"||typeof u=="function")for(let a of ui(u))!ri.call(t,a)&&a!=="default"&&Ie(t,a,{get:()=>u[a],enumerable:!(r=ei(u,a))||r.enumerable});return t},S=t=>ni(Pu(Ie(t!=null?Zn(ti(t)):{},"default",t&&t.__esModule&&"default"in t?{get:()=>t.default,enumerable:!0}:{value:t,enumerable:!0})),t);var Pe=o((ge,ru)=>{"use strict";Object.defineProperty(ge,"__esModule",{value:!0});ge.default=ii;function Me(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Me=function(r){return typeof r}:Me=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Me(t)}function ii(t){var u=typeof t=="string"||t instanceof String;if(!u){var r=Me(t);throw t===null?r="null":r==="object"&&(r=t.constructor.name),new TypeError("Expected a string but received a ".concat(r))}}ru.exports=ge.default;ru.exports.default=ge.default});var nu=o((pe,au)=>{"use strict";Object.defineProperty(pe,"__esModule",{value:!0});pe.default=di;function di(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=arguments.length>1?arguments[1]:void 0;for(var r in u)typeof t[r]=="undefined"&&(t[r]=u[r]);return t}au.exports=pe.default;au.exports.default=pe.default});var Lu=o((_e,iu)=>{"use strict";Object.defineProperty(_e,"__esModule",{value:!0});_e.default=li;var oi=qu(Pe()),si=qu(nu());function qu(t){return t&&t.__esModule?t:{default:t}}var Di={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1};function li(t,u){(0,oi.default)(t),u=(0,si.default)(u,Di),u.allow_trailing_dot&&t[t.length-1]==="."&&(t=t.substring(0,t.length-1));var r=t.split("."),a=r[r.length-1];return u.require_tld&&(r.length<2||!/^([a-z\u00a1-\uffff]{2,}|xn[a-z0-9-]{2,})$/i.test(a)||/[\s\u2002-\u200B\u202F\u205F\u3000\uFEFF\uDB40\uDC20\u00A9\uFFFD]/.test(a))||!u.allow_numeric_tld&&/^\d+$/.test(a)?!1:r.every(function(n){return!(n.length>63||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(n)||/[\uff01-\uff5e]/.test(n)||/^-|-$/.test(n)||!u.allow_underscores&&/_/.test(n))})}iu.exports=_e.default;iu.exports.default=_e.default});var ju=o((he,ou)=>{"use strict";Object.defineProperty(he,"__esModule",{value:!0});he.default=du;var ci=fi(Pe());function fi(t){return t&&t.__esModule?t:{default:t}}var Ou="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",Y="(".concat(Ou,"[.]){3}").concat(Ou),mi=new RegExp("^".concat(Y,"$")),I="(?:[0-9a-fA-F]{1,4})",gi=new RegExp("^("+"(?:".concat(I,":){7}(?:").concat(I,"|:)|")+"(?:".concat(I,":){6}(?:").concat(Y,"|:").concat(I,"|:)|")+"(?:".concat(I,":){5}(?::").concat(Y,"|(:").concat(I,"){1,2}|:)|")+"(?:".concat(I,":){4}(?:(:").concat(I,"){0,1}:").concat(Y,"|(:").concat(I,"){1,3}|:)|")+"(?:".concat(I,":){3}(?:(:").concat(I,"){0,2}:").concat(Y,"|(:").concat(I,"){1,4}|:)|")+"(?:".concat(I,":){2}(?:(:").concat(I,"){0,3}:").concat(Y,"|(:").concat(I,"){1,5}|:)|")+"(?:".concat(I,":){1}(?:(:").concat(I,"){0,4}:").concat(Y,"|(:").concat(I,"){1,6}|:)|")+"(?::((?::".concat(I,"){0,5}:").concat(Y,"|(?::").concat(I,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");function du(t){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if((0,ci.default)(t),u=String(u),!u)return du(t,4)||du(t,6);if(u==="4"){if(!mi.test(t))return!1;var r=t.split(".").sort(function(a,n){return a-n});return r[3]<=255}return u==="6"?!!gi.test(t):!1}ou.exports=he.default;ou.exports.default=he.default});var Nu=o((be,su)=>{"use strict";Object.defineProperty(be,"__esModule",{value:!0});be.default=Ei;var pi=qe(Pe()),_i=qe(Lu()),Ru=qe(ju()),hi=qe(nu());function qe(t){return t&&t.__esModule?t:{default:t}}var bi={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,validate_length:!0},Fi=/^\[([^\]]+)\](?::([0-9]+))?$/;function Ci(t){return Object.prototype.toString.call(t)==="[object RegExp]"}function Hu(t,u){for(var r=0;r<u.length;r++){var a=u[r];if(t===a||Ci(a)&&a.test(t))return!0}return!1}function Ei(t,u){if((0,pi.default)(t),!t||/[\s<>]/.test(t)||t.indexOf("mailto:")===0||(u=(0,hi.default)(u,bi),u.validate_length&&t.length>=2083))return!1;var r,a,n,i,s,l,D,c;if(D=t.split("#"),t=D.shift(),D=t.split("?"),t=D.shift(),D=t.split("://"),D.length>1){if(r=D.shift().toLowerCase(),u.require_valid_protocol&&u.protocols.indexOf(r)===-1)return!1}else{if(u.require_protocol)return!1;if(t.substr(0,2)==="//"){if(!u.allow_protocol_relative_urls)return!1;D[0]=t.substr(2)}}if(t=D.join("://"),t==="")return!1;if(D=t.split("/"),t=D.shift(),t===""&&!u.require_host)return!0;if(D=t.split("@"),D.length>1&&(u.disallow_auth||D[0]===""||D[0].substr(0,1)===":"||(a=D.shift(),a.indexOf(":")>=0&&a.split(":").length>2)))return!1;i=D.join("@"),l=null,c=null;var b=i.match(Fi);if(b?(n="",c=b[1],l=b[2]||null):(D=i.split(":"),n=D.shift(),D.length&&(l=D.join(":"))),l!==null){if(s=parseInt(l,10),!/^[0-9]+$/.test(l)||s<=0||s>65535)return!1}else if(u.require_port)return!1;return!(!(0,Ru.default)(n)&&!(0,_i.default)(n,u)&&(!c||!(0,Ru.default)(c,6))||(n=n||c,u.host_whitelist&&!Hu(n,u.host_whitelist))||u.host_blacklist&&Hu(n,u.host_blacklist))}su.exports=be.default;su.exports.default=be.default});var Du=o((QO,Vu)=>{Vu.exports=()=>/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFF\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFE\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFD\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFC\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFB\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|[\u2695\u2696\u2708]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])))|\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\u200D[\u2695\u2696\u2708])?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F?\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F?\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3C-\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83D\uDC41\uFE0F?\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83C\uDFF3\uFE0F?\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F?\u200D\u26A7|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDEF1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764(?:\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\u200D(?:\uD83D\uDD25|\uD83E\uDE79))|\uD83D\uDC41\uFE0F?|\uD83C\uDFF3\uFE0F?|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3C-\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F?\u20E3|\uD83E\uDD3C(?:\uD83C[\uDFFB-\uDFFF])|\u2764\uFE0F?|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF6])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD3C\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF6]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B50]|\uD83C[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDD-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7C\uDE80-\uDE86\uDE90-\uDEAC\uDEB0-\uDEBA\uDEC0-\uDEC2\uDED0-\uDED9\uDEE0-\uDEE7]/g});var Xu=o((wN,Yu)=>{function YI(t,u,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),u!==void 0&&(t=t>=u?t:u)),t}Yu.exports=YI});var Qu=o((yN,Ju)=>{var XI=/\s/;function JI(t){for(var u=t.length;u--&&XI.test(t.charAt(u)););return u}Ju.exports=JI});var et=o((vN,Zu)=>{var QI=Qu(),ZI=/^\s+/;function eM(t){return t&&t.slice(0,QI(t)+1).replace(ZI,"")}Zu.exports=eM});var Ve=o((TN,ut)=>{function uM(t){var u=typeof t;return t!=null&&(u=="object"||u=="function")}ut.exports=uM});var cu=o((BN,tt)=>{var tM=typeof global=="object"&&global&&global.Object===Object&&global;tt.exports=tM});var $=o((xN,rt)=>{var rM=cu(),aM=typeof self=="object"&&self&&self.Object===Object&&self,nM=rM||aM||Function("return this")();rt.exports=nM});var ze=o((AN,at)=>{var iM=$(),dM=iM.Symbol;at.exports=dM});var ot=o((kN,dt)=>{var nt=ze(),it=Object.prototype,oM=it.hasOwnProperty,sM=it.toString,Fe=nt?nt.toStringTag:void 0;function DM(t){var u=oM.call(t,Fe),r=t[Fe];try{t[Fe]=void 0;var a=!0}catch{}var n=sM.call(t);return a&&(u?t[Fe]=r:delete t[Fe]),n}dt.exports=DM});var Dt=o((SN,st)=>{var lM=Object.prototype,cM=lM.toString;function fM(t){return cM.call(t)}st.exports=fM});var ne=o((IN,ft)=>{var lt=ze(),mM=ot(),gM=Dt(),pM="[object Null]",_M="[object Undefined]",ct=lt?lt.toStringTag:void 0;function hM(t){return t==null?t===void 0?_M:pM:ct&&ct in Object(t)?mM(t):gM(t)}ft.exports=hM});var ie=o((MN,mt)=>{function bM(t){return t!=null&&typeof t=="object"}mt.exports=bM});var pt=o((PN,gt)=>{var FM=ne(),CM=ie(),EM="[object Symbol]";function wM(t){return typeof t=="symbol"||CM(t)&&FM(t)==EM}gt.exports=wM});var Ft=o((qN,bt)=>{var yM=et(),_t=Ve(),vM=pt(),ht=0/0,TM=/^[-+]0x[0-9a-f]+$/i,BM=/^0b[01]+$/i,xM=/^0o[0-7]+$/i,AM=parseInt;function kM(t){if(typeof t=="number")return t;if(vM(t))return ht;if(_t(t)){var u=typeof t.valueOf=="function"?t.valueOf():t;t=_t(u)?u+"":u}if(typeof t!="string")return t===0?t:+t;t=yM(t);var r=BM.test(t);return r||xM.test(t)?AM(t.slice(2),r?2:8):TM.test(t)?ht:+t}bt.exports=kM});var Et=o((LN,Ct)=>{var SM=Xu(),fu=Ft();function IM(t,u,r){return r===void 0&&(r=u,u=void 0),r!==void 0&&(r=fu(r),r=r===r?r:0),u!==void 0&&(u=fu(u),u=u===u?u:0),SM(fu(t),u,r)}Ct.exports=IM});var Mt=o((tV,It)=>{function VM(){this.__data__=[],this.size=0}It.exports=VM});var mu=o((rV,Pt)=>{function zM(t,u){return t===u||t!==t&&u!==u}Pt.exports=zM});var Ee=o((aV,qt)=>{var $M=mu();function GM(t,u){for(var r=t.length;r--;)if($M(t[r][0],u))return r;return-1}qt.exports=GM});var Ot=o((nV,Lt)=>{var KM=Ee(),UM=Array.prototype,WM=UM.splice;function YM(t){var u=this.__data__,r=KM(u,t);if(r<0)return!1;var a=u.length-1;return r==a?u.pop():WM.call(u,r,1),--this.size,!0}Lt.exports=YM});var Rt=o((iV,jt)=>{var XM=Ee();function JM(t){var u=this.__data__,r=XM(u,t);return r<0?void 0:u[r][1]}jt.exports=JM});var Nt=o((dV,Ht)=>{var QM=Ee();function ZM(t){return QM(this.__data__,t)>-1}Ht.exports=ZM});var zt=o((oV,Vt)=>{var eP=Ee();function uP(t,u){var r=this.__data__,a=eP(r,t);return a<0?(++this.size,r.push([t,u])):r[a][1]=u,this}Vt.exports=uP});var we=o((sV,$t)=>{var tP=Mt(),rP=Ot(),aP=Rt(),nP=Nt(),iP=zt();function de(t){var u=-1,r=t==null?0:t.length;for(this.clear();++u<r;){var a=t[u];this.set(a[0],a[1])}}de.prototype.clear=tP;de.prototype.delete=rP;de.prototype.get=aP;de.prototype.has=nP;de.prototype.set=iP;$t.exports=de});var Kt=o((DV,Gt)=>{var dP=we();function oP(){this.__data__=new dP,this.size=0}Gt.exports=oP});var Wt=o((lV,Ut)=>{function sP(t){var u=this.__data__,r=u.delete(t);return this.size=u.size,r}Ut.exports=sP});var Xt=o((cV,Yt)=>{function DP(t){return this.__data__.get(t)}Yt.exports=DP});var Qt=o((fV,Jt)=>{function lP(t){return this.__data__.has(t)}Jt.exports=lP});var gu=o((mV,Zt)=>{var cP=ne(),fP=Ve(),mP="[object AsyncFunction]",gP="[object Function]",pP="[object GeneratorFunction]",_P="[object Proxy]";function hP(t){if(!fP(t))return!1;var u=cP(t);return u==gP||u==pP||u==mP||u==_P}Zt.exports=hP});var ur=o((gV,er)=>{var bP=$(),FP=bP["__core-js_shared__"];er.exports=FP});var ar=o((pV,rr)=>{var pu=ur(),tr=function(){var t=/[^.]+$/.exec(pu&&pu.keys&&pu.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function CP(t){return!!tr&&tr in t}rr.exports=CP});var _u=o((_V,nr)=>{var EP=Function.prototype,wP=EP.toString;function yP(t){if(t!=null){try{return wP.call(t)}catch{}try{return t+""}catch{}}return""}nr.exports=yP});var dr=o((hV,ir)=>{var vP=gu(),TP=ar(),BP=Ve(),xP=_u(),AP=/[\\^$.*+?()[\]{}|]/g,kP=/^\[object .+?Constructor\]$/,SP=Function.prototype,IP=Object.prototype,MP=SP.toString,PP=IP.hasOwnProperty,qP=RegExp("^"+MP.call(PP).replace(AP,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function LP(t){if(!BP(t)||TP(t))return!1;var u=vP(t)?qP:kP;return u.test(xP(t))}ir.exports=LP});var sr=o((bV,or)=>{function OP(t,u){return t==null?void 0:t[u]}or.exports=OP});var J=o((FV,Dr)=>{var jP=dr(),RP=sr();function HP(t,u){var r=RP(t,u);return jP(r)?r:void 0}Dr.exports=HP});var Je=o((CV,lr)=>{var NP=J(),VP=$(),zP=NP(VP,"Map");lr.exports=zP});var ye=o((EV,cr)=>{var $P=J(),GP=$P(Object,"create");cr.exports=GP});var gr=o((wV,mr)=>{var fr=ye();function KP(){this.__data__=fr?fr(null):{},this.size=0}mr.exports=KP});var _r=o((yV,pr)=>{function UP(t){var u=this.has(t)&&delete this.__data__[t];return this.size-=u?1:0,u}pr.exports=UP});var br=o((vV,hr)=>{var WP=ye(),YP="__lodash_hash_undefined__",XP=Object.prototype,JP=XP.hasOwnProperty;function QP(t){var u=this.__data__;if(WP){var r=u[t];return r===YP?void 0:r}return JP.call(u,t)?u[t]:void 0}hr.exports=QP});var Cr=o((TV,Fr)=>{var ZP=ye(),e5=Object.prototype,u5=e5.hasOwnProperty;function t5(t){var u=this.__data__;return ZP?u[t]!==void 0:u5.call(u,t)}Fr.exports=t5});var wr=o((BV,Er)=>{var r5=ye(),a5="__lodash_hash_undefined__";function n5(t,u){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=r5&&u===void 0?a5:u,this}Er.exports=n5});var vr=o((xV,yr)=>{var i5=gr(),d5=_r(),o5=br(),s5=Cr(),D5=wr();function oe(t){var u=-1,r=t==null?0:t.length;for(this.clear();++u<r;){var a=t[u];this.set(a[0],a[1])}}oe.prototype.clear=i5;oe.prototype.delete=d5;oe.prototype.get=o5;oe.prototype.has=s5;oe.prototype.set=D5;yr.exports=oe});var xr=o((AV,Br)=>{var Tr=vr(),l5=we(),c5=Je();function f5(){this.size=0,this.__data__={hash:new Tr,map:new(c5||l5),string:new Tr}}Br.exports=f5});var kr=o((kV,Ar)=>{function m5(t){var u=typeof t;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?t!=="__proto__":t===null}Ar.exports=m5});var ve=o((SV,Sr)=>{var g5=kr();function p5(t,u){var r=t.__data__;return g5(u)?r[typeof u=="string"?"string":"hash"]:r.map}Sr.exports=p5});var Mr=o((IV,Ir)=>{var _5=ve();function h5(t){var u=_5(this,t).delete(t);return this.size-=u?1:0,u}Ir.exports=h5});var qr=o((MV,Pr)=>{var b5=ve();function F5(t){return b5(this,t).get(t)}Pr.exports=F5});var Or=o((PV,Lr)=>{var C5=ve();function E5(t){return C5(this,t).has(t)}Lr.exports=E5});var Rr=o((qV,jr)=>{var w5=ve();function y5(t,u){var r=w5(this,t),a=r.size;return r.set(t,u),this.size+=r.size==a?0:1,this}jr.exports=y5});var hu=o((LV,Hr)=>{var v5=xr(),T5=Mr(),B5=qr(),x5=Or(),A5=Rr();function se(t){var u=-1,r=t==null?0:t.length;for(this.clear();++u<r;){var a=t[u];this.set(a[0],a[1])}}se.prototype.clear=v5;se.prototype.delete=T5;se.prototype.get=B5;se.prototype.has=x5;se.prototype.set=A5;Hr.exports=se});var Vr=o((OV,Nr)=>{var k5=we(),S5=Je(),I5=hu(),M5=200;function P5(t,u){var r=this.__data__;if(r instanceof k5){var a=r.__data__;if(!S5||a.length<M5-1)return a.push([t,u]),this.size=++r.size,this;r=this.__data__=new I5(a)}return r.set(t,u),this.size=r.size,this}Nr.exports=P5});var $r=o((jV,zr)=>{var q5=we(),L5=Kt(),O5=Wt(),j5=Xt(),R5=Qt(),H5=Vr();function De(t){var u=this.__data__=new q5(t);this.size=u.size}De.prototype.clear=L5;De.prototype.delete=O5;De.prototype.get=j5;De.prototype.has=R5;De.prototype.set=H5;zr.exports=De});var Kr=o((RV,Gr)=>{var N5="__lodash_hash_undefined__";function V5(t){return this.__data__.set(t,N5),this}Gr.exports=V5});var Wr=o((HV,Ur)=>{function z5(t){return this.__data__.has(t)}Ur.exports=z5});var Xr=o((NV,Yr)=>{var $5=hu(),G5=Kr(),K5=Wr();function Qe(t){var u=-1,r=t==null?0:t.length;for(this.__data__=new $5;++u<r;)this.add(t[u])}Qe.prototype.add=Qe.prototype.push=G5;Qe.prototype.has=K5;Yr.exports=Qe});var Qr=o((VV,Jr)=>{function U5(t,u){for(var r=-1,a=t==null?0:t.length;++r<a;)if(u(t[r],r,t))return!0;return!1}Jr.exports=U5});var ea=o((zV,Zr)=>{function W5(t,u){return t.has(u)}Zr.exports=W5});var bu=o(($V,ua)=>{var Y5=Xr(),X5=Qr(),J5=ea(),Q5=1,Z5=2;function eq(t,u,r,a,n,i){var s=r&Q5,l=t.length,D=u.length;if(l!=D&&!(s&&D>l))return!1;var c=i.get(t),b=i.get(u);if(c&&b)return c==u&&b==t;var h=-1,m=!0,g=r&Z5?new Y5:void 0;for(i.set(t,u),i.set(u,t);++h<l;){var y=t[h],F=u[h];if(a)var v=s?a(F,y,h,u,t,i):a(y,F,h,t,u,i);if(v!==void 0){if(v)continue;m=!1;break}if(g){if(!X5(u,function(M,C){if(!J5(g,C)&&(y===M||n(y,M,r,a,i)))return g.push(C)})){m=!1;break}}else if(!(y===F||n(y,F,r,a,i))){m=!1;break}}return i.delete(t),i.delete(u),m}ua.exports=eq});var ra=o((GV,ta)=>{var uq=$(),tq=uq.Uint8Array;ta.exports=tq});var na=o((KV,aa)=>{function rq(t){var u=-1,r=Array(t.size);return t.forEach(function(a,n){r[++u]=[n,a]}),r}aa.exports=rq});var da=o((UV,ia)=>{function aq(t){var u=-1,r=Array(t.size);return t.forEach(function(a){r[++u]=a}),r}ia.exports=aq});var ca=o((WV,la)=>{var oa=ze(),sa=ra(),nq=mu(),iq=bu(),dq=na(),oq=da(),sq=1,Dq=2,lq="[object Boolean]",cq="[object Date]",fq="[object Error]",mq="[object Map]",gq="[object Number]",pq="[object RegExp]",_q="[object Set]",hq="[object String]",bq="[object Symbol]",Fq="[object ArrayBuffer]",Cq="[object DataView]",Da=oa?oa.prototype:void 0,Fu=Da?Da.valueOf:void 0;function Eq(t,u,r,a,n,i,s){switch(r){case Cq:if(t.byteLength!=u.byteLength||t.byteOffset!=u.byteOffset)return!1;t=t.buffer,u=u.buffer;case Fq:return!(t.byteLength!=u.byteLength||!i(new sa(t),new sa(u)));case lq:case cq:case gq:return nq(+t,+u);case fq:return t.name==u.name&&t.message==u.message;case pq:case hq:return t==u+"";case mq:var l=dq;case _q:var D=a&sq;if(l||(l=oq),t.size!=u.size&&!D)return!1;var c=s.get(t);if(c)return c==u;a|=Dq,s.set(t,u);var b=iq(l(t),l(u),a,n,i,s);return s.delete(t),b;case bq:if(Fu)return Fu.call(t)==Fu.call(u)}return!1}la.exports=Eq});var ma=o((YV,fa)=>{function wq(t,u){for(var r=-1,a=u.length,n=t.length;++r<a;)t[n+r]=u[r];return t}fa.exports=wq});var Ze=o((XV,ga)=>{var yq=Array.isArray;ga.exports=yq});var _a=o((JV,pa)=>{var vq=ma(),Tq=Ze();function Bq(t,u,r){var a=u(t);return Tq(t)?a:vq(a,r(t))}pa.exports=Bq});var ba=o((QV,ha)=>{function xq(t,u){for(var r=-1,a=t==null?0:t.length,n=0,i=[];++r<a;){var s=t[r];u(s,r,t)&&(i[n++]=s)}return i}ha.exports=xq});var Ca=o((ZV,Fa)=>{function Aq(){return[]}Fa.exports=Aq});var ya=o((ez,wa)=>{var kq=ba(),Sq=Ca(),Iq=Object.prototype,Mq=Iq.propertyIsEnumerable,Ea=Object.getOwnPropertySymbols,Pq=Ea?function(t){return t==null?[]:(t=Object(t),kq(Ea(t),function(u){return Mq.call(t,u)}))}:Sq;wa.exports=Pq});var Ta=o((uz,va)=>{function qq(t,u){for(var r=-1,a=Array(t);++r<t;)a[r]=u(r);return a}va.exports=qq});var xa=o((tz,Ba)=>{var Lq=ne(),Oq=ie(),jq="[object Arguments]";function Rq(t){return Oq(t)&&Lq(t)==jq}Ba.exports=Rq});var Ia=o((rz,Sa)=>{var Aa=xa(),Hq=ie(),ka=Object.prototype,Nq=ka.hasOwnProperty,Vq=ka.propertyIsEnumerable,zq=Aa(function(){return arguments}())?Aa:function(t){return Hq(t)&&Nq.call(t,"callee")&&!Vq.call(t,"callee")};Sa.exports=zq});var Pa=o((az,Ma)=>{function $q(){return!1}Ma.exports=$q});var Cu=o((Te,le)=>{var Gq=$(),Kq=Pa(),qa=typeof Te=="object"&&Te&&!Te.nodeType&&Te,La=qa&&typeof le=="object"&&le&&!le.nodeType&&le,Uq=La&&La.exports===qa,Oa=Uq?Gq.Buffer:void 0,Wq=Oa?Oa.isBuffer:void 0,Yq=Wq||Kq;le.exports=Yq});var Ra=o((nz,ja)=>{var Xq=9007199254740991,Jq=/^(?:0|[1-9]\d*)$/;function Qq(t,u){var r=typeof t;return u=u??Xq,!!u&&(r=="number"||r!="symbol"&&Jq.test(t))&&t>-1&&t%1==0&&t<u}ja.exports=Qq});var Eu=o((iz,Ha)=>{var Zq=9007199254740991;function eL(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=Zq}Ha.exports=eL});var Va=o((dz,Na)=>{var uL=ne(),tL=Eu(),rL=ie(),aL="[object Arguments]",nL="[object Array]",iL="[object Boolean]",dL="[object Date]",oL="[object Error]",sL="[object Function]",DL="[object Map]",lL="[object Number]",cL="[object Object]",fL="[object RegExp]",mL="[object Set]",gL="[object String]",pL="[object WeakMap]",_L="[object ArrayBuffer]",hL="[object DataView]",bL="[object Float32Array]",FL="[object Float64Array]",CL="[object Int8Array]",EL="[object Int16Array]",wL="[object Int32Array]",yL="[object Uint8Array]",vL="[object Uint8ClampedArray]",TL="[object Uint16Array]",BL="[object Uint32Array]",T={};T[bL]=T[FL]=T[CL]=T[EL]=T[wL]=T[yL]=T[vL]=T[TL]=T[BL]=!0;T[aL]=T[nL]=T[_L]=T[iL]=T[hL]=T[dL]=T[oL]=T[sL]=T[DL]=T[lL]=T[cL]=T[fL]=T[mL]=T[gL]=T[pL]=!1;function xL(t){return rL(t)&&tL(t.length)&&!!T[uL(t)]}Na.exports=xL});var $a=o((oz,za)=>{function AL(t){return function(u){return t(u)}}za.exports=AL});var Ka=o((xe,ce)=>{var kL=cu(),Ga=typeof xe=="object"&&xe&&!xe.nodeType&&xe,Be=Ga&&typeof ce=="object"&&ce&&!ce.nodeType&&ce,SL=Be&&Be.exports===Ga,wu=SL&&kL.process,IL=function(){try{var t=Be&&Be.require&&Be.require("util").types;return t||wu&&wu.binding&&wu.binding("util")}catch{}}();ce.exports=IL});var yu=o((sz,Ya)=>{var ML=Va(),PL=$a(),Ua=Ka(),Wa=Ua&&Ua.isTypedArray,qL=Wa?PL(Wa):ML;Ya.exports=qL});var Ja=o((Dz,Xa)=>{var LL=Ta(),OL=Ia(),jL=Ze(),RL=Cu(),HL=Ra(),NL=yu(),VL=Object.prototype,zL=VL.hasOwnProperty;function $L(t,u){var r=jL(t),a=!r&&OL(t),n=!r&&!a&&RL(t),i=!r&&!a&&!n&&NL(t),s=r||a||n||i,l=s?LL(t.length,String):[],D=l.length;for(var c in t)(u||zL.call(t,c))&&!(s&&(c=="length"||n&&(c=="offset"||c=="parent")||i&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||HL(c,D)))&&l.push(c);return l}Xa.exports=$L});var Za=o((lz,Qa)=>{var GL=Object.prototype;function KL(t){var u=t&&t.constructor,r=typeof u=="function"&&u.prototype||GL;return t===r}Qa.exports=KL});var un=o((cz,en)=>{function UL(t,u){return function(r){return t(u(r))}}en.exports=UL});var rn=o((fz,tn)=>{var WL=un(),YL=WL(Object.keys,Object);tn.exports=YL});var nn=o((mz,an)=>{var XL=Za(),JL=rn(),QL=Object.prototype,ZL=QL.hasOwnProperty;function eO(t){if(!XL(t))return JL(t);var u=[];for(var r in Object(t))ZL.call(t,r)&&r!="constructor"&&u.push(r);return u}an.exports=eO});var on=o((gz,dn)=>{var uO=gu(),tO=Eu();function rO(t){return t!=null&&tO(t.length)&&!uO(t)}dn.exports=rO});var Dn=o((pz,sn)=>{var aO=Ja(),nO=nn(),iO=on();function dO(t){return iO(t)?aO(t):nO(t)}sn.exports=dO});var cn=o((_z,ln)=>{var oO=_a(),sO=ya(),DO=Dn();function lO(t){return oO(t,DO,sO)}ln.exports=lO});var gn=o((hz,mn)=>{var fn=cn(),cO=1,fO=Object.prototype,mO=fO.hasOwnProperty;function gO(t,u,r,a,n,i){var s=r&cO,l=fn(t),D=l.length,c=fn(u),b=c.length;if(D!=b&&!s)return!1;for(var h=D;h--;){var m=l[h];if(!(s?m in u:mO.call(u,m)))return!1}var g=i.get(t),y=i.get(u);if(g&&y)return g==u&&y==t;var F=!0;i.set(t,u),i.set(u,t);for(var v=s;++h<D;){m=l[h];var M=t[m],C=u[m];if(a)var j=s?a(C,M,m,u,t,i):a(M,C,m,t,u,i);if(!(j===void 0?M===C||n(M,C,r,a,i):j)){F=!1;break}v||(v=m=="constructor")}if(F&&!v){var x=t.constructor,d=u.constructor;x!=d&&"constructor"in t&&"constructor"in u&&!(typeof x=="function"&&x instanceof x&&typeof d=="function"&&d instanceof d)&&(F=!1)}return i.delete(t),i.delete(u),F}mn.exports=gO});var _n=o((bz,pn)=>{var pO=J(),_O=$(),hO=pO(_O,"DataView");pn.exports=hO});var bn=o((Fz,hn)=>{var bO=J(),FO=$(),CO=bO(FO,"Promise");hn.exports=CO});var Cn=o((Cz,Fn)=>{var EO=J(),wO=$(),yO=EO(wO,"Set");Fn.exports=yO});var wn=o((Ez,En)=>{var vO=J(),TO=$(),BO=vO(TO,"WeakMap");En.exports=BO});var Sn=o((wz,kn)=>{var vu=_n(),Tu=Je(),Bu=bn(),xu=Cn(),Au=wn(),yn=ne(),fe=_u(),vn="[object Map]",xO="[object Object]",Tn="[object Promise]",Bn="[object Set]",xn="[object WeakMap]",An="[object DataView]",AO=fe(vu),kO=fe(Tu),SO=fe(Bu),IO=fe(xu),MO=fe(Au),Q=yn;(vu&&Q(new vu(new ArrayBuffer(1)))!=An||Tu&&Q(new Tu)!=vn||Bu&&Q(Bu.resolve())!=Tn||xu&&Q(new xu)!=Bn||Au&&Q(new Au)!=xn)&&(Q=function(t){var u=yn(t),r=u==xO?t.constructor:void 0,a=r?fe(r):"";if(a)switch(a){case AO:return An;case kO:return vn;case SO:return Tn;case IO:return Bn;case MO:return xn}return u});kn.exports=Q});var Rn=o((yz,jn)=>{var ku=$r(),PO=bu(),qO=ca(),LO=gn(),In=Sn(),Mn=Ze(),Pn=Cu(),OO=yu(),jO=1,qn="[object Arguments]",Ln="[object Array]",eu="[object Object]",RO=Object.prototype,On=RO.hasOwnProperty;function HO(t,u,r,a,n,i){var s=Mn(t),l=Mn(u),D=s?Ln:In(t),c=l?Ln:In(u);D=D==qn?eu:D,c=c==qn?eu:c;var b=D==eu,h=c==eu,m=D==c;if(m&&Pn(t)){if(!Pn(u))return!1;s=!0,b=!1}if(m&&!b)return i||(i=new ku),s||OO(t)?PO(t,u,r,a,n,i):qO(t,u,D,r,a,n,i);if(!(r&jO)){var g=b&&On.call(t,"__wrapped__"),y=h&&On.call(u,"__wrapped__");if(g||y){var F=g?t.value():t,v=y?u.value():u;return i||(i=new ku),n(F,v,r,a,i)}}return m?(i||(i=new ku),LO(t,u,r,a,n,i)):!1}jn.exports=HO});var zn=o((vz,Vn)=>{var NO=Rn(),Hn=ie();function Nn(t,u,r,a,n){return t===u?!0:t==null||u==null||!Hn(t)&&!Hn(u)?t!==t&&u!==u:NO(t,u,r,a,Nn,n)}Vn.exports=Nn});var Gn=o((Tz,$n)=>{var VO=zn();function zO(t,u){return VO(t,u)}$n.exports=zO});ai(exports,{default:()=>Mu});var U=S(require("obsidian")),Qn=S(Nu());var $u=S(require("obsidian")),Gu=S(Du());var wi=function(){"use strict";var t={base:"https://twemoji.maxcdn.com/v/13.1.0/",ext:".png",size:"72x72",className:"emoji",convert:{fromCodePoint:C,toCodePoint:p},onerror:function(){this.parentNode&&this.parentNode.replaceChild(D(this.alt,!1),this)},parse:j,replace:x,test:d},u={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},r=/(?:\ud83d\udc68\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffc-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb\udffd-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb\udffc\udffe\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb-\udffd\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb-\udffe]|\ud83d\udc68\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffc-\udfff]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb\udffd-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb\udffc\udffe\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb-\udffd\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d\udc8f\ud83c[\udffb-\udfff]|\ud83d\udc91\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d\udc8f\udc91])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd4\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83d\ude36\u200d\ud83c\udf2b\ufe0f|\u2764\ufe0f\u200d\ud83d\udd25|\u2764\ufe0f\u200d\ud83e\ude79|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83d\ude2e\u200d\ud83d\udca8|\ud83d\ude35\u200d\ud83d\udcab|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udc8e\udc90\udc92-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g,a=/\uFE0F/g,n=String.fromCharCode(8205),i=/[&<>'"]/g,s=/^(?:iframe|noframes|noscript|script|select|style|textarea)$/,l=String.fromCharCode;return t;function D(_,f){return document.createTextNode(f?_.replace(a,""):_)}function c(_){return _.replace(i,F)}function b(_,f){return"".concat(f.base,f.size,"/",_,f.ext)}function h(_,f){for(var R=_.childNodes,A=R.length,k,O;A--;)k=R[A],O=k.nodeType,O===3?f.push(k):O===1&&!("ownerSVGElement"in k)&&!s.test(k.nodeName.toLowerCase())&&h(k,f);return f}function m(_){return p(_.indexOf(n)<0?_.replace(a,""):_)}function g(_,f){for(var R=h(_,[]),A=R.length,k,O,W,N,Ae,me,uu,ue,ke,V,te,Se,tu;A--;){for(W=!1,N=document.createDocumentFragment(),Ae=R[A],me=Ae.nodeValue,ue=0;uu=r.exec(me);){if(ke=uu.index,ke!==ue&&N.appendChild(D(me.slice(ue,ke),!0)),te=uu[0],Se=m(te),ue=ke+te.length,tu=f.callback(Se,f),Se&&tu){V=new Image,V.onerror=f.onerror,V.setAttribute("draggable","false"),k=f.attributes(te,Se);for(O in k)k.hasOwnProperty(O)&&O.indexOf("on")!==0&&!V.hasAttribute(O)&&V.setAttribute(O,k[O]);V.className=f.className,V.alt=te,V.src=tu,W=!0,N.appendChild(V)}V||N.appendChild(D(te,!1)),V=null}W&&(ue<me.length&&N.appendChild(D(me.slice(ue),!0)),Ae.parentNode.replaceChild(N,Ae))}return _}function y(_,f){return x(_,function(R){var A=R,k=m(R),O=f.callback(k,f),W,N;if(k&&O){A="<img ".concat('class="',f.className,'" ','draggable="false" ','alt="',R,'"',' src="',O,'"'),W=f.attributes(R,k);for(N in W)W.hasOwnProperty(N)&&N.indexOf("on")!==0&&A.indexOf(" "+N+"=")===-1&&(A=A.concat(" ",N,'="',c(W[N]),'"'));A=A.concat("/>")}return A})}function F(_){return u[_]}function v(){return null}function M(_){return typeof _=="number"?_+"x"+_:_}function C(_){var f=typeof _=="string"?parseInt(_,16):_;return f<65536?l(f):(f-=65536,l(55296+(f>>10),56320+(f&1023)))}function j(_,f){return(!f||typeof f=="function")&&(f={callback:f}),(typeof _=="string"?y:g)(_,{callback:f.callback||b,attributes:typeof f.attributes=="function"?f.attributes:v,base:typeof f.base=="string"?f.base:t.base,ext:f.ext||t.ext,size:f.folder||M(f.size||t.size),className:f.className||t.className,onerror:f.onerror||t.onerror})}function x(_,f){return String(_).replace(r,f)}function d(_){r.lastIndex=0;var f=r.test(_);return r.lastIndex=0,f}function p(_,f){for(var R=[],A=0,k=0,O=0;O<_.length;)A=_.charCodeAt(O++),k?(R.push((65536+(k-55296<<10)+(A-56320)).toString(16)),k=0):55296<=A&&A<=56319?k=A:R.push(A.toString(16));return R.join(f||"-")}}(),Le=wi;var yi="\u{1F4AF}",vi="\u{1F522}",Ti="\u2614",Bi="\u2615",xi="\u2648",Ai="\u2649",ki="\u2650",Si="\u2651",Ii="\u2652",Mi="\u2653",Pi="\u2693",qi="\u2705",Li="\u2728",Oi="\u2753",ji="\u2754",Ri="\u2755",Hi="\u2757",Ni="\u2757",Vi="\u2795",zi="\u2796",$i="\u2797",Gi="#\uFE0F\u20E3",Ki="*\uFE0F\u20E3",Ui="0\uFE0F\u20E3",Wi="1\uFE0F\u20E3",Yi="2\uFE0F\u20E3",Xi="3\uFE0F\u20E3",Ji="4\uFE0F\u20E3",Qi="5\uFE0F\u20E3",Zi="6\uFE0F\u20E3",ed="7\uFE0F\u20E3",ud="8\uFE0F\u20E3",td="9\uFE0F\u20E3",rd="\xA9\uFE0F",ad="\xAE\uFE0F",nd="\u{1F004}",id="\u{1F0CF}",dd="\u{1F170}\uFE0F",od="\u{1F171}\uFE0F",sd="\u{1F17E}\uFE0F",Dd="\u{1F17F}\uFE0F",ld="\u{1F18E}",cd="\u{1F191}",fd="\u{1F192}",md="\u{1F193}",gd="\u{1F194}",pd="\u{1F195}",_d="\u{1F196}",hd="\u{1F197}",bd="\u{1F198}",Fd="\u{1F199}",Cd="\u{1F19A}",Ed="\u{1F1E6}\u{1F1E8}",wd="\u{1F1E6}\u{1F1E9}",yd="\u{1F1E6}\u{1F1EA}",vd="\u{1F1E6}\u{1F1EB}",Td="\u{1F1E6}\u{1F1EC}",Bd="\u{1F1E6}\u{1F1EE}",xd="\u{1F1E6}\u{1F1F1}",Ad="\u{1F1E6}\u{1F1F2}",kd="\u{1F1E6}\u{1F1F4}",Sd="\u{1F1E6}\u{1F1F6}",Id="\u{1F1E6}\u{1F1F7}",Md="\u{1F1E6}\u{1F1F8}",Pd="\u{1F1E6}\u{1F1F9}",qd="\u{1F1E6}\u{1F1FA}",Ld="\u{1F1E6}\u{1F1FC}",Od="\u{1F1E6}\u{1F1FD}",jd="\u{1F1E6}\u{1F1FF}",Rd="\u{1F1E7}\u{1F1E6}",Hd="\u{1F1E7}\u{1F1E7}",Nd="\u{1F1E7}\u{1F1E9}",Vd="\u{1F1E7}\u{1F1EA}",zd="\u{1F1E7}\u{1F1EB}",$d="\u{1F1E7}\u{1F1EC}",Gd="\u{1F1E7}\u{1F1ED}",Kd="\u{1F1E7}\u{1F1EE}",Ud="\u{1F1E7}\u{1F1EF}",Wd="\u{1F1E7}\u{1F1F1}",Yd="\u{1F1E7}\u{1F1F2}",Xd="\u{1F1E7}\u{1F1F3}",Jd="\u{1F1E7}\u{1F1F4}",Qd="\u{1F1E7}\u{1F1F6}",Zd="\u{1F1E7}\u{1F1F7}",eo="\u{1F1E7}\u{1F1F8}",uo="\u{1F1E7}\u{1F1F9}",to="\u{1F1E7}\u{1F1FB}",ro="\u{1F1E7}\u{1F1FC}",ao="\u{1F1E7}\u{1F1FE}",no="\u{1F1E7}\u{1F1FF}",io="\u{1F1E8}\u{1F1E6}",oo="\u{1F1E8}\u{1F1E8}",so="\u{1F1E8}\u{1F1E9}",Do="\u{1F1E8}\u{1F1EB}",lo="\u{1F1E8}\u{1F1EC}",co="\u{1F1E8}\u{1F1ED}",fo="\u{1F1E8}\u{1F1EE}",mo="\u{1F1E8}\u{1F1F0}",go="\u{1F1E8}\u{1F1F1}",po="\u{1F1E8}\u{1F1F2}",_o="\u{1F1E8}\u{1F1F3}",ho="\u{1F1E8}\u{1F1F3}",bo="\u{1F1E8}\u{1F1F4}",Fo="\u{1F1E8}\u{1F1F5}",Co="\u{1F1E8}\u{1F1F7}",Eo="\u{1F1E8}\u{1F1FA}",wo="\u{1F1E8}\u{1F1FB}",yo="\u{1F1E8}\u{1F1FC}",vo="\u{1F1E8}\u{1F1FD}",To="\u{1F1E8}\u{1F1FE}",Bo="\u{1F1E8}\u{1F1FF}",xo="\u{1F1E9}\u{1F1EA}",Ao="\u{1F1E9}\u{1F1EA}",ko="\u{1F1E9}\u{1F1EC}",So="\u{1F1E9}\u{1F1EF}",Io="\u{1F1E9}\u{1F1F0}",Mo="\u{1F1E9}\u{1F1F2}",Po="\u{1F1E9}\u{1F1F4}",qo="\u{1F1E9}\u{1F1FF}",Lo="\u{1F1EA}\u{1F1E6}",Oo="\u{1F1EA}\u{1F1E8}",jo="\u{1F1EA}\u{1F1EA}",Ro="\u{1F1EA}\u{1F1EC}",Ho="\u{1F1EA}\u{1F1ED}",No="\u{1F1EA}\u{1F1F7}",Vo="\u{1F1EA}\u{1F1F8}",zo="\u{1F1EA}\u{1F1F8}",$o="\u{1F1EA}\u{1F1F9}",Go="\u{1F1EA}\u{1F1FA}",Ko="\u{1F1EB}\u{1F1EE}",Uo="\u{1F1EB}\u{1F1EF}",Wo="\u{1F1EB}\u{1F1F0}",Yo="\u{1F1EB}\u{1F1F2}",Xo="\u{1F1EB}\u{1F1F4}",Jo="\u{1F1EB}\u{1F1F7}",Qo="\u{1F1EB}\u{1F1F7}",Zo="\u{1F1EC}\u{1F1E6}",es="\u{1F1EC}\u{1F1E7}",us="\u{1F1EC}\u{1F1E7}",ts="\u{1F1EC}\u{1F1E7}",rs="\u{1F1EC}\u{1F1E9}",as="\u{1F1EC}\u{1F1EA}",ns="\u{1F1EC}\u{1F1EB}",is="\u{1F1EC}\u{1F1EC}",ds="\u{1F1EC}\u{1F1ED}",os="\u{1F1EC}\u{1F1EE}",ss="\u{1F1EC}\u{1F1F1}",Ds="\u{1F1EC}\u{1F1F2}",ls="\u{1F1EC}\u{1F1F3}",cs="\u{1F1EC}\u{1F1F5}",fs="\u{1F1EC}\u{1F1F6}",ms="\u{1F1EC}\u{1F1F7}",gs="\u{1F1EC}\u{1F1F8}",ps="\u{1F1EC}\u{1F1F9}",_s="\u{1F1EC}\u{1F1FA}",hs="\u{1F1EC}\u{1F1FC}",bs="\u{1F1EC}\u{1F1FE}",Fs="\u{1F1ED}\u{1F1F0}",Cs="\u{1F1ED}\u{1F1F2}",Es="\u{1F1ED}\u{1F1F3}",ws="\u{1F1ED}\u{1F1F7}",ys="\u{1F1ED}\u{1F1F9}",vs="\u{1F1ED}\u{1F1FA}",Ts="\u{1F1EE}\u{1F1E8}",Bs="\u{1F1EE}\u{1F1E9}",xs="\u{1F1EE}\u{1F1EA}",As="\u{1F1EE}\u{1F1F1}",ks="\u{1F1EE}\u{1F1F2}",Ss="\u{1F1EE}\u{1F1F3}",Is="\u{1F1EE}\u{1F1F4}",Ms="\u{1F1EE}\u{1F1F6}",Ps="\u{1F1EE}\u{1F1F7}",qs="\u{1F1EE}\u{1F1F8}",Ls="\u{1F1EE}\u{1F1F9}",Os="\u{1F1EE}\u{1F1F9}",js="\u{1F1EF}\u{1F1EA}",Rs="\u{1F1EF}\u{1F1F2}",Hs="\u{1F1EF}\u{1F1F4}",Ns="\u{1F1EF}\u{1F1F5}",Vs="\u{1F1EF}\u{1F1F5}",zs="\u{1F1F0}\u{1F1EA}",$s="\u{1F1F0}\u{1F1EC}",Gs="\u{1F1F0}\u{1F1ED}",Ks="\u{1F1F0}\u{1F1EE}",Us="\u{1F1F0}\u{1F1F2}",Ws="\u{1F1F0}\u{1F1F3}",Ys="\u{1F1F0}\u{1F1F5}",Xs="\u{1F1F0}\u{1F1F7}",Js="\u{1F1F0}\u{1F1F7}",Qs="\u{1F1F0}\u{1F1FC}",Zs="\u{1F1F0}\u{1F1FE}",eD="\u{1F1F0}\u{1F1FF}",uD="\u{1F1F1}\u{1F1E6}",tD="\u{1F1F1}\u{1F1E7}",rD="\u{1F1F1}\u{1F1E8}",aD="\u{1F1F1}\u{1F1EE}",nD="\u{1F1F1}\u{1F1F0}",iD="\u{1F1F1}\u{1F1F7}",dD="\u{1F1F1}\u{1F1F8}",oD="\u{1F1F1}\u{1F1F9}",sD="\u{1F1F1}\u{1F1FA}",DD="\u{1F1F1}\u{1F1FB}",lD="\u{1F1F1}\u{1F1FE}",cD="\u{1F1F2}\u{1F1E6}",fD="\u{1F1F2}\u{1F1E8}",mD="\u{1F1F2}\u{1F1E9}",gD="\u{1F1F2}\u{1F1EA}",pD="\u{1F1F2}\u{1F1EB}",_D="\u{1F1F2}\u{1F1EC}",hD="\u{1F1F2}\u{1F1ED}",bD="\u{1F1F2}\u{1F1F0}",FD="\u{1F1F2}\u{1F1F1}",CD="\u{1F1F2}\u{1F1F2}",ED="\u{1F1F2}\u{1F1F3}",wD="\u{1F1F2}\u{1F1F4}",yD="\u{1F1F2}\u{1F1F5}",vD="\u{1F1F2}\u{1F1F6}",TD="\u{1F1F2}\u{1F1F7}",BD="\u{1F1F2}\u{1F1F8}",xD="\u{1F1F2}\u{1F1F9}",AD="\u{1F1F2}\u{1F1FA}",kD="\u{1F1F2}\u{1F1FB}",SD="\u{1F1F2}\u{1F1FC}",ID="\u{1F1F2}\u{1F1FD}",MD="\u{1F1F2}\u{1F1FE}",PD="\u{1F1F2}\u{1F1FF}",qD="\u{1F1F3}\u{1F1E6}",LD="\u{1F1F3}\u{1F1E8}",OD="\u{1F1F3}\u{1F1EA}",jD="\u{1F1F3}\u{1F1EB}",RD="\u{1F1F3}\u{1F1EC}",HD="\u{1F1F3}\u{1F1EE}",ND="\u{1F1F3}\u{1F1F1}",VD="\u{1F1F3}\u{1F1F4}",zD="\u{1F1F3}\u{1F1F5}",$D="\u{1F1F3}\u{1F1F7}",GD="\u{1F1F3}\u{1F1FA}",KD="\u{1F1F3}\u{1F1FF}",UD="\u{1F1F4}\u{1F1F2}",WD="\u{1F1F5}\u{1F1E6}",YD="\u{1F1F5}\u{1F1EA}",XD="\u{1F1F5}\u{1F1EB}",JD="\u{1F1F5}\u{1F1EC}",QD="\u{1F1F5}\u{1F1ED}",ZD="\u{1F1F5}\u{1F1F0}",el="\u{1F1F5}\u{1F1F1}",ul="\u{1F1F5}\u{1F1F2}",tl="\u{1F1F5}\u{1F1F3}",rl="\u{1F1F5}\u{1F1F7}",al="\u{1F1F5}\u{1F1F8}",nl="\u{1F1F5}\u{1F1F9}",il="\u{1F1F5}\u{1F1FC}",dl="\u{1F1F5}\u{1F1FE}",ol="\u{1F1F6}\u{1F1E6}",sl="\u{1F1F7}\u{1F1EA}",Dl="\u{1F1F7}\u{1F1F4}",ll="\u{1F1F7}\u{1F1F8}",cl="\u{1F1F7}\u{1F1FA}",fl="\u{1F1F7}\u{1F1FA}",ml="\u{1F1F7}\u{1F1FC}",gl="\u{1F1F8}\u{1F1E6}",pl="\u{1F1F8}\u{1F1E7}",_l="\u{1F1F8}\u{1F1E8}",hl="\u{1F1F8}\u{1F1E9}",bl="\u{1F1F8}\u{1F1EA}",Fl="\u{1F1F8}\u{1F1EC}",Cl="\u{1F1F8}\u{1F1ED}",El="\u{1F1F8}\u{1F1EE}",wl="\u{1F1F8}\u{1F1EF}",yl="\u{1F1F8}\u{1F1F0}",vl="\u{1F1F8}\u{1F1F1}",Tl="\u{1F1F8}\u{1F1F2}",Bl="\u{1F1F8}\u{1F1F3}",xl="\u{1F1F8}\u{1F1F4}",Al="\u{1F1F8}\u{1F1F7}",kl="\u{1F1F8}\u{1F1F8}",Sl="\u{1F1F8}\u{1F1F9}",Il="\u{1F1F8}\u{1F1FB}",Ml="\u{1F1F8}\u{1F1FD}",Pl="\u{1F1F8}\u{1F1FE}",ql="\u{1F1F8}\u{1F1FF}",Ll="\u{1F1F9}\u{1F1E6}",Ol="\u{1F1F9}\u{1F1E8}",jl="\u{1F1F9}\u{1F1E9}",Rl="\u{1F1F9}\u{1F1EB}",Hl="\u{1F1F9}\u{1F1EC}",Nl="\u{1F1F9}\u{1F1ED}",Vl="\u{1F1F9}\u{1F1EF}",zl="\u{1F1F9}\u{1F1F0}",$l="\u{1F1F9}\u{1F1F1}",Gl="\u{1F1F9}\u{1F1F2}",Kl="\u{1F1F9}\u{1F1F3}",Ul="\u{1F1F9}\u{1F1F4}",Wl="\u{1F1F9}\u{1F1F7}",Yl="\u{1F1F9}\u{1F1F9}",Xl="\u{1F1F9}\u{1F1FB}",Jl="\u{1F1F9}\u{1F1FC}",Ql="\u{1F1F9}\u{1F1FF}",Zl="\u{1F1FA}\u{1F1E6}",ec="\u{1F1FA}\u{1F1EC}",uc="\u{1F1FA}\u{1F1F2}",tc="\u{1F1FA}\u{1F1F3}",rc="\u{1F1FA}\u{1F1F8}",ac="\u{1F1FA}\u{1F1F8}",nc="\u{1F1FA}\u{1F1FE}",ic="\u{1F1FA}\u{1F1FF}",dc="\u{1F1FB}\u{1F1E6}",oc="\u{1F1FB}\u{1F1E8}",sc="\u{1F1FB}\u{1F1EA}",Dc="\u{1F1FB}\u{1F1EC}",lc="\u{1F1FB}\u{1F1EE}",cc="\u{1F1FB}\u{1F1F3}",fc="\u{1F1FB}\u{1F1FA}",mc="\u{1F1FC}\u{1F1EB}",gc="\u{1F1FC}\u{1F1F8}",pc="\u{1F1FD}\u{1F1F0}",_c="\u{1F1FE}\u{1F1EA}",hc="\u{1F1FE}\u{1F1F9}",bc="\u{1F1FF}\u{1F1E6}",Fc="\u{1F1FF}\u{1F1F2}",Cc="\u{1F1FF}\u{1F1FC}",Ec="\u{1F201}",wc="\u{1F202}\uFE0F",yc="\u{1F21A}",vc="\u{1F22F}",Tc="\u{1F232}",Bc="\u{1F233}",xc="\u{1F234}",Ac="\u{1F235}",kc="\u{1F236}",Sc="\u{1F237}\uFE0F",Ic="\u{1F238}",Mc="\u{1F239}",Pc="\u{1F23A}",qc="\u{1F250}",Lc="\u{1F251}",Oc="\u{1F300}",jc="\u{1F301}",Rc="\u{1F302}",Hc="\u{1F303}",Nc="\u{1F304}",Vc="\u{1F305}",zc="\u{1F306}",$c="\u{1F307}",Gc="\u{1F308}",Kc="\u{1F309}",Uc="\u{1F30A}",Wc="\u{1F30B}",Yc="\u{1F30C}",Xc="\u{1F30D}",Jc="\u{1F30E}",Qc="\u{1F30F}",Zc="\u{1F310}",ef="\u{1F311}",uf="\u{1F312}",tf="\u{1F313}",rf="\u{1F314}",af="\u{1F314}",nf="\u{1F315}",df="\u{1F316}",of="\u{1F317}",sf="\u{1F318}",Df="\u{1F319}",lf="\u{1F31A}",cf="\u{1F31B}",ff="\u{1F31C}",mf="\u{1F31D}",gf="\u{1F31E}",pf="\u{1F31F}",_f="\u{1F320}",hf="\u{1F321}\uFE0F",bf="\u{1F324}\uFE0F",Ff="\u{1F324}\uFE0F",Cf="\u{1F325}\uFE0F",Ef="\u{1F325}\uFE0F",wf="\u{1F326}\uFE0F",yf="\u{1F326}\uFE0F",vf="\u{1F327}\uFE0F",Tf="\u{1F328}\uFE0F",Bf="\u{1F329}\uFE0F",xf="\u{1F329}\uFE0F",Af="\u{1F32A}\uFE0F",kf="\u{1F32A}\uFE0F",Sf="\u{1F32B}\uFE0F",If="\u{1F32C}\uFE0F",Mf="\u{1F32D}",Pf="\u{1F32E}",qf="\u{1F32F}",Lf="\u{1F330}",Of="\u{1F331}",jf="\u{1F332}",Rf="\u{1F333}",Hf="\u{1F334}",Nf="\u{1F335}",Vf="\u{1F336}\uFE0F",zf="\u{1F337}",$f="\u{1F338}",Gf="\u{1F339}",Kf="\u{1F33A}",Uf="\u{1F33B}",Wf="\u{1F33C}",Yf="\u{1F33D}",Xf="\u{1F33E}",Jf="\u{1F33F}",Qf="\u{1F340}",Zf="\u{1F341}",em="\u{1F342}",um="\u{1F343}",tm="\u{1F344}",rm="\u{1F345}",am="\u{1F346}",nm="\u{1F347}",im="\u{1F348}",dm="\u{1F349}",om="\u{1F34A}",sm="\u{1F34B}",Dm="\u{1F34C}",lm="\u{1F34D}",cm="\u{1F34E}",fm="\u{1F34F}",mm="\u{1F350}",gm="\u{1F351}",pm="\u{1F352}",_m="\u{1F353}",hm="\u{1F354}",bm="\u{1F355}",Fm="\u{1F356}",Cm="\u{1F357}",Em="\u{1F358}",wm="\u{1F359}",ym="\u{1F35A}",vm="\u{1F35B}",Tm="\u{1F35C}",Bm="\u{1F35D}",xm="\u{1F35E}",Am="\u{1F35F}",km="\u{1F360}",Sm="\u{1F361}",Im="\u{1F362}",Mm="\u{1F363}",Pm="\u{1F364}",qm="\u{1F365}",Lm="\u{1F366}",Om="\u{1F367}",jm="\u{1F368}",Rm="\u{1F369}",Hm="\u{1F36A}",Nm="\u{1F36B}",Vm="\u{1F36C}",zm="\u{1F36D}",$m="\u{1F36E}",Gm="\u{1F36F}",Km="\u{1F370}",Um="\u{1F371}",Wm="\u{1F372}",Ym="\u{1F373}",Xm="\u{1F373}",Jm="\u{1F374}",Qm="\u{1F375}",Zm="\u{1F376}",eg="\u{1F377}",ug="\u{1F378}",tg="\u{1F379}",rg="\u{1F37A}",ag="\u{1F37B}",ng="\u{1F37C}",ig="\u{1F37D}\uFE0F",dg="\u{1F37E}",og="\u{1F37F}",sg="\u{1F380}",Dg="\u{1F381}",lg="\u{1F382}",cg="\u{1F383}",fg="\u{1F384}",mg="\u{1F385}",gg="\u{1F386}",pg="\u{1F387}",_g="\u{1F388}",hg="\u{1F389}",bg="\u{1F38A}",Fg="\u{1F38B}",Cg="\u{1F38C}",Eg="\u{1F38D}",wg="\u{1F38E}",yg="\u{1F38F}",vg="\u{1F390}",Tg="\u{1F391}",Bg="\u{1F392}",xg="\u{1F393}",Ag="\u{1F396}\uFE0F",kg="\u{1F397}\uFE0F",Sg="\u{1F399}\uFE0F",Ig="\u{1F39A}\uFE0F",Mg="\u{1F39B}\uFE0F",Pg="\u{1F39E}\uFE0F",qg="\u{1F39F}\uFE0F",Lg="\u{1F3A0}",Og="\u{1F3A1}",jg="\u{1F3A2}",Rg="\u{1F3A3}",Hg="\u{1F3A4}",Ng="\u{1F3A5}",Vg="\u{1F3A6}",zg="\u{1F3A7}",$g="\u{1F3A8}",Gg="\u{1F3A9}",Kg="\u{1F3AA}",Ug="\u{1F3AB}",Wg="\u{1F3AC}",Yg="\u{1F3AD}",Xg="\u{1F3AE}",Jg="\u{1F3AF}",Qg="\u{1F3B0}",Zg="\u{1F3B1}",ep="\u{1F3B2}",up="\u{1F3B3}",tp="\u{1F3B4}",rp="\u{1F3B5}",ap="\u{1F3B6}",np="\u{1F3B7}",ip="\u{1F3B8}",dp="\u{1F3B9}",op="\u{1F3BA}",sp="\u{1F3BB}",Dp="\u{1F3BC}",lp="\u{1F3BD}",cp="\u{1F3BE}",fp="\u{1F3BF}",mp="\u{1F3C0}",gp="\u{1F3C1}",pp="\u{1F3C2}",_p="\u{1F3C3}\u200D\u2640\uFE0F",hp="\u{1F3C3}\u200D\u2642\uFE0F",bp="\u{1F3C3}\u200D\u2642\uFE0F",Fp="\u{1F3C3}\u200D\u2642\uFE0F",Cp="\u{1F3C4}\u200D\u2640\uFE0F",Ep="\u{1F3C4}\u200D\u2642\uFE0F",wp="\u{1F3C4}\u200D\u2642\uFE0F",yp="\u{1F3C5}",vp="\u{1F3C6}",Tp="\u{1F3C7}",Bp="\u{1F3C8}",xp="\u{1F3C9}",Ap="\u{1F3CA}\u200D\u2640\uFE0F",kp="\u{1F3CA}\u200D\u2642\uFE0F",Sp="\u{1F3CA}\u200D\u2642\uFE0F",Ip="\u{1F3CB}\uFE0F\u200D\u2640\uFE0F",Mp="\u{1F3CB}\uFE0F\u200D\u2642\uFE0F",Pp="\u{1F3CB}\uFE0F\u200D\u2642\uFE0F",qp="\u{1F3CC}\uFE0F\u200D\u2640\uFE0F",Lp="\u{1F3CC}\uFE0F\u200D\u2642\uFE0F",Op="\u{1F3CC}\uFE0F\u200D\u2642\uFE0F",jp="\u{1F3CD}\uFE0F",Rp="\u{1F3CE}\uFE0F",Hp="\u{1F3CF}",Np="\u{1F3D0}",Vp="\u{1F3D1}",zp="\u{1F3D2}",$p="\u{1F3D3}",Gp="\u{1F3D4}\uFE0F",Kp="\u{1F3D5}\uFE0F",Up="\u{1F3D6}\uFE0F",Wp="\u{1F3D7}\uFE0F",Yp="\u{1F3D8}\uFE0F",Xp="\u{1F3D9}\uFE0F",Jp="\u{1F3DA}\uFE0F",Qp="\u{1F3DB}\uFE0F",Zp="\u{1F3DC}\uFE0F",e_="\u{1F3DD}\uFE0F",u_="\u{1F3DE}\uFE0F",t_="\u{1F3DF}\uFE0F",r_="\u{1F3E0}",a_="\u{1F3E1}",n_="\u{1F3E2}",i_="\u{1F3E3}",d_="\u{1F3E4}",o_="\u{1F3E5}",s_="\u{1F3E6}",D_="\u{1F3E7}",l_="\u{1F3E8}",c_="\u{1F3E9}",f_="\u{1F3EA}",m_="\u{1F3EB}",g_="\u{1F3EC}",p_="\u{1F3ED}",__="\u{1F3EE}",h_="\u{1F3EE}",b_="\u{1F3EF}",F_="\u{1F3F0}",C_="\u{1F3F3}\uFE0F\u200D\u{1F308}",E_="\u{1F3F3}\uFE0F\u200D\u26A7\uFE0F",w_="\u{1F3F3}\uFE0F",y_="\u{1F3F4}\u200D\u2620\uFE0F",v_="\u{1F3F4}\u{E0067}\u{E0062}\u{E0065}\u{E006E}\u{E0067}\u{E007F}",T_="\u{1F3F4}\u{E0067}\u{E0062}\u{E0073}\u{E0063}\u{E0074}\u{E007F}",B_="\u{1F3F4}\u{E0067}\u{E0062}\u{E0077}\u{E006C}\u{E0073}\u{E007F}",x_="\u{1F3F4}",A_="\u{1F3F5}\uFE0F",k_="\u{1F3F7}\uFE0F",S_="\u{1F3F8}",I_="\u{1F3F9}",M_="\u{1F3FA}",P_="\u{1F3FB}",q_="\u{1F3FC}",L_="\u{1F3FD}",O_="\u{1F3FE}",j_="\u{1F3FF}",R_="\u{1F400}",H_="\u{1F401}",N_="\u{1F402}",V_="\u{1F403}",z_="\u{1F404}",$_="\u{1F405}",G_="\u{1F406}",K_="\u{1F407}",U_="\u{1F408}\u200D\u2B1B",W_="\u{1F408}",Y_="\u{1F409}",X_="\u{1F40A}",J_="\u{1F40B}",Q_="\u{1F40C}",Z_="\u{1F40D}",eh="\u{1F40E}",uh="\u{1F40F}",th="\u{1F410}",rh="\u{1F411}",ah="\u{1F412}",nh="\u{1F413}",ih="\u{1F414}",dh="\u{1F415}\u200D\u{1F9BA}",oh="\u{1F415}",sh="\u{1F416}",Dh="\u{1F417}",lh="\u{1F418}",ch="\u{1F419}",fh="\u{1F41A}",mh="\u{1F41B}",gh="\u{1F41C}",ph="\u{1F41D}",_h="\u{1F41D}",hh="\u{1F41E}",bh="\u{1F41E}",Fh="\u{1F41F}",Ch="\u{1F420}",Eh="\u{1F421}",wh="\u{1F422}",yh="\u{1F423}",vh="\u{1F424}",Th="\u{1F425}",Bh="\u{1F426}",xh="\u{1F427}",Ah="\u{1F428}",kh="\u{1F429}",Sh="\u{1F42A}",Ih="\u{1F42B}",Mh="\u{1F42C}",Ph="\u{1F42C}",qh="\u{1F42D}",Lh="\u{1F42E}",Oh="\u{1F42F}",jh="\u{1F430}",Rh="\u{1F431}",Hh="\u{1F432}",Nh="\u{1F433}",Vh="\u{1F434}",zh="\u{1F435}",$h="\u{1F436}",Gh="\u{1F437}",Kh="\u{1F438}",Uh="\u{1F439}",Wh="\u{1F43A}",Yh="\u{1F43B}\u200D\u2744\uFE0F",Xh="\u{1F43B}",Jh="\u{1F43C}",Qh="\u{1F43D}",Zh="\u{1F43E}",e0="\u{1F43E}",u0="\u{1F43F}\uFE0F",t0="\u{1F440}",r0="\u{1F441}\uFE0F\u200D\u{1F5E8}\uFE0F",a0="\u{1F441}\uFE0F",n0="\u{1F442}",i0="\u{1F443}",d0="\u{1F444}",o0="\u{1F445}",s0="\u{1F446}",D0="\u{1F447}",l0="\u{1F448}",c0="\u{1F449}",f0="\u{1F44A}",m0="\u{1F44A}",g0="\u{1F44B}",p0="\u{1F44C}",_0="\u{1F44D}",h0="\u{1F44D}",b0="\u{1F44E}",F0="\u{1F44E}",C0="\u{1F44F}",E0="\u{1F450}",w0="\u{1F451}",y0="\u{1F452}",v0="\u{1F453}",T0="\u{1F454}",B0="\u{1F455}",x0="\u{1F455}",A0="\u{1F456}",k0="\u{1F457}",S0="\u{1F458}",I0="\u{1F459}",M0="\u{1F45A}",P0="\u{1F45B}",q0="\u{1F45C}",L0="\u{1F45D}",O0="\u{1F45E}",j0="\u{1F45E}",R0="\u{1F45F}",H0="\u{1F460}",N0="\u{1F461}",V0="\u{1F462}",z0="\u{1F463}",$0="\u{1F464}",G0="\u{1F465}",K0="\u{1F466}",U0="\u{1F467}",W0="\u{1F468}\u200D\u{1F33E}",Y0="\u{1F468}\u200D\u{1F373}",X0="\u{1F468}\u200D\u{1F37C}",J0="\u{1F468}\u200D\u{1F393}",Q0="\u{1F468}\u200D\u{1F3A4}",Z0="\u{1F468}\u200D\u{1F3A8}",e8="\u{1F468}\u200D\u{1F3EB}",u8="\u{1F468}\u200D\u{1F3ED}",t8="\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",r8="\u{1F468}\u200D\u{1F466}",a8="\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",n8="\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",i8="\u{1F468}\u200D\u{1F467}",d8="\u{1F468}\u200D\u{1F468}\u200D\u{1F466}",o8="\u{1F468}\u200D\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",s8="\u{1F468}\u200D\u{1F468}\u200D\u{1F467}",D8="\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",l8="\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",c8="\u{1F468}\u200D\u{1F469}\u200D\u{1F466}",f8="\u{1F468}\u200D\u{1F469}\u200D\u{1F466}",m8="\u{1F468}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",g8="\u{1F468}\u200D\u{1F469}\u200D\u{1F467}",p8="\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",_8="\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",h8="\u{1F468}\u200D\u{1F4BB}",b8="\u{1F468}\u200D\u{1F4BC}",F8="\u{1F468}\u200D\u{1F527}",C8="\u{1F468}\u200D\u{1F52C}",E8="\u{1F468}\u200D\u{1F680}",w8="\u{1F468}\u200D\u{1F692}",y8="\u{1F468}\u200D\u{1F9AF}",v8="\u{1F468}\u200D\u{1F9B0}",T8="\u{1F468}\u200D\u{1F9B1}",B8="\u{1F468}\u200D\u{1F9B2}",x8="\u{1F468}\u200D\u{1F9B3}",A8="\u{1F468}\u200D\u{1F9BC}",k8="\u{1F468}\u200D\u{1F9BD}",S8="\u{1F468}\u200D\u2695\uFE0F",I8="\u{1F468}\u200D\u2696\uFE0F",M8="\u{1F468}\u200D\u2708\uFE0F",P8="\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F468}",q8="\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F468}",L8="\u{1F468}",O8="\u{1F469}\u200D\u{1F33E}",j8="\u{1F469}\u200D\u{1F373}",R8="\u{1F469}\u200D\u{1F37C}",H8="\u{1F469}\u200D\u{1F393}",N8="\u{1F469}\u200D\u{1F3A4}",V8="\u{1F469}\u200D\u{1F3A8}",z8="\u{1F469}\u200D\u{1F3EB}",$8="\u{1F469}\u200D\u{1F3ED}",G8="\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",K8="\u{1F469}\u200D\u{1F466}",U8="\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",W8="\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",Y8="\u{1F469}\u200D\u{1F467}",X8="\u{1F469}\u200D\u{1F469}\u200D\u{1F466}",J8="\u{1F469}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",Q8="\u{1F469}\u200D\u{1F469}\u200D\u{1F467}",Z8="\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",e3="\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",u3="\u{1F469}\u200D\u{1F4BB}",t3="\u{1F469}\u200D\u{1F4BC}",r3="\u{1F469}\u200D\u{1F527}",a3="\u{1F469}\u200D\u{1F52C}",n3="\u{1F469}\u200D\u{1F680}",i3="\u{1F469}\u200D\u{1F692}",d3="\u{1F469}\u200D\u{1F9AF}",o3="\u{1F469}\u200D\u{1F9B0}",s3="\u{1F469}\u200D\u{1F9B1}",D3="\u{1F469}\u200D\u{1F9B2}",l3="\u{1F469}\u200D\u{1F9B3}",c3="\u{1F469}\u200D\u{1F9BC}",f3="\u{1F469}\u200D\u{1F9BD}",m3="\u{1F469}\u200D\u2695\uFE0F",g3="\u{1F469}\u200D\u2696\uFE0F",p3="\u{1F469}\u200D\u2708\uFE0F",_3="\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F468}",h3="\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F469}",b3="\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F468}",F3="\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F469}",C3="\u{1F469}",E3="\u{1F46B}",w3="\u{1F46B}",y3="\u{1F46B}",v3="\u{1F46C}",T3="\u{1F46C}",B3="\u{1F46D}",x3="\u{1F46D}",A3="\u{1F46E}\u200D\u2640\uFE0F",k3="\u{1F46E}\u200D\u2642\uFE0F",S3="\u{1F46E}\u200D\u2642\uFE0F",I3="\u{1F46F}\u200D\u2640\uFE0F",M3="\u{1F46F}\u200D\u2640\uFE0F",P3="\u{1F46F}\u200D\u2640\uFE0F",q3="\u{1F46F}\u200D\u2642\uFE0F",L3="\u{1F46F}\u200D\u2642\uFE0F",O3="\u{1F470}\u200D\u2640\uFE0F",j3="\u{1F470}\u200D\u2642\uFE0F",R3="\u{1F470}",H3="\u{1F471}\u200D\u2640\uFE0F",N3="\u{1F471}\u200D\u2642\uFE0F",V3="\u{1F471}\u200D\u2642\uFE0F",z3="\u{1F472}",$3="\u{1F473}\u200D\u2640\uFE0F",G3="\u{1F473}\u200D\u2642\uFE0F",K3="\u{1F473}\u200D\u2642\uFE0F",U3="\u{1F474}",W3="\u{1F475}",Y3="\u{1F476}",X3="\u{1F477}\u200D\u2640\uFE0F",J3="\u{1F477}\u200D\u2642\uFE0F",Q3="\u{1F477}\u200D\u2642\uFE0F",Z3="\u{1F478}",e2="\u{1F479}",u2="\u{1F47A}",t2="\u{1F47B}",r2="\u{1F47C}",a2="\u{1F47D}",n2="\u{1F47E}",i2="\u{1F47F}",d2="\u{1F480}",o2="\u{1F481}\u200D\u2640\uFE0F",s2="\u{1F481}\u200D\u2640\uFE0F",D2="\u{1F481}\u200D\u2642\uFE0F",l2="\u{1F482}\u200D\u2640\uFE0F",c2="\u{1F482}\u200D\u2642\uFE0F",f2="\u{1F482}\u200D\u2642\uFE0F",m2="\u{1F483}",g2="\u{1F484}",p2="\u{1F485}",_2="\u{1F486}\u200D\u2640\uFE0F",h2="\u{1F486}\u200D\u2640\uFE0F",b2="\u{1F486}\u200D\u2642\uFE0F",F2="\u{1F487}\u200D\u2640\uFE0F",C2="\u{1F487}\u200D\u2640\uFE0F",E2="\u{1F487}\u200D\u2642\uFE0F",w2="\u{1F488}",y2="\u{1F489}",v2="\u{1F48A}",T2="\u{1F48B}",B2="\u{1F48C}",x2="\u{1F48D}",A2="\u{1F48E}",k2="\u{1F48F}",S2="\u{1F490}",I2="\u{1F491}",M2="\u{1F492}",P2="\u{1F493}",q2="\u{1F494}",L2="\u{1F495}",O2="\u{1F496}",j2="\u{1F497}",R2="\u{1F498}",H2="\u{1F499}",N2="\u{1F49A}",V2="\u{1F49B}",z2="\u{1F49C}",$2="\u{1F49D}",G2="\u{1F49E}",K2="\u{1F49F}",U2="\u{1F4A0}",W2="\u{1F4A1}",Y2="\u{1F4A2}",X2="\u{1F4A3}",J2="\u{1F4A4}",Q2="\u{1F4A5}",Z2="\u{1F4A5}",eb="\u{1F4A6}",ub="\u{1F4A7}",tb="\u{1F4A8}",rb="\u{1F4A9}",ab="\u{1F4A9}",nb="\u{1F4A9}",ib="\u{1F4AA}",db="\u{1F4AB}",ob="\u{1F4AC}",sb="\u{1F4AD}",Db="\u{1F4AE}",lb="\u{1F4B0}",cb="\u{1F4B1}",fb="\u{1F4B2}",mb="\u{1F4B3}",gb="\u{1F4B4}",pb="\u{1F4B5}",_b="\u{1F4B6}",hb="\u{1F4B7}",bb="\u{1F4B8}",Fb="\u{1F4B9}",Cb="\u{1F4BA}",Eb="\u{1F4BB}",wb="\u{1F4BC}",yb="\u{1F4BD}",vb="\u{1F4BE}",Tb="\u{1F4BF}",Bb="\u{1F4C0}",xb="\u{1F4C1}",Ab="\u{1F4C2}",kb="\u{1F4C3}",Sb="\u{1F4C4}",Ib="\u{1F4C5}",Mb="\u{1F4C6}",Pb="\u{1F4C7}",qb="\u{1F4C8}",Lb="\u{1F4C9}",Ob="\u{1F4CA}",jb="\u{1F4CB}",Rb="\u{1F4CC}",Hb="\u{1F4CD}",Nb="\u{1F4CE}",Vb="\u{1F4CF}",zb="\u{1F4D0}",$b="\u{1F4D1}",Gb="\u{1F4D2}",Kb="\u{1F4D3}",Ub="\u{1F4D4}",Wb="\u{1F4D5}",Yb="\u{1F4D6}",Xb="\u{1F4D6}",Jb="\u{1F4D7}",Qb="\u{1F4D8}",Zb="\u{1F4D9}",eF="\u{1F4DA}",uF="\u{1F4DB}",tF="\u{1F4DC}",rF="\u{1F4DD}",aF="\u{1F4DD}",nF="\u{1F4DE}",iF="\u{1F4DF}",dF="\u{1F4E0}",oF="\u{1F4E1}",sF="\u{1F4E2}",DF="\u{1F4E3}",lF="\u{1F4E4}",cF="\u{1F4E5}",fF="\u{1F4E6}",mF="\u{1F4E7}",gF="\u{1F4E8}",pF="\u{1F4E9}",_F="\u{1F4EA}",hF="\u{1F4EB}",bF="\u{1F4EC}",FF="\u{1F4ED}",CF="\u{1F4EE}",EF="\u{1F4EF}",wF="\u{1F4F0}",yF="\u{1F4F1}",vF="\u{1F4F2}",TF="\u{1F4F3}",BF="\u{1F4F4}",xF="\u{1F4F5}",AF="\u{1F4F6}",kF="\u{1F4F7}",SF="\u{1F4F8}",IF="\u{1F4F9}",MF="\u{1F4FA}",PF="\u{1F4FB}",qF="\u{1F4FC}",LF="\u{1F4FD}\uFE0F",OF="\u{1F4FF}",jF="\u{1F500}",RF="\u{1F501}",HF="\u{1F502}",NF="\u{1F503}",VF="\u{1F504}",zF="\u{1F505}",$F="\u{1F506}",GF="\u{1F507}",KF="\u{1F508}",UF="\u{1F509}",WF="\u{1F50A}",YF="\u{1F50B}",XF="\u{1F50C}",JF="\u{1F50D}",QF="\u{1F50E}",ZF="\u{1F50F}",eC="\u{1F510}",uC="\u{1F511}",tC="\u{1F512}",rC="\u{1F513}",aC="\u{1F514}",nC="\u{1F515}",iC="\u{1F516}",dC="\u{1F517}",oC="\u{1F518}",sC="\u{1F519}",DC="\u{1F51A}",lC="\u{1F51B}",cC="\u{1F51C}",fC="\u{1F51D}",mC="\u{1F51E}",gC="\u{1F51F}",pC="\u{1F520}",_C="\u{1F521}",hC="\u{1F523}",bC="\u{1F524}",FC="\u{1F525}",CC="\u{1F526}",EC="\u{1F527}",wC="\u{1F528}",yC="\u{1F529}",vC="\u{1F52A}",TC="\u{1F52A}",BC="\u{1F52B}",xC="\u{1F52C}",AC="\u{1F52D}",kC="\u{1F52E}",SC="\u{1F52F}",IC="\u{1F530}",MC="\u{1F531}",PC="\u{1F532}",qC="\u{1F533}",LC="\u{1F534}",OC="\u{1F535}",jC="\u{1F536}",RC="\u{1F537}",HC="\u{1F538}",NC="\u{1F539}",VC="\u{1F53A}",zC="\u{1F53B}",$C="\u{1F53C}",GC="\u{1F53D}",KC="\u{1F549}\uFE0F",UC="\u{1F54A}\uFE0F",WC="\u{1F54B}",YC="\u{1F54C}",XC="\u{1F54D}",JC="\u{1F54E}",QC="\u{1F550}",ZC="\u{1F551}",e6="\u{1F552}",u6="\u{1F553}",t6="\u{1F554}",r6="\u{1F555}",a6="\u{1F556}",n6="\u{1F557}",i6="\u{1F558}",d6="\u{1F559}",o6="\u{1F55A}",s6="\u{1F55B}",D6="\u{1F55C}",l6="\u{1F55D}",c6="\u{1F55E}",f6="\u{1F55F}",m6="\u{1F560}",g6="\u{1F561}",p6="\u{1F562}",_6="\u{1F563}",h6="\u{1F564}",b6="\u{1F565}",F6="\u{1F566}",C6="\u{1F567}",E6="\u{1F56F}\uFE0F",w6="\u{1F570}\uFE0F",y6="\u{1F573}\uFE0F",v6="\u{1F574}\uFE0F",T6="\u{1F575}\uFE0F\u200D\u2640\uFE0F",B6="\u{1F575}\uFE0F\u200D\u2642\uFE0F",x6="\u{1F575}\uFE0F\u200D\u2642\uFE0F",A6="\u{1F576}\uFE0F",k6="\u{1F577}\uFE0F",S6="\u{1F578}\uFE0F",I6="\u{1F579}\uFE0F",M6="\u{1F57A}",P6="\u{1F587}\uFE0F",q6="\u{1F58A}\uFE0F",L6="\u{1F58B}\uFE0F",O6="\u{1F58C}\uFE0F",j6="\u{1F58D}\uFE0F",R6="\u{1F590}\uFE0F",H6="\u{1F595}",N6="\u{1F595}",V6="\u{1F596}",z6="\u{1F5A4}",$6="\u{1F5A5}\uFE0F",G6="\u{1F5A8}\uFE0F",K6="\u{1F5B1}\uFE0F",U6="\u{1F5B2}\uFE0F",W6="\u{1F5BC}\uFE0F",Y6="\u{1F5C2}\uFE0F",X6="\u{1F5C3}\uFE0F",J6="\u{1F5C4}\uFE0F",Q6="\u{1F5D1}\uFE0F",Z6="\u{1F5D2}\uFE0F",eE="\u{1F5D3}\uFE0F",uE="\u{1F5DC}\uFE0F",tE="\u{1F5DD}\uFE0F",rE="\u{1F5DE}\uFE0F",aE="\u{1F5E1}\uFE0F",nE="\u{1F5E3}\uFE0F",iE="\u{1F5E8}\uFE0F",dE="\u{1F5EF}\uFE0F",oE="\u{1F5F3}\uFE0F",sE="\u{1F5FA}\uFE0F",DE="\u{1F5FB}",lE="\u{1F5FC}",cE="\u{1F5FD}",fE="\u{1F5FE}",mE="\u{1F5FF}",gE="\u{1F600}",pE="\u{1F601}",_E="\u{1F602}",hE="\u{1F603}",bE="\u{1F604}",FE="\u{1F605}",CE="\u{1F606}",EE="\u{1F606}",wE="\u{1F607}",yE="\u{1F608}",vE="\u{1F609}",TE="\u{1F60A}",BE="\u{1F60B}",xE="\u{1F60C}",AE="\u{1F60D}",kE="\u{1F60E}",SE="\u{1F60F}",IE="\u{1F610}",ME="\u{1F611}",PE="\u{1F612}",qE="\u{1F613}",LE="\u{1F614}",OE="\u{1F615}",jE="\u{1F616}",RE="\u{1F617}",HE="\u{1F618}",NE="\u{1F619}",VE="\u{1F61A}",zE="\u{1F61B}",$E="\u{1F61C}",GE="\u{1F61D}",KE="\u{1F61E}",UE="\u{1F61F}",WE="\u{1F620}",YE="\u{1F621}",XE="\u{1F622}",JE="\u{1F623}",QE="\u{1F624}",ZE="\u{1F625}",ew="\u{1F626}",uw="\u{1F627}",tw="\u{1F628}",rw="\u{1F629}",aw="\u{1F62A}",nw="\u{1F62B}",iw="\u{1F62C}",dw="\u{1F62D}",ow="\u{1F62E}\u200D\u{1F4A8}",sw="\u{1F62E}",Dw="\u{1F62F}",lw="\u{1F630}",cw="\u{1F631}",fw="\u{1F632}",mw="\u{1F633}",gw="\u{1F634}",pw="\u{1F635}\u200D\u{1F4AB}",_w="\u{1F635}",hw="\u{1F636}\u200D\u{1F32B}\uFE0F",bw="\u{1F636}",Fw="\u{1F637}",Cw="\u{1F638}",Ew="\u{1F639}",ww="\u{1F63A}",yw="\u{1F63B}",vw="\u{1F63C}",Tw="\u{1F63D}",Bw="\u{1F63E}",xw="\u{1F63F}",Aw="\u{1F640}",kw="\u{1F641}",Sw="\u{1F642}",Iw="\u{1F643}",Mw="\u{1F644}",Pw="\u{1F645}\u200D\u2640\uFE0F",qw="\u{1F645}\u200D\u2640\uFE0F",Lw="\u{1F645}\u200D\u2642\uFE0F",Ow="\u{1F646}\u200D\u2640\uFE0F",jw="\u{1F646}\u200D\u2640\uFE0F",Rw="\u{1F646}\u200D\u2642\uFE0F",Hw="\u{1F647}\u200D\u2640\uFE0F",Nw="\u{1F647}\u200D\u2642\uFE0F",Vw="\u{1F647}\u200D\u2642\uFE0F",zw="\u{1F648}",$w="\u{1F649}",Gw="\u{1F64A}",Kw="\u{1F64B}\u200D\u2640\uFE0F",Uw="\u{1F64B}\u200D\u2640\uFE0F",Ww="\u{1F64B}\u200D\u2642\uFE0F",Yw="\u{1F64C}",Xw="\u{1F64D}\u200D\u2640\uFE0F",Jw="\u{1F64D}\u200D\u2640\uFE0F",Qw="\u{1F64D}\u200D\u2642\uFE0F",Zw="\u{1F64E}\u200D\u2640\uFE0F",ey="\u{1F64E}\u200D\u2640\uFE0F",uy="\u{1F64E}\u200D\u2642\uFE0F",ty="\u{1F64F}",ry="\u{1F680}",ay="\u{1F681}",ny="\u{1F682}",iy="\u{1F683}",dy="\u{1F684}",oy="\u{1F685}",sy="\u{1F686}",Dy="\u{1F687}",ly="\u{1F688}",cy="\u{1F689}",fy="\u{1F68A}",my="\u{1F68B}",gy="\u{1F68C}",py="\u{1F68D}",_y="\u{1F68E}",hy="\u{1F68F}",by="\u{1F690}",Fy="\u{1F691}",Cy="\u{1F692}",Ey="\u{1F693}",wy="\u{1F694}",yy="\u{1F695}",vy="\u{1F696}",Ty="\u{1F697}",By="\u{1F697}",xy="\u{1F698}",Ay="\u{1F699}",ky="\u{1F69A}",Sy="\u{1F69B}",Iy="\u{1F69C}",My="\u{1F69D}",Py="\u{1F69E}",qy="\u{1F69F}",Ly="\u{1F6A0}",Oy="\u{1F6A1}",jy="\u{1F6A2}",Ry="\u{1F6A3}\u200D\u2640\uFE0F",Hy="\u{1F6A3}\u200D\u2642\uFE0F",Ny="\u{1F6A3}\u200D\u2642\uFE0F",Vy="\u{1F6A4}",zy="\u{1F6A5}",$y="\u{1F6A6}",Gy="\u{1F6A7}",Ky="\u{1F6A8}",Uy="\u{1F6A9}",Wy="\u{1F6AA}",Yy="\u{1F6AB}",Xy="\u{1F6AC}",Jy="\u{1F6AD}",Qy="\u{1F6AE}",Zy="\u{1F6AF}",ev="\u{1F6B0}",uv="\u{1F6B1}",tv="\u{1F6B2}",rv="\u{1F6B3}",av="\u{1F6B4}\u200D\u2640\uFE0F",nv="\u{1F6B4}\u200D\u2642\uFE0F",iv="\u{1F6B4}\u200D\u2642\uFE0F",dv="\u{1F6B5}\u200D\u2640\uFE0F",ov="\u{1F6B5}\u200D\u2642\uFE0F",sv="\u{1F6B5}\u200D\u2642\uFE0F",Dv="\u{1F6B6}\u200D\u2640\uFE0F",lv="\u{1F6B6}\u200D\u2642\uFE0F",cv="\u{1F6B6}\u200D\u2642\uFE0F",fv="\u{1F6B7}",mv="\u{1F6B8}",gv="\u{1F6B9}",pv="\u{1F6BA}",_v="\u{1F6BB}",hv="\u{1F6BC}",bv="\u{1F6BD}",Fv="\u{1F6BE}",Cv="\u{1F6BF}",Ev="\u{1F6C0}",wv="\u{1F6C1}",yv="\u{1F6C2}",vv="\u{1F6C3}",Tv="\u{1F6C4}",Bv="\u{1F6C5}",xv="\u{1F6CB}\uFE0F",Av="\u{1F6CC}",kv="\u{1F6CD}\uFE0F",Sv="\u{1F6CE}\uFE0F",Iv="\u{1F6CF}\uFE0F",Mv="\u{1F6D0}",Pv="\u{1F6D1}",qv="\u{1F6D2}",Lv="\u{1F6D5}",Ov="\u{1F6D6}",jv="\u{1F6D7}",Rv="\u{1F6E0}\uFE0F",Hv="\u{1F6E1}\uFE0F",Nv="\u{1F6E2}\uFE0F",Vv="\u{1F6E3}\uFE0F",zv="\u{1F6E4}\uFE0F",$v="\u{1F6E5}\uFE0F",Gv="\u{1F6E9}\uFE0F",Kv="\u{1F6EB}",Uv="\u{1F6EC}",Wv="\u{1F6F0}\uFE0F",Yv="\u{1F6F3}\uFE0F",Xv="\u{1F6F4}",Jv="\u{1F6F5}",Qv="\u{1F6F6}",Zv="\u{1F6F7}",e7="\u{1F6F8}",u7="\u{1F6F9}",t7="\u{1F6FA}",r7="\u{1F6FB}",a7="\u{1F6FC}",n7="\u{1F7E0}",i7="\u{1F7E1}",d7="\u{1F7E2}",o7="\u{1F7E3}",s7="\u{1F7E4}",D7="\u{1F7E5}",l7="\u{1F7E6}",c7="\u{1F7E7}",f7="\u{1F7E8}",m7="\u{1F7E9}",g7="\u{1F7EA}",p7="\u{1F7EB}",_7="\u{1F90C}",h7="\u{1F90D}",b7="\u{1F90E}",F7="\u{1F90F}",C7="\u{1F910}",E7="\u{1F911}",w7="\u{1F912}",y7="\u{1F913}",v7="\u{1F914}",T7="\u{1F915}",B7="\u{1F916}",x7="\u{1F917}",A7="\u{1F918}",k7="\u{1F918}",S7="\u{1F919}",I7="\u{1F91A}",M7="\u{1F91B}",P7="\u{1F91C}",q7="\u{1F91D}",L7="\u{1F91E}",O7="\u{1F91E}",j7="\u{1F91F}",R7="\u{1F920}",H7="\u{1F921}",N7="\u{1F922}",V7="\u{1F923}",z7="\u{1F924}",$7="\u{1F925}",G7="\u{1F926}\u200D\u2640\uFE0F",K7="\u{1F926}\u200D\u2642\uFE0F",U7="\u{1F926}",W7="\u{1F927}",Y7="\u{1F928}",X7="\u{1F928}",J7="\u{1F929}",Q7="\u{1F929}",Z7="\u{1F92A}",eT="\u{1F92A}",uT="\u{1F92B}",tT="\u{1F92B}",rT="\u{1F92C}",aT="\u{1F92C}",nT="\u{1F92D}",iT="\u{1F92D}",dT="\u{1F92E}",oT="\u{1F92E}",sT="\u{1F92F}",DT="\u{1F92F}",lT="\u{1F930}",cT="\u{1F931}",fT="\u{1F932}",mT="\u{1F933}",gT="\u{1F934}",pT="\u{1F935}\u200D\u2640\uFE0F",_T="\u{1F935}\u200D\u2642\uFE0F",hT="\u{1F935}",bT="\u{1F936}",FT="\u{1F936}",CT="\u{1F937}\u200D\u2640\uFE0F",ET="\u{1F937}\u200D\u2642\uFE0F",wT="\u{1F937}",yT="\u{1F938}\u200D\u2640\uFE0F",vT="\u{1F938}\u200D\u2642\uFE0F",TT="\u{1F938}",BT="\u{1F939}\u200D\u2640\uFE0F",xT="\u{1F939}\u200D\u2642\uFE0F",AT="\u{1F939}",kT="\u{1F93A}",ST="\u{1F93C}\u200D\u2640\uFE0F",IT="\u{1F93C}\u200D\u2642\uFE0F",MT="\u{1F93C}",PT="\u{1F93D}\u200D\u2640\uFE0F",qT="\u{1F93D}\u200D\u2642\uFE0F",LT="\u{1F93D}",OT="\u{1F93E}\u200D\u2640\uFE0F",jT="\u{1F93E}\u200D\u2642\uFE0F",RT="\u{1F93E}",HT="\u{1F93F}",NT="\u{1F940}",VT="\u{1F941}",zT="\u{1F942}",$T="\u{1F943}",GT="\u{1F944}",KT="\u{1F945}",UT="\u{1F947}",WT="\u{1F948}",YT="\u{1F949}",XT="\u{1F94A}",JT="\u{1F94B}",QT="\u{1F94C}",ZT="\u{1F94D}",eB="\u{1F94E}",uB="\u{1F94F}",tB="\u{1F950}",rB="\u{1F951}",aB="\u{1F952}",nB="\u{1F953}",iB="\u{1F954}",dB="\u{1F955}",oB="\u{1F956}",sB="\u{1F957}",DB="\u{1F958}",lB="\u{1F959}",cB="\u{1F95A}",fB="\u{1F95B}",mB="\u{1F95C}",gB="\u{1F95D}",pB="\u{1F95E}",_B="\u{1F95F}",hB="\u{1F960}",bB="\u{1F961}",FB="\u{1F962}",CB="\u{1F963}",EB="\u{1F964}",wB="\u{1F965}",yB="\u{1F966}",vB="\u{1F967}",TB="\u{1F968}",BB="\u{1F969}",xB="\u{1F96A}",AB="\u{1F96B}",kB="\u{1F96C}",SB="\u{1F96D}",IB="\u{1F96E}",MB="\u{1F96F}",PB="\u{1F970}",qB="\u{1F971}",LB="\u{1F972}",OB="\u{1F973}",jB="\u{1F974}",RB="\u{1F975}",HB="\u{1F976}",NB="\u{1F977}",VB="\u{1F978}",zB="\u{1F97A}",$B="\u{1F97B}",GB="\u{1F97C}",KB="\u{1F97D}",UB="\u{1F97E}",WB="\u{1F97F}",YB="\u{1F980}",XB="\u{1F981}",JB="\u{1F982}",QB="\u{1F983}",ZB="\u{1F984}",ex="\u{1F985}",ux="\u{1F986}",tx="\u{1F987}",rx="\u{1F988}",ax="\u{1F989}",nx="\u{1F98A}",ix="\u{1F98B}",dx="\u{1F98C}",ox="\u{1F98D}",sx="\u{1F98E}",Dx="\u{1F98F}",lx="\u{1F990}",cx="\u{1F991}",fx="\u{1F992}",mx="\u{1F993}",gx="\u{1F994}",px="\u{1F995}",_x="\u{1F996}",hx="\u{1F997}",bx="\u{1F998}",Fx="\u{1F999}",Cx="\u{1F99A}",Ex="\u{1F99B}",wx="\u{1F99C}",yx="\u{1F99D}",vx="\u{1F99E}",Tx="\u{1F99F}",Bx="\u{1F9A0}",xx="\u{1F9A1}",Ax="\u{1F9A2}",kx="\u{1F9A3}",Sx="\u{1F9A4}",Ix="\u{1F9A5}",Mx="\u{1F9A6}",Px="\u{1F9A7}",qx="\u{1F9A8}",Lx="\u{1F9A9}",Ox="\u{1F9AA}",jx="\u{1F9AB}",Rx="\u{1F9AC}",Hx="\u{1F9AD}",Nx="\u{1F9AE}",Vx="\u{1F9AF}",zx="\u{1F9B4}",$x="\u{1F9B5}",Gx="\u{1F9B6}",Kx="\u{1F9B7}",Ux="\u{1F9B8}\u200D\u2640\uFE0F",Wx="\u{1F9B8}\u200D\u2642\uFE0F",Yx="\u{1F9B8}",Xx="\u{1F9B9}\u200D\u2640\uFE0F",Jx="\u{1F9B9}\u200D\u2642\uFE0F",Qx="\u{1F9B9}",Zx="\u{1F9BA}",e9="\u{1F9BB}",u9="\u{1F9BC}",t9="\u{1F9BD}",r9="\u{1F9BE}",a9="\u{1F9BF}",n9="\u{1F9C0}",i9="\u{1F9C1}",d9="\u{1F9C2}",o9="\u{1F9C3}",s9="\u{1F9C4}",D9="\u{1F9C5}",l9="\u{1F9C6}",c9="\u{1F9C7}",f9="\u{1F9C8}",m9="\u{1F9C9}",g9="\u{1F9CA}",p9="\u{1F9CB}",_9="\u{1F9CD}\u200D\u2640\uFE0F",h9="\u{1F9CD}\u200D\u2642\uFE0F",b9="\u{1F9CD}",F9="\u{1F9CE}\u200D\u2640\uFE0F",C9="\u{1F9CE}\u200D\u2642\uFE0F",E9="\u{1F9CE}",w9="\u{1F9CF}\u200D\u2640\uFE0F",y9="\u{1F9CF}\u200D\u2642\uFE0F",v9="\u{1F9CF}",T9="\u{1F9D0}",B9="\u{1F9D1}\u200D\u{1F33E}",x9="\u{1F9D1}\u200D\u{1F373}",A9="\u{1F9D1}\u200D\u{1F37C}",k9="\u{1F9D1}\u200D\u{1F384}",S9="\u{1F9D1}\u200D\u{1F393}",I9="\u{1F9D1}\u200D\u{1F3A4}",M9="\u{1F9D1}\u200D\u{1F3A8}",P9="\u{1F9D1}\u200D\u{1F3EB}",q9="\u{1F9D1}\u200D\u{1F3ED}",L9="\u{1F9D1}\u200D\u{1F4BB}",O9="\u{1F9D1}\u200D\u{1F4BC}",j9="\u{1F9D1}\u200D\u{1F527}",R9="\u{1F9D1}\u200D\u{1F52C}",H9="\u{1F9D1}\u200D\u{1F680}",N9="\u{1F9D1}\u200D\u{1F692}",V9="\u{1F9D1}\u200D\u{1F91D}\u200D\u{1F9D1}",z9="\u{1F9D1}\u200D\u{1F9AF}",$9="\u{1F9D1}\u200D\u{1F9B0}",G9="\u{1F9D1}\u200D\u{1F9B1}",K9="\u{1F9D1}\u200D\u{1F9B2}",U9="\u{1F9D1}\u200D\u{1F9B3}",W9="\u{1F9D1}\u200D\u{1F9BC}",Y9="\u{1F9D1}\u200D\u{1F9BD}",X9="\u{1F9D1}\u200D\u2695\uFE0F",J9="\u{1F9D1}\u200D\u2696\uFE0F",Q9="\u{1F9D1}\u200D\u2708\uFE0F",Z9="\u{1F9D1}",eA="\u{1F9D2}",uA="\u{1F9D3}",tA="\u{1F9D4}\u200D\u2640\uFE0F",rA="\u{1F9D4}\u200D\u2642\uFE0F",aA="\u{1F9D4}",nA="\u{1F9D5}",iA="\u{1F9D6}\u200D\u2640\uFE0F",dA="\u{1F9D6}\u200D\u2642\uFE0F",oA="\u{1F9D6}\u200D\u2642\uFE0F",sA="\u{1F9D7}\u200D\u2640\uFE0F",DA="\u{1F9D7}\u200D\u2640\uFE0F",lA="\u{1F9D7}\u200D\u2642\uFE0F",cA="\u{1F9D8}\u200D\u2640\uFE0F",fA="\u{1F9D8}\u200D\u2640\uFE0F",mA="\u{1F9D8}\u200D\u2642\uFE0F",gA="\u{1F9D9}\u200D\u2640\uFE0F",pA="\u{1F9D9}\u200D\u2640\uFE0F",_A="\u{1F9D9}\u200D\u2642\uFE0F",hA="\u{1F9DA}\u200D\u2640\uFE0F",bA="\u{1F9DA}\u200D\u2640\uFE0F",FA="\u{1F9DA}\u200D\u2642\uFE0F",CA="\u{1F9DB}\u200D\u2640\uFE0F",EA="\u{1F9DB}\u200D\u2640\uFE0F",wA="\u{1F9DB}\u200D\u2642\uFE0F",yA="\u{1F9DC}\u200D\u2640\uFE0F",vA="\u{1F9DC}\u200D\u2642\uFE0F",TA="\u{1F9DC}\u200D\u2642\uFE0F",BA="\u{1F9DD}\u200D\u2640\uFE0F",xA="\u{1F9DD}\u200D\u2642\uFE0F",AA="\u{1F9DD}\u200D\u2642\uFE0F",kA="\u{1F9DE}\u200D\u2640\uFE0F",SA="\u{1F9DE}\u200D\u2642\uFE0F",IA="\u{1F9DE}\u200D\u2642\uFE0F",MA="\u{1F9DF}\u200D\u2640\uFE0F",PA="\u{1F9DF}\u200D\u2642\uFE0F",qA="\u{1F9DF}\u200D\u2642\uFE0F",LA="\u{1F9E0}",OA="\u{1F9E1}",jA="\u{1F9E2}",RA="\u{1F9E3}",HA="\u{1F9E4}",NA="\u{1F9E5}",VA="\u{1F9E6}",zA="\u{1F9E7}",$A="\u{1F9E8}",GA="\u{1F9E9}",KA="\u{1F9EA}",UA="\u{1F9EB}",WA="\u{1F9EC}",YA="\u{1F9ED}",XA="\u{1F9EE}",JA="\u{1F9EF}",QA="\u{1F9F0}",ZA="\u{1F9F1}",e4="\u{1F9F2}",u4="\u{1F9F3}",t4="\u{1F9F4}",r4="\u{1F9F5}",a4="\u{1F9F6}",n4="\u{1F9F7}",i4="\u{1F9F8}",d4="\u{1F9F9}",o4="\u{1F9FA}",s4="\u{1F9FB}",D4="\u{1F9FC}",l4="\u{1F9FD}",c4="\u{1F9FE}",f4="\u{1F9FF}",m4="\u{1FA70}",g4="\u{1FA71}",p4="\u{1FA72}",_4="\u{1FA73}",h4="\u{1FA74}",b4="\u{1FA78}",F4="\u{1FA79}",C4="\u{1FA7A}",E4="\u{1FA80}",w4="\u{1FA81}",y4="\u{1FA82}",v4="\u{1FA83}",T4="\u{1FA84}",B4="\u{1FA85}",x4="\u{1FA86}",A4="\u{1FA90}",k4="\u{1FA91}",S4="\u{1FA92}",I4="\u{1FA93}",M4="\u{1FA94}",P4="\u{1FA95}",q4="\u{1FA96}",L4="\u{1FA97}",O4="\u{1FA98}",j4="\u{1FA99}",R4="\u{1FA9A}",H4="\u{1FA9B}",N4="\u{1FA9C}",V4="\u{1FA9D}",z4="\u{1FA9E}",$4="\u{1FA9F}",G4="\u{1FAA0}",K4="\u{1FAA1}",U4="\u{1FAA2}",W4="\u{1FAA3}",Y4="\u{1FAA4}",X4="\u{1FAA5}",J4="\u{1FAA6}",Q4="\u{1FAA7}",Z4="\u{1FAA8}",e1="\u{1FAB0}",u1="\u{1FAB1}",t1="\u{1FAB2}",r1="\u{1FAB3}",a1="\u{1FAB4}",n1="\u{1FAB5}",i1="\u{1FAB6}",d1="\u{1FAC0}",o1="\u{1FAC1}",s1="\u{1FAC2}",D1="\u{1FAD0}",l1="\u{1FAD1}",c1="\u{1FAD2}",f1="\u{1FAD3}",m1="\u{1FAD4}",g1="\u{1FAD5}",p1="\u{1FAD6}",_1="\u203C\uFE0F",h1="\u2049\uFE0F",b1="\u2122\uFE0F",F1="\u2139\uFE0F",C1="\u2194\uFE0F",E1="\u2195\uFE0F",w1="\u2196\uFE0F",y1="\u2197\uFE0F",v1="\u2198\uFE0F",T1="\u2199\uFE0F",B1="\u21A9\uFE0F",x1="\u21AA\uFE0F",A1="\u231A",k1="\u231B",S1="\u2328\uFE0F",I1="\u23CF\uFE0F",M1="\u23E9",P1="\u23EA",q1="\u23EB",L1="\u23EC",O1="\u23ED\uFE0F",j1="\u23EE\uFE0F",R1="\u23EF\uFE0F",H1="\u23F0",N1="\u23F1\uFE0F",V1="\u23F2\uFE0F",z1="\u23F3",$1="\u23F8\uFE0F",G1="\u23F9\uFE0F",K1="\u23FA\uFE0F",U1="\u24C2\uFE0F",W1="\u25AA\uFE0F",Y1="\u25AB\uFE0F",X1="\u25B6\uFE0F",J1="\u25C0\uFE0F",Q1="\u25FB\uFE0F",Z1="\u25FC\uFE0F",ek="\u25FD",uk="\u25FE",tk="\u2600\uFE0F",rk="\u2601\uFE0F",ak="\u2602\uFE0F",nk="\u2603\uFE0F",ik="\u2604\uFE0F",dk="\u260E\uFE0F",ok="\u260E\uFE0F",sk="\u2611\uFE0F",Dk="\u2618\uFE0F",lk="\u261D\uFE0F",ck="\u2620\uFE0F",fk="\u2622\uFE0F",mk="\u2623\uFE0F",gk="\u2626\uFE0F",pk="\u262A\uFE0F",_k="\u262E\uFE0F",hk="\u262F\uFE0F",bk="\u2638\uFE0F",Fk="\u2639\uFE0F",Ck="\u263A\uFE0F",Ek="\u2640\uFE0F",wk="\u2642\uFE0F",yk="\u264A",vk="\u264B",Tk="\u264C",Bk="\u264D",xk="\u264E",Ak="\u264F",kk="\u265F\uFE0F",Sk="\u2660\uFE0F",Ik="\u2663\uFE0F",Mk="\u2665\uFE0F",Pk="\u2666\uFE0F",qk="\u2668\uFE0F",Lk="\u267B\uFE0F",Ok="\u267E\uFE0F",jk="\u267F",Rk="\u2692\uFE0F",Hk="\u2694\uFE0F",Nk="\u2695\uFE0F",Vk="\u2695\uFE0F",zk="\u2696\uFE0F",$k="\u2697\uFE0F",Gk="\u2699\uFE0F",Kk="\u269B\uFE0F",Uk="\u269C\uFE0F",Wk="\u26A0\uFE0F",Yk="\u26A1",Xk="\u26A7\uFE0F",Jk="\u26AA",Qk="\u26AB",Zk="\u26B0\uFE0F",eS="\u26B1\uFE0F",uS="\u26BD",tS="\u26BE",rS="\u26C4",aS="\u26C5",nS="\u26C8\uFE0F",iS="\u26CE",dS="\u26CF\uFE0F",oS="\u26D1\uFE0F",sS="\u26D3\uFE0F",DS="\u26D4",lS="\u26E9\uFE0F",cS="\u26EA",fS="\u26F0\uFE0F",mS="\u26F1\uFE0F",gS="\u26F2",pS="\u26F3",_S="\u26F4\uFE0F",hS="\u26F5",bS="\u26F5",FS="\u26F7\uFE0F",CS="\u26F8\uFE0F",ES="\u26F9\uFE0F\u200D\u2640\uFE0F",wS="\u26F9\uFE0F\u200D\u2642\uFE0F",yS="\u26F9\uFE0F\u200D\u2642\uFE0F",vS="\u26FA",TS="\u26FD",BS="\u2702\uFE0F",xS="\u2708\uFE0F",AS="\u2709\uFE0F",kS="\u2709\uFE0F",SS="\u270A",IS="\u270B",MS="\u270B",PS="\u270C\uFE0F",qS="\u270D\uFE0F",LS="\u270F\uFE0F",OS="\u2712\uFE0F",jS="\u2714\uFE0F",RS="\u2716\uFE0F",HS="\u271D\uFE0F",NS="\u2721\uFE0F",VS="\u2733\uFE0F",zS="\u2734\uFE0F",$S="\u2744\uFE0F",GS="\u2747\uFE0F",KS="\u274C",US="\u274E",WS="\u2763\uFE0F",YS="\u2764\uFE0F\u200D\u{1F525}",XS="\u2764\uFE0F\u200D\u{1FA79}",JS="\u2764\uFE0F",QS="\u27A1\uFE0F",ZS="\u27B0",eI="\u27BF",uI="\u2934\uFE0F",tI="\u2935\uFE0F",rI="\u2B05\uFE0F",aI="\u2B06\uFE0F",nI="\u2B07\uFE0F",iI="\u2B1B",dI="\u2B1C",oI="\u2B50",sI="\u2B55",DI="\u3030\uFE0F",lI="\u303D\uFE0F",cI="\u3297\uFE0F",fI="\u3299\uFE0F",zu={"100":yi,"1234":vi,umbrella_with_rain_drops:Ti,coffee:Bi,aries:xi,taurus:Ai,sagittarius:ki,capricorn:Si,aquarius:Ii,pisces:Mi,anchor:Pi,white_check_mark:qi,sparkles:Li,question:Oi,grey_question:ji,grey_exclamation:Ri,exclamation:Hi,heavy_exclamation_mark:Ni,heavy_plus_sign:Vi,heavy_minus_sign:zi,heavy_division_sign:$i,hash:Gi,keycap_star:Ki,zero:Ui,one:Wi,two:Yi,three:Xi,four:Ji,five:Qi,six:Zi,seven:ed,eight:ud,nine:td,copyright:rd,registered:ad,mahjong:nd,black_joker:id,a:dd,b:od,o2:sd,parking:Dd,ab:ld,cl:cd,cool:fd,free:md,id:gd,new:pd,ng:_d,ok:hd,sos:bd,up:Fd,vs:Cd,"flag-ac":Ed,"flag-ad":wd,"flag-ae":yd,"flag-af":vd,"flag-ag":Td,"flag-ai":Bd,"flag-al":xd,"flag-am":Ad,"flag-ao":kd,"flag-aq":Sd,"flag-ar":Id,"flag-as":Md,"flag-at":Pd,"flag-au":qd,"flag-aw":Ld,"flag-ax":Od,"flag-az":jd,"flag-ba":Rd,"flag-bb":Hd,"flag-bd":Nd,"flag-be":Vd,"flag-bf":zd,"flag-bg":$d,"flag-bh":Gd,"flag-bi":Kd,"flag-bj":Ud,"flag-bl":Wd,"flag-bm":Yd,"flag-bn":Xd,"flag-bo":Jd,"flag-bq":Qd,"flag-br":Zd,"flag-bs":eo,"flag-bt":uo,"flag-bv":to,"flag-bw":ro,"flag-by":ao,"flag-bz":no,"flag-ca":io,"flag-cc":oo,"flag-cd":so,"flag-cf":Do,"flag-cg":lo,"flag-ch":co,"flag-ci":fo,"flag-ck":mo,"flag-cl":go,"flag-cm":po,cn:_o,"flag-cn":ho,"flag-co":bo,"flag-cp":Fo,"flag-cr":Co,"flag-cu":Eo,"flag-cv":wo,"flag-cw":yo,"flag-cx":vo,"flag-cy":To,"flag-cz":Bo,de:xo,"flag-de":Ao,"flag-dg":ko,"flag-dj":So,"flag-dk":Io,"flag-dm":Mo,"flag-do":Po,"flag-dz":qo,"flag-ea":Lo,"flag-ec":Oo,"flag-ee":jo,"flag-eg":Ro,"flag-eh":Ho,"flag-er":No,es:Vo,"flag-es":zo,"flag-et":$o,"flag-eu":Go,"flag-fi":Ko,"flag-fj":Uo,"flag-fk":Wo,"flag-fm":Yo,"flag-fo":Xo,fr:Jo,"flag-fr":Qo,"flag-ga":Zo,gb:es,uk:us,"flag-gb":ts,"flag-gd":rs,"flag-ge":as,"flag-gf":ns,"flag-gg":is,"flag-gh":ds,"flag-gi":os,"flag-gl":ss,"flag-gm":Ds,"flag-gn":ls,"flag-gp":cs,"flag-gq":fs,"flag-gr":ms,"flag-gs":gs,"flag-gt":ps,"flag-gu":_s,"flag-gw":hs,"flag-gy":bs,"flag-hk":Fs,"flag-hm":Cs,"flag-hn":Es,"flag-hr":ws,"flag-ht":ys,"flag-hu":vs,"flag-ic":Ts,"flag-id":Bs,"flag-ie":xs,"flag-il":As,"flag-im":ks,"flag-in":Ss,"flag-io":Is,"flag-iq":Ms,"flag-ir":Ps,"flag-is":qs,it:Ls,"flag-it":Os,"flag-je":js,"flag-jm":Rs,"flag-jo":Hs,jp:Ns,"flag-jp":Vs,"flag-ke":zs,"flag-kg":$s,"flag-kh":Gs,"flag-ki":Ks,"flag-km":Us,"flag-kn":Ws,"flag-kp":Ys,kr:Xs,"flag-kr":Js,"flag-kw":Qs,"flag-ky":Zs,"flag-kz":eD,"flag-la":uD,"flag-lb":tD,"flag-lc":rD,"flag-li":aD,"flag-lk":nD,"flag-lr":iD,"flag-ls":dD,"flag-lt":oD,"flag-lu":sD,"flag-lv":DD,"flag-ly":lD,"flag-ma":cD,"flag-mc":fD,"flag-md":mD,"flag-me":gD,"flag-mf":pD,"flag-mg":_D,"flag-mh":hD,"flag-mk":bD,"flag-ml":FD,"flag-mm":CD,"flag-mn":ED,"flag-mo":wD,"flag-mp":yD,"flag-mq":vD,"flag-mr":TD,"flag-ms":BD,"flag-mt":xD,"flag-mu":AD,"flag-mv":kD,"flag-mw":SD,"flag-mx":ID,"flag-my":MD,"flag-mz":PD,"flag-na":qD,"flag-nc":LD,"flag-ne":OD,"flag-nf":jD,"flag-ng":RD,"flag-ni":HD,"flag-nl":ND,"flag-no":VD,"flag-np":zD,"flag-nr":$D,"flag-nu":GD,"flag-nz":KD,"flag-om":UD,"flag-pa":WD,"flag-pe":YD,"flag-pf":XD,"flag-pg":JD,"flag-ph":QD,"flag-pk":ZD,"flag-pl":el,"flag-pm":ul,"flag-pn":tl,"flag-pr":rl,"flag-ps":al,"flag-pt":nl,"flag-pw":il,"flag-py":dl,"flag-qa":ol,"flag-re":sl,"flag-ro":Dl,"flag-rs":ll,ru:cl,"flag-ru":fl,"flag-rw":ml,"flag-sa":gl,"flag-sb":pl,"flag-sc":_l,"flag-sd":hl,"flag-se":bl,"flag-sg":Fl,"flag-sh":Cl,"flag-si":El,"flag-sj":wl,"flag-sk":yl,"flag-sl":vl,"flag-sm":Tl,"flag-sn":Bl,"flag-so":xl,"flag-sr":Al,"flag-ss":kl,"flag-st":Sl,"flag-sv":Il,"flag-sx":Ml,"flag-sy":Pl,"flag-sz":ql,"flag-ta":Ll,"flag-tc":Ol,"flag-td":jl,"flag-tf":Rl,"flag-tg":Hl,"flag-th":Nl,"flag-tj":Vl,"flag-tk":zl,"flag-tl":$l,"flag-tm":Gl,"flag-tn":Kl,"flag-to":Ul,"flag-tr":Wl,"flag-tt":Yl,"flag-tv":Xl,"flag-tw":Jl,"flag-tz":Ql,"flag-ua":Zl,"flag-ug":ec,"flag-um":uc,"flag-un":tc,us:rc,"flag-us":ac,"flag-uy":nc,"flag-uz":ic,"flag-va":dc,"flag-vc":oc,"flag-ve":sc,"flag-vg":Dc,"flag-vi":lc,"flag-vn":cc,"flag-vu":fc,"flag-wf":mc,"flag-ws":gc,"flag-xk":pc,"flag-ye":_c,"flag-yt":hc,"flag-za":bc,"flag-zm":Fc,"flag-zw":Cc,koko:Ec,sa:wc,u7121:yc,u6307:vc,u7981:Tc,u7a7a:Bc,u5408:xc,u6e80:Ac,u6709:kc,u6708:Sc,u7533:Ic,u5272:Mc,u55b6:Pc,ideograph_advantage:qc,accept:Lc,cyclone:Oc,foggy:jc,closed_umbrella:Rc,night_with_stars:Hc,sunrise_over_mountains:Nc,sunrise:Vc,city_sunset:zc,city_sunrise:$c,rainbow:Gc,bridge_at_night:Kc,ocean:Uc,volcano:Wc,milky_way:Yc,earth_africa:Xc,earth_americas:Jc,earth_asia:Qc,globe_with_meridians:Zc,new_moon:ef,waxing_crescent_moon:uf,first_quarter_moon:tf,moon:rf,waxing_gibbous_moon:af,full_moon:nf,waning_gibbous_moon:df,last_quarter_moon:of,waning_crescent_moon:sf,crescent_moon:Df,new_moon_with_face:lf,first_quarter_moon_with_face:cf,last_quarter_moon_with_face:ff,full_moon_with_face:mf,sun_with_face:gf,star2:pf,stars:_f,thermometer:hf,mostly_sunny:bf,sun_small_cloud:Ff,barely_sunny:Cf,sun_behind_cloud:Ef,partly_sunny_rain:wf,sun_behind_rain_cloud:yf,rain_cloud:vf,snow_cloud:Tf,lightning:Bf,lightning_cloud:xf,tornado:Af,tornado_cloud:kf,fog:Sf,wind_blowing_face:If,hotdog:Mf,taco:Pf,burrito:qf,chestnut:Lf,seedling:Of,evergreen_tree:jf,deciduous_tree:Rf,palm_tree:Hf,cactus:Nf,hot_pepper:Vf,tulip:zf,cherry_blossom:$f,rose:Gf,hibiscus:Kf,sunflower:Uf,blossom:Wf,corn:Yf,ear_of_rice:Xf,herb:Jf,four_leaf_clover:Qf,maple_leaf:Zf,fallen_leaf:em,leaves:um,mushroom:tm,tomato:rm,eggplant:am,grapes:nm,melon:im,watermelon:dm,tangerine:om,lemon:sm,banana:Dm,pineapple:lm,apple:cm,green_apple:fm,pear:mm,peach:gm,cherries:pm,strawberry:_m,hamburger:hm,pizza:bm,meat_on_bone:Fm,poultry_leg:Cm,rice_cracker:Em,rice_ball:wm,rice:ym,curry:vm,ramen:Tm,spaghetti:Bm,bread:xm,fries:Am,sweet_potato:km,dango:Sm,oden:Im,sushi:Mm,fried_shrimp:Pm,fish_cake:qm,icecream:Lm,shaved_ice:Om,ice_cream:jm,doughnut:Rm,cookie:Hm,chocolate_bar:Nm,candy:Vm,lollipop:zm,custard:$m,honey_pot:Gm,cake:Km,bento:Um,stew:Wm,fried_egg:Ym,cooking:Xm,fork_and_knife:Jm,tea:Qm,sake:Zm,wine_glass:eg,cocktail:ug,tropical_drink:tg,beer:rg,beers:ag,baby_bottle:ng,knife_fork_plate:ig,champagne:dg,popcorn:og,ribbon:sg,gift:Dg,birthday:lg,jack_o_lantern:cg,christmas_tree:fg,santa:mg,fireworks:gg,sparkler:pg,balloon:_g,tada:hg,confetti_ball:bg,tanabata_tree:Fg,crossed_flags:Cg,bamboo:Eg,dolls:wg,flags:yg,wind_chime:vg,rice_scene:Tg,school_satchel:Bg,mortar_board:xg,medal:Ag,reminder_ribbon:kg,studio_microphone:Sg,level_slider:Ig,control_knobs:Mg,film_frames:Pg,admission_tickets:qg,carousel_horse:Lg,ferris_wheel:Og,roller_coaster:jg,fishing_pole_and_fish:Rg,microphone:Hg,movie_camera:Ng,cinema:Vg,headphones:zg,art:$g,tophat:Gg,circus_tent:Kg,ticket:Ug,clapper:Wg,performing_arts:Yg,video_game:Xg,dart:Jg,slot_machine:Qg,"8ball":Zg,game_die:ep,bowling:up,flower_playing_cards:tp,musical_note:rp,notes:ap,saxophone:np,guitar:ip,musical_keyboard:dp,trumpet:op,violin:sp,musical_score:Dp,running_shirt_with_sash:lp,tennis:cp,ski:fp,basketball:mp,checkered_flag:gp,snowboarder:pp,"woman-running":_p,"man-running":hp,runner:bp,running:Fp,"woman-surfing":Cp,"man-surfing":Ep,surfer:wp,sports_medal:yp,trophy:vp,horse_racing:Tp,football:Bp,rugby_football:xp,"woman-swimming":Ap,"man-swimming":kp,swimmer:Sp,"woman-lifting-weights":Ip,"man-lifting-weights":Mp,weight_lifter:Pp,"woman-golfing":qp,"man-golfing":Lp,golfer:Op,racing_motorcycle:jp,racing_car:Rp,cricket_bat_and_ball:Hp,volleyball:Np,field_hockey_stick_and_ball:Vp,ice_hockey_stick_and_puck:zp,table_tennis_paddle_and_ball:$p,snow_capped_mountain:Gp,camping:Kp,beach_with_umbrella:Up,building_construction:Wp,house_buildings:Yp,cityscape:Xp,derelict_house_building:Jp,classical_building:Qp,desert:Zp,desert_island:e_,national_park:u_,stadium:t_,house:r_,house_with_garden:a_,office:n_,post_office:i_,european_post_office:d_,hospital:o_,bank:s_,atm:D_,hotel:l_,love_hotel:c_,convenience_store:f_,school:m_,department_store:g_,factory:p_,izakaya_lantern:__,lantern:h_,japanese_castle:b_,european_castle:F_,"rainbow-flag":C_,transgender_flag:E_,waving_white_flag:w_,pirate_flag:y_,"flag-england":v_,"flag-scotland":T_,"flag-wales":B_,waving_black_flag:x_,rosette:A_,label:k_,badminton_racquet_and_shuttlecock:S_,bow_and_arrow:I_,amphora:M_,"skin-tone-2":P_,"skin-tone-3":q_,"skin-tone-4":L_,"skin-tone-5":O_,"skin-tone-6":j_,rat:R_,mouse2:H_,ox:N_,water_buffalo:V_,cow2:z_,tiger2:$_,leopard:G_,rabbit2:K_,black_cat:U_,cat2:W_,dragon:Y_,crocodile:X_,whale2:J_,snail:Q_,snake:Z_,racehorse:eh,ram:uh,goat:th,sheep:rh,monkey:ah,rooster:nh,chicken:ih,service_dog:dh,dog2:oh,pig2:sh,boar:Dh,elephant:lh,octopus:ch,shell:fh,bug:mh,ant:gh,bee:ph,honeybee:_h,ladybug:hh,lady_beetle:bh,fish:Fh,tropical_fish:Ch,blowfish:Eh,turtle:wh,hatching_chick:yh,baby_chick:vh,hatched_chick:Th,bird:Bh,penguin:xh,koala:Ah,poodle:kh,dromedary_camel:Sh,camel:Ih,dolphin:Mh,flipper:Ph,mouse:qh,cow:Lh,tiger:Oh,rabbit:jh,cat:Rh,dragon_face:Hh,whale:Nh,horse:Vh,monkey_face:zh,dog:$h,pig:Gh,frog:Kh,hamster:Uh,wolf:Wh,polar_bear:Yh,bear:Xh,panda_face:Jh,pig_nose:Qh,feet:Zh,paw_prints:e0,chipmunk:u0,eyes:t0,"eye-in-speech-bubble":r0,eye:a0,ear:n0,nose:i0,lips:d0,tongue:o0,point_up_2:s0,point_down:D0,point_left:l0,point_right:c0,facepunch:f0,punch:m0,wave:g0,ok_hand:p0,"+1":_0,thumbsup:h0,"-1":b0,thumbsdown:F0,clap:C0,open_hands:E0,crown:w0,womans_hat:y0,eyeglasses:v0,necktie:T0,shirt:B0,tshirt:x0,jeans:A0,dress:k0,kimono:S0,bikini:I0,womans_clothes:M0,purse:P0,handbag:q0,pouch:L0,mans_shoe:O0,shoe:j0,athletic_shoe:R0,high_heel:H0,sandal:N0,boot:V0,footprints:z0,bust_in_silhouette:$0,busts_in_silhouette:G0,boy:K0,girl:U0,"male-farmer":W0,"male-cook":Y0,man_feeding_baby:X0,"male-student":J0,"male-singer":Q0,"male-artist":Z0,"male-teacher":e8,"male-factory-worker":u8,"man-boy-boy":t8,"man-boy":r8,"man-girl-boy":a8,"man-girl-girl":n8,"man-girl":i8,"man-man-boy":d8,"man-man-boy-boy":o8,"man-man-girl":s8,"man-man-girl-boy":D8,"man-man-girl-girl":l8,"man-woman-boy":c8,family:f8,"man-woman-boy-boy":m8,"man-woman-girl":g8,"man-woman-girl-boy":p8,"man-woman-girl-girl":_8,"male-technologist":h8,"male-office-worker":b8,"male-mechanic":F8,"male-scientist":C8,"male-astronaut":E8,"male-firefighter":w8,man_with_probing_cane:y8,red_haired_man:v8,curly_haired_man:T8,bald_man:B8,white_haired_man:x8,man_in_motorized_wheelchair:A8,man_in_manual_wheelchair:k8,"male-doctor":S8,"male-judge":I8,"male-pilot":M8,"man-heart-man":P8,"man-kiss-man":q8,man:L8,"female-farmer":O8,"female-cook":j8,woman_feeding_baby:R8,"female-student":H8,"female-singer":N8,"female-artist":V8,"female-teacher":z8,"female-factory-worker":$8,"woman-boy-boy":G8,"woman-boy":K8,"woman-girl-boy":U8,"woman-girl-girl":W8,"woman-girl":Y8,"woman-woman-boy":X8,"woman-woman-boy-boy":J8,"woman-woman-girl":Q8,"woman-woman-girl-boy":Z8,"woman-woman-girl-girl":e3,"female-technologist":u3,"female-office-worker":t3,"female-mechanic":r3,"female-scientist":a3,"female-astronaut":n3,"female-firefighter":i3,woman_with_probing_cane:d3,red_haired_woman:o3,curly_haired_woman:s3,bald_woman:D3,white_haired_woman:l3,woman_in_motorized_wheelchair:c3,woman_in_manual_wheelchair:f3,"female-doctor":m3,"female-judge":g3,"female-pilot":p3,"woman-heart-man":_3,"woman-heart-woman":h3,"woman-kiss-man":b3,"woman-kiss-woman":F3,woman:C3,man_and_woman_holding_hands:E3,woman_and_man_holding_hands:w3,couple:y3,two_men_holding_hands:v3,men_holding_hands:T3,two_women_holding_hands:B3,women_holding_hands:x3,"female-police-officer":A3,"male-police-officer":k3,cop:S3,"women-with-bunny-ears-partying":I3,"woman-with-bunny-ears-partying":M3,dancers:P3,"men-with-bunny-ears-partying":q3,"man-with-bunny-ears-partying":L3,woman_with_veil:O3,man_with_veil:j3,bride_with_veil:R3,"blond-haired-woman":H3,"blond-haired-man":N3,person_with_blond_hair:V3,man_with_gua_pi_mao:z3,"woman-wearing-turban":$3,"man-wearing-turban":G3,man_with_turban:K3,older_man:U3,older_woman:W3,baby:Y3,"female-construction-worker":X3,"male-construction-worker":J3,construction_worker:Q3,princess:Z3,japanese_ogre:e2,japanese_goblin:u2,ghost:t2,angel:r2,alien:a2,space_invader:n2,imp:i2,skull:d2,"woman-tipping-hand":o2,information_desk_person:s2,"man-tipping-hand":D2,"female-guard":l2,"male-guard":c2,guardsman:f2,dancer:m2,lipstick:g2,nail_care:p2,"woman-getting-massage":_2,massage:h2,"man-getting-massage":b2,"woman-getting-haircut":F2,haircut:C2,"man-getting-haircut":E2,barber:w2,syringe:y2,pill:v2,kiss:T2,love_letter:B2,ring:x2,gem:A2,couplekiss:k2,bouquet:S2,couple_with_heart:I2,wedding:M2,heartbeat:P2,broken_heart:q2,two_hearts:L2,sparkling_heart:O2,heartpulse:j2,cupid:R2,blue_heart:H2,green_heart:N2,yellow_heart:V2,purple_heart:z2,gift_heart:$2,revolving_hearts:G2,heart_decoration:K2,diamond_shape_with_a_dot_inside:U2,bulb:W2,anger:Y2,bomb:X2,zzz:J2,boom:Q2,collision:Z2,sweat_drops:eb,droplet:ub,dash:tb,hankey:rb,poop:ab,shit:nb,muscle:ib,dizzy:db,speech_balloon:ob,thought_balloon:sb,white_flower:Db,moneybag:lb,currency_exchange:cb,heavy_dollar_sign:fb,credit_card:mb,yen:gb,dollar:pb,euro:_b,pound:hb,money_with_wings:bb,chart:Fb,seat:Cb,computer:Eb,briefcase:wb,minidisc:yb,floppy_disk:vb,cd:Tb,dvd:Bb,file_folder:xb,open_file_folder:Ab,page_with_curl:kb,page_facing_up:Sb,date:Ib,calendar:Mb,card_index:Pb,chart_with_upwards_trend:qb,chart_with_downwards_trend:Lb,bar_chart:Ob,clipboard:jb,pushpin:Rb,round_pushpin:Hb,paperclip:Nb,straight_ruler:Vb,triangular_ruler:zb,bookmark_tabs:$b,ledger:Gb,notebook:Kb,notebook_with_decorative_cover:Ub,closed_book:Wb,book:Yb,open_book:Xb,green_book:Jb,blue_book:Qb,orange_book:Zb,books:eF,name_badge:uF,scroll:tF,memo:rF,pencil:aF,telephone_receiver:nF,pager:iF,fax:dF,satellite_antenna:oF,loudspeaker:sF,mega:DF,outbox_tray:lF,inbox_tray:cF,package:fF,"e-mail":mF,incoming_envelope:gF,envelope_with_arrow:pF,mailbox_closed:_F,mailbox:hF,mailbox_with_mail:bF,mailbox_with_no_mail:FF,postbox:CF,postal_horn:EF,newspaper:wF,iphone:yF,calling:vF,vibration_mode:TF,mobile_phone_off:BF,no_mobile_phones:xF,signal_strength:AF,camera:kF,camera_with_flash:SF,video_camera:IF,tv:MF,radio:PF,vhs:qF,film_projector:LF,prayer_beads:OF,twisted_rightwards_arrows:jF,repeat:RF,repeat_one:HF,arrows_clockwise:NF,arrows_counterclockwise:VF,low_brightness:zF,high_brightness:$F,mute:GF,speaker:KF,sound:UF,loud_sound:WF,battery:YF,electric_plug:XF,mag:JF,mag_right:QF,lock_with_ink_pen:ZF,closed_lock_with_key:eC,key:uC,lock:tC,unlock:rC,bell:aC,no_bell:nC,bookmark:iC,link:dC,radio_button:oC,back:sC,end:DC,on:lC,soon:cC,top:fC,underage:mC,keycap_ten:gC,capital_abcd:pC,abcd:_C,symbols:hC,abc:bC,fire:FC,flashlight:CC,wrench:EC,hammer:wC,nut_and_bolt:yC,hocho:vC,knife:TC,gun:BC,microscope:xC,telescope:AC,crystal_ball:kC,six_pointed_star:SC,beginner:IC,trident:MC,black_square_button:PC,white_square_button:qC,red_circle:LC,large_blue_circle:OC,large_orange_diamond:jC,large_blue_diamond:RC,small_orange_diamond:HC,small_blue_diamond:NC,small_red_triangle:VC,small_red_triangle_down:zC,arrow_up_small:$C,arrow_down_small:GC,om_symbol:KC,dove_of_peace:UC,kaaba:WC,mosque:YC,synagogue:XC,menorah_with_nine_branches:JC,clock1:QC,clock2:ZC,clock3:e6,clock4:u6,clock5:t6,clock6:r6,clock7:a6,clock8:n6,clock9:i6,clock10:d6,clock11:o6,clock12:s6,clock130:D6,clock230:l6,clock330:c6,clock430:f6,clock530:m6,clock630:g6,clock730:p6,clock830:_6,clock930:h6,clock1030:b6,clock1130:F6,clock1230:C6,candle:E6,mantelpiece_clock:w6,hole:y6,man_in_business_suit_levitating:v6,"female-detective":T6,"male-detective":B6,sleuth_or_spy:x6,dark_sunglasses:A6,spider:k6,spider_web:S6,joystick:I6,man_dancing:M6,linked_paperclips:P6,lower_left_ballpoint_pen:q6,lower_left_fountain_pen:L6,lower_left_paintbrush:O6,lower_left_crayon:j6,raised_hand_with_fingers_splayed:R6,middle_finger:H6,reversed_hand_with_middle_finger_extended:N6,"spock-hand":V6,black_heart:z6,desktop_computer:$6,printer:G6,three_button_mouse:K6,trackball:U6,frame_with_picture:W6,card_index_dividers:Y6,card_file_box:X6,file_cabinet:J6,wastebasket:Q6,spiral_note_pad:Z6,spiral_calendar_pad:eE,compression:uE,old_key:tE,rolled_up_newspaper:rE,dagger_knife:aE,speaking_head_in_silhouette:nE,left_speech_bubble:iE,right_anger_bubble:dE,ballot_box_with_ballot:oE,world_map:sE,mount_fuji:DE,tokyo_tower:lE,statue_of_liberty:cE,japan:fE,moyai:mE,grinning:gE,grin:pE,joy:_E,smiley:hE,smile:bE,sweat_smile:FE,laughing:CE,satisfied:EE,innocent:wE,smiling_imp:yE,wink:vE,blush:TE,yum:BE,relieved:xE,heart_eyes:AE,sunglasses:kE,smirk:SE,neutral_face:IE,expressionless:ME,unamused:PE,sweat:qE,pensive:LE,confused:OE,confounded:jE,kissing:RE,kissing_heart:HE,kissing_smiling_eyes:NE,kissing_closed_eyes:VE,stuck_out_tongue:zE,stuck_out_tongue_winking_eye:$E,stuck_out_tongue_closed_eyes:GE,disappointed:KE,worried:UE,angry:WE,rage:YE,cry:XE,persevere:JE,triumph:QE,disappointed_relieved:ZE,frowning:ew,anguished:uw,fearful:tw,weary:rw,sleepy:aw,tired_face:nw,grimacing:iw,sob:dw,face_exhaling:ow,open_mouth:sw,hushed:Dw,cold_sweat:lw,scream:cw,astonished:fw,flushed:mw,sleeping:gw,face_with_spiral_eyes:pw,dizzy_face:_w,face_in_clouds:hw,no_mouth:bw,mask:Fw,smile_cat:Cw,joy_cat:Ew,smiley_cat:ww,heart_eyes_cat:yw,smirk_cat:vw,kissing_cat:Tw,pouting_cat:Bw,crying_cat_face:xw,scream_cat:Aw,slightly_frowning_face:kw,slightly_smiling_face:Sw,upside_down_face:Iw,face_with_rolling_eyes:Mw,"woman-gesturing-no":Pw,no_good:qw,"man-gesturing-no":Lw,"woman-gesturing-ok":Ow,ok_woman:jw,"man-gesturing-ok":Rw,"woman-bowing":Hw,"man-bowing":Nw,bow:Vw,see_no_evil:zw,hear_no_evil:$w,speak_no_evil:Gw,"woman-raising-hand":Kw,raising_hand:Uw,"man-raising-hand":Ww,raised_hands:Yw,"woman-frowning":Xw,person_frowning:Jw,"man-frowning":Qw,"woman-pouting":Zw,person_with_pouting_face:ey,"man-pouting":uy,pray:ty,rocket:ry,helicopter:ay,steam_locomotive:ny,railway_car:iy,bullettrain_side:dy,bullettrain_front:oy,train2:sy,metro:Dy,light_rail:ly,station:cy,tram:fy,train:my,bus:gy,oncoming_bus:py,trolleybus:_y,busstop:hy,minibus:by,ambulance:Fy,fire_engine:Cy,police_car:Ey,oncoming_police_car:wy,taxi:yy,oncoming_taxi:vy,car:Ty,red_car:By,oncoming_automobile:xy,blue_car:Ay,truck:ky,articulated_lorry:Sy,tractor:Iy,monorail:My,mountain_railway:Py,suspension_railway:qy,mountain_cableway:Ly,aerial_tramway:Oy,ship:jy,"woman-rowing-boat":Ry,"man-rowing-boat":Hy,rowboat:Ny,speedboat:Vy,traffic_light:zy,vertical_traffic_light:$y,construction:Gy,rotating_light:Ky,triangular_flag_on_post:Uy,door:Wy,no_entry_sign:Yy,smoking:Xy,no_smoking:Jy,put_litter_in_its_place:Qy,do_not_litter:Zy,potable_water:ev,"non-potable_water":uv,bike:tv,no_bicycles:rv,"woman-biking":av,"man-biking":nv,bicyclist:iv,"woman-mountain-biking":dv,"man-mountain-biking":ov,mountain_bicyclist:sv,"woman-walking":Dv,"man-walking":lv,walking:cv,no_pedestrians:fv,children_crossing:mv,mens:gv,womens:pv,restroom:_v,baby_symbol:hv,toilet:bv,wc:Fv,shower:Cv,bath:Ev,bathtub:wv,passport_control:yv,customs:vv,baggage_claim:Tv,left_luggage:Bv,couch_and_lamp:xv,sleeping_accommodation:Av,shopping_bags:kv,bellhop_bell:Sv,bed:Iv,place_of_worship:Mv,octagonal_sign:Pv,shopping_trolley:qv,hindu_temple:Lv,hut:Ov,elevator:jv,hammer_and_wrench:Rv,shield:Hv,oil_drum:Nv,motorway:Vv,railway_track:zv,motor_boat:$v,small_airplane:Gv,airplane_departure:Kv,airplane_arriving:Uv,satellite:Wv,passenger_ship:Yv,scooter:Xv,motor_scooter:Jv,canoe:Qv,sled:Zv,flying_saucer:e7,skateboard:u7,auto_rickshaw:t7,pickup_truck:r7,roller_skate:a7,large_orange_circle:n7,large_yellow_circle:i7,large_green_circle:d7,large_purple_circle:o7,large_brown_circle:s7,large_red_square:D7,large_blue_square:l7,large_orange_square:c7,large_yellow_square:f7,large_green_square:m7,large_purple_square:g7,large_brown_square:p7,pinched_fingers:_7,white_heart:h7,brown_heart:b7,pinching_hand:F7,zipper_mouth_face:C7,money_mouth_face:E7,face_with_thermometer:w7,nerd_face:y7,thinking_face:v7,face_with_head_bandage:T7,robot_face:B7,hugging_face:x7,the_horns:A7,sign_of_the_horns:k7,call_me_hand:S7,raised_back_of_hand:I7,"left-facing_fist":M7,"right-facing_fist":P7,handshake:q7,crossed_fingers:L7,hand_with_index_and_middle_fingers_crossed:O7,i_love_you_hand_sign:j7,face_with_cowboy_hat:R7,clown_face:H7,nauseated_face:N7,rolling_on_the_floor_laughing:V7,drooling_face:z7,lying_face:$7,"woman-facepalming":G7,"man-facepalming":K7,face_palm:U7,sneezing_face:W7,face_with_raised_eyebrow:Y7,face_with_one_eyebrow_raised:X7,"star-struck":J7,grinning_face_with_star_eyes:Q7,zany_face:Z7,grinning_face_with_one_large_and_one_small_eye:eT,shushing_face:uT,face_with_finger_covering_closed_lips:tT,face_with_symbols_on_mouth:rT,serious_face_with_symbols_covering_mouth:aT,face_with_hand_over_mouth:nT,smiling_face_with_smiling_eyes_and_hand_covering_mouth:iT,face_vomiting:dT,face_with_open_mouth_vomiting:oT,exploding_head:sT,shocked_face_with_exploding_head:DT,pregnant_woman:lT,"breast-feeding":cT,palms_up_together:fT,selfie:mT,prince:gT,woman_in_tuxedo:pT,man_in_tuxedo:_T,person_in_tuxedo:hT,mrs_claus:bT,mother_christmas:FT,"woman-shrugging":CT,"man-shrugging":ET,shrug:wT,"woman-cartwheeling":yT,"man-cartwheeling":vT,person_doing_cartwheel:TT,"woman-juggling":BT,"man-juggling":xT,juggling:AT,fencer:kT,"woman-wrestling":ST,"man-wrestling":IT,wrestlers:MT,"woman-playing-water-polo":PT,"man-playing-water-polo":qT,water_polo:LT,"woman-playing-handball":OT,"man-playing-handball":jT,handball:RT,diving_mask:HT,wilted_flower:NT,drum_with_drumsticks:VT,clinking_glasses:zT,tumbler_glass:$T,spoon:GT,goal_net:KT,first_place_medal:UT,second_place_medal:WT,third_place_medal:YT,boxing_glove:XT,martial_arts_uniform:JT,curling_stone:QT,lacrosse:ZT,softball:eB,flying_disc:uB,croissant:tB,avocado:rB,cucumber:aB,bacon:nB,potato:iB,carrot:dB,baguette_bread:oB,green_salad:sB,shallow_pan_of_food:DB,stuffed_flatbread:lB,egg:cB,glass_of_milk:fB,peanuts:mB,kiwifruit:gB,pancakes:pB,dumpling:_B,fortune_cookie:hB,takeout_box:bB,chopsticks:FB,bowl_with_spoon:CB,cup_with_straw:EB,coconut:wB,broccoli:yB,pie:vB,pretzel:TB,cut_of_meat:BB,sandwich:xB,canned_food:AB,leafy_green:kB,mango:SB,moon_cake:IB,bagel:MB,smiling_face_with_3_hearts:PB,yawning_face:qB,smiling_face_with_tear:LB,partying_face:OB,woozy_face:jB,hot_face:RB,cold_face:HB,ninja:NB,disguised_face:VB,pleading_face:zB,sari:$B,lab_coat:GB,goggles:KB,hiking_boot:UB,womans_flat_shoe:WB,crab:YB,lion_face:XB,scorpion:JB,turkey:QB,unicorn_face:ZB,eagle:ex,duck:ux,bat:tx,shark:rx,owl:ax,fox_face:nx,butterfly:ix,deer:dx,gorilla:ox,lizard:sx,rhinoceros:Dx,shrimp:lx,squid:cx,giraffe_face:fx,zebra_face:mx,hedgehog:gx,sauropod:px,"t-rex":_x,cricket:hx,kangaroo:bx,llama:Fx,peacock:Cx,hippopotamus:Ex,parrot:wx,raccoon:yx,lobster:vx,mosquito:Tx,microbe:Bx,badger:xx,swan:Ax,mammoth:kx,dodo:Sx,sloth:Ix,otter:Mx,orangutan:Px,skunk:qx,flamingo:Lx,oyster:Ox,beaver:jx,bison:Rx,seal:Hx,guide_dog:Nx,probing_cane:Vx,bone:zx,leg:$x,foot:Gx,tooth:Kx,female_superhero:Ux,male_superhero:Wx,superhero:Yx,female_supervillain:Xx,male_supervillain:Jx,supervillain:Qx,safety_vest:Zx,ear_with_hearing_aid:e9,motorized_wheelchair:u9,manual_wheelchair:t9,mechanical_arm:r9,mechanical_leg:a9,cheese_wedge:n9,cupcake:i9,salt:d9,beverage_box:o9,garlic:s9,onion:D9,falafel:l9,waffle:c9,butter:f9,mate_drink:m9,ice_cube:g9,bubble_tea:p9,woman_standing:_9,man_standing:h9,standing_person:b9,woman_kneeling:F9,man_kneeling:C9,kneeling_person:E9,deaf_woman:w9,deaf_man:y9,deaf_person:v9,face_with_monocle:T9,farmer:B9,cook:x9,person_feeding_baby:A9,mx_claus:k9,student:S9,singer:I9,artist:M9,teacher:P9,factory_worker:q9,technologist:L9,office_worker:O9,mechanic:j9,scientist:R9,astronaut:H9,firefighter:N9,people_holding_hands:V9,person_with_probing_cane:z9,red_haired_person:$9,curly_haired_person:G9,bald_person:K9,white_haired_person:U9,person_in_motorized_wheelchair:W9,person_in_manual_wheelchair:Y9,health_worker:X9,judge:J9,pilot:Q9,adult:Z9,child:eA,older_adult:uA,woman_with_beard:tA,man_with_beard:rA,bearded_person:aA,person_with_headscarf:nA,woman_in_steamy_room:iA,man_in_steamy_room:dA,person_in_steamy_room:oA,woman_climbing:sA,person_climbing:DA,man_climbing:lA,woman_in_lotus_position:cA,person_in_lotus_position:fA,man_in_lotus_position:mA,female_mage:gA,mage:pA,male_mage:_A,female_fairy:hA,fairy:bA,male_fairy:FA,female_vampire:CA,vampire:EA,male_vampire:wA,mermaid:yA,merman:vA,merperson:TA,female_elf:BA,male_elf:xA,elf:AA,female_genie:kA,male_genie:SA,genie:IA,female_zombie:MA,male_zombie:PA,zombie:qA,brain:LA,orange_heart:OA,billed_cap:jA,scarf:RA,gloves:HA,coat:NA,socks:VA,red_envelope:zA,firecracker:$A,jigsaw:GA,test_tube:KA,petri_dish:UA,dna:WA,compass:YA,abacus:XA,fire_extinguisher:JA,toolbox:QA,bricks:ZA,magnet:e4,luggage:u4,lotion_bottle:t4,thread:r4,yarn:a4,safety_pin:n4,teddy_bear:i4,broom:d4,basket:o4,roll_of_paper:s4,soap:D4,sponge:l4,receipt:c4,nazar_amulet:f4,ballet_shoes:m4,"one-piece_swimsuit":g4,briefs:p4,shorts:_4,thong_sandal:h4,drop_of_blood:b4,adhesive_bandage:F4,stethoscope:C4,"yo-yo":E4,kite:w4,parachute:y4,boomerang:v4,magic_wand:T4,pinata:B4,nesting_dolls:x4,ringed_planet:A4,chair:k4,razor:S4,axe:I4,diya_lamp:M4,banjo:P4,military_helmet:q4,accordion:L4,long_drum:O4,coin:j4,carpentry_saw:R4,screwdriver:H4,ladder:N4,hook:V4,mirror:z4,window:$4,plunger:G4,sewing_needle:K4,knot:U4,bucket:W4,mouse_trap:Y4,toothbrush:X4,headstone:J4,placard:Q4,rock:Z4,fly:e1,worm:u1,beetle:t1,cockroach:r1,potted_plant:a1,wood:n1,feather:i1,anatomical_heart:d1,lungs:o1,people_hugging:s1,blueberries:D1,bell_pepper:l1,olive:c1,flatbread:f1,tamale:m1,fondue:g1,teapot:p1,bangbang:_1,interrobang:h1,tm:b1,information_source:F1,left_right_arrow:C1,arrow_up_down:E1,arrow_upper_left:w1,arrow_upper_right:y1,arrow_lower_right:v1,arrow_lower_left:T1,leftwards_arrow_with_hook:B1,arrow_right_hook:x1,watch:A1,hourglass:k1,keyboard:S1,eject:I1,fast_forward:M1,rewind:P1,arrow_double_up:q1,arrow_double_down:L1,black_right_pointing_double_triangle_with_vertical_bar:O1,black_left_pointing_double_triangle_with_vertical_bar:j1,black_right_pointing_triangle_with_double_vertical_bar:R1,alarm_clock:H1,stopwatch:N1,timer_clock:V1,hourglass_flowing_sand:z1,double_vertical_bar:$1,black_square_for_stop:G1,black_circle_for_record:K1,m:U1,black_small_square:W1,white_small_square:Y1,arrow_forward:X1,arrow_backward:J1,white_medium_square:Q1,black_medium_square:Z1,white_medium_small_square:ek,black_medium_small_square:uk,sunny:tk,cloud:rk,umbrella:ak,snowman:nk,comet:ik,phone:dk,telephone:ok,ballot_box_with_check:sk,shamrock:Dk,point_up:lk,skull_and_crossbones:ck,radioactive_sign:fk,biohazard_sign:mk,orthodox_cross:gk,star_and_crescent:pk,peace_symbol:_k,yin_yang:hk,wheel_of_dharma:bk,white_frowning_face:Fk,relaxed:Ck,female_sign:Ek,male_sign:wk,gemini:yk,cancer:vk,leo:Tk,virgo:Bk,libra:xk,scorpius:Ak,chess_pawn:kk,spades:Sk,clubs:Ik,hearts:Mk,diamonds:Pk,hotsprings:qk,recycle:Lk,infinity:Ok,wheelchair:jk,hammer_and_pick:Rk,crossed_swords:Hk,medical_symbol:Nk,staff_of_aesculapius:Vk,scales:zk,alembic:$k,gear:Gk,atom_symbol:Kk,fleur_de_lis:Uk,warning:Wk,zap:Yk,transgender_symbol:Xk,white_circle:Jk,black_circle:Qk,coffin:Zk,funeral_urn:eS,soccer:uS,baseball:tS,snowman_without_snow:rS,partly_sunny:aS,thunder_cloud_and_rain:nS,ophiuchus:iS,pick:dS,helmet_with_white_cross:oS,chains:sS,no_entry:DS,shinto_shrine:lS,church:cS,mountain:fS,umbrella_on_ground:mS,fountain:gS,golf:pS,ferry:_S,boat:hS,sailboat:bS,skier:FS,ice_skate:CS,"woman-bouncing-ball":ES,"man-bouncing-ball":wS,person_with_ball:yS,tent:vS,fuelpump:TS,scissors:BS,airplane:xS,email:AS,envelope:kS,fist:SS,hand:IS,raised_hand:MS,v:PS,writing_hand:qS,pencil2:LS,black_nib:OS,heavy_check_mark:jS,heavy_multiplication_x:RS,latin_cross:HS,star_of_david:NS,eight_spoked_asterisk:VS,eight_pointed_black_star:zS,snowflake:$S,sparkle:GS,x:KS,negative_squared_cross_mark:US,heavy_heart_exclamation_mark_ornament:WS,heart_on_fire:YS,mending_heart:XS,heart:JS,arrow_right:QS,curly_loop:ZS,loop:eI,arrow_heading_up:uI,arrow_heading_down:tI,arrow_left:rI,arrow_up:aI,arrow_down:nI,black_large_square:iI,white_large_square:dI,star:oI,o:sI,wavy_dash:DI,part_alternation_mark:lI,congratulations:cI,secret:fI};var mI=(0,Gu.default)(),re=class extends $u.FuzzySuggestModal{constructor(u,r){super(u.app);this.plugin=u,this.metaManager=u.metaManager,this.containerEl.addClass("banner-icon-modal"),this.targetFile=r,this.emojis=Object.entries(zu).map(([a,n])=>({code:a,emoji:n})),this.limit=50,this.setPlaceholder("Pick an emoji to use as an icon")}getItems(){return this.inputEl.value.length?this.emojis:[]}getItemText(u){return u.code}getSuggestions(u){let r=u.match(mI)?.join("");return r?[{item:{code:"Paste inputted emoji(s)",emoji:r},match:{score:1,matches:[]}}]:super.getSuggestions(u)}renderSuggestion(u,r){super.renderSuggestion(u,r);let{useTwemoji:a}=this.plugin.settings,{emoji:n}=u.item,i=a?Le.parse(n):`<span class="regular-emoji">${n} </span>`;r.insertAdjacentHTML("afterbegin",i)}async onChooseItem(u){await this.metaManager.upsertBannerData(this.targetFile,{icon:u.emoji})}};var gI=function(){function t(u,r){for(var a=0;a<r.length;a++){var n=r[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(u,n.key,n)}}return function(u,r,a){return r&&t(u.prototype,r),a&&t(u,a),u}}(),pI=_I(["",""],["",""]);function _I(t,u){return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(u)}}))}function hI(t,u){if(!(t instanceof u))throw new TypeError("Cannot call a class as a function")}var bI=function(){function t(){for(var u=this,r=arguments.length,a=Array(r),n=0;n<r;n++)a[n]=arguments[n];return hI(this,t),this.tag=function(i){for(var s=arguments.length,l=Array(s>1?s-1:0),D=1;D<s;D++)l[D-1]=arguments[D];return typeof i=="function"?u.interimTag.bind(u,i):typeof i=="string"?u.transformEndResult(i):(i=i.map(u.transformString.bind(u)),u.transformEndResult(i.reduce(u.processSubstitutions.bind(u,l))))},a.length>0&&Array.isArray(a[0])&&(a=a[0]),this.transformers=a.map(function(i){return typeof i=="function"?i():i}),this.tag}return gI(t,[{key:"interimTag",value:function(r,a){for(var n=arguments.length,i=Array(n>2?n-2:0),s=2;s<n;s++)i[s-2]=arguments[s];return this.tag(pI,r.apply(void 0,[a].concat(i)))}},{key:"processSubstitutions",value:function(r,a,n){var i=this.transformSubstitution(r.shift(),a);return"".concat(a,i,n)}},{key:"transformString",value:function(r){var a=function(i,s){return s.onString?s.onString(i):i};return this.transformers.reduce(a,r)}},{key:"transformSubstitution",value:function(r,a){var n=function(s,l){return l.onSubstitution?l.onSubstitution(s,a):s};return this.transformers.reduce(n,r)}},{key:"transformEndResult",value:function(r){var a=function(i,s){return s.onEndResult?s.onEndResult(i):i};return this.transformers.reduce(a,r)}}]),t}(),E=bI;var FI=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return{onEndResult:function(a){if(u==="")return a.trim();if(u=u.toLowerCase(),u==="start"||u==="left")return a.replace(/^\s*/,"");if(u==="end"||u==="right")return a.replace(/\s*$/,"");throw new Error("Side not supported: "+u)}}},w=FI;function CI(t){if(Array.isArray(t)){for(var u=0,r=Array(t.length);u<t.length;u++)r[u]=t[u];return r}else return Array.from(t)}var EI=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"initial";return{onEndResult:function(a){if(u==="initial"){var n=a.match(/^[^\S\n]*(?=\S)/gm),i=n&&Math.min.apply(Math,CI(n.map(function(l){return l.length})));if(i){var s=new RegExp("^.{"+i+"}","gm");return a.replace(s,"")}return a}if(u==="all")return a.replace(/^[^\S\n]+/gm,"");throw new Error("Unknown type: "+u)}}},P=EI;var wI=function(u,r){return{onEndResult:function(n){if(u==null||r==null)throw new Error("replaceResultTransformer requires at least 2 arguments.");return n.replace(u,r)}}},H=wI;var yI=function(u,r){return{onSubstitution:function(n,i){if(u==null||r==null)throw new Error("replaceSubstitutionTransformer requires at least 2 arguments.");return n==null?n:n.toString().replace(u,r)}}},K=yI;var TI={separator:"",conjunction:"",serial:!1},BI=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:TI;return{onSubstitution:function(a,n){if(Array.isArray(a)){var i=a.length,s=u.separator,l=u.conjunction,D=u.serial,c=n.match(/(\n?[^\S\n]+)$/);if(c?a=a.join(s+c[1]):a=a.join(s+" "),l&&i>1){var b=a.lastIndexOf(s);a=a.slice(0,b)+(D?s:"")+" "+l+a.slice(b+1)}}return a}}},B=BI;var xI=function(u){return{onSubstitution:function(a,n){if(u!=null&&typeof u=="string")typeof a=="string"&&a.includes(u)&&(a=a.split(u));else throw new Error("You need to specify a string character to split by.");return a}}},ae=xI;var Ku=function(u){return u!=null&&!Number.isNaN(u)&&typeof u!="boolean"},AI=function(){return{onSubstitution:function(r){return Array.isArray(r)?r.filter(Ku):Ku(r)?r:""}}},Oe=AI;var Oj=new E(B({separator:","}),P,w);var Gj=new E(B({separator:",",conjunction:"and"}),P,w);var Zj=new E(B({separator:",",conjunction:"or"}),P,w);var MI=new E(ae(`
`),Oe,B,P,w),z=MI;var ER=new E(ae(`
`),B,P,w,K(/&/g,"&amp;"),K(/</g,"&lt;"),K(/>/g,"&gt;"),K(/"/g,"&quot;"),K(/'/g,"&#x27;"),K(/`/g,"&#x60;"));var AR=new E(H(/(?:\n(?:\s*))+/g," "),w);var LR=new E(H(/(?:\n\s*)/g,""),w);var $R=new E(B({separator:","}),H(/(?:\s+)/g," "),w);var QR=new E(B({separator:",",conjunction:"or"}),H(/(?:\s+)/g," "),w);var iH=new E(B({separator:",",conjunction:"and"}),H(/(?:\s+)/g," "),w);var mH=new E(B,P,w);var EH=new E(B,H(/(?:\s+)/g," "),w);var AH=new E(P,w);var zI=new E(P("all"),w),je=zI;var X=S(require("obsidian"));var q=S(require("obsidian")),Uu={height:null,style:"solid",showInInternalEmbed:!0,internalEmbedHeight:null,showInPreviewEmbed:!0,previewEmbedHeight:null,frontmatterField:null,bannerDragModifier:"none",iconHorizontalAlignment:"left",iconHorizontalTransform:null,iconVerticalAlignment:"center",iconVerticalTransform:null,useTwemoji:!0,showPreviewInLocalModal:!0,localSuggestionsLimit:null,bannersFolder:null,allowMobileDrag:!1},L={height:250,internalEmbedHeight:200,previewEmbedHeight:120,frontmatterField:"banner",iconHorizontalTransform:"0px",iconVerticalTransform:"0px",localSuggestionsLimit:10,bannersFolder:"/"},$I={solid:"Solid",gradient:"Gradient"},GI={none:"None",shift:"\u21E7 Shift",ctrl:"\u2303 Ctrl",alt:"\u2387 Alt",meta:"\u2318 Meta"},KI={left:"Left",center:"Center",right:"Right",custom:"Custom"},UI={above:"Above",center:"Center",below:"Below",custom:"Custom"},Re=class extends q.PluginSettingTab{constructor(u){super(u.app,u);this.plugin=u,this.containerEl.addClass("banner-settings")}async saveSettings(u,r={}){this.plugin.settings={...this.plugin.settings,...u},await this.plugin.saveData(this.plugin.settings),this.plugin.loadStyles();let{refreshViews:a,reloadSettings:n}=r;n&&this.display(),a&&this.plugin.refreshViews()}display(){let{containerEl:u}=this,{height:r,style:a,showInInternalEmbed:n,internalEmbedHeight:i,showInPreviewEmbed:s,previewEmbedHeight:l,frontmatterField:D,bannerDragModifier:c,iconHorizontalAlignment:b,iconHorizontalTransform:h,iconVerticalAlignment:m,iconVerticalTransform:g,useTwemoji:y,showPreviewInLocalModal:F,localSuggestionsLimit:v,bannersFolder:M,allowMobileDrag:C}=this.plugin.settings;u.empty(),this.createHeader("Banners","A nice, lil' thing to add some flair to your notes"),new q.Setting(u).setName("Banner height").setDesc("Set how big the banner should be in pixels").addText(d=>{d.inputEl.type="number",d.setValue(`${r}`),d.setPlaceholder(`${L.height}`),d.onChange(async p=>this.saveSettings({height:p?parseInt(p):null}))}),new q.Setting(u).setName("Banner style").setDesc("Set a style for all of your banners").addDropdown(d=>d.addOptions($I).setValue(a).onChange(async p=>this.saveSettings({style:p},{refreshViews:!0}))),new q.Setting(u).setName("Show banner in internal embed").setDesc(createFragment(d=>{d.appendText("Choose whether to display the banner in the internal embed. This is the embed that appears when you write "),d.createEl("code",{text:"![[file]]"}),d.appendText(" in a file")})).addToggle(d=>d.setValue(n).onChange(async p=>this.saveSettings({showInInternalEmbed:p},{reloadSettings:!0,refreshViews:!0}))),this.plugin.settings.showInInternalEmbed&&new q.Setting(u).setName("Internal embed banner height").setDesc("Set the banner size inside the internal embed").addText(d=>{d.inputEl.type="number",d.setValue(`${i}`),d.setPlaceholder(`${L.internalEmbedHeight}`),d.onChange(async p=>this.saveSettings({internalEmbedHeight:p?parseInt(p):null}))}),new q.Setting(u).setName("Show banner in preview embed").setDesc(createFragment(d=>{d.appendText("Choose whether to display the banner in the page preview embed. This is the embed that appears from the "),d.createEl("span",{text:"Page Preview ",attr:{style:"color: --var(text-normal)"}}),d.appendText("core plugin")})).addToggle(d=>d.setValue(s).onChange(async p=>this.saveSettings({showInPreviewEmbed:p},{reloadSettings:!0}))),this.plugin.settings.showInPreviewEmbed&&new q.Setting(u).setName("Preview embed banner height").setDesc("Set the banner size inside the page preview embed").addText(d=>{d.inputEl.type="number",d.setValue(`${l}`),d.setPlaceholder(`${L.previewEmbedHeight}`),d.onChange(async p=>this.saveSettings({previewEmbedHeight:p?parseInt(p):null}))}),new q.Setting(u).setName("Frontmatter field name").setDesc(createFragment(d=>{d.appendText("Set a customizable frontmatter field to use for banner data."),d.createEl("br"),d.appendText("For example, the default value "),d.createEl("code",{text:L.frontmatterField}),d.appendText(" will use the fields "),d.createEl("code",{text:L.frontmatterField}),d.appendText(", "),d.createEl("code",{text:`${L.frontmatterField}_x`}),d.appendText(", "),d.createEl("code",{text:`${L.frontmatterField}_y`}),d.appendText(", and so on...")})).addText(d=>d.setValue(D).setPlaceholder(L.frontmatterField).onChange(async p=>this.saveSettings({frontmatterField:p||null},{refreshViews:!0}))),new q.Setting(u).setName("Banner drag modifier key").setDesc(createFragment(d=>{d.appendText("Set a modifier key that must be used to drag a banner."),d.createEl("br"),d.appendText("For example, setting it to "),d.createEl("b",{text:"\u21E7 Shift"}),d.appendText(" means that you must hold down Shift as you drag the banner to move it. This can help to avoid accidental banner shifts.")})).addDropdown(d=>d.addOptions(GI).setValue(c).onChange(async p=>{await this.saveSettings({bannerDragModifier:p},{refreshViews:!0}),this.plugin.toggleBannerCursor(p==="none")})),this.createHeader("Banner Icons","Give people a lil' notion of what your note is about");let j=new q.Setting(u).setName("Horizontal alignment").setDesc(createFragment(d=>{d.appendText("Align the icon horizontally."),d.createEl("br"),d.appendText("If set to "),d.createEl("b",{text:"Custom"}),d.appendText(", you can set an offset, relative to the left side of the note. This can be any valid "),d.createEl("a",{text:"CSS length value",href:"https://developer.mozilla.org/en-US/docs/Learn/CSS/Building_blocks/Values_and_units#lengths"}),d.appendText(", such as "),d.createEl("code",{text:"10px"}),d.appendText(", "),d.createEl("code",{text:"-30%"}),d.appendText(", "),d.createEl("code",{text:"calc(1em + 10px)"}),d.appendText(", and so on...")}));b==="custom"&&j.addText(d=>d.setValue(h).setPlaceholder(L.iconHorizontalTransform).onChange(async p=>this.saveSettings({iconHorizontalTransform:p||null},{refreshViews:!0}))),j.addDropdown(d=>d.addOptions(KI).setValue(b).onChange(async p=>this.saveSettings({iconHorizontalAlignment:p},{reloadSettings:!0,refreshViews:!0})));let x=new q.Setting(u).setName("Vertical alignment").setDesc(createFragment(d=>{d.appendText("Align the icon vertically, relative to a banner (if any)."),d.createEl("br"),d.appendText("If set to "),d.createEl("b",{text:"Custom"}),d.appendText(", you can set an offset, relative to the center of a banner's lower edge. This follows the same format as the setting above.")}));m==="custom"&&x.addText(d=>d.setValue(g).setPlaceholder(L.iconVerticalTransform).onChange(async p=>this.saveSettings({iconVerticalTransform:p||null},{refreshViews:!0}))),x.addDropdown(d=>d.addOptions(UI).setValue(m).onChange(async p=>this.saveSettings({iconVerticalAlignment:p},{reloadSettings:!0,refreshViews:!0}))),new q.Setting(u).setName("Use Twemoji").setDesc(createFragment(d=>{d.appendText("Twitter's emoji have better support here. "),d.createEl("b",{text:"NOTE: "}),d.appendText("This is only applied in the Icon modal and the banner icon in the preview view")})).addToggle(d=>d.setValue(y).onChange(async p=>this.saveSettings({useTwemoji:p},{refreshViews:!0}))),this.createHeader("Local Image Modal",'For the modal that shows when you run the "Add/Change banner with local image" command'),new q.Setting(u).setName("Show preview images").setDesc("Enabling this will display a preview of the images suggested").addToggle(d=>d.setValue(F).onChange(async p=>this.saveSettings({showPreviewInLocalModal:p}))),new q.Setting(u).setName("Suggestions limit").setDesc(createFragment(d=>{d.appendText("Show up to this many suggestions when searching through local images."),d.createEl("br"),d.createEl("b",{text:"NOTE: "}),d.appendText("Using a high number while "),d.createEl("span",{text:"Show preview images ",attr:{style:"color: var(--text-normal)"}}),d.appendText("is on can lead to some slowdowns")})).addText(d=>{d.inputEl.type="number",d.setValue(`${v}`),d.setPlaceholder(`${L.localSuggestionsLimit}`),d.onChange(async p=>this.saveSettings({localSuggestionsLimit:p?parseInt(p):null}))}),new q.Setting(u).setName("Banners folder").setDesc(createFragment(d=>{d.appendText("Select a folder to exclusively search for banner files in."),d.createEl("br"),d.appendText("If empty, it will search the entire vault for image files")})).addText(d=>d.setValue(M).setPlaceholder(L.bannersFolder).onChange(async p=>this.saveSettings({bannersFolder:p||null}))),this.createHeader("Experimental Things","Not as well-tested and probably finicky"),new q.Setting(u).setName("Allow mobile drag").setDesc(createFragment(d=>{d.appendText("Allow dragging the banner on mobile devices."),d.createEl("br"),d.createEl("b",{text:"NOTE: "}),d.appendText("App reload might be necessary")})).addToggle(d=>d.setValue(C).onChange(async p=>this.saveSettings({allowMobileDrag:p},{refreshViews:!0})))}createHeader(u,r=null){let a=this.containerEl.createDiv({cls:"setting-item setting-item-heading banner-setting-header"});a.createEl("p",{text:u,cls:"banner-setting-header-title"}),r&&a.createEl("p",{text:r,cls:"banner-setting-header-description"})}};var Wu=["apng","avif","gif","jpg","jpeg","jpe","jif","jfif","png","webp"],He=class extends X.FuzzySuggestModal{constructor(u,r){super(u.app);this.plugin=u,this.vault=u.app.vault,this.metadataCache=u.app.metadataCache,this.metaManager=u.metaManager,this.containerEl.addClass("banner-local-image-modal"),this.targetFile=r,this.limit=this.plugin.getSettingValue("localSuggestionsLimit"),this.setPlaceholder("Pick an image to use as a banner")}getItems(){let u=this.plugin.getSettingValue("bannersFolder");if(u===L.bannersFolder)return this.vault.getFiles().filter(a=>Wu.includes(a.extension));let r=this.vault.getAbstractFileByPath(u);return!r||!(r instanceof X.TFolder)?(new X.Notice(createFragment(a=>{a.appendText("ERROR! Make sure that you set the "),a.createEl("b",{text:"Banners folder"}),a.appendText(" to a valid folder in the settings.")}),7e3),this.close(),[]):this.getImagesInFolder(r)}getItemText(u){return u.path}renderSuggestion(u,r){super.renderSuggestion(u,r);let{showPreviewInLocalModal:a}=this.plugin.settings;if(a){let n=r.innerHTML;r.addClass("banner-suggestion-item"),r.innerHTML=z`
        <p class="suggestion-text">${n}</p>
        <div class="suggestion-image-wrapper">
          <img src="${this.vault.getResourcePath(u.item)}" />
        </div>
      `}}async onChooseItem(u){let r=this.metadataCache.fileToLinktext(u,this.targetFile.path);await this.metaManager.upsertBannerData(this.targetFile,{src:`"![[${r}]]"`})}getImagesInFolder(u){let r=[];return u.children.forEach(a=>{a instanceof X.TFolder&&r.push(...this.getImagesInFolder(u));let n=a;Wu.includes(n.extension)&&r.push(n)}),r}};var lu=S(require("obsidian"));var WI=["src","x","y","icon","lock"],Ne=class{constructor(u){this.plugin=u,this.metadata=u.app.metadataCache,this.vault=u.app.vault}getBannerData(u){if(!u)return null;let r=this.plugin.getSettingValue("frontmatterField"),{[r]:a,[`${r}_x`]:n,[`${r}_y`]:i,[`${r}_icon`]:s,[`${r}_lock`]:l}=u;return{src:a,x:this.parseBannerPos(n),y:this.parseBannerPos(i),icon:s,lock:typeof l=="boolean"?l:l==="true"}}getBannerDataFromFile(u){if(!u)return null;let{frontmatter:r}=this.metadata.getFileCache(u);return this.getBannerData(r)}async upsertBannerData(u,r){let a=u instanceof lu.TFile?u:this.getFileByPath(u);if(!a)return;let{src:n,x:i,y:s,icon:l,lock:D}=r,c=this.plugin.getSettingValue("frontmatterField"),b={...n!==void 0&&{[c]:n},...i!==void 0&&{[`${c}_x`]:i},...s!==void 0&&{[`${c}_y`]:s},...l!==void 0&&{[`${c}_icon`]:l},...D!==void 0&&{[`${c}_lock`]:D}},h=Object.keys(b),g=(await this.vault.read(a)).split(`
`),y=g.indexOf("---"),F=y!==-1&&g.slice(0,y).every(C=>!C),v=!1;if(F){let C;for(C=y+1;C<g.length&&h.length&&!g[C].startsWith("---");C++){let[x,d]=g[C].split(": "),p=h.indexOf(x);if(p===-1)continue;let _=b[x];d!==_&&(g[C]=`${x}: ${_}`,v=!0),h.splice(p,1)}h.length&&(g.splice(C,0,...this.formatYamlFields(h,b)),C+=h.length,v=!0),g.indexOf("---",C)===-1&&(g.splice(C,0,"---"),v=!0)}else g.unshift(je`
        ---
        ${this.formatYamlFields(h,b).join(`
`)}
        ---
      `),v=!0;if(!v)return;let M=g.join(`
`);await this.vault.modify(a,M)}async removeBannerData(u,r=WI){let a=Array.isArray(r)?r:[r],n=a.indexOf("src");n>-1&&a.splice(n,1,"");let i=this.plugin.getSettingValue("frontmatterField"),s=a.map(F=>`${i}${F?`_${F}`:""}`),{frontmatter:{position:l,...D}}=this.metadata.getFileCache(u),c=Object.keys(D??{});if(!D||!s.some(F=>c.includes(F)))return;let h=(await this.vault.read(u)).split(`
`),{line:m}=l.start,{line:g}=l.end;if(c.every(F=>s.includes(F)))h.splice(m,g-m+1);else for(let F=m+1;F<g&&s.length;F++){let[v]=h[F].split(": "),M=s.indexOf(v);M!==-1&&(h.splice(F,1),s.splice(M,1),F--,g--)}let y=h.join(`
`);await this.vault.modify(u,y)}getFileByPath(u){let r=this.vault.getAbstractFileByPath(u);return r instanceof lu.TFile?r:null}parseBannerPos(u){if(u!==void 0)return typeof u=="number"?u:parseFloat(u)}formatYamlFields(u,r){return u.map(a=>[a,`${r[a]}`]).sort((a,n)=>a[0].localeCompare(n[0])).map(([a,n])=>`${a}: ${n}`)}};var vt=S(require("obsidian"));var wt=S(require("obsidian")),Ce=S(Et());var yt=t=>{let{clientX:u,clientY:r}=t instanceof MouseEvent?t:t.targetTouches[0];return{x:u,y:r}},MM=(t,u,r)=>{if(!r&&t instanceof MouseEvent)return;let{x:a,y:n}=yt(t),{clientHeight:i,clientWidth:s,naturalHeight:l,naturalWidth:D}=t.target;u.x=a,u.y=n,u.isDragging=!0,u.vertical=l/D>=i/s},PM=(t,u)=>{if(!u.isDragging)return;let r=t.target,{x:a,y:n}=yt(t),i={x:(u.x-a)/r.clientWidth*100,y:(u.y-n)/r.clientHeight*100};u.x=a,u.y=n;let[s,l]=r.style.objectPosition.split(" ").map(D=>parseFloat(D));if(u.vertical){let D=(0,Ce.default)(l+i.y,0,100);r.style.objectPosition=`${s}% ${D}%`}else{let D=(0,Ce.default)(s+i.x,0,100);r.style.objectPosition=`${D}% ${l}%`}},qM=async(t,u,r,a)=>{if(!r.isDragging)return;r.isDragging=!1;let[n,i]=t.style.objectPosition.split(" ").map(s=>Math.round(parseFloat(s)*1e3)/1e5);await a.metaManager.upsertBannerData(u,r.vertical?{y:i}:{x:n})},LM=(t,u,r)=>{if(/^\!\[\[.+\]\]$/.test(u)){let i=u.slice(3,-2),s=t.metadataCache.getFirstLinkpathDest(i,r);return s?t.vault.getResourcePath(s):i}let a=u.startsWith("/")?u.slice(1):u,n=t.vault.getAbstractFileByPath(a);return n instanceof wt.TFile?t.vault.getResourcePath(n):u},OM=(t,u,r,a,n,i=!1)=>{let{src:s,x:l=.5,y:D=.5,lock:c}=u,b={x:null,y:null,isDragging:!1,vertical:!0},h=!i&&!c,m=document.createElement("div");m.className="banner-message",m.innerHTML=z`
    <div class="spinner">
      <div class="bounce1"></div>
      <div class="bounce2"></div>
      <div class="bounce3"></div>
    </div>
  `;let g=document.createElement("img"),y=(0,Ce.default)(l,0,1),F=(0,Ce.default)(D,0,1);g.className="banner-image full-width",g.style.objectPosition=`${y*100}% ${F*100}%`,g.draggable=!1,g.onload=()=>a.addClass("loaded"),g.onerror=()=>{m.innerHTML=`<p>Error loading banner image! Is the <code>${t.getSettingValue("frontmatterField")}</code> field valid?</p>`,a.addClass("error")};let v=[];if(h){g.classList.toggle("draggable",t.settings.bannerDragModifier==="none"||t.holdingDragModKey);let C=d=>MM(d,b,t.holdingDragModKey),j=d=>PM(d,b),x=()=>qM(g,r,b,t);v.push({target:g,ev:"mousedown",func:C},{target:g,ev:"mousemove",func:j},{target:n.parentElement,ev:"mouseup",func:x}),t.settings.allowMobileDrag&&v.push({target:g,ev:"touchstart",func:C},{target:g,ev:"touchmove",func:j},{target:n.parentElement,ev:"touchend",func:x})}v.forEach(({target:C,ev:j,func:x})=>C.addEventListener(j,x));let M=()=>v.forEach(({target:C,ev:j,func:x})=>C.removeEventListener(j,x));return g.src=LM(t,s,r),[[m,g],M]},$e=OM;var Ge=class extends vt.MarkdownRenderChild{constructor(u,r,a,n,i){super(document.createElement("div"));this.plugin=u,this.wrapper=r,this.ctx=a,this.bannerData=n,this.isEmbed=i,this.removeListeners=()=>{}}onload(){let{style:u}=this.plugin.settings,{containerEl:r,sourcePath:a}=this.ctx;this.wrapper.addClass("obsidian-banner-wrapper"),this.containerEl.addClass("obsidian-banner","cm5-banner",u);let[n,i]=$e(this.plugin,this.bannerData,a,this.containerEl,r,this.isEmbed);this.containerEl.append(...n),this.removeListeners=i,this.wrapper.prepend(this.containerEl)}onunload(){this.removeListeners()}};var Bt=S(require("obsidian"));var Tt=S(Du());var jM=(0,Tt.default)(),RM=t=>{let{iconHorizontalAlignment:u,iconVerticalAlignment:r}=t.settings,{iconHorizontalTransform:a,iconVerticalTransform:n}=L,i=u==="custom"?t.getSettingValue("iconHorizontalTransform"):a,s=r==="custom"?t.getSettingValue("iconVerticalTransform"):n;return i!==a||s!==n?`translate(${i}, ${s})`:null},HM=(t,u,r)=>{let a=document.createElement("span");a.addClass("icon-box"),a.style.transform=RM(t);let n=u.match(jM)?.join("")??u[0];return t.settings.useTwemoji?a.innerHTML=Le.parse(n):a.textContent=n,a.onclick=async()=>new re(t,r).open(),a},Ke=HM;var Ue=class extends Bt.MarkdownRenderChild{constructor(u,r,a,n){super(document.createElement("div"));this.plugin=u,this.wrapper=r,this.icon=a,this.file=n}onload(){let{iconHorizontalAlignment:u,iconVerticalAlignment:r}=this.plugin.settings;this.wrapper.addClass("has-banner-icon"),this.containerEl.addClass("obsidian-banner-icon","cm5-banner-icon",`h-${u}`,`v-${r}`);let a=Ke(this.plugin,this.icon,this.file);this.containerEl.append(a),this.wrapper.prepend(this.containerEl)}};var NM=t=>(u,r)=>{if(!u.querySelector("pre.frontmatter"))return;let{showInInternalEmbed:a,showInPreviewEmbed:n}=t.settings,{containerEl:i,frontmatter:s,sourcePath:l}=r,D=t.metaManager.getBannerData(s),c=t.metadataCache.getFirstLinkpathDest(l,"/"),b=i?.parentElement?.parentElement?.parentElement?.parentElement,h=b?.hasClass("internal-embed")??!1,m=b?.hasClass("popover")??!1;D?.icon&&r.addChild(new Ue(t,u,D.icon,c)),D?.src&&(!h||a)&&(!m||n)&&r.addChild(new Ge(t,u,r,D,h||m))},xt=NM;var Un=S(require("obsidian")),G=S(require("@codemirror/view")),Wn=S(require("@codemirror/language"));var At=S(require("@codemirror/view"));var We=class extends At.WidgetType{constructor(u,r,a,n,i){super();this.plugin=u,this.bannerData=r,this.filepath=a,this.contentEl=n,this.settingsFacet=i,this.removeListeners=()=>{}}eq(u){let{bannerData:{src:r,x:a,y:n,lock:i},filepath:s,settingsFacet:l}=u;return this.bannerData.src===r&&this.bannerData.x===a&&this.bannerData.y===n&&this.bannerData.lock===i&&this.filepath===s&&this.settingsFacet===l}toDOM(){let{plugin:u,bannerData:r,filepath:a,contentEl:n}=this,i=document.createElement("div");i.addClass("obsidian-banner","cm6-banner",u.settings.style);let[s,l]=$e(u,r,a,i,n);return i.append(...s),this.removeListeners=l,i}destroy(){this.removeListeners()}};var kt=S(require("@codemirror/view")),Ye=class extends kt.WidgetType{toDOM(){let u=document.createElement("div");return u.addClass("obsidian-banner-spacer"),u}};var St=S(require("@codemirror/view"));var Xe=class extends St.WidgetType{constructor(u,r,a,n){super();this.plugin=u,this.icon=r,this.file=a,this.settingsFacet=n}eq(u){let{icon:r,file:a,settingsFacet:n}=u;return this.icon===r&&this.file===a&&this.settingsFacet===n}toDOM(){let{iconHorizontalAlignment:u,iconVerticalAlignment:r}=this.plugin.settings,a=document.createElement("div");a.addClass("obsidian-banner-icon","cm6-banner-icon",`h-${u}`,`v-${r}`);let n=Ke(this.plugin,this.icon,this.file);return a.append(n),a}};var Su=S(require("@codemirror/state")),Iu=S(Gn()),Kn=(t,u)=>t.length?u.reduce((r,a)=>{let n=t[0][a];return r[a]=n,r},{}):null,$O=["style","frontmatterField","bannerDragModifier"],Z=Su.Facet.define({combine:t=>Kn(t,$O),compare:(t,u)=>(0,Iu.default)(t,u)}),GO=["iconHorizontalAlignment","iconHorizontalTransform","iconVerticalAlignment","iconVerticalTransform","useTwemoji"],ee=Su.Facet.define({combine:t=>Kn(t,GO),compare:(t,u)=>(0,Iu.default)(t,u)});var Yn="def_hmd-frontmatter",KO="atom_hmd-frontmatter",Xn="hmd-frontmatter_string",UO=["hmd-frontmatter","hmd-frontmatter_number","hmd-frontmatter_keyword",Xn],WO=t=>G.ViewPlugin.fromClass(class{constructor(r){this.decor=this.decorate(r.state)}update(r){let{docChanged:a,view:n,state:i,startState:s}=r;(a||i.facet(Z)!==s.facet(Z)||i.facet(ee)!==s.facet(ee))&&(this.decor=this.decorate(n.state))}decorate(r){let a=(0,Wn.syntaxTree)(r).cursor();if(a.firstChild(),a.name!==Yn)return G.Decoration.none;let n={},i;for(;a.nextSibling()&&a.name!==Yn;){let{from:m,to:g,name:y}=a;if(y===KO)i=r.sliceDoc(m,g);else if(UO.includes(y)&&!n[i]){let F=y===Xn,v=r.sliceDoc(m+(F?1:0),g-(F?1:0));n[i]=v}}let s=t.metaManager.getBannerData(n),{src:l,icon:D}=s,{contentEl:c,file:b}=r.field(Un.editorViewField),h=[];if(l){let m=r.facet(Z);h.push(G.Decoration.widget({widget:new We(t,s,b.path,c,m)}),G.Decoration.widget({widget:new Ye}),G.Decoration.line({class:"has-banner"}))}if(D){let m=r.facet(ee);h.push(G.Decoration.widget({widget:new Xe(t,D,b,m)}),G.Decoration.line({class:"has-banner-icon",attributes:{"data-icon-v":m.iconVerticalAlignment}}))}return G.Decoration.set(h.map(m=>m.range(0)),!0)}},{decorations:u=>u.decor}),Jn=WO;var Mu=class extends U.Plugin{constructor(){super(...arguments);this.isDragModHeld=u=>{let r;if(u)switch(this.settings.bannerDragModifier){case"alt":r=u.altKey;break;case"ctrl":r=u.ctrlKey;break;case"meta":r=u.metaKey;break;case"shift":r=u.shiftKey;break;default:r=!0}else r=this.settings.bannerDragModifier==="none";this.holdingDragModKey=r,this.toggleBannerCursor(r)};this.toggleBannerCursor=u=>{document.querySelectorAll(".banner-image").forEach(r=>r.toggleClass("draggable",u))}}async onload(){console.log("Loading Banners..."),this.settings=Object.assign({},Uu,await this.loadData()),this.workspace=this.app.workspace,this.vault=this.app.vault,this.metadataCache=this.app.metadataCache,this.metaManager=new Ne(this),this.holdingDragModKey=!1,this.loadProcessor(),this.loadExtension(),this.loadCommands(),this.loadStyles(),this.loadListeners(),this.loadPrecheck(),this.addSettingTab(new Re(this)),this.refreshViews()}async onunload(){console.log("Unloading Banners..."),this.unloadListeners(),this.unloadBanners(),this.unloadStyles()}loadListeners(){window.addEventListener("keydown",this.isDragModHeld),window.addEventListener("keyup",this.isDragModHeld)}loadProcessor(){let u=xt(this);this.registerMarkdownPostProcessor(u)}loadExtension(){this.extensions=[Z.of(this.settings),ee.of(this.settings),Jn(this)],this.registerEditorExtension(this.extensions)}loadCommands(){this.addCommand({id:"banners:addBanner",name:"Add/Change banner with local image",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return!!r;new He(this,r).open()}}),this.addCommand({id:"banners:addIcon",name:"Add/Change emoji icon",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return!!r;new re(this,r).open()}}),this.addCommand({id:"banners:pasteBanner",name:"Paste banner from clipboard",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return!!r;this.pasteBanner(r)}}),this.addCommand({id:"banners:lockBanner",name:"Lock/Unlock banner position",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return!!r;this.toggleBannerLock(r)}}),this.addCommand({id:"banners:removeBanner",name:"Remove banner",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return r?!!this.metaManager.getBannerDataFromFile(r)?.src:!1;this.removeBanner(r)}}),this.addCommand({id:"banners:removeIcon",name:"Remove icon",checkCallback:u=>{let r=this.workspace.getActiveFile();if(u)return r?!!this.metaManager.getBannerDataFromFile(r)?.icon:!1;this.removeIcon(r)}})}loadStyles(){document.documentElement.style.setProperty("--banner-height",`${this.getSettingValue("height")}px`),document.documentElement.style.setProperty("--banner-internal-embed-height",`${this.getSettingValue("internalEmbedHeight")}px`),document.documentElement.style.setProperty("--banner-preview-embed-height",`${this.getSettingValue("previewEmbedHeight")}px`)}loadPrecheck(){let u=this.workspace.getLeavesOfType("markdown").map(a=>a.view.file);[...new Set(u)].forEach(a=>this.lintBannerSource(a)),this.registerEvent(this.workspace.on("file-open",a=>this.lintBannerSource(a)))}unloadListeners(){window.removeEventListener("keydown",this.isDragModHeld),window.removeEventListener("keyup",this.isDragModHeld)}unloadBanners(){this.workspace.containerEl.querySelectorAll(".obsidian-banner-wrapper").forEach(u=>{u.querySelector(".obsidian-banner")?.remove(),u.querySelector(".obsidian-banner-icon")?.remove(),u.removeClasses(["obsidian-banner-wrapper","has-banner-icon"])})}unloadStyles(){document.documentElement.style.removeProperty("--banner-height"),document.documentElement.style.removeProperty("--banner-internal-embed-height"),document.documentElement.style.removeProperty("--banner-preview-embed-height")}refreshViews(){this.extensions[0]=Z.of(this.settings),this.extensions[1]=ee.of(this.settings),this.workspace.updateOptions(),this.workspace.getLeavesOfType("markdown").forEach(u=>{u.getViewState().state.mode.includes("preview")&&u.view.previewMode.rerender(!0)}),this.isDragModHeld()}async pasteBanner(u){let r=await navigator.clipboard.readText();(0,Qn.default)(r)?(await this.metaManager.upsertBannerData(u,{src:`"${r}"`}),new U.Notice("Pasted a new banner!")):(new U.Notice("Your clipboard didn't had a valid URL! Please try again (and check the console if you wanna debug)."),console.error({clipboard:r}))}async toggleBannerLock(u){let{lock:r=!1}=this.metaManager.getBannerDataFromFile(u);r?(await this.metaManager.removeBannerData(u,"lock"),new U.Notice(`Unlocked banner position for ${u.name}!`)):(await this.metaManager.upsertBannerData(u,{lock:!0}),new U.Notice(`Locked banner position for ${u.name}!`))}async removeBanner(u){await this.metaManager.removeBannerData(u,["src","x","y","lock"]),new U.Notice(`Removed banner for ${u.name}!`)}async removeIcon(u){await this.metaManager.removeBannerData(u,"icon"),new U.Notice(`Removed banner icon for ${u.name}!`)}async lintBannerSource(u){if(!u)return;let{src:r}=this.metaManager.getBannerDataFromFile(u)??{};r&&typeof r=="string"&&await this.metaManager.upsertBannerData(u,{src:`"${r}"`})}getSettingValue(u){return this.settings[u]??L[u]}};
/*! Copyright Twitter Inc. and other contributors. Licensed under MIT */
