
## 家人


```dataviewjs
// Family birthdays using DataviewJS
let pages = dv.pages("#family").where(p => p.birthday);

var start = moment().startOf('day');
var end = moment(start).add(dv.current().duration);
var dateformat = "YYYY-MM-DD"

function nextBirthday(birthday) {
    var bday = moment(birthday.toString());
    var bdayNext = moment(bday).year(start.year());
    if (bdayNext.isBefore(start, 'day')) {
        bdayNext.add(1, "year");
    }
    return bdayNext;
}

function countdown(birthday){
	var bday = moment(birthday.toString())
	const setTime = new Date(bday);
	const nowTime = new Date();
	const restSec = setTime.getTime() - nowTime.getTime();
	const day = parseInt(restSec / (60*60*24*1000));
	const str = day + "天";
	return str;
}


dv.table(["姓名", "生日", "年龄","倒计时", "手机号"],
  pages.sort(p => moment(p.birthday.toString()).format("MM-DD"), 'asc')
  .sort (p => p.birthday, 'desc')
  .map(p => [
    // The name
    p.file.link,
    // Formatted birthday from YAML frontmatter
    moment(p.birthday.toString()).format("YYYY年MM月DD日"),
    // Current age in years
    moment().diff(moment(p.birthday.toString()), 'years'),	
	countdown(nextBirthday(p.birthday)),
	p.phone,
	]
  )
);
```