{"command_timeout": 5, "templates_folder": "40 - Obsidian/模板", "templates_pairs": [["<PERSON>ian<PERSON>", "curl -H \"Accept-Language: zh-CN\" \"wttr.in/上海?format=4\""]], "trigger_on_file_creation": true, "auto_jump_to_cursor": false, "enable_system_commands": true, "shell_path": "C:\\Windows\\System32\\cmd.exe", "user_scripts_folder": "40 - Obsidian/脚本/template", "enable_folder_templates": true, "folder_templates": [{"folder": "40 - Obsidian/模板", "template": ""}], "syntax_highlighting": true, "enabled_templates_hotkeys": [], "startup_templates": [""], "enable_ribbon_icon": true}