/*
	MIT License

	Copyright (c) 2021-2022 kometenstaub

	Permission is hereby granted, free of charge, to any person obtaining a copy
	of this software and associated documentation files (the "Software"), to deal
	in the Software without restriction, including without limitation the rights
	to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	copies of the Software, and to permit persons to whom the Software is
	furnished to do so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
*/

/* @settings

name: Customizable Page Header and Title Bar
id: customizable-page-header-buttons
settings:
    - 
        id: page-header-spacing-mobile
        title: Page Header Button Spacing (mobile)
        type: variable-number-slider
        default: 12
        min: 0
        max: 30
        step: 1
        format: px
    - 
        id: page-header-spacing-desktop
        title: Page Header Button Spacing (desktop)
        type: variable-number-slider
        default: 8
        min: 0
        max: 30
        step: 1
        format: px
*/

.page-header-button.titlebar-center{flex-grow:1;font-size:12px;height:100%;left:0;letter-spacing:.05em;opacity:.8;position:absolute;text-align:center;top:0;width:100%}:not(.is-mobile) .view-actions{align-items:center}:not(.is-mobile) .view-action.pane-relief{display:flex;position:unset}:not(.is-mobile) .view-action.pane-relief.app\:go-back:before,:not(.is-mobile) .view-action.pane-relief.app\:go-forward:after{display:inline;font-size:1em;line-height:1;vertical-align:text-top}:not(.is-mobile) .view-action.pane-relief.app\:go-forward:after{content:var(--pane-relief-forward-count);padding-left:.4em}:not(.is-mobile) .view-action.pane-relief.app\:go-back:before{content:var(--pane-relief-backward-count);padding-right:.4em}:not(.is-mobile) .view-action:not(:last-child){margin-right:var(--page-header-spacing-desktop)}:not(.is-mobile) .pane-relief body:not(.no-svg-replace) svg{vertical-align:top}.is-mobile .view-action:not(:last-child){margin-right:var(--page-header-spacing-mobile)}