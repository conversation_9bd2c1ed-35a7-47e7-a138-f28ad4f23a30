---
banner: "https://i.pinimg.com/originals/48/b1/e8/48b1e87e34036e53980efba9baa2f7d4.gif"
---
---
banner: "https://i.pinimg.com/originals/48/b1/e8/48b1e87e34036e53980efba9baa2f7d4.gif"
---
---
banner: "https://i.pinimg.com/originals/48/b1/e8/48b1e87e34036e53980efba9baa2f7d4.gif"
cssclass: myhome
banner_icon: 📓
banner_y: 0.5
---

- MCIT
	- [GitHub](https://github.com/xxxxxx/MCIT)
	- [VS Code](vscode://file/Users/<USER>/MCIT/MCIT.code-workspace)
- Obsidian
	- [[Dataview模板合集|Dateview]]
	- [[Python]]
- Work
	- [[00. 算法主页|常用算法]]


## 库内代码

```dataviewjs
let excludeFolderChoicePath = "40 - Obsidian/模板"

dv.table(["名称","语言","框架","简述","创建时间"], dv.pages('#code_snippet')
	.filter(b => !b.file.path.includes(excludeFolderChoicePath))
	.sort(b => b.file.ctime,'desc')
	.map(b =>[dv.fileLink(b.file.name, false, b.代码名称),b.语言,b.框架, b.简述, b.file.cday])
	)
```

