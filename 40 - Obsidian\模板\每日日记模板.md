---
creation date: <% tp.file.creation_date() %>
tags: DailyNote
week: <% tp.date.now("YYYY-WW") %>
---

<< [[<% tp.date.now("YYYY年MM月DD日", -1, tp.file.title, "YYYY年MM月DD日") %>]] | [[<% tp.date.now("YYYY年MM月DD日", 1, tp.file.title, "YYYY年MM月DD日") %>]] >>


- 体重记录 ::
- 健康记录 ::
- 每日总结 :: 

# 日记
%% 处理收件箱中的每一项事情（广义上定义为所有收件箱 [email、微信、slack、短信] 和所有待办事项）。 如果该操作需要 <2 分钟才能完成，请立即执行。不然，将其放入以下清单 %% 

==今日计划==
%% 今日需要完成的任务，不包含长期项目进程的独立任务%%

==项目进程==
%% 当前进行中的项目今日需要完成的任务 %%

===健身计划===
<%*
const dayOfWeek = tp.date.now("dddd", 0, tp.file.title, "YYYY年MM月DD日");
let workout = "";

const today = tp.date.now("YYYY-MM-DD", 0, tp.file.title, "YYYY年MM月DD日");

switch(dayOfWeek) {
    case "Monday":
    case "星期一":
        workout = `**胸部训练**
- [ ] 🛫 ${today} ==地板哑铃卧推== 4×12次
- [ ] 🛫 ${today} ==地板哑铃飞鸟== 4×12次  
- [ ] 🛫 ${today} ==腰间俯卧撑== 4×12次
- [ ] 🛫 ${today} ==上斜俯卧撑== 4×12次`;
        break;
    case "Tuesday":
    case "星期二":
        workout = `**肩部＋手臂训练**
- [ ] 🛫 ${today} ==站姿哑铃推肩== 4×12次
- [ ] 🛫 ${today} ==站姿哑铃侧平举== 4×12次
- [ ] 🛫 ${today} ==哑铃交替弯举== 4×12次/侧
- [ ] 🛫 ${today} ==哑铃集中弯举== 4×12次/侧  
- [ ] 🛫 ${today} ==臂屈伸== 4×12次`;
        break;
    case "Wednesday":
    case "星期三":
        workout = `**背部训练**
- [ ] 🛫 ${today} ==哑铃俯身划船== 4×12次
- [ ] 🛫 ${today} ==单臂哑铃划船== 4×12次/侧
- [ ] 🛫 ${today} ==俯卧哑铃划船== 4×12次
- [ ] 🛫 ${today} ==地板哑铃过头拉== 4×12次`;
        break;
    case "Thursday":
    case "星期四":
        workout = `**休息日**
- [ ] 🛫 ${today} ==休息日活动== 可以进行轻松的散步或拉伸`;
        break;
    case "Friday":
    case "星期五":
        workout = `**腿部训练**
- [ ] 🛫 ${today} ==哑铃高脚杯深蹲== 4×12次
- [ ] 🛫 ${today} ==哑铃罗马尼亚硬拉== 4×12次
- [ ] 🛫 ${today} ==哑铃箭步蹲== 4×12次
- [ ] 🛫 ${today} ==徒手侧弓步== 4×15次/侧`;
        break;
    case "Saturday":
    case "星期六":
        workout = `**腹部训练**
- [ ] 🛫 ${today} ==平板支持== 4×60s
- [ ] 🛫 ${today} ==卷腹== 4×15次
- [ ] 🛫 ${today} ==V字卷腹== 4×15次
- [ ] 🛫 ${today} ==俄罗斯转体== 4×12次/侧`;
        break;
    case "Sunday":
    case "星期日":
        workout = `**休息日**
- [ ] 🛫 ${today} ==休息日活动== 可以进行轻松的散步或拉伸`;
        break;
    default:
        workout = `**自定义训练**
- [ ] 🛫 ${today} ==自定义训练内容== 根据个人情况安排训练内容`;
}
%>
<% workout %>

==搁置任务==
%% 要求他人完成并正在等待他们完成的任务 %%

==讨论汇总==
%% 将不紧急任务分组并一次性讨论 %%


## 日程

## 本周记录：
[[<%tp.date.now("YYYY年WW周记录",0, tp.file.title, "YYYY年MM月DD日")%>]]

## 宠物
**[[<% tp.date.now("YYYY年MM月DD日", 0, tp.file.title, "YYYY年MM月DD日") %>]]**
#记录 
^1
