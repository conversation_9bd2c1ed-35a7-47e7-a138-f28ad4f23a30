---
title: Day19
date: 2025-01-15 15:56:18
categories:
  - - 笔记
    - 编程
    - 面试训练营
tags:
  - 计算机网络
---
**2025-01-15**🌱上海: ☀️   🌡️+6°C 🌬️↓18km/h
## HTTP 1.0 和 2.0 有什么区别？

### 思维导图总结

![image.png](https://cdn.easymuzi.cn/img/20250115155712091.png)


### [HTTP协议](https://so.csdn.net/so/search?q=HTTP%E5%8D%8F%E8%AE%AE&spm=1001.2101.3001.7020)层次结构图

![image.png](https://cdn.easymuzi.cn/img/20250115155719852.png)


### HTTP1.0

![image.png](https://cdn.easymuzi.cn/img/20250115155736891.png)


**HTTP/1.0是无状态、无连接的应用层协议。**

HTTP 1.0 是首个在通讯中指定版本号的 HTTP 协议版本。该版本中，浏览器与服务器连接短暂，每次请求都要新建 TCP 连接，服务器处理完请求后即刻断开连接，且不跟踪客户、不记录过往请求，即每次与服务器交互都需新开连接 。

![image.png](https://cdn.easymuzi.cn/img/20250115155742611.png)


**无连接**：每次请求都要建立连接，需要使用 **keep-alive** 参数建立**长连接**、HTTP1.1默认长连接keep-alive， 无法复用连接，每次发送请求都要进行TCP连接，TCP的连接释放都比较费事，会导致网络利用率低

**队头阻塞(head of line blocking)**：由于HTTP1.0规定下一个请求必须在前一个请求响应到达之前才能发送，假设前一个请求响应一直不到达，那么下一个请求就不发送，后面的请求就阻塞了。

![image.png](https://cdn.easymuzi.cn/img/20250115155750162.png)


**缓存**:在HTTP1.0中主要使用header里的协商缓存 last-modified\if-modified-since，强缓存 Expires来做为缓存判断的标准

![image.png](https://cdn.easymuzi.cn/img/20250115155758741.png)


**Expires**是RFC 2616（HTTP/1.0）协议中和网页缓存相关字段。用来控制缓存的失效日期。

**HOST域**：认为每个服务器绑定唯一一个IP地址，因此在请求消息的URL中没有主机名，HTTP1.0没有host域。而现在在一台服务器上可以存在多个虚拟主机，并且它们共享一个IP地址。

  HTTP1.0不支持断点续传功能，每次都会传送全部的页面和数据。如果只需要部分数据就会浪费多余带宽

### HTTP/1.1

![image.png](https://cdn.easymuzi.cn/img/20250115155805543.png)


![](https://cdn.nlark.com/yuque/0/2025/png/26566882/1736593953938-8903607f-7256-4d1d-a912-b0808421e0ea.png)

| **分类** | **详情**                                                                                                                                                                                                                                                                                                    |
| ------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 特点     | **简单**：基本报文格式为 header + body，头部信息以 key - value 简单文本形式呈现，易于理解  <br>**灵活、易扩展**：请求方法、URL、状态码等组成部分未固定，开发者可自定义扩充；在应用层，下层可灵活变化（如 https 是在 HTTP 与 TCP 间增加 SSL/TSL 安全传输协议）  <br>**应用广泛、支持跨平台**                                                                                                                    |
| 优缺点    | **无状态**  <br>**好处**：服务器无需额外资源记录，减轻负担，提高 CPU 内存利用效率  <br>**坏处**：每次需确认验证信息；一般通过 Cookie 解决（Cookie 通过在请求和响应报文中写入 Cookie 信息来控制客户端的状态）  <br>**明文传输**：传输过程中信息可通过抓包直接获取，存在信息暴露问题，安全性差  <br>**不安全**  <br>- 通信使用明文传输，易导致信息泄露  <br>- 不验证通信双方身份，可能进入伪装网站  <br>- 无法证明报文完整性  <br>**解决方式**：使用 HTTPS 方式，引入 SSL/TLS 层提升安全性 |

**长连接**：HTTP/1.0 因每次都需建立连接致使通信效率低，鉴于 HTTP 基于 TCP/IP 协议，HTTP/1.1 提出长连接（持久连接）通信方式。其好处是减少 TCP 连接重复建立与断开的额外开销，减轻服务器负载。特点是任意一端未明确断开则保持连接状态，若长连接超一定时间无数据交互，服务端会主动断开 。

![image.png](https://cdn.easymuzi.cn/img/20250115155911956.png)


**管道传输**：HTTP/1.1 采用长连接方式让管道（pipeline）网络传输成为可能。在同一个 TCP 连接中，客户端能发起多个请求，无需等待前一个请求响应即可发送下一个，可减少整体响应时间。不过，服务器需按接收顺序发送对管道化请求的响应，若服务端处理某一请求耗时较长，后续请求处理会被阻塞，即「队头堵塞」。因此，HTTP/1.1 管道解决了请求的队头阻塞问题，但未解决响应的队头阻塞问题。

### HTTP/2.0

HTTP/2.0协议是**基于HTTPS**的，更加安全

相比与HTTP/1.1，HTTP/2.0增加如下几点的重大优化

**头部压缩：**HTTP2.0会压缩（Header）部分；如果同时多个请求其头部一样或相似，那么协议会消除重复部分。

利用HPAK算法：在客户端和服务器同时维护一张头信息表，所有字段都会存入这个表，生成一个索引号，就不用重复发送同样字段了，只发送索引号，减少数据量提高速度

**二进制格式**：HTTP/1.0和HTTP/1.1中，报文都是纯文本的格式简单易读；而在2.0中采用了二进制的格式

报头和数据体称为：帧（frame）-》头信息帧（Headers Frame）和数据帧（Data Frame）

![image.png](https://cdn.easymuzi.cn/img/20250115155923637.png)


文本形式信息保存为一个一个字符，占用空间多，每个字符对应比特位多，接受方还需要将报文转换为二进制，而直接用二进制减少了传输数据量，提高数据传输效率

**1.0**：

![image.png](https://cdn.easymuzi.cn/img/20250115155935666.png)


**2.0**：

![image.png](https://cdn.easymuzi.cn/img/20250115155942101.png)


HTTP/2 中数据以**数据流形式按字节单位**发送，数据包可**不按顺序**发送。每个请求或响应的所有数据包构成一个**数据流（Stream）**，有**唯一编号（Stream ID）**。所有通信在一个 TCP 连接上完成，该连接可承载双向任意流量的数据流。数据流以消息形式发送，消息由一或多个帧组成，不同 Stream 的帧能乱序发送（**可并发不同 Stream**），接收端依据帧头部的 **Stream ID** 有序组装成 HTTP 消息。此外，客户端能指定数据流优先级，服务器优先响应优先级高的请求。

**多路复用**

HTTP2.0实现了真正的并行传输，它能够在一个TCP上进行任意数量的HTTP请求，由于其二进制分帧特性

![image.png](https://cdn.easymuzi.cn/img/20250115155951283.png)


HTTP/2 能在一个连接中并发多个请求或回应，无需按顺序一一对应。它移除了 HTTP/1.1 的串行请求，无需排队等待，彻底解决 “队头阻塞” 问题，降低延迟并大幅提升连接利用率。

**服务端推送**

HTTP/2 还在一定程度上改善了传统的「请求 - 应答」工作模式，服务端不再是被动地响应，可以主动向**客户端发送消息**、**推送额外的资源**。

![image.png](https://cdn.easymuzi.cn/img/20250115155959112.png)


**TCP导致队头阻塞**

由于 TCP 面向字节流传输，且要保证**传输可靠性**与**数据完整性**，只有 TCP 获得完整连续的数据，内核才会将缓冲区数据交给 HTTP 应用。若前一个字节未收到，HTTP 就无法从内核缓冲区获取数据，直至该字节到达，这一过程仍会导致队头阻塞。

![image.png](https://cdn.easymuzi.cn/img/20250115160005873.png)

发送方发送多个带有序号（可视为 TCP 序列号）的 packet，其中 packet 3 在网络中丢失。即便 packet 4 - 6 被接收方收到，因内核中 TCP 数据不连续，接收方应用层无法从内核读取数据，需等 packet 3 重传后，应用层才能读取，这就是 HTTP/2 在 TCP 层面发生的队头阻塞问题。因此如果出现丢包就会触发TCP的超时重传，这样后续缓冲队列中所有数据都得等丢了的重传

具体细节可以参考该文章：[深入理解HTTP/2.0](https://links.jianshu.com/go?to=https://mp.weixin.qq.com/s/a83_NE-ww36FZsy320MQFQ)

## HTTP 2.0 和 3.0 有什么区别？

### 思维导图总结

![image.png](https://cdn.easymuzi.cn/img/20250115160020978.png)


### 扩展分析

基于Google的QUIC，HTTP3 背后的主要思想是放弃 TCP，转而使用基于 UDP 的 QUIC 协议。

为了解决HTTP/2.0中TCP造成的队头阻塞问题，HTTP/3.0直接放弃使用TCP，将传输层协议改成UDP；但是因为UDP是不可靠传输，所以这就需要QUIC实现可靠机制

QUIC 也是需要三次握手来建立连接的，主要目的是为了确定连接 ID。

![image.png](https://cdn.easymuzi.cn/img/20250115160030446.png)


**QUIC特点**：

QUIC（Quick UDP Internet Connections）是谷歌开发的基于 UDP 的传输层协议，旨在替代 TCP 并改进 HTTP/2 性能，目标是减少连接延迟、提升传输效率与安全性，在传输层和应用层间提供可靠、低延迟传输服务 。

| **优势**  | **详情**                                                                    |
| ------- | ------------------------------------------------------------------------- |
| 低延迟连接建立 | 采用 0-RTT（Zero Round Trip Time）技术，已建立过连接的客户端在首次握手时能直接发送数据，无需等待服务器响应，减少连接延迟 |
| 内置加密    | 默认采用 TLS 1.3 进行端到端加密，提升数据传输安全性，简化协议设计，无需像 TCP 那样配置额外加密层                   |
| 减少队头阻塞  | 每个连接内使用多个独立流，一个流上的丢包不会阻碍其他流的数据传输，有效减少队头阻塞问题，提高传输效率                        |
| 更快的拥塞控制 | 能快速调整拥塞控制算法，可获取更多上下文信息（如链路的 RTT、丢包率等），且支持在应用层定制优化                         |
| 连接迁移    | 支持连接迁移，当客户端 IP 地址或网络环境改变（如从 Wi-Fi 切换到蜂窝网络）时，连接仍能保持，不像 TCP 会中断             |
| 更高带宽利用率 | 通过改进的流量控制机制，能更好地利用可用带宽，提升传输速度和效率                                          |

**无队头阻塞**

QUIC 协议也有类似 HTTP/2 Stream 与多路复用的概念，也是可以在同一条连接上并发传输多个 Stream，Stream 可以认为就是一条 HTTP 请求。

QUIC 有自己的一套机制可以保证传输的可靠性的。当某个流发生丢包时，只会阻塞这个流，其他流不会受到影响，因此不存在队头阻塞问题。这与 HTTP/2 不同，HTTP/2 只要某个流中的数据包丢失了，其他流也会因此受影响。

所以，QUIC 连接上的多个 Stream 之间并没有依赖，都是独立的，某个流发生丢包了，只会影响该流，其他流不受影响。

**连接建立**

HTTP/3 在传输数据前虽然需要 QUIC 协议握手，这个握手过程只需要 1 RTT，握手的目的是为确认双方的「连接 ID」，连接迁移就是基于连接 ID 实现的。

需要注意一点：**首次1RTT**，**后续0RTT**

QUIC 协议握手所需的 RTT（往返时延）数分为以下两种情况：

- **首次连接**：需要 1-RTT。客户端首先发送一个包含 ClientHello 的 Initial 包，服务器收到后回复一个包含 ServerHello 等信息的 Initial 包以及一个 Handshake 包，客户端再发送 1-RTT 包，其中可包含应用数据，服务器回复 1-RTT 包，携带 HANDSHAKE_DONE 以及应用数据等，至此握手完成。
- **后续连接**：如果客户端缓存了上一次连接的信息（如 TLS 1.3 Diffie-Hellman 公钥、传输参数、<代码开始>NEW_TOKEN< 代码结束 >帧生成的令牌等），则可以实现 0-RTT 握手，直接在一个经过认证且加密的通道传输数据。

![image.png](https://cdn.easymuzi.cn/img/20250115160042833.png)


**连接迁移**

基于 TCP 传输协议的 HTTP 协议，通过四元组（源 IP、源端口、目的 IP、目的端口）确定 TCP 连接，当设备网络变化（如连接 wifi 导致 IP 地址改变）时需重新建立连接，这涉及 TCP 三次握手、TSL 四次握手及 TCP 慢启动，会让使用者有卡顿感。

而 QUIC 通过连接 ID 标记自身，客户端和服务器可各自选 ID 标记，即便移动设备网络变化致使 IP 地址改变，只要有相关上下文信息（如连接 ID、TLS 密钥等），就能 “无缝” 复用原连接，实现连接迁移，消除重连成本，无卡顿感。

本质上，QUIC 是基于 UDP 的、融合了伪 TCP + TLS + HTTP/2 多路复用的协议 。

具体可学习该文章：[字节一面：如何用 UDP 实现可靠传输？](https://mp.weixin.qq.com/s/hX75YxVOMtsnB6Sm_yjm0g)

## HTTP 和 HTTPS 有什么区别？

### 思维导图总结

![image.png](https://cdn.easymuzi.cn/img/20250115160050208.png)


![image.png](https://cdn.easymuzi.cn/img/20250115160056707.png)


### HTTPS

HTTPS 是以安全为目标的 HTTP 通道，在 HTTP 基础上通过传输加密和身份认证保障传输安全，其内容加密、身份验证及数据完整性保护原理主要依赖 SSL/TLS 协议，接下来将详细介绍相关实现原理 。

#### 内容加密

HTTPS使用对称加密和非对称加密相结合的方式来实现内容加密。

**对称加密：**

密钥交换完成后，客户端与服务器生成共享会话密钥，后续以此会话密钥通过对称加密算法（如 AES）对传输数据进行加密和解密，保障数据传输安全。

![image.png](https://cdn.easymuzi.cn/img/20250115160103275.png)


对称加密虽然保证了消息的保密性，但是因**Client和Service共享一个密匙**，这样就导致密匙特别容易泄露。

**非对称加密**：

HTTPS 握手阶段，服务器向客户端发送公钥用于加密通信，客户端用该公钥加密随机数并发送给服务器，服务器用私钥解密随机数，以此安全交换密钥，此过程即密钥交换。

![image.png](https://cdn.easymuzi.cn/img/20250115160109197.png)


- 非对称加密时需要使用到接收方的公匙对消息进行加密，但是公匙不保密，可以截获客户端发来的消息，然后篡改形成攻击
- 非对称加密的性能会慢上至少几倍，增加系统消耗。因此，Https将两种加密结合起来使用。

#### 身份验证

HTTPS使用数字证书来验证服务器的身份。

1. **数字证书**：由权威证书颁发机构（CA）颁发，包含服务器公钥、身份信息及 CA 签名等，服务器在与客户端建立 HTTPS 连接时发送给客户端。
2. **验证过程**：客户端收到证书后进行合法性验证，检查是否由受信任 CA 颁发、是否过期、服务器身份信息是否匹配，并用 CA 公钥验证签名，确认未被篡改，若验证通过则认定服务器身份可信。

![image.png](https://cdn.easymuzi.cn/img/20250115160119004.png)


#### 数据完整性

HTTPS通过消息认证码（MAC）来确保数据的完整性。

1. **消息认证码**：HTTPS 通信中，每个传输数据包附带 MAC 值，该值由数据包内容与会话密钥输入哈希函数计算得出，只有持有相同会话密钥的接收方才能算出正确 MAC 值。
2. **完整性校验**：接收方收到数据包后，用相同会话密钥和哈希函数计算 MAC 值，与数据包附带的 MAC 值比较，若两者相同，则可确认数据包在传输中未被篡改，保障数据完整性与安全性。

![image.png](https://cdn.easymuzi.cn/img/20250115160125434.png)


学习资料：[图解HTTP](https://gitcode.com/Open-source-documentation-tutorial/5ceae/blob/main/%E5%9B%BE%E8%A7%A3HTTP-%E5%BD%A9%E8%89%B2%E7%89%88.pdf)

[只看这一篇就能搞懂Http和Https协议区别](https://zhuanlan.zhihu.com/p/631089816)
