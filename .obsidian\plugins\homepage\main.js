"use strict";var ti=Object.create;var et=Object.defineProperty;var oi=Object.getOwnPropertyDescriptor;var ii=Object.getOwnPropertyNames;var ni=Object.getPrototypeOf,ai=Object.prototype.hasOwnProperty;var ri=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),si=(t,e)=>{for(var o in e)et(t,o,{get:e[o],enumerable:!0})},to=(t,e,o,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of ii(e))!ai.call(t,n)&&n!==o&&et(t,n,{get:()=>e[n],enumerable:!(i=oi(e,n))||i.enumerable});return t};var li=(t,e,o)=>(o=t!=null?ti(ni(t)):{},to(e||!t||!t.__esModule?et(o,"default",{value:t,enumerable:!0}):o,t)),pi=t=>to(et({},"__esModule",{value:!0}),t);var xo=ri(h=>{"use strict";Object.defineProperty(h,"__esModule",{value:!0});var T=require("obsidian"),yt="YYYY-MM-DD",bt="gggg-[W]ww",so="YYYY-MM",lo="YYYY-[Q]Q",po="YYYY";function Ce(t){let e=window.app.plugins.getPlugin("periodic-notes");return e&&e.settings?.[t]?.enabled}function Fe(){try{let{internalPlugins:t,plugins:e}=window.app;if(Ce("daily")){let{format:a,folder:p,template:s}=e.getPlugin("periodic-notes")?.settings?.daily||{};return{format:a||yt,folder:p?.trim()||"",template:s?.trim()||""}}let{folder:o,format:i,template:n}=t.getPluginById("daily-notes")?.instance?.options||{};return{format:i||yt,folder:o?.trim()||"",template:n?.trim()||""}}catch(t){console.info("No custom daily note settings found!",t)}}function He(){try{let t=window.app.plugins,e=t.getPlugin("calendar")?.options,o=t.getPlugin("periodic-notes")?.settings?.weekly;if(Ce("weekly"))return{format:o.format||bt,folder:o.folder?.trim()||"",template:o.template?.trim()||""};let i=e||{};return{format:i.weeklyNoteFormat||bt,folder:i.weeklyNoteFolder?.trim()||"",template:i.weeklyNoteTemplate?.trim()||""}}catch(t){console.info("No custom weekly note settings found!",t)}}function Re(){let t=window.app.plugins;try{let e=Ce("monthly")&&t.getPlugin("periodic-notes")?.settings?.monthly||{};return{format:e.format||so,folder:e.folder?.trim()||"",template:e.template?.trim()||""}}catch(e){console.info("No custom monthly note settings found!",e)}}function Ve(){let t=window.app.plugins;try{let e=Ce("quarterly")&&t.getPlugin("periodic-notes")?.settings?.quarterly||{};return{format:e.format||lo,folder:e.folder?.trim()||"",template:e.template?.trim()||""}}catch(e){console.info("No custom quarterly note settings found!",e)}}function Ie(){let t=window.app.plugins;try{let e=Ce("yearly")&&t.getPlugin("periodic-notes")?.settings?.yearly||{};return{format:e.format||po,folder:e.folder?.trim()||"",template:e.template?.trim()||""}}catch(e){console.info("No custom yearly note settings found!",e)}}function co(...t){let e=[];for(let i=0,n=t.length;i<n;i++)e=e.concat(t[i].split("/"));let o=[];for(let i=0,n=e.length;i<n;i++){let a=e[i];!a||a==="."||o.push(a)}return e[0]===""&&o.unshift(""),o.join("/")}function ci(t){let e=t.substring(t.lastIndexOf("/")+1);return e.lastIndexOf(".")!=-1&&(e=e.substring(0,e.lastIndexOf("."))),e}async function di(t){let e=t.replace(/\\/g,"/").split("/");if(e.pop(),e.length){let o=co(...e);window.app.vault.getAbstractFileByPath(o)||await window.app.vault.createFolder(o)}}async function We(t,e){e.endsWith(".md")||(e+=".md");let o=T.normalizePath(co(t,e));return await di(o),o}async function Ee(t){let{metadataCache:e,vault:o}=window.app,i=T.normalizePath(t);if(i==="/")return Promise.resolve(["",null]);try{let n=e.getFirstLinkpathDest(i,""),a=await o.cachedRead(n),p=window.app.foldManager.load(n);return[a,p]}catch(n){return console.error(`Failed to read the daily note template '${i}'`,n),new T.Notice("Failed to read the daily note template"),["",null]}}function Q(t,e="day"){let o=t.clone().startOf(e).format();return`${e}-${o}`}function uo(t){return t.replace(/\[[^\]]*\]/g,"")}function ui(t,e){if(e==="week"){let o=uo(t);return/w{1,2}/i.test(o)&&(/M{1,4}/.test(o)||/D{1,4}/.test(o))}return!1}function Ne(t,e){return mo(t.basename,e)}function mi(t,e){return mo(ci(t),e)}function mo(t,e){let i={day:Fe,week:He,month:Re,quarter:Ve,year:Ie}[e]().format.split("/").pop(),n=window.moment(t,i,!0);if(!n.isValid())return null;if(ui(i,e)&&e==="week"){let a=uo(i);if(/w{1,2}/i.test(a))return window.moment(t,i.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return n}var xt=class extends Error{};async function fo(t){let e=window.app,{vault:o}=e,i=window.moment,{template:n,format:a,folder:p}=Fe(),[s,r]=await Ee(n),l=t.format(a),c=await We(p,l);try{let g=await o.create(c,s.replace(/{{\s*date\s*}}/gi,l).replace(/{{\s*time\s*}}/gi,i().format("HH:mm")).replace(/{{\s*title\s*}}/gi,l).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(v,d,w,m,u,f)=>{let P=i(),E=t.clone().set({hour:P.get("hour"),minute:P.get("minute"),second:P.get("second")});return w&&E.add(parseInt(m,10),u),f?E.format(f.substring(1).trim()):E.format(a)}).replace(/{{\s*yesterday\s*}}/gi,t.clone().subtract(1,"day").format(a)).replace(/{{\s*tomorrow\s*}}/gi,t.clone().add(1,"d").format(a)));return e.foldManager.save(g,r),g}catch(g){console.error(`Failed to create file: '${c}'`,g),new T.Notice("Unable to create new file.")}}function fi(t,e){return e[Q(t,"day")]??null}function gi(){let{vault:t}=window.app,{folder:e}=Fe(),o=t.getAbstractFileByPath(T.normalizePath(e));if(!o)throw new xt("Failed to find daily notes folder");let i={};return T.Vault.recurseChildren(o,n=>{if(n instanceof T.TFile){let a=Ne(n,"day");if(a){let p=Q(a,"day");i[p]=n}}}),i}var Pt=class extends Error{};function hi(){let{moment:t}=window,e=t.localeData()._week.dow,o=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;e;)o.push(o.shift()),e--;return o}function vi(t){return hi().indexOf(t.toLowerCase())}async function go(t){let{vault:e}=window.app,{template:o,format:i,folder:n}=He(),[a,p]=await Ee(o),s=t.format(i),r=await We(n,s);try{let l=await e.create(r,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,g,v,d,w,m)=>{let u=window.moment(),f=t.clone().set({hour:u.get("hour"),minute:u.get("minute"),second:u.get("second")});return v&&f.add(parseInt(d,10),w),m?f.format(m.substring(1).trim()):f.format(i)}).replace(/{{\s*title\s*}}/gi,s).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(c,g,v)=>{let d=vi(g);return t.weekday(d).format(v.trim())}));return window.app.foldManager.save(l,p),l}catch(l){console.error(`Failed to create file: '${r}'`,l),new T.Notice("Unable to create new file.")}}function wi(t,e){return e[Q(t,"week")]??null}function yi(){let t={};if(!vo())return t;let{vault:e}=window.app,{folder:o}=He(),i=e.getAbstractFileByPath(T.normalizePath(o));if(!i)throw new Pt("Failed to find weekly notes folder");return T.Vault.recurseChildren(i,n=>{if(n instanceof T.TFile){let a=Ne(n,"week");if(a){let p=Q(a,"week");t[p]=n}}}),t}var Et=class extends Error{};async function ho(t){let{vault:e}=window.app,{template:o,format:i,folder:n}=Re(),[a,p]=await Ee(o),s=t.format(i),r=await We(n,s);try{let l=await e.create(r,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,g,v,d,w,m)=>{let u=window.moment(),f=t.clone().set({hour:u.get("hour"),minute:u.get("minute"),second:u.get("second")});return v&&f.add(parseInt(d,10),w),m?f.format(m.substring(1).trim()):f.format(i)}).replace(/{{\s*date\s*}}/gi,s).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,s));return window.app.foldManager.save(l,p),l}catch(l){console.error(`Failed to create file: '${r}'`,l),new T.Notice("Unable to create new file.")}}function bi(t,e){return e[Q(t,"month")]??null}function xi(){let t={};if(!wo())return t;let{vault:e}=window.app,{folder:o}=Re(),i=e.getAbstractFileByPath(T.normalizePath(o));if(!i)throw new Et("Failed to find monthly notes folder");return T.Vault.recurseChildren(i,n=>{if(n instanceof T.TFile){let a=Ne(n,"month");if(a){let p=Q(a,"month");t[p]=n}}}),t}var Nt=class extends Error{};async function Pi(t){let{vault:e}=window.app,{template:o,format:i,folder:n}=Ve(),[a,p]=await Ee(o),s=t.format(i),r=await We(n,s);try{let l=await e.create(r,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,g,v,d,w,m)=>{let u=window.moment(),f=t.clone().set({hour:u.get("hour"),minute:u.get("minute"),second:u.get("second")});return v&&f.add(parseInt(d,10),w),m?f.format(m.substring(1).trim()):f.format(i)}).replace(/{{\s*date\s*}}/gi,s).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,s));return window.app.foldManager.save(l,p),l}catch(l){console.error(`Failed to create file: '${r}'`,l),new T.Notice("Unable to create new file.")}}function Ei(t,e){return e[Q(t,"quarter")]??null}function Ni(){let t={};if(!yo())return t;let{vault:e}=window.app,{folder:o}=Ve(),i=e.getAbstractFileByPath(T.normalizePath(o));if(!i)throw new Nt("Failed to find quarterly notes folder");return T.Vault.recurseChildren(i,n=>{if(n instanceof T.TFile){let a=Ne(n,"quarter");if(a){let p=Q(a,"quarter");t[p]=n}}}),t}var Ot=class extends Error{};async function Oi(t){let{vault:e}=window.app,{template:o,format:i,folder:n}=Ie(),[a,p]=await Ee(o),s=t.format(i),r=await We(n,s);try{let l=await e.create(r,a.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,g,v,d,w,m)=>{let u=window.moment(),f=t.clone().set({hour:u.get("hour"),minute:u.get("minute"),second:u.get("second")});return v&&f.add(parseInt(d,10),w),m?f.format(m.substring(1).trim()):f.format(i)}).replace(/{{\s*date\s*}}/gi,s).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,s));return window.app.foldManager.save(l,p),l}catch(l){console.error(`Failed to create file: '${r}'`,l),new T.Notice("Unable to create new file.")}}function Si(t,e){return e[Q(t,"year")]??null}function Di(){let t={};if(!bo())return t;let{vault:e}=window.app,{folder:o}=Ie(),i=e.getAbstractFileByPath(T.normalizePath(o));if(!i)throw new Ot("Failed to find yearly notes folder");return T.Vault.recurseChildren(i,n=>{if(n instanceof T.TFile){let a=Ne(n,"year");if(a){let p=Q(a,"year");t[p]=n}}}),t}function Ti(){let{app:t}=window,e=t.internalPlugins.plugins["daily-notes"];if(e&&e.enabled)return!0;let o=t.plugins.getPlugin("periodic-notes");return o&&o.settings?.daily?.enabled}function vo(){let{app:t}=window;if(t.plugins.getPlugin("calendar"))return!0;let e=t.plugins.getPlugin("periodic-notes");return e&&e.settings?.weekly?.enabled}function wo(){let{app:t}=window,e=t.plugins.getPlugin("periodic-notes");return e&&e.settings?.monthly?.enabled}function yo(){let{app:t}=window,e=t.plugins.getPlugin("periodic-notes");return e&&e.settings?.quarterly?.enabled}function bo(){let{app:t}=window,e=t.plugins.getPlugin("periodic-notes");return e&&e.settings?.yearly?.enabled}function ki(t){let e={day:Fe,week:He,month:Re,quarter:Ve,year:Ie}[t];return e()}function Mi(t,e){return{day:fo,month:ho,week:go}[t](e)}h.DEFAULT_DAILY_NOTE_FORMAT=yt;h.DEFAULT_MONTHLY_NOTE_FORMAT=so;h.DEFAULT_QUARTERLY_NOTE_FORMAT=lo;h.DEFAULT_WEEKLY_NOTE_FORMAT=bt;h.DEFAULT_YEARLY_NOTE_FORMAT=po;h.appHasDailyNotesPluginLoaded=Ti;h.appHasMonthlyNotesPluginLoaded=wo;h.appHasQuarterlyNotesPluginLoaded=yo;h.appHasWeeklyNotesPluginLoaded=vo;h.appHasYearlyNotesPluginLoaded=bo;h.createDailyNote=fo;h.createMonthlyNote=ho;h.createPeriodicNote=Mi;h.createQuarterlyNote=Pi;h.createWeeklyNote=go;h.createYearlyNote=Oi;h.getAllDailyNotes=gi;h.getAllMonthlyNotes=xi;h.getAllQuarterlyNotes=Ni;h.getAllWeeklyNotes=yi;h.getAllYearlyNotes=Di;h.getDailyNote=fi;h.getDailyNoteSettings=Fe;h.getDateFromFile=Ne;h.getDateFromPath=mi;h.getDateUID=Q;h.getMonthlyNote=bi;h.getMonthlyNoteSettings=Re;h.getPeriodicNoteSettings=ki;h.getQuarterlyNote=Ei;h.getQuarterlyNoteSettings=Ve;h.getTemplateInfo=Ee;h.getWeeklyNote=wi;h.getWeeklyNoteSettings=He;h.getYearlyNote=Si;h.getYearlyNoteSettings=Ie});var mn={};si(mn,{default:()=>ft});module.exports=pi(mn);var se=require("obsidian");var q=require("obsidian");var Po=require("obsidian");var oo=require("obsidian");function ee(t){return t?t.extension=="md"?t.path.slice(0,-3):t.path:""}function io(t){return t.split("/").slice(-1)[0].contains(".")?t:`${t}.md`}function no(t,e){return(t%e+e)%e}function ao(t){let e=t.vault.getFiles().filter(o=>["md","canvas"].contains(o.extension));if(e.length){let o=Math.floor(Math.random()*e.length);return ee(e[o])}}function ro(t){return t.workspace.getActiveViewOfType(oo.View)?.getViewType()=="empty"}var O=li(xo()),St={["Daily Note"]:{noun:"day",adjective:"daily",create:O.createDailyNote,get:O.getDailyNote,getAll:O.getAllDailyNotes},["Weekly Note"]:{noun:"week",adjective:"weekly",create:O.createWeeklyNote,get:O.getWeeklyNote,getAll:O.getAllWeeklyNotes},["Monthly Note"]:{noun:"month",adjective:"monthly",create:O.createMonthlyNote,get:O.getMonthlyNote,getAll:O.getAllMonthlyNotes},["Yearly Note"]:{noun:"year",adjective:"yearly",create:O.createYearlyNote,get:O.getYearlyNote,getAll:O.getAllYearlyNotes}},Dt=["Daily Note","Weekly Note","Monthly Note","Yearly Note"];async function Eo(t,e){let o=e.communityPlugins["periodic-notes"],i=St[t],n=(0,Po.moment)().startOf(i.noun),a;if(Oo(o)){let p=i.getAll();Object.keys(p).length?a=i.get(n,p)||await i.create(n):a=await i.create(n)}else o.cache.initialize(),a=o.getPeriodicNote(i.noun,n)||await o.createPeriodicNote(i.noun,n);return ee(a)}function No(t,e){if(t=="Daily Note"&&e.internalPlugins["daily-notes"]?.enabled)return!0;let o=e.communityPlugins["periodic-notes"];if(!o)return!1;if(Oo(o)){let i=St[t].adjective;return e.communityPlugins["periodic-notes"].settings[i]?.enabled}else{let i=St[t].noun;return e.communityPlugins["periodic-notes"]?.calendarSetManager?.getActiveSet()[i]?.enabled}}function tt(t){let e=t.internalPlugins["daily-notes"];return e?.enabled&&e?.instance.options.autorun}function Oo(t){return(t?.manifest.version||"0").startsWith("0")}var So=["markdown","canvas","kanban"],me="Main Homepage",ot="Mobile Homepage",it=(i=>(i.ReplaceAll="Replace all open notes",i.ReplaceLast="Replace last note",i.Retain="Keep open notes",i))(it||{}),Tt=(n=>(n.Default="Default view",n.Reading="Reading view",n.Source="Editing view (Source)",n.LivePreview="Editing view (Live Preview)",n))(Tt||{}),_e=(l=>(l.File="File",l.Workspace="Workspace",l.Random="Random file",l.Graph="Graph view",l.DailyNote="Daily Note",l.WeeklyNote="Weekly Note",l.MonthlyNote="Monthly Note",l.YearlyNote="Yearly Note",l.MomentDate="Date-dependent file",l))(_e||{}),Be=class{constructor(e,o){this.lastView=void 0;this.openedViews=new WeakMap;this.name=e,this.plugin=o,this.app=o.app,this.data=o.settings.homepages[e],this.data.commands||(this.data.commands=[],this.save()),this.data?.hasRibbonIcon==!1&&this.app.workspace.onLayoutReady(async()=>{let i=this.app.workspace.leftRibbon;i.items.find(n=>n.id==="homepage:Open homepage").hidden=!0,i.onChange(!0),delete this.data.hasRibbonIcon,this.save()})}async open(e=!1){if(this.plugin.hasRequiredPlugin(this.data.kind))if(this.data.kind=="Workspace")await this.launchWorkspace();else{let o=this.plugin.loaded?this.data.manualOpenMode:this.data.openMode;e&&(o="Keep open notes"),await this.launchLeaf(o)}else{new q.Notice("Homepage cannot be opened due to plugin unavailablity.");return}if(this.data.commands)for(let o of this.data.commands)this.app.commands.executeCommandById(o)}async launchWorkspace(){let e=this.plugin.internalPlugins.workspaces?.instance;if(!(this.data.value in e.workspaces)){new q.Notice(`Cannot find the workspace "${this.data.value}" to use as the homepage.`);return}e.loadWorkspace(this.data.value)}async launchLeaf(e){let o;if(this.computedValue=await this.computeValue(),this.plugin.executing=!0,!(tt(this.plugin)&&!this.plugin.loaded)){if(e!="Replace all open notes"){let i=this.getOpened();if(i.length>0){this.app.workspace.setActiveLeaf(i[0]),await this.configure(i[0]);return}else e=="Keep open notes"&&ro(this.app)&&(e="Replace last note")}this.data.kind==="Graph view"?o=await this.launchGraph(e):o=await this.launchNote(e),o&&(await this.configure(o),e=="Replace all open notes"&&(this.app.workspace.iterateAllLeaves(i=>{So.includes(i.getViewState().type)&&o.id!=i.id&&i.detach()}),this.app.workspace.detachLeavesOfType("empty")))}}async launchGraph(e){if(e==="Keep open notes"){let o=this.app.workspace.getLeaf("tab");this.app.workspace.setActiveLeaf(o)}return this.app.commands.executeCommandById("graph:open"),this.app.workspace.getActiveViewOfType(q.View)?.leaf}async launchNote(e){let o=this.app.metadataCache.getFirstLinkpathDest(this.computedValue,"/");if(!o){if(!this.data.autoCreate){new q.Notice(`Homepage "${this.computedValue}" does not exist.`);return}o=await this.app.vault.create(io(this.computedValue),"")}let i=this.app.workspace.getLeaf(e=="Keep open notes");return await i.openFile(o),this.app.workspace.setActiveLeaf(i),i}async configure(e){this.plugin.executing=!1;let o=e.view;if(!(o instanceof q.MarkdownView)){this.data.pin&&o.leaf.setPinned(!0);return}let i=o.getState();if(this.data.revertView&&(this.lastView=new WeakRef(o)),this.data.autoScroll){let n=o.editor.lineCount();i.mode=="preview"?o.previewMode.applyScroll(n-4):(o.editor.setCursor(n),o.editor.focus())}if(this.data.pin&&o.leaf.setPinned(!0),this.data.view!="Default view"){switch(this.data.view){case"Editing view (Live Preview)":case"Editing view (Source)":i.mode="source",i.source=this.data.view!="Editing view (Live Preview)";break;case"Reading view":i.mode="preview";break}await o.leaf.setViewState({type:"markdown",state:i}),this.plugin.loaded&&this.data.refreshDataview&&this.plugin.communityPlugins.dataview?.index.touch()}}getOpened(){return this.data.kind=="Graph view"?this.app.workspace.getLeavesOfType("graph"):So.flatMap(o=>this.app.workspace.getLeavesOfType(o)).filter(o=>ee(o.view.file)==this.computedValue)}async computeValue(){let e=this.data.value;switch(this.data.kind){case"Date-dependent file":e=(0,q.moment)().format(this.data.value);break;case"Random file":let o=ao(this.app);o&&(e=o);break;case"Daily Note":case"Weekly Note":case"Monthly Note":case"Yearly Note":e=await Eo(this.data.kind,this.plugin);break}return e}async save(){this.plugin.settings.homepages[this.name]=this.data,await this.plugin.saveSettings()}async revertView(){if(this.lastView==null||this.data.view=="Default view")return;let e=this.lastView.deref();if(!e||ee(e.file)==this.computedValue)return;let o=e.getState(),i=this.app.vault.config,n=i.defaultViewMode||"source",a=i.livePreview!==void 0?!i.livePreview:!1;e.leaf.getViewState().type=="markdown"&&(n!=o.mode||a!=o.source)&&(o.mode=n,o.source=a,await e.leaf.setViewState({type:"markdown",state:o,active:!0})),this.lastView=void 0}async openWhenEmpty(){if(!this.plugin.loaded)return;let e=this.app.workspace.getActiveViewOfType(q.View)?.leaf;e?.getViewState().type!=="empty"||e?.parentSplit.children.length!=1||await this.open()}async apply(){let e=this.app.workspace.getActiveViewOfType(q.FileView);if(!e)return;let o=ee(e.file);this.openedViews.get(e)!==o&&(this.openedViews.set(e,o),o===await this.computeValue()&&this.plugin.loaded&&!this.plugin.executing&&await this.configure(e.leaf))}};var W=require("obsidian");var Me=require("obsidian");var Ko=require("obsidian");var k="top",H="bottom",A="right",M="left",nt="auto",ae=[k,H,A,M],te="start",fe="end",Do="clippingParents",at="viewport",Oe="popper",To="reference",kt=ae.reduce(function(t,e){return t.concat([e+"-"+te,e+"-"+fe])},[]),rt=[].concat(ae,[nt]).reduce(function(t,e){return t.concat([e,e+"-"+te,e+"-"+fe])},[]),Ai="beforeRead",Li="read",Ci="afterRead",Fi="beforeMain",Hi="main",Ri="afterMain",Vi="beforeWrite",Ii="write",Wi="afterWrite",ko=[Ai,Li,Ci,Fi,Hi,Ri,Vi,Ii,Wi];function V(t){return t?(t.nodeName||"").toLowerCase():null}function S(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function U(t){var e=S(t).Element;return t instanceof e||t instanceof Element}function R(t){var e=S(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function Se(t){if(typeof ShadowRoot>"u")return!1;var e=S(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function _i(t){var e=t.state;Object.keys(e.elements).forEach(function(o){var i=e.styles[o]||{},n=e.attributes[o]||{},a=e.elements[o];!R(a)||!V(a)||(Object.assign(a.style,i),Object.keys(n).forEach(function(p){var s=n[p];s===!1?a.removeAttribute(p):a.setAttribute(p,s===!0?"":s)}))})}function Bi(t){var e=t.state,o={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,o.popper),e.styles=o,e.elements.arrow&&Object.assign(e.elements.arrow.style,o.arrow),function(){Object.keys(e.elements).forEach(function(i){var n=e.elements[i],a=e.attributes[i]||{},p=Object.keys(e.styles.hasOwnProperty(i)?e.styles[i]:o[i]),s=p.reduce(function(r,l){return r[l]="",r},{});!R(n)||!V(n)||(Object.assign(n.style,s),Object.keys(a).forEach(function(r){n.removeAttribute(r)}))})}}var Mo={name:"applyStyles",enabled:!0,phase:"write",fn:_i,effect:Bi,requires:["computeStyles"]};function I(t){return t.split("-")[0]}var X=Math.max,ge=Math.min,oe=Math.round;function De(){var t=navigator.userAgentData;return t!=null&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function je(){return!/^((?!chrome|android).)*safari/i.test(De())}function z(t,e,o){e===void 0&&(e=!1),o===void 0&&(o=!1);var i=t.getBoundingClientRect(),n=1,a=1;e&&R(t)&&(n=t.offsetWidth>0&&oe(i.width)/t.offsetWidth||1,a=t.offsetHeight>0&&oe(i.height)/t.offsetHeight||1);var p=U(t)?S(t):window,s=p.visualViewport,r=!je()&&o,l=(i.left+(r&&s?s.offsetLeft:0))/n,c=(i.top+(r&&s?s.offsetTop:0))/a,g=i.width/n,v=i.height/a;return{width:g,height:v,top:c,right:l+g,bottom:c+v,left:l,x:l,y:c}}function he(t){var e=z(t),o=t.offsetWidth,i=t.offsetHeight;return Math.abs(e.width-o)<=1&&(o=e.width),Math.abs(e.height-i)<=1&&(i=e.height),{x:t.offsetLeft,y:t.offsetTop,width:o,height:i}}function Ye(t,e){var o=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(o&&Se(o)){var i=e;do{if(i&&t.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function j(t){return S(t).getComputedStyle(t)}function Mt(t){return["table","td","th"].indexOf(V(t))>=0}function _(t){return((U(t)?t.ownerDocument:t.document)||window.document).documentElement}function ie(t){return V(t)==="html"?t:t.assignedSlot||t.parentNode||(Se(t)?t.host:null)||_(t)}function Ao(t){return!R(t)||j(t).position==="fixed"?null:t.offsetParent}function ji(t){var e=/firefox/i.test(De()),o=/Trident/i.test(De());if(o&&R(t)){var i=j(t);if(i.position==="fixed")return null}var n=ie(t);for(Se(n)&&(n=n.host);R(n)&&["html","body"].indexOf(V(n))<0;){var a=j(n);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||e&&a.willChange==="filter"||e&&a.filter&&a.filter!=="none")return n;n=n.parentNode}return null}function J(t){for(var e=S(t),o=Ao(t);o&&Mt(o)&&j(o).position==="static";)o=Ao(o);return o&&(V(o)==="html"||V(o)==="body"&&j(o).position==="static")?e:o||ji(t)||e}function ve(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function we(t,e,o){return X(t,ge(e,o))}function Lo(t,e,o){var i=we(t,e,o);return i>o?o:i}function $e(){return{top:0,right:0,bottom:0,left:0}}function qe(t){return Object.assign({},$e(),t)}function Ue(t,e){return e.reduce(function(o,i){return o[i]=t,o},{})}var Yi=function(e,o){return e=typeof e=="function"?e(Object.assign({},o.rects,{placement:o.placement})):e,qe(typeof e!="number"?e:Ue(e,ae))};function $i(t){var e,o=t.state,i=t.name,n=t.options,a=o.elements.arrow,p=o.modifiersData.popperOffsets,s=I(o.placement),r=ve(s),l=[M,A].indexOf(s)>=0,c=l?"height":"width";if(!(!a||!p)){var g=Yi(n.padding,o),v=he(a),d=r==="y"?k:M,w=r==="y"?H:A,m=o.rects.reference[c]+o.rects.reference[r]-p[r]-o.rects.popper[c],u=p[r]-o.rects.reference[r],f=J(a),P=f?r==="y"?f.clientHeight||0:f.clientWidth||0:0,E=m/2-u/2,y=g[d],b=P-v[c]-g[w],x=P/2-v[c]/2+E,D=we(y,x,b),L=r;o.modifiersData[i]=(e={},e[L]=D,e.centerOffset=D-x,e)}}function qi(t){var e=t.state,o=t.options,i=o.element,n=i===void 0?"[data-popper-arrow]":i;n!=null&&(typeof n=="string"&&(n=e.elements.popper.querySelector(n),!n)||Ye(e.elements.popper,n)&&(e.elements.arrow=n))}var Co={name:"arrow",enabled:!0,phase:"main",fn:$i,effect:qi,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function K(t){return t.split("-")[1]}var Ui={top:"auto",right:"auto",bottom:"auto",left:"auto"};function zi(t,e){var o=t.x,i=t.y,n=e.devicePixelRatio||1;return{x:oe(o*n)/n||0,y:oe(i*n)/n||0}}function Fo(t){var e,o=t.popper,i=t.popperRect,n=t.placement,a=t.variation,p=t.offsets,s=t.position,r=t.gpuAcceleration,l=t.adaptive,c=t.roundOffsets,g=t.isFixed,v=p.x,d=v===void 0?0:v,w=p.y,m=w===void 0?0:w,u=typeof c=="function"?c({x:d,y:m}):{x:d,y:m};d=u.x,m=u.y;var f=p.hasOwnProperty("x"),P=p.hasOwnProperty("y"),E=M,y=k,b=window;if(l){var x=J(o),D="clientHeight",L="clientWidth";if(x===S(o)&&(x=_(o),j(x).position!=="static"&&s==="absolute"&&(D="scrollHeight",L="scrollWidth")),x=x,n===k||(n===M||n===A)&&a===fe){y=H;var C=g&&x===b&&b.visualViewport?b.visualViewport.height:x[D];m-=C-i.height,m*=r?1:-1}if(n===M||(n===k||n===H)&&a===fe){E=A;var F=g&&x===b&&b.visualViewport?b.visualViewport.width:x[L];d-=F-i.width,d*=r?1:-1}}var N=Object.assign({position:s},l&&Ui),Y=c===!0?zi({x:d,y:m},S(o)):{x:d,y:m};if(d=Y.x,m=Y.y,r){var B;return Object.assign({},N,(B={},B[y]=P?"0":"",B[E]=f?"0":"",B.transform=(b.devicePixelRatio||1)<=1?"translate("+d+"px, "+m+"px)":"translate3d("+d+"px, "+m+"px, 0)",B))}return Object.assign({},N,(e={},e[y]=P?m+"px":"",e[E]=f?d+"px":"",e.transform="",e))}function Ki(t){var e=t.state,o=t.options,i=o.gpuAcceleration,n=i===void 0?!0:i,a=o.adaptive,p=a===void 0?!0:a,s=o.roundOffsets,r=s===void 0?!0:s;if(!1)var l;var c={placement:I(e.placement),variation:K(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:n,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Fo(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:p,roundOffsets:r})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Fo(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:r})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var Ho={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Ki,data:{}};var st={passive:!0};function Gi(t){var e=t.state,o=t.instance,i=t.options,n=i.scroll,a=n===void 0?!0:n,p=i.resize,s=p===void 0?!0:p,r=S(e.elements.popper),l=[].concat(e.scrollParents.reference,e.scrollParents.popper);return a&&l.forEach(function(c){c.addEventListener("scroll",o.update,st)}),s&&r.addEventListener("resize",o.update,st),function(){a&&l.forEach(function(c){c.removeEventListener("scroll",o.update,st)}),s&&r.removeEventListener("resize",o.update,st)}}var Ro={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Gi,data:{}};var Qi={left:"right",right:"left",bottom:"top",top:"bottom"};function Te(t){return t.replace(/left|right|bottom|top/g,function(e){return Qi[e]})}var Xi={start:"end",end:"start"};function lt(t){return t.replace(/start|end/g,function(e){return Xi[e]})}function ye(t){var e=S(t),o=e.pageXOffset,i=e.pageYOffset;return{scrollLeft:o,scrollTop:i}}function be(t){return z(_(t)).left+ye(t).scrollLeft}function At(t,e){var o=S(t),i=_(t),n=o.visualViewport,a=i.clientWidth,p=i.clientHeight,s=0,r=0;if(n){a=n.width,p=n.height;var l=je();(l||!l&&e==="fixed")&&(s=n.offsetLeft,r=n.offsetTop)}return{width:a,height:p,x:s+be(t),y:r}}function Lt(t){var e,o=_(t),i=ye(t),n=(e=t.ownerDocument)==null?void 0:e.body,a=X(o.scrollWidth,o.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),p=X(o.scrollHeight,o.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),s=-i.scrollLeft+be(t),r=-i.scrollTop;return j(n||o).direction==="rtl"&&(s+=X(o.clientWidth,n?n.clientWidth:0)-a),{width:a,height:p,x:s,y:r}}function xe(t){var e=j(t),o=e.overflow,i=e.overflowX,n=e.overflowY;return/auto|scroll|overlay|hidden/.test(o+n+i)}function pt(t){return["html","body","#document"].indexOf(V(t))>=0?t.ownerDocument.body:R(t)&&xe(t)?t:pt(ie(t))}function re(t,e){var o;e===void 0&&(e=[]);var i=pt(t),n=i===((o=t.ownerDocument)==null?void 0:o.body),a=S(i),p=n?[a].concat(a.visualViewport||[],xe(i)?i:[]):i,s=e.concat(p);return n?s:s.concat(re(ie(p)))}function ke(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Ji(t,e){var o=z(t,!1,e==="fixed");return o.top=o.top+t.clientTop,o.left=o.left+t.clientLeft,o.bottom=o.top+t.clientHeight,o.right=o.left+t.clientWidth,o.width=t.clientWidth,o.height=t.clientHeight,o.x=o.left,o.y=o.top,o}function Vo(t,e,o){return e===at?ke(At(t,o)):U(e)?Ji(e,o):ke(Lt(_(t)))}function Zi(t){var e=re(ie(t)),o=["absolute","fixed"].indexOf(j(t).position)>=0,i=o&&R(t)?J(t):t;return U(i)?e.filter(function(n){return U(n)&&Ye(n,i)&&V(n)!=="body"}):[]}function Ct(t,e,o,i){var n=e==="clippingParents"?Zi(t):[].concat(e),a=[].concat(n,[o]),p=a[0],s=a.reduce(function(r,l){var c=Vo(t,l,i);return r.top=X(c.top,r.top),r.right=ge(c.right,r.right),r.bottom=ge(c.bottom,r.bottom),r.left=X(c.left,r.left),r},Vo(t,p,i));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function ze(t){var e=t.reference,o=t.element,i=t.placement,n=i?I(i):null,a=i?K(i):null,p=e.x+e.width/2-o.width/2,s=e.y+e.height/2-o.height/2,r;switch(n){case k:r={x:p,y:e.y-o.height};break;case H:r={x:p,y:e.y+e.height};break;case A:r={x:e.x+e.width,y:s};break;case M:r={x:e.x-o.width,y:s};break;default:r={x:e.x,y:e.y}}var l=n?ve(n):null;if(l!=null){var c=l==="y"?"height":"width";switch(a){case te:r[l]=r[l]-(e[c]/2-o[c]/2);break;case fe:r[l]=r[l]+(e[c]/2-o[c]/2);break;default:}}return r}function Z(t,e){e===void 0&&(e={});var o=e,i=o.placement,n=i===void 0?t.placement:i,a=o.strategy,p=a===void 0?t.strategy:a,s=o.boundary,r=s===void 0?Do:s,l=o.rootBoundary,c=l===void 0?at:l,g=o.elementContext,v=g===void 0?Oe:g,d=o.altBoundary,w=d===void 0?!1:d,m=o.padding,u=m===void 0?0:m,f=qe(typeof u!="number"?u:Ue(u,ae)),P=v===Oe?To:Oe,E=t.rects.popper,y=t.elements[w?P:v],b=Ct(U(y)?y:y.contextElement||_(t.elements.popper),r,c,p),x=z(t.elements.reference),D=ze({reference:x,element:E,strategy:"absolute",placement:n}),L=ke(Object.assign({},E,D)),C=v===Oe?L:x,F={top:b.top-C.top+f.top,bottom:C.bottom-b.bottom+f.bottom,left:b.left-C.left+f.left,right:C.right-b.right+f.right},N=t.modifiersData.offset;if(v===Oe&&N){var Y=N[n];Object.keys(F).forEach(function(B){var le=[A,H].indexOf(B)>=0?1:-1,pe=[k,H].indexOf(B)>=0?"y":"x";F[B]+=Y[pe]*le})}return F}function Ft(t,e){e===void 0&&(e={});var o=e,i=o.placement,n=o.boundary,a=o.rootBoundary,p=o.padding,s=o.flipVariations,r=o.allowedAutoPlacements,l=r===void 0?rt:r,c=K(i),g=c?s?kt:kt.filter(function(w){return K(w)===c}):ae,v=g.filter(function(w){return l.indexOf(w)>=0});v.length===0&&(v=g);var d=v.reduce(function(w,m){return w[m]=Z(t,{placement:m,boundary:n,rootBoundary:a,padding:p})[I(m)],w},{});return Object.keys(d).sort(function(w,m){return d[w]-d[m]})}function en(t){if(I(t)===nt)return[];var e=Te(t);return[lt(t),e,lt(e)]}function tn(t){var e=t.state,o=t.options,i=t.name;if(!e.modifiersData[i]._skip){for(var n=o.mainAxis,a=n===void 0?!0:n,p=o.altAxis,s=p===void 0?!0:p,r=o.fallbackPlacements,l=o.padding,c=o.boundary,g=o.rootBoundary,v=o.altBoundary,d=o.flipVariations,w=d===void 0?!0:d,m=o.allowedAutoPlacements,u=e.options.placement,f=I(u),P=f===u,E=r||(P||!w?[Te(u)]:en(u)),y=[u].concat(E).reduce(function(Pe,ne){return Pe.concat(I(ne)===nt?Ft(e,{placement:ne,boundary:c,rootBoundary:g,padding:l,flipVariations:w,allowedAutoPlacements:m}):ne)},[]),b=e.rects.reference,x=e.rects.popper,D=new Map,L=!0,C=y[0],F=0;F<y.length;F++){var N=y[F],Y=I(N),B=K(N)===te,le=[k,H].indexOf(Y)>=0,pe=le?"width":"height",$=Z(e,{placement:N,boundary:c,rootBoundary:g,altBoundary:v,padding:l}),G=le?B?A:M:B?H:k;b[pe]>x[pe]&&(G=Te(G));var Ge=Te(G),ce=[];if(a&&ce.push($[Y]<=0),s&&ce.push($[G]<=0,$[Ge]<=0),ce.every(function(Pe){return Pe})){C=N,L=!1;break}D.set(N,ce)}if(L)for(var Qe=w?3:1,gt=function(ne){var Le=y.find(function(Je){var de=D.get(Je);if(de)return de.slice(0,ne).every(function(ht){return ht})});if(Le)return C=Le,"break"},Ae=Qe;Ae>0;Ae--){var Xe=gt(Ae);if(Xe==="break")break}e.placement!==C&&(e.modifiersData[i]._skip=!0,e.placement=C,e.reset=!0)}}var Io={name:"flip",enabled:!0,phase:"main",fn:tn,requiresIfExists:["offset"],data:{_skip:!1}};function Wo(t,e,o){return o===void 0&&(o={x:0,y:0}),{top:t.top-e.height-o.y,right:t.right-e.width+o.x,bottom:t.bottom-e.height+o.y,left:t.left-e.width-o.x}}function _o(t){return[k,A,H,M].some(function(e){return t[e]>=0})}function on(t){var e=t.state,o=t.name,i=e.rects.reference,n=e.rects.popper,a=e.modifiersData.preventOverflow,p=Z(e,{elementContext:"reference"}),s=Z(e,{altBoundary:!0}),r=Wo(p,i),l=Wo(s,n,a),c=_o(r),g=_o(l);e.modifiersData[o]={referenceClippingOffsets:r,popperEscapeOffsets:l,isReferenceHidden:c,hasPopperEscaped:g},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":g})}var Bo={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:on};function nn(t,e,o){var i=I(t),n=[M,k].indexOf(i)>=0?-1:1,a=typeof o=="function"?o(Object.assign({},e,{placement:t})):o,p=a[0],s=a[1];return p=p||0,s=(s||0)*n,[M,A].indexOf(i)>=0?{x:s,y:p}:{x:p,y:s}}function an(t){var e=t.state,o=t.options,i=t.name,n=o.offset,a=n===void 0?[0,0]:n,p=rt.reduce(function(c,g){return c[g]=nn(g,e.rects,a),c},{}),s=p[e.placement],r=s.x,l=s.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=r,e.modifiersData.popperOffsets.y+=l),e.modifiersData[i]=p}var jo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:an};function rn(t){var e=t.state,o=t.name;e.modifiersData[o]=ze({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var Yo={name:"popperOffsets",enabled:!0,phase:"read",fn:rn,data:{}};function Ht(t){return t==="x"?"y":"x"}function sn(t){var e=t.state,o=t.options,i=t.name,n=o.mainAxis,a=n===void 0?!0:n,p=o.altAxis,s=p===void 0?!1:p,r=o.boundary,l=o.rootBoundary,c=o.altBoundary,g=o.padding,v=o.tether,d=v===void 0?!0:v,w=o.tetherOffset,m=w===void 0?0:w,u=Z(e,{boundary:r,rootBoundary:l,padding:g,altBoundary:c}),f=I(e.placement),P=K(e.placement),E=!P,y=ve(f),b=Ht(y),x=e.modifiersData.popperOffsets,D=e.rects.reference,L=e.rects.popper,C=typeof m=="function"?m(Object.assign({},e.rects,{placement:e.placement})):m,F=typeof C=="number"?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),N=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,Y={x:0,y:0};if(x){if(a){var B,le=y==="y"?k:M,pe=y==="y"?H:A,$=y==="y"?"height":"width",G=x[y],Ge=G+u[le],ce=G-u[pe],Qe=d?-L[$]/2:0,gt=P===te?D[$]:L[$],Ae=P===te?-L[$]:-D[$],Xe=e.elements.arrow,Pe=d&&Xe?he(Xe):{width:0,height:0},ne=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:$e(),Le=ne[le],Je=ne[pe],de=we(0,D[$],Pe[$]),ht=E?D[$]/2-Qe-de-Le-F.mainAxis:gt-de-Le-F.mainAxis,Go=E?-D[$]/2+Qe+de+Je+F.mainAxis:Ae+de+Je+F.mainAxis,vt=e.elements.arrow&&J(e.elements.arrow),Qo=vt?y==="y"?vt.clientTop||0:vt.clientLeft||0:0,Ut=(B=N?.[y])!=null?B:0,Xo=G+ht-Ut-Qo,Jo=G+Go-Ut,zt=we(d?ge(Ge,Xo):Ge,G,d?X(ce,Jo):ce);x[y]=zt,Y[y]=zt-G}if(s){var Kt,Zo=y==="x"?k:M,ei=y==="x"?H:A,ue=x[b],Ze=b==="y"?"height":"width",Gt=ue+u[Zo],Qt=ue-u[ei],wt=[k,M].indexOf(f)!==-1,Xt=(Kt=N?.[b])!=null?Kt:0,Jt=wt?Gt:ue-D[Ze]-L[Ze]-Xt+F.altAxis,Zt=wt?ue+D[Ze]+L[Ze]-Xt-F.altAxis:Qt,eo=d&&wt?Lo(Jt,ue,Zt):we(d?Jt:Gt,ue,d?Zt:Qt);x[b]=eo,Y[b]=eo-ue}e.modifiersData[i]=Y}}var $o={name:"preventOverflow",enabled:!0,phase:"main",fn:sn,requiresIfExists:["offset"]};function Rt(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function Vt(t){return t===S(t)||!R(t)?ye(t):Rt(t)}function ln(t){var e=t.getBoundingClientRect(),o=oe(e.width)/t.offsetWidth||1,i=oe(e.height)/t.offsetHeight||1;return o!==1||i!==1}function It(t,e,o){o===void 0&&(o=!1);var i=R(e),n=R(e)&&ln(e),a=_(e),p=z(t,n,o),s={scrollLeft:0,scrollTop:0},r={x:0,y:0};return(i||!i&&!o)&&((V(e)!=="body"||xe(a))&&(s=Vt(e)),R(e)?(r=z(e,!0),r.x+=e.clientLeft,r.y+=e.clientTop):a&&(r.x=be(a))),{x:p.left+s.scrollLeft-r.x,y:p.top+s.scrollTop-r.y,width:p.width,height:p.height}}function pn(t){var e=new Map,o=new Set,i=[];t.forEach(function(a){e.set(a.name,a)});function n(a){o.add(a.name);var p=[].concat(a.requires||[],a.requiresIfExists||[]);p.forEach(function(s){if(!o.has(s)){var r=e.get(s);r&&n(r)}}),i.push(a)}return t.forEach(function(a){o.has(a.name)||n(a)}),i}function Wt(t){var e=pn(t);return ko.reduce(function(o,i){return o.concat(e.filter(function(n){return n.phase===i}))},[])}function _t(t){var e;return function(){return e||(e=new Promise(function(o){Promise.resolve().then(function(){e=void 0,o(t())})})),e}}function Bt(t){var e=t.reduce(function(o,i){var n=o[i.name];return o[i.name]=n?Object.assign({},n,i,{options:Object.assign({},n.options,i.options),data:Object.assign({},n.data,i.data)}):i,o},{});return Object.keys(e).map(function(o){return e[o]})}var qo={placement:"bottom",modifiers:[],strategy:"absolute"};function Uo(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];return!e.some(function(i){return!(i&&typeof i.getBoundingClientRect=="function")})}function zo(t){t===void 0&&(t={});var e=t,o=e.defaultModifiers,i=o===void 0?[]:o,n=e.defaultOptions,a=n===void 0?qo:n;return function(s,r,l){l===void 0&&(l=a);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},qo,a),modifiersData:{},elements:{reference:s,popper:r},attributes:{},styles:{}},g=[],v=!1,d={state:c,setOptions:function(f){var P=typeof f=="function"?f(c.options):f;m(),c.options=Object.assign({},a,c.options,P),c.scrollParents={reference:U(s)?re(s):s.contextElement?re(s.contextElement):[],popper:re(r)};var E=Wt(Bt([].concat(i,c.options.modifiers)));if(c.orderedModifiers=E.filter(function(N){return N.enabled}),!1){var y;if(getBasePlacement(c.options.placement)===auto)var b;var x,D,L,C,F}return w(),d.update()},forceUpdate:function(){if(!v){var f=c.elements,P=f.reference,E=f.popper;if(Uo(P,E)){c.rects={reference:It(P,J(E),c.options.strategy==="fixed"),popper:he(E)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(N){return c.modifiersData[N.name]=Object.assign({},N.data)});for(var y=0,b=0;b<c.orderedModifiers.length;b++){if(c.reset===!0){c.reset=!1,b=-1;continue}var x=c.orderedModifiers[b],D=x.fn,L=x.options,C=L===void 0?{}:L,F=x.name;typeof D=="function"&&(c=D({state:c,options:C,name:F,instance:d})||c)}}}},update:_t(function(){return new Promise(function(u){d.forceUpdate(),u(c)})}),destroy:function(){m(),v=!0}};if(!Uo(s,r))return d;d.setOptions(l).then(function(u){!v&&l.onFirstUpdate&&l.onFirstUpdate(u)});function w(){c.orderedModifiers.forEach(function(u){var f=u.name,P=u.options,E=P===void 0?{}:P,y=u.effect;if(typeof y=="function"){var b=y({state:c,name:f,instance:d,options:E}),x=function(){};g.push(b||x)}})}function m(){g.forEach(function(u){return u()}),g=[]}return d}}var cn=[Ro,Yo,Ho,Mo,jo,Io,$o,Co,Bo],jt=zo({defaultModifiers:cn});var Yt=class{constructor(e,o,i){this.owner=e,this.containerEl=o,o.on("click",".suggestion-item",this.onSuggestionClick.bind(this)),o.on("mousemove",".suggestion-item",this.onSuggestionMouseover.bind(this)),i.register([],"ArrowUp",n=>{if(!n.isComposing)return this.setSelectedItem(this.selectedItem-1,!0),!1}),i.register([],"ArrowDown",n=>{if(!n.isComposing)return this.setSelectedItem(this.selectedItem+1,!0),!1}),i.register([],"Enter",n=>{if(!n.isComposing)return this.useSelectedItem(n),!1})}onSuggestionClick(e,o){e.preventDefault();let i=this.suggestions.indexOf(o);this.setSelectedItem(i,!1),this.useSelectedItem(e)}onSuggestionMouseover(e,o){let i=this.suggestions.indexOf(o);this.setSelectedItem(i,!1)}setSuggestions(e){this.containerEl.empty();let o=[];e.forEach(i=>{let n=this.containerEl.createDiv("suggestion-item");this.owner.renderSuggestion(i,n),o.push(n)}),this.values=e,this.suggestions=o,this.setSelectedItem(0,!1)}useSelectedItem(e){let o=this.values[this.selectedItem];o&&this.owner.selectSuggestion(o,e)}setSelectedItem(e,o){let i=no(e,this.suggestions.length),n=this.suggestions[this.selectedItem],a=this.suggestions[i];n?.removeClass("is-selected"),a?.addClass("is-selected"),this.selectedItem=i,o&&a.scrollIntoView(!1)}},Ke=class{constructor(e,o){this.app=e,this.inputEl=o,this.scope=new Ko.Scope,this.suggestEl=createDiv("suggestion-container");let i=this.suggestEl.createDiv("suggestion");this.suggest=new Yt(this,i,this.scope),this.scope.register([],"Escape",this.close.bind(this)),this.inputEl.addEventListener("input",this.onInputChanged.bind(this)),this.inputEl.addEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.addEventListener("blur",this.close.bind(this)),this.suggestEl.on("mousedown",".suggestion-container",n=>{n.preventDefault()})}onInputChanged(){let e=this.inputEl.value,o=this.getSuggestions(e);o.length>0&&(this.suggest.setSuggestions(o),this.open(this.app.dom.appContainerEl,this.inputEl))}open(e,o){this.app.keymap.pushScope(this.scope),e.appendChild(this.suggestEl),this.popper=jt(o,this.suggestEl,{placement:"bottom-start",modifiers:[{name:"sameWidth",enabled:!0,fn:({state:i,instance:n})=>{let a=`${i.rects.reference.width+100}px`;i.styles.popper.width!==a&&(i.styles.popper.width=a,n.update())},phase:"beforeWrite",requires:["computeStyles"]}]})}close(){this.app.keymap.popScope(this.scope),this.suggest.setSuggestions([]),this.popper&&this.popper.destroy(),this.suggestEl.detach()}};var ct=class extends Ke{getSuggestions(e){let o=this.app.vault.getAllLoadedFiles(),i=[],n=e.toLowerCase();return o.forEach(a=>{a instanceof Me.TFile&&["md","canvas"].contains(a.extension)&&a.path.toLowerCase().contains(n)&&i.push(a)}),i}renderSuggestion(e,o){e.extension=="md"?o.setText(ee(e)):(o.setText(e.path.slice(0,-7)),o.insertAdjacentHTML("beforeend",'<div class="nav-file-tag" style="display:inline-block;vertical-align:middle">canvas</div>'))}selectSuggestion(e){this.inputEl.value=ee(e),this.inputEl.trigger("input"),this.close()}},dt=class extends Ke{getSuggestions(e){let o=Object.keys(this.app.internalPlugins.plugins.workspaces?.instance.workspaces),i=e.toLowerCase();return o.filter(n=>n.toLowerCase().contains(i))}renderSuggestion(e,o){o.setText(e)}selectSuggestion(e){this.inputEl.value=e,this.inputEl.trigger("input"),this.close()}},ut=class extends Me.FuzzySuggestModal{constructor(o){super(o.plugin.app);this.homepage=o.plugin.homepage,this.tab=o}getItems(){return Object.values(this.app.commands.commands)}getItemText(o){return o.name}onChooseItem(o){if(o.id==="homepage:open-homepage"){new Me.Notice("Really?");return}else this.homepage.data.commands||(this.homepage.data.commands=[]);this.homepage.data.commands.push(o.id),this.homepage.save(),this.tab.updateCommandBox()}};var qt={version:3,homepages:{[me]:{value:"Home",kind:"File",openOnStartup:!0,openMode:"Replace all open notes",manualOpenMode:"Keep open notes",view:"Default view",revertView:!0,openWhenEmpty:!1,refreshDataview:!1,autoCreate:!0,autoScroll:!1,pin:!1,commands:[],alwaysApply:!1}},separateMobile:!1},$t=qt.homepages[me],dn=["Random file","Graph view",...Dt],mt=class extends W.PluginSettingTab{constructor(o,i){super(o,i);this.plugin=i,this.settings=i.settings}sanitiseNote(o){return o===null||o.match(/^\s*$/)!==null?null:(0,W.normalizePath)(o)}display(){let o=this.plugin.homepage.data.kind=="Workspace",i=tt(this.plugin),n=!1,a=document.createElement("article"),p=o?dt:ct;this.containerEl.empty(),this.elements={},a.id="nv-desc";let s=new W.Setting(this.containerEl).setName("Homepage").addDropdown(async r=>{for(let l of Object.values(_e)){if(!this.plugin.hasRequiredPlugin(l))if(l==this.plugin.homepage.data.kind)n=!0;else continue;let c=l;l=="Date-dependent file"&&(c="Moment (legacy)"),r.addOption(l,c)}r.setValue(this.plugin.homepage.data.kind),r.onChange(async l=>{this.plugin.homepage.data.kind=l,await this.plugin.homepage.save(),this.display()})});switch(s.settingEl.id="nv-main-setting",s.settingEl.append(a),this.plugin.homepage.data.kind){case"File":a.innerHTML="Enter a note or canvas to use.";break;case"Workspace":a.innerHTML="Enter an Obsidian workspace to use.";break;case"Graph view":a.innerHTML="Your graph view will be used.";break;case"Date-dependent file":a.innerHTML=`<span class="mod-warning">This type is deprecated and will eventually be removed. Use Daily/Weekly/Monthly/Yearly Note instead, which works natively with Daily and Periodic Notes.</span><br>
				Enter a note or canvas to use based on <a href="https://momentjs.com/docs/#/displaying/format/" target="_blank" rel="noopener">Moment date formatting</a>.<small> Surround words in <code>[brackets]</code> to include them unmodified.
				<br> Currently, your specification will produce: </small>`;break;case"Random file":a.innerHTML="A random note or canvas from your Obsidian folder will be selected.";break;case"Daily Note":a.innerHTML="Your Daily Note or Periodic Daily Note will be used.";break;case"Weekly Note":case"Monthly Note":case"Yearly Note":a.innerHTML=`Your Periodic ${this.plugin.homepage.data.kind} will be used.`;break}if(n&&a.createDiv({text:"The plugin required for this homepage type isn't available.",attr:{class:"mod-warning"}}),this.plugin.homepage.data.kind=="Date-dependent file"){let r=a.lastChild.createEl("b",{attr:{class:"u-pop"}});s.addMomentFormat(l=>l.setDefaultFormat("YYYY-MM-DD").setValue(this.plugin.homepage.data.value).onChange(async c=>{this.plugin.homepage.data.value=c,await this.plugin.homepage.save()}).setSampleEl(r))}else dn.includes(this.plugin.homepage.data.kind)?s.addText(r=>{r.setDisabled(!0)}):s.addText(r=>{new p(this.app,r.inputEl),r.setPlaceholder($t.value),r.setValue($t.value==this.plugin.homepage.data.value?"":this.plugin.homepage.data.value),r.onChange(async l=>{this.plugin.homepage.data.value=this.sanitiseNote(l)||$t.value,await this.plugin.homepage.save()})});this.addToggle("Open on startup","When launching Obsidian, open the homepage.","openOnStartup",r=>this.display()),i&&(this.elements.openOnStartup.descEl.createDiv({text:`This setting has been disabled, as it isn't compatible with Daily Notes' "Open daily note on startup" functionality. To use it, disable the Daily Notes setting.`,attr:{class:"mod-warning"}}),this.disableSetting("openOnStartup")),this.addToggle("Open when empty","When there are no tabs open, open the homepage.","openWhenEmpty"),this.addToggle("Use when opening normally","Use homepage settings when opening it normally, such as from a link or the file browser.","alwaysApply"),new W.Setting(this.containerEl).setName("Separate mobile homepage").setDesc("For mobile devices, store the homepage and its settings separately.").addToggle(r=>r.setValue(this.plugin.settings.separateMobile).onChange(async l=>{this.plugin.settings.separateMobile=l,this.plugin.homepage=this.plugin.getHomepage(),await this.plugin.saveSettings(),this.display()})),this.addHeading("Commands","commandsHeading"),this.containerEl.createDiv({cls:"nv-command-desc setting-item-description",text:"Select commands that will be executed when opening the homepage."}),this.commandBox=this.containerEl.createDiv({cls:"nv-command-box"}),this.updateCommandBox(),this.addHeading("Vault environment","vaultHeading"),this.addDropdown("Opening method","Determine how extant tabs and views are affected on startup.","openMode",it),this.addDropdown("Manual opening method","Determine how extant tabs and views are affected when opening with commands or the ribbon button.","manualOpenMode",it),this.addToggle("Auto-create","If the homepage doesn't exist, create a note with the specified name.","autoCreate"),this.addToggle("Pin","Pin the homepage when opening.","pin"),this.addHeading("Opened view","paneHeading"),this.addDropdown("Homepage view","Choose what view to open the homepage in.","view",Tt),this.addToggle("Revert view on close","When navigating away from the homepage, restore the default view.","revertView"),this.addToggle("Auto-scroll","When opening the homepage, scroll to the bottom and focus on the last line.","autoScroll"),"dataview"in this.plugin.communityPlugins&&(this.addToggle("Refresh Dataview","Always attempt to reload Dataview views when opening the homepage.","refreshDataview"),this.elements.refreshDataview.descEl.createDiv({text:"Requires Dataview auto-refresh to be enabled.",attr:{class:"mod-warning"}})),W.Platform.isMobile||new W.ButtonComponent(this.containerEl).setButtonText("Copy debug info").setClass("nv-debug-button").onClick(async()=>await this.copyDebugInfo()),o&&this.disableSettings("openWhenEmpty","alwaysApply","vaultHeading","openMode","manualOpenMode","autoCreate","pin"),(o||this.plugin.homepage.data.kind=="Graph view")&&this.disableSettings("paneHeading","view","revertView","autoScroll","refreshDataview"),(!this.plugin.homepage.data.openOnStartup||i)&&this.disableSetting("openMode"),Dt.includes(this.plugin.homepage.data.kind)&&this.disableSetting("autoCreate")}disableSetting(o){this.elements[o]?.settingEl.setAttribute("nv-greyed","")}disableSettings(...o){o.forEach(i=>this.disableSetting(i))}addHeading(o,i){let n=new W.Setting(this.containerEl).setHeading().setName(o);this.elements[i]=n}addDropdown(o,i,n,a,p){let s=new W.Setting(this.containerEl).setName(o).setDesc(i).addDropdown(async r=>{for(let l of Object.values(a))r.addOption(l,l);r.setValue(this.plugin.homepage.data[n]),r.onChange(async l=>{this.plugin.homepage.data[n]=l,await this.plugin.homepage.save(),p&&p(l)})});this.elements[n]=s}addToggle(o,i,n,a){let p=new W.Setting(this.containerEl).setName(o).setDesc(i).addToggle(s=>s.setValue(this.plugin.homepage.data[n]).onChange(async r=>{this.plugin.homepage.data[n]=r,await this.plugin.homepage.save(),a&&a(r)}));this.elements[n]=p}updateCommandBox(){this.commandBox.innerHTML="";for(let[o,i]of this.plugin.homepage.data.commands.entries()){let n=this.app.commands.findCommand(i);if(!n)continue;let a=this.commandBox.createDiv({cls:"nv-command-pill",text:n.name});new W.ButtonComponent(a).setIcon("trash-2").setClass("clickable-icon").onClick(()=>{this.plugin.homepage.data.commands.splice(o,1),this.plugin.homepage.save(),this.updateCommandBox()})}new W.ButtonComponent(this.commandBox).setClass("nv-command-add-button").setButtonText("Add...").onClick(()=>{new ut(this).open()})}async copyDebugInfo(){let o=this.app.vault.config,i={...this.settings,_defaultViewMode:o.defaultViewMode||"default",_livePreview:o.livePreview!==void 0?o.livePreview:"default",_focusNewTab:o.focusNewTab!==void 0?o.focusNewTab:"default",_plugins:Object.keys(this.plugin.communityPlugins),_internalPlugins:Object.values(this.plugin.internalPlugins).flatMap(n=>n.enabled?[n.instance.id]:[]),_obsidianVersion:window.electron.ipcRenderer.sendSync("version")};await navigator.clipboard.writeText(JSON.stringify(i)),new W.Notice("Copied homepage debug information to clipboard")}};var un='<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5"><path d="M10.025 21H6v-7H3v-1.5L12 3l9 9.5V14h-3v7h-4v-7h-3.975v7Z" style="fill:none;stroke:currentColor;stroke-width:2px"/></svg>',ft=class extends se.Plugin{constructor(){super(...arguments);this.loaded=!1;this.executing=!1;this.onLayoutChange=async()=>{this.homepage.data.revertView&&await this.homepage.revertView(),this.homepage.data.openWhenEmpty&&await this.homepage.openWhenEmpty(),this.homepage.data.alwaysApply&&await this.homepage.apply()}}async onload(){let o=document.body.querySelector(".progress-bar")!==null;this.settings=await this.loadSettings(),this.internalPlugins=this.app.internalPlugins.plugins,this.communityPlugins=this.app.plugins.plugins,this.homepage=this.getHomepage(),this.app.workspace.onLayoutReady(async()=>{let i=this.communityPlugins["new-tab-default-page"];i&&(i._checkForNewTab=i.checkForNewTab,i.checkForNewTab=async n=>{if(!(this&&this.executing))return await i._checkForNewTab(n)}),o&&this.homepage.data.openOnStartup&&await this.homepage.open(),this.loaded=!0}),(0,se.addIcon)("homepage",un),this.addRibbonIcon("homepage","Open homepage",i=>this.homepage.open(i.button==1||i.button==2||se.Keymap.isModifier(i,"Mod"))).setAttribute("id","nv-homepage-icon"),this.registerEvent(this.app.workspace.on("layout-change",this.onLayoutChange)),this.addSettingTab(new mt(this.app,this)),this.addCommand({id:"open-homepage",name:"Open homepage",callback:()=>this.homepage.open()}),console.log(`Homepage: ${this.homepage.data.value} (method: ${this.homepage.data.openMode}, view: ${this.homepage.data.view}, kind: ${this.homepage.data.kind})`)}async onunload(){this.app.workspace.off("layout-change",this.onLayoutChange);let o=this.communityPlugins["new-tab-default-page"];o&&(o.checkForNewTab=o._checkForNewTab)}getHomepage(){return this.settings.separateMobile&&se.Platform.isMobile?(ot in this.settings.homepages||(this.settings.homepages[ot]={...this.settings.homepages[me]}),new Be(ot,this)):new Be(me,this)}async loadSettings(){let o=await this.loadData();if(!o||o.version!==2)return Object.assign({},qt,o);{let i={version:3,homepages:{},separateMobile:!1},n=o;return o.workspaceEnabled?(n.value=n.workspace,n.kind="Workspace"):o.useMoment?(n.value=n.momentFormat,n.kind="Date-dependent file"):(n.value=n.defaultNote,n.kind="File"),n.commands=[],delete n.workspace,delete n.momentFormat,delete n.defaultNote,delete n.useMoment,delete n.workspaceEnabled,i.homepages[me]=n,i}}async saveSettings(){await this.saveData(this.settings)}hasRequiredPlugin(o){switch(o){case"Workspace":return this.internalPlugins.workspaces?.enabled;case"Graph view":return this.internalPlugins.graph?.enabled;case"Daily Note":case"Weekly Note":case"Monthly Note":case"Yearly Note":return No(o,this);default:return!0}}};
