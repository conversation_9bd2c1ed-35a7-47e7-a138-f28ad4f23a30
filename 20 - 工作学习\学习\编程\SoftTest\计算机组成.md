---
title: 计算机组成
date: 2025-07-07 20:56:27
categories:
  - - 笔记
    - 编程
    - 软考
tags:
  - 软件设计师
---
**2025-07-07**🌱上海: ⛅️  🌡️+31°C 🌬️↖16km/h

# 计算机组成
## CPU
  计算机硬件基本系统有五大部分组成：**运算器**，**控制器**，**存储器**，**输入设备**，**输出设备**。
  
 1. 存储器分为**内部存储器（即内存，容量小，速度快，临时存放数据）和外部存储器（即硬盘，光盘等，容量大，速度慢，长期保存数据）。**
2. 中央处理单元组成：由**运算器，控制器，寄存器组和内部总线**组成。
	1. 中央处理单元功能：实现**程序控制，操作控制，时间控制，数据处理**功能。
3. 运算器组成：**算术逻辑单元ALU（实现对数据的算术和逻辑运算），累加寄存器AC（运算结果或源操作数的存放区），数据缓冲寄存器DR（暂时存放内容的指令或数据），状态条件寄存器PSW（保存指令运行结果的条件码内容，如溢出标志）**
	1. 运算器功能：**执行所有的算术运算**，如加减乘除等。**执行所有的逻辑运算并进行逻辑测试**，如与，或，非，比较等。
4. 控制器组成：**指令寄存器IR（暂存CPU执行指令），程序计数器PC（存放吓一跳执行地址），地址寄存器AR（保存当前CPU所访问的内存地址），指令译码器ID（分析指令操作码）等组成**。
	1. 控制器功能：控制整个CPU的工作，最为重要，**包括程序控制，时序控制等。**

![image.png](https://cdn.easymuzi.cn/img/20250707210703193.png)

## 寻址方式

| 寻址方式    | 描述                                |
| ------- | --------------------------------- |
| 立即寻址    | 操作数就包含在指令中。                       |
| 直接寻址    | 操作数存放在内存单元中，指令中直接给出操作数所在的存储单元的地址。 |
| 寄存器寻址   | 操作数存放在某一寄存器中，指令中给出存放操作数的寄存器名。     |
| 寄存器间接寻址 | 操作数存放在内存单元中，操作数所在存储单元的地址在某个寄存器中。  |
| 间接寻址    | 指令中给出操作数地址的地址。                    |

> **寻址速度由快到慢依次为：**  
> 立即寻址 → 寄存器寻址 → 直接寻址 → 寄存器间接寻址 → 间接寻址

## 校验码
**码距**：是指**两个合法码字之间不同位数的个数**（就是指一个编码系统中任意两个合法编码之间有多少个二进制位不同）
![image.png](https://cdn.easymuzi.cn/img/20250707214519789.png)
### 奇偶校验码（只能检一位错，并且不能纠错）
**奇偶校验码**是一种简单有效的检验方法，这种方法通过在编码中**增加一位检验位来使编码中1的个数位**奇数（奇校验）或者为偶数（偶校验），从而使码距变为2。
在接收端，重新计算数据中“1”的个数并与预期奇偶性进行比对：
- 若**匹配** → 无错（或是偶数个比特出错，可能无法发现）
- 若**不匹配** → 检测到有错（至少1位比特发生错误）
> ❗ 注意：奇偶校验**只能检测**奇数个比特错误，**不能纠正**错误，也不能检测偶数个比特同时出错。

### 循环冗余校验码CRC（只能检错，不能纠错）
循环冗余校验码广泛应用于数据通信领域和磁介质存储系统，它利用生成多项式为k个数据位产生r个检验位来进行编码，**其编码长度位k+r**。CRC的代码格式为：
![image.png](https://cdn.easymuzi.cn/img/20250707220646130.png)
由此可知，CRC是由两部分组成，左边为信息位（数据），右边为检验码。若信息码占K位，则检验码占n-k位，检验码是由信息码产生的，校验码的位数越多，该代码的检验能力就越强。**在求CRC编码时，采用的是模2运算（按位运算，不发生借位和进位）。**

![image.png](https://cdn.easymuzi.cn/img/20250707221353992.png)
**参考题目示例：**
![image.png](https://cdn.easymuzi.cn/img/20250707221812699.png)

### 海明码
海明码是一种利用**奇偶性来检错和纠错的校验方法**。海明码的构成方法是在数据位之间的特定位置上插入k个检验位，通过扩大码距来实现检错和纠错。
设数据位为n位，检验位是k位，则n和k必须满足以下关系：**2^k -1≥n+k**
**试题参考**：
![image.png](https://cdn.easymuzi.cn/img/20250707222905571.png)
![image.png](https://cdn.easymuzi.cn/img/20250707223307247.png)

| 类型                     | 检错能力      | 纠错能力 | 原理                                     | 条件/备注                     |
| ---------------------- | --------- | ---- | -------------------------------------- | ------------------------- |
| **码距 (Code Distance)** | —         | —    | 任意两个合法编码之间二进制位不同的位数                    | —                         |
| **奇偶校验码**              | 可检测 1 位错误 | 不可纠错 | 在编码中增加 1 位校验位，使所有 “1” 的个数为奇（或偶）        | 码距 = 2                    |
| **循环冗余校验码 (CRC)**      | 可检测错误     | 不可纠错 | 利用生成多项式 f 对 k 位数据生成 r 位校验码，编码时采用模 2 运算 | 编码长度 = k + r；广泛应用于数据通信和存储 |
| **海明码 (Hamming Code)** | 可检测错误     | 可纠错  | 在数据位的特定位置插入 k 个校验位，通过扩展码距实现检错与纠错       | 需满足 2ᵏ − 1 ≥ n + k        |
|                        |           |      |                                        |                           |
## 计算机体系结构分类
### Flynn分类法（理论存在：多指令单数据MISD）
![image.png](https://cdn.easymuzi.cn/img/20250708144411697.png)
### 指令系统
**CISC是复杂指令系统，兼容性强，指令繁多，长度可变，由微程序实现。**
**RISC是精简指令系统，指令少，使用频率接近，主要依靠硬件实现（通用寄存器，硬布线逻辑控制）**
具体区别如下：

| 特性                 | RISC（精简指令集计算机）             | CISC（复杂指令集计算机）          |
|----------------------|------------------------------------|----------------------------------|
| 指令种类             | 少，精简                            | 多，丰富                          |
| 指令复杂度           | 低（简单）                         | 高（复杂）                        |
| 指令长度             | 固定                                | 变化                              |
| 寻址方式             | 少                                  | 复杂多样                          |
| 实现（译码）方式     | 硬布线控制逻辑（组合逻辑控制器）    | 微程序控制技术                    |
| 通用寄存器数量       | 多，大量                            | 一般                              |
| 流水线技术           | 必须实现                            | 可以通过一定方式实现              |
### 指令流水线
流水线周期:指令分成不同执行段，其中执行最长的为段位流水线周期。
**流水线执行时间：1条指令的总执行时间+（总指令条数-1）* 流水线周期。**
流水线吞吐率：总指令条数/流水线执行时间。
流水线加速比：不使用流水线总执行时间/使用流水线总执行时间。
