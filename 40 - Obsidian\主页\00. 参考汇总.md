---
banner: "https://i.pinimg.com/originals/55/74/03/557403fb6cd94aeb6e6a7407c704b666.gif"
banner_icon: 🧭
banner_x: 0.5
banner_y: 0.56828
---

# 导航
[mdict词典论坛](https://www.pdawiki.com/forum/forum.php)
[Regex Learn - 正则表达式交互式课程](https://regexlearn.com/zh-cn/learn)
[苹果URL scheme](https://github.com/bhagyas/app-urls)
[git学习](https://learngitbranching.js.org/)

# Obsidian
[Templater Documentation](https://silentvoid13.github.io/Templater/internal-functions/internal-modules/date-module.html)
[Dataview Documentation](https://blacksmithgu.github.io/obsidian-dataview/api/code-examples/)
[Tasks Documentation](https://schemar.github.io/obsidian-tasks/)
[Minimal Documentation](https://minimal.guide/Home)
[BookNote Documentation](https://kknwfe6755.feishu.cn/docs/doccnBfbtETItLHMmbDBGBRdPrh)
[Advanced URI Documentation](https://vinzent03.github.io/obsidian-advanced-uri/concepts/schema)

# 信息源汇总
[中文Newsletter导航](https://www.notion.so/kfang/Newsletter-68ee46c0a4574f659fb8a873ead438c6)
[优质信息源](https://www.notion.so/ca290ef313804bae8584804440548c80?v=4470668a5078437f816b0273ed042ebf)
[https://rss-source.com](https://rss-source.com/)
[https://www.podletter.club](https://www.podletter.club/)

# 教程
[成双酱的mermaid教程](https://publish.obsidian.md/csj-obsidian/0+-+Obsidian/Mermaid/Mermaid+%E6%B5%81%E5%9B%BE)
[我用的dailynote模板](https://benenewton.medium.com/my-obsidian-daily-note-template-a4bdab53dc62)

# 原子化知识库
[xlore](https://www.xlore.cn/)

# 语言学
[WASL数据库](https://wals.info/languoid/lect/wals_code_mnd)

# 工具
[MathML2Latex](https://demo.wiris.com/mathtype/en/developers.php#output-formats)



