// 定义英文到中文的映射
const translationMap = {
    "Soft": "软件",
    "StudyTool": "学习工具", 
    "学习": "笔记",
    "Code": "编程",
    "100Test": "100test",
    "Interview": "面试训练营",
    "SpringBoot": "SpringBoot",
    "SoftTest": "软考",
    "每日日记": "日记",
    "人员管理": "人员",
    "工作学习": "学习",
    "阅读笔记": "阅读",
    "Obsidian": "工具",
    "卡片知识": "卡片",
};

async function get_categories_from_path(tp) {
    try {
        // 1. 获取文件路径并生成分类
        const currentFile = tp.file.find_tfile(tp.file.title);
        const filePath = currentFile ? currentFile.path : "";
        
        console.log('当前文件路径:', filePath);
        
        // 解析路径生成分类
        const categories = generateCategoriesFromPath(filePath);
        
        // 2. 获取所有标签
        const vault = app.vault;
        const metadataCache = app.metadataCache;
        const allFiles = vault.getMarkdownFiles();
        const usedTags = new Set();
        
        // 遍历所有文件收集标签
        for (const file of allFiles) {
            try {
                const cache = metadataCache.getFileCache(file);
                
                // 从frontmatter获取标签
                if (cache && cache.frontmatter && cache.frontmatter.tags) {
                    const tags = Array.isArray(cache.frontmatter.tags) 
                        ? cache.frontmatter.tags 
                        : [cache.frontmatter.tags];
                    
                    tags.forEach(tag => {
                        if (typeof tag === 'string' && tag.length > 0) {
                            usedTags.add(tag.replace(/^#/, ''));
                        }
                    });
                }
                
                // 从正文获取标签
                if (cache && cache.tags) {
                    cache.tags.forEach(tagRef => {
                        if (tagRef.tag) {
                            usedTags.add(tagRef.tag.replace(/^#/, ''));
                        }
                    });
                }
            } catch (e) {
                console.warn('处理文件时出错:', file.path, e);
            }
        }
        
        // 转换为数组并排序
        const tagArray = Array.from(usedTags).filter(tag => tag && tag.length > 0).sort();
        
        console.log('找到标签数量:', tagArray.length);
        
        // 3. 让用户选择标签
        const selectedTags = [];
        
        if (tagArray.length > 0) {
            // 添加"不选择任何标签"选项
            const tagOptions = ['[不选择标签]', ...tagArray];
            
            while (selectedTags.length < 5) { // 最多选择5个标签
                const choice = await tp.system.suggester(
                    tagOptions.map(tag => 
                        tag === '[不选择标签]' ? tag : 
                        selectedTags.includes(tag) ? `✓ ${tag}` : `  ${tag}`
                    ),
                    tagOptions,
                    false,
                    selectedTags.length === 0 ? 
                        '选择标签 (ESC跳过)' : 
                        `已选择: ${selectedTags.join(', ')} (ESC完成)`
                );
                
                if (!choice || choice === '[不选择标签]') {
                    break;
                }
                
                if (!selectedTags.includes(choice)) {
                    selectedTags.push(choice);
                } else {
                    // 如果已选择，则取消选择
                    const index = selectedTags.indexOf(choice);
                    selectedTags.splice(index, 1);
                }
            }
        }
        
        // 4. 构建最终结果
        let tagsString = '';
        if (selectedTags.length > 0) {
            tagsString = '\n' + selectedTags.map(tag => `  - ${tag}`).join('\n');
        }
        
        const result = `categories:${categories}
tags:${tagsString}`;
        
        console.log('生成结果:', result);
        return result;
        
    } catch (error) {
        console.error('脚本执行失败:', error);
        return `categories:
  - [错误]
tags:
  - 默认标签`;
    }
}

/**
 * 根据文件路径生成分类
 * @param {string} filePath - 文件的完整路径
 * @returns {string} - 格式化的分类字符串
 */
function generateCategoriesFromPath(filePath) {
    if (!filePath || filePath.trim() === '') {
        console.log('文件路径为空，返回空分类');
        return '';
    }
    
    // 1. 去除文件扩展名
    const pathWithoutExtension = filePath.replace(/\.[^/.]+$/, '');
    console.log('去除扩展名后的路径:', pathWithoutExtension);
    
    // 2. 按路径分隔符分割
    let pathParts = pathWithoutExtension.split('/').filter(part => part.trim() !== '');
    console.log('路径分割结果:', pathParts);
    
    // 3. 去除首个文件夹和最后一个文件名
    if (pathParts.length <= 2) {
        // 如果路径层级太少（只有根目录和文件名，或更少），返回空分类
        console.log('路径层级不足，无法生成分类');
        return '';
    }
    
    // 去除首位（根目录）和末位（文件名）
    const categoryParts = pathParts.slice(1, -1);
    console.log('提取的分类路径:', categoryParts);
    
    // 4. 根据映射转换路径名称
    const translatedParts = categoryParts.map(part => {
        const translated = translationMap[part];
        if (translated) {
            console.log(`路径映射: "${part}" -> "${translated}"`);
            return translated;
        } else {
            console.log(`路径保持原样: "${part}"`);
            return part;
        }
    });
    
    console.log('最终分类:', translatedParts);
    
    // 5. 构建分类字符串
    if (translatedParts.length > 0) {
        return `\n  - [${translatedParts.join(', ')}]`;
    } else {
        return '';
    }
}

module.exports = get_categories_from_path;