---
tags:
  - 健身
searchterm: "#健身"
duration: 1 year
cssclasses: []
---



记录日期：2025年07月14日 ~ 2025年07月20日

## 周待办
- [ ] 1
- [ ] 2

# 健康数据分析

## 体重趋势图
``` tracker
searchType: text  
searchTarget: '体重记录\s*::\s*(?<value>[\-]?[0-9]+[\.][0-9]+|[\-]?[0-9]+)'
folder: 00 - 每日日记/01-DailyNote
dateFormat: 'YYYY年MM月DD日'
line:
    title: "本周体重变化"
    xAxisLabel: 日期
    yAxisLabel: 体重(kg)
    fillGap: true
    lineColor: blue
```

## 健康记录统计
```dataviewjs
// 获取本周日期范围
let today = new Date()
let currentWeekDay = today.getDay()
let mondayOffset = currentWeekDay === 0 ? -6 : 1 - currentWeekDay
let monday = new Date(today.getTime() + mondayOffset * 24 * 60 * 60 * 1000)

function formatDate(date) {
    return date.getFullYear() + "年" + 
           String(date.getMonth() + 1).padStart(2, '0') + "月" + 
           String(date.getDate()).padStart(2, '0') + "日"
}

// 生成本周所有日期
let weekDates = []
for (let i = 0; i < 7; i++) {
    let currentDate = new Date(monday.getTime() + i * 24 * 60 * 60 * 1000)
    weekDates.push(formatDate(currentDate))
}

let folderPath = "00 - 每日日记/01-DailyNote"

// 获取对应的文件
let healthData = []
for (let dateStr of weekDates) {
    let fileName = dateStr + ".md"
    let file = app.vault.getAbstractFileByPath(folderPath + "/" + fileName)
    
    if (file) {
        try {
            const content = await app.vault.cachedRead(file)
            let lines = content.split("\n")
            
            // 提取体重记录
            let weightLine = lines.find(line => line.includes("体重记录 ::"))
            let weight = weightLine ? weightLine.split("::")[1].trim() : "-"
            
            // 提取健康记录
            let healthLine = lines.find(line => line.includes("健康记录 ::"))
            let health = healthLine ? healthLine.split("::")[1].trim() : "-"
            
            // 提取每日总结
            let summaryLine = lines.find(line => line.includes("每日总结 ::"))
            let summary = summaryLine ? summaryLine.split("::")[1].trim() : "-"
            
            healthData.push([
                `**[[${dateStr}]]**`, 
                weight ? `**${weight}kg**` : "-", 
                health !== "-" ? `==**${health}**==` : "-",
                summary !== "-" ? `*${summary}*` : "-"
            ])
        } catch (error) {
            healthData.push([
                `**[[${dateStr}]]**`, 
                "-", 
                "-",
                "-"
            ])
        }
    } else {
        healthData.push([
            `**[[${dateStr}]]**`, 
            "-", 
            "-",
            "-"
        ])
    }
}

dv.table(["**日期**", "**体重(kg)**", "**健康记录**", "**每日总结**"], healthData)
```
# 周总结与分析

## 体重变化分析
```dataviewjs
// 获取本周日期范围
let today = new Date()
let currentWeekDay = today.getDay()
let mondayOffset = currentWeekDay === 0 ? -6 : 1 - currentWeekDay
let monday = new Date(today.getTime() + mondayOffset * 24 * 60 * 60 * 1000)

function formatDate(date) {
    return date.getFullYear() + "年" + 
           String(date.getMonth() + 1).padStart(2, '0') + "月" + 
           String(date.getDate()).padStart(2, '0') + "日"
}

// 生成本周所有日期
let weekDates = []
for (let i = 0; i < 7; i++) {
    let currentDate = new Date(monday.getTime() + i * 24 * 60 * 60 * 1000)
    weekDates.push(formatDate(currentDate))
}

let folderPath = "00 - 每日日记/01-DailyNote"

// 收集体重数据
let weightData = []
for (let dateStr of weekDates) {
    let fileName = dateStr + ".md"
    let file = app.vault.getAbstractFileByPath(folderPath + "/" + fileName)
    
    if (file) {
        try {
            const content = await app.vault.cachedRead(file)
            let lines = content.split("\n")
            let weightLine = lines.find(line => line.includes("体重记录 ::"))
            if (weightLine) {
                let weight = weightLine.split("::")[1].trim()
                if (weight && weight !== "-" && !isNaN(parseFloat(weight))) {
                    weightData.push({
                        date: dateStr,
                        weight: parseFloat(weight)
                    })
                }
            }
        } catch (error) {
            // 文件读取失败，跳过
        }
    }
}

// 分析体重变化
if (weightData.length > 1) {
    // 按日期排序
    weightData.sort((a, b) => a.date.localeCompare(b.date))
    
    let firstWeight = weightData[0].weight
    let lastWeight = weightData[weightData.length - 1].weight
    let change = (lastWeight - firstWeight).toFixed(2)
    let changePercent = ((change / firstWeight) * 100).toFixed(2)
    
    // 计算平均体重
    let avgWeight = (weightData.reduce((sum, item) => sum + item.weight, 0) / weightData.length).toFixed(2)
    
    // 趋势分析
    let trend = ""
    let trendIcon = ""
    if (parseFloat(change) > 0) {
        trend = "体重上升"
        trendIcon = "📈"
    } else if (parseFloat(change) < 0) {
        trend = "体重下降"
        trendIcon = "📉"
    } else {
        trend = "体重稳定"
        trendIcon = "➡️"
    }
    
    // 使用表格显示分析结果
    let analysisData = [
        ["📊 **本周体重变化**", `**${change > 0 ? '+' : ''}${change}kg** (${changePercent > 0 ? '+' : ''}${changePercent}%)`],
        ["📅 **起始体重**", `**${firstWeight}kg** (${weightData[0].date})`],
        ["📅 **结束体重**", `**${lastWeight}kg** (${weightData[weightData.length - 1].date})`],
        ["📈 **平均体重**", `**${avgWeight}kg**`],
        ["📝 **记录天数**", `**${weightData.length}天**`],
        ["🎯 **变化趋势**", `**${trendIcon} ${trend}**`]
    ]
    
    dv.table(["**项目**", "**数值**"], analysisData)
    
} else if (weightData.length === 1) {
    let singleData = [
        ["📊 **本周体重记录**", `**${weightData[0].weight}kg**`],
        ["📅 **记录日期**", `**${weightData[0].date}**`],
        ["⚠️ **状态**", "==只有一次记录，无法分析趋势=="]
    ]
    dv.table(["**项目**", "**数值**"], singleData)
} else {
    let noData = [
        ["❌ **状态**", "==本周无体重记录=="],
        ["💡 **建议**", "==请在每日日记中记录体重数据=="]
    ]
    dv.table(["**项目**", "**说明**"], noData)
}
```

## 健康记录汇总
```dataviewjs
// 获取本周日期范围
let today = new Date()
let currentWeekDay = today.getDay()
let mondayOffset = currentWeekDay === 0 ? -6 : 1 - currentWeekDay
let monday = new Date(today.getTime() + mondayOffset * 24 * 60 * 60 * 1000)

function formatDate(date) {
    return date.getFullYear() + "年" + 
           String(date.getMonth() + 1).padStart(2, '0') + "月" + 
           String(date.getDate()).padStart(2, '0') + "日"
}

// 生成本周所有日期
let weekDates = []
for (let i = 0; i < 7; i++) {
    let currentDate = new Date(monday.getTime() + i * 24 * 60 * 60 * 1000)
    weekDates.push(formatDate(currentDate))
}

let folderPath = "00 - 每日日记/01-DailyNote"

// 收集健康记录
let healthRecords = []
for (let dateStr of weekDates) {
    let fileName = dateStr + ".md"
    let file = app.vault.getAbstractFileByPath(folderPath + "/" + fileName)
    
    if (file) {
        try {
            const content = await app.vault.cachedRead(file)
            let lines = content.split("\n")
            let healthLine = lines.find(line => line.includes("健康记录 ::"))
            if (healthLine) {
                let health = healthLine.split("::")[1].trim()
                if (health && health !== "-" && health !== "") {
                    healthRecords.push({
                        date: dateStr,
                        record: health
                    })
                }
            }
        } catch (error) {
            // 文件读取失败，跳过
        }
    }
}

// 显示健康记录汇总
if (healthRecords.length > 0) {
    // 统计信息表格
    let statsData = [
        ["📊 **记录天数**", `**${healthRecords.length}天**`],
        ["📈 **记录率**", `**${((healthRecords.length / 7) * 100).toFixed(1)}%**`],
        ["📅 **记录周期**", `**${weekDates[0]} ~ ${weekDates[6]}**`]
    ]
    
    dv.table(["**统计项目**", "**数值**"], statsData)
    
    // 详细记录表格
    dv.paragraph("### 📝 详细健康记录")
    let detailData = healthRecords.map(record => [
        `**${record.date}**`,
        `==**${record.record}**==`
    ])
    
    dv.table(["**日期**", "**健康状况**"], detailData)
    
} else {
    let noHealthData = [
        ["❌ **状态**", "==本周无健康记录=="],
        ["💡 **建议**", "==请在每日日记中记录健康状况=="],
        ["📅 **记录周期**", `**${weekDates[0]} ~ ${weekDates[6]}**`]
    ]
    dv.table(["**项目**", "**说明**"], noHealthData)
}
```



# 2025年29周记录
![[2025年07月14日#^1]] 
![[2025年07月15日#^1]] 
![[2025年07月16日#^1]] 
![[2025年07月17日#^1]] 
![[2025年07月18日#^1]] 
![[2025年07月19日#^1]] 
![[2025年07月20日#^1]] 

